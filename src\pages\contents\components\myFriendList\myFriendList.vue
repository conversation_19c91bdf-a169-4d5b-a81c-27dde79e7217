<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="u-flex u-row-between" style="width: 750rpx; padding-right: 30rpx; background: #ffffff">
                    <view class="u-flex" @click="backPage">
                        <img
                            src="@/static/goodDetail/back2.png"
                            style="width: 48rpx; height: 48rpx; margin-left: 20rpx"
                        />
                        <view style="margin-left: 20rpx; font-size: 36rpx; font-weight: 600; color: #121212"
                            >添加好友</view
                        >
                    </view>
                    <view class="community-search"> </view>
                </view>
                <block>
                    <view class="swiper-box">
                        <view class="swiper-item">
                            <scroll-view
                                class="scroll-view"
                                scroll-y
                                using-sticky
                                :refresher-enabled="canPullDown"
                                :refresher-triggered="isRefreshing"
                                :upper-threshold="0"
                                :lower-threshold="300"
                                @refresherrefresh="refresherrefresh"
                                @scrolltolower="onScrollToLower"
                            >
                                <view class="u-flex-col">
                                    <view class="u-flex u-search-top u-row-between">
                                        <view class="u-flex" @click="goSearch">
                                            <img
                                                src="@/static/friend/icon_friend_search.png"
                                                style="width: 36rpx; height: 36rpx"
                                            />
                                            <input class="u-search" type="text" placeholder="搜索用户名字" />
                                        </view>
                                        <view @click="goScan" class="u-flex">
                                            <img
                                                src="@/static/friend/icon_friend_scan.png"
                                                style="width: 36rpx; height: 36rpx"
                                            />
                                        </view>
                                    </view>
                                </view>
                                <view class="u-flex-col u-col-center u-avatur-top">
                                    <view class="u-avatar">
                                        <canvas
                                            id="qrcodeCanvas"
                                            canvas-id="qrcodeCanvas"
                                            style="width: 100%; height: 100%"
                                        ></canvas>
                                    </view>
                                    <view class="u-title"> {{ userInfo.nick }} </view>
                                    <view class="u-id">追觅号：{{ userInfo.uid }}</view>
                                    <view class="u-weixin" @click="goScan">
                                        <view class="u-flex">
                                            <img
                                                src="@/static/friend/icon_weixin_scan.png"
                                                style="width: 96rpx; height: 96rpx"
                                            />
                                            <view class="u-flex-col" style="margin-left: 32rpx">
                                                <view class="u-weixin-title">扫一扫</view>
                                                <view class="u-weixin-name" style="margin-top: 20rpx"
                                                    >扫描二维码加朋友</view
                                                >
                                            </view>
                                        </view>
                                        <img src="@/static/customer_arrow.png" style="width: 36rpx; height: 36rpx" />
                                    </view>
                                    <view
                                        class="u-weixin"
                                        @click="goAddressBook"
                                        v-if="UserModule && UserModule.sdkVersion >= 16"
                                    >
                                        <view class="u-flex">
                                            <img
                                                src="@/static/friend/icon_book_address.png"
                                                style="width: 96rpx; height: 96rpx"
                                            />
                                            <view class="u-flex-col" style="margin-left: 32rpx">
                                                <view class="u-weixin-title">手机通讯录</view>
                                                <view class="u-weixin-name" style="margin-top: 20rpx"
                                                    >添加通讯录中朋友
                                                </view>
                                            </view>
                                        </view>
                                        <img src="@/static/customer_arrow.png" style="width: 36rpx; height: 36rpx" />
                                    </view>
                                    <view class="u-weixin" @click="shareWeXin">
                                        <view class="u-flex">
                                            <img
                                                src="@/static/friend/icon_weixin.png"
                                                style="width: 96rpx; height: 96rpx"
                                            />
                                            <view class="u-flex-col" style="margin-left: 32rpx">
                                                <view class="u-weixin-title">添加微信朋友</view>
                                                <view class="u-weixin-name" style="margin-top: 20rpx"
                                                    >分享我的口令到微信添加朋友
                                                </view>
                                            </view>
                                        </view>
                                        <img src="@/static/customer_arrow.png" style="width: 36rpx; height: 36rpx" />
                                    </view>
                                    <view class="u-weixin" @click="shareWeXinCircle">
                                        <view class="u-flex">
                                            <img
                                                src="@/static/friend/icon_weixin_circle.png"
                                                style="width: 96rpx; height: 96rpx"
                                            />
                                            <view class="u-flex-col" style="margin-left: 32rpx">
                                                <view class="u-weixin-title">分享到朋友圈</view>
                                                <view class="u-weixin-name" style="margin-top: 20rpx"
                                                    >分享到朋友圈添加朋友</view
                                                >
                                            </view>
                                        </view>
                                        <img src="@/static/customer_arrow.png" style="width: 36rpx; height: 36rpx" />
                                    </view>
                                </view>
                                <view class="u-recommend-box" style="margin-top: 40rpx">
                                    <view class="u-flex u-row-between">
                                        <view class="u-recommend-title">推荐关注</view>
                                        <view class="u-flex" style="margin-right: 24rpx" @click="goRefresh">
                                            <img
                                                src="@/static/friend/icon_friend_refresh.png"
                                                style="width: 40rpx; height: 40rpx"
                                            />
                                            <view style="font-size: 28rpx; color: #777777; margin-left: 10rpx"
                                                >刷新
                                            </view>
                                        </view>
                                    </view>
                                    <view
                                        class="u-recommend-content"
                                        v-for="(item, index) in recommentList"
                                        :key="index"
                                    >
                                        <view class="u-recommend-left">
                                            <view
                                                style="width: 96rpx; height: 96rpx; border-radius: 48rpx; position: relative;"
                                                @click="goUserInfo(item.user_id)"
                                            >
                                                <img
                                                    :src="item.avatar"
                                                    style="width: 96rpx; height: 96rpx; border-radius: 48rpx"
                                                />
                                              <img v-if="item.user_avatar_icon" :src="item.user_avatar_icon" style="width: 35rpx; height:35rpx; border-radius: 17.5rpx; position: absolute;bottom: 0;right: 0;" />
                                            </view>
                                            <view class="u-flex-col" style="margin-left: 32rpx; width: 320rpx">
                                                <view style="font-size: 32rpx; color: #121212"
                                                    >{{ item.nick_name }}
                                                </view>
                                                <view style="font-size: 28rpx; color: #777777">{{ item.uid }}</view>
                                            </view>
                                        </view>
                                        <view
                                            class="u-recomment-right"
                                            v-if="item.follow_status == 0"
                                            @click="handleUserFollow(item, index)"
                                        >
                                            <img
                                                src="@/static/friend/icon_recomment_add.png"
                                                style="width: 32rpx; height: 32rpx"
                                            />
                                            关注
                                        </view>
                                        <view
                                            class="u-recomment-right-yes"
                                            v-if="item.follow_status == 1"
                                            @click="handleUserFollow(item, index)"
                                        >
                                            已关注
                                        </view>
                                    </view>
                                </view>
                                <view class="u-good-box" style="margin-top: 40rpx">
                                    <view class="u-good-title">推荐商品</view>
                                    <view class="u-good-content">
                                        <block v-for="(itm, index) in goodsList" :key="index">
                                            <view class="class-item" @click="gotoGdetailPage(itm.gid, itm.name)">
                                                <view class="class-item-top" :class="{ extend: !!itm.atmosphere_img }">
                                                    <LazyImage :src="itm.cover_image" class="lazyImg"> </LazyImage>
                                                </view>
                                                <view class="class-item-bottom">
                                                    <view class="u-good-title-img">
                                                        <view class="text" style="font-size: 28rpx; color: #121212">
                                                            <!-- <img class="indent-icon"
                                                                src="@/static/friend/icon_brand.png"
                                                                style="width: 52rpx; height: 26rpx;"> -->
                                                            {{ itm.name }}
                                                        </view>
                                                    </view>
                                                    <view
                                                        style="
                                                            font-size: 28rpx;
                                                            color: #404040;
                                                            font-weight: 500;
                                                            margin-top: 24rpx;
                                                        "
                                                        >￥{{ itm.price }}</view
                                                    >
                                                </view>
                                            </view>
                                        </block>
                                    </view>
                                    <view class="u-good-bottom" @click="gotoMoreGood(2, '商品')"> 查看更多商品 </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </block>
            </view>
        </view>
        <NoNetwork v-else :isNetworkConnect="isNetworkConnect" @onRefresh="onRefresh" />
        <u-popup :show="show" @close="close" mode="center" round="24rpx">
            <view class="u-share-weixin">
                <view class="u-share-weixin-title"
                    >分享
                    <view class="u-share-weixin-title-close" @click="close">
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68935c1c587663620011876.png"
                            style="width: 48rpx; height: 48rpx"
                        />
                    </view>
                </view>
                <view class="u-share-weixin-content">
                    <!-- 用户头像 -->
                    <view class="u-share-weixin-content-avatar">
                        <img :src="userInfo.avatar" style="width: 204rpx; height: 204rpx" />
                    </view>
                    <!-- 用户口令 -->
                    <view class="u-share-weixin-content-password">
                        {{ sharePassword }}
                    </view>
                    <view class="u-share-weixin-content-description"> 口令已复制，发送朋友可以关注你 </view>
                    <view class="u-share-weixin-content-button" @click="copyPassword">
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68935e7d4f7543250010377.png"
                        />
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import { AppModule } from '@/store/modules/app';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import {
    searchFollowList,
    userFollow,
    userRelation,
    userFollowDelete,
    getRecommentList,
} from '@/http/requestGo/community';
import { goodsList } from '@/http/goods';
import * as QRCode from '@/wxcomponents/painter/lib/qrcode';
import Constants from '@/common/Constants';
import { VIPModule } from '@/store/modules/vip';

@Component({
    components: {
        NoNetwork,
        Comment,
        DefaultPage,
    },
})
export default class Contents extends Vue {
    $refs: {
        exploreRef;
        CommunityRef;
    };

    // 网络状态
    get isNetworkConnect(): Boolean {
        return true;
    }

    get UserModule() {
        return UserModule;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    public personalList: any = [];
    public recommentList: any = [];
    public allCommentList: any = [];
    public goodsList: any = [];
    public currentPage: number = 1;
    public show: boolean = false;
    public commentCount: number = 0;

    public scrollTop: number = 0; // 控制scroll-view滚动位置
    public flowList: any = [];
    public isLoading: Boolean = true;
    public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成
    public isRefreshing: Boolean = false;

    public page: number = 1;
    public page_size: number = 10;
    public Pid: number = 0;
    public totalPages: number = 1;
    public timer: any = null;
    public index: number = 0;
    public creator: any = -1;
    public sharePassword: string = '';
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    refresherrefresh() {
        // 重置分页信息，重新加载第一页
        this.page = 1;
        this.totalPages = 1;
        this.personalList = [];
        this.isRefreshing = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
        }, 200);
        this.$emit('refresh');
    }

    onScrollToLower() {
        console.log(';onScrollToLower');
        if (this.page >= this.totalPages) {
            return;
        }
        this.loadMore();
    }

    shareWeXin() {
        this.openShareWeXin();
    }

    loadMore() {
        // 如果正在加载中或已加载全部数据，不执行操作
        if (this.isLoading || this.page >= this.totalPages) return;
        this.page++;
        this.init();
    }

    // 在二维码中心绘制圆形头像（带白色底圈）
    private drawCenterAvatar(ctx: any, centerX: number, centerY: number, avatarSize: number): Promise<void> {
        const avatarUrl = this.userInfo?.avatar || 'https://wpm-cdn.dreame.tech/images/202301/185295-1673921873152.png';
        return new Promise<void>((resolve) => {
            uni.getImageInfo({
                src: avatarUrl,
                success: (info) => {
                    const size = Math.floor(avatarSize);
                    const border = Math.max(2, Math.floor(size * 0.06));
                    const x = Math.floor(centerX - size / 2);
                    const y = Math.floor(centerY - size / 2);
                    const radius = Math.floor(size * 0.3); // 圆角半径（更大）

                    const roundedRectPath = (px: number, py: number, w: number, h: number, r: number) => {
                        const rr = Math.min(r, Math.floor(Math.min(w, h) / 2));
                        ctx.beginPath();
                        ctx.moveTo(px + rr, py);
                        ctx.lineTo(px + w - rr, py);
                        ctx.arc(px + w - rr, py + rr, rr, -Math.PI / 2, 0);
                        ctx.lineTo(px + w, py + h - rr);
                        ctx.arc(px + w - rr, py + h - rr, rr, 0, Math.PI / 2);
                        ctx.lineTo(px + rr, py + h);
                        ctx.arc(px + rr, py + h - rr, rr, Math.PI / 2, Math.PI);
                        ctx.lineTo(px, py + rr);
                        ctx.arc(px + rr, py + rr, rr, Math.PI, 1.5 * Math.PI);
                        ctx.closePath();
                    };

                    // 白色圆角底，增强可识别性
                    ctx.save();
                    roundedRectPath(x - border, y - border, size + border * 2, size + border * 2, radius + border);
                    ctx.setFillStyle('#FFFFFF');
                    ctx.fill();
                    ctx.restore();

                    // 圆角裁剪并绘制头像
                    ctx.save();
                    roundedRectPath(x, y, size, size, radius);
                    ctx.clip();
                    // 以cover方式裁切：居中裁剪，保证铺满且不变形
                    const imgW = info.width || size;
                    const imgH = info.height || size;
                    let sX = 0;
                    let sY = 0;
                    let sW = imgW;
                    let sH = imgH;
                    if (imgW / imgH > 1) {
                        // 图片偏宽，左右裁切
                        sH = imgH;
                        sW = imgH;
                        sX = Math.floor((imgW - sW) / 2);
                        sY = 0;
                    } else if (imgW / imgH < 1) {
                        // 图片偏高，上下裁切
                        sW = imgW;
                        sH = imgW;
                        sX = 0;
                        sY = Math.floor((imgH - sH) / 2);
                    }
                    ctx.drawImage(info.path, sX, sY, sW, sH, x, y, size, size);
                    ctx.restore();
                    resolve();
                },
                fail: () => resolve(),
            });
        });
    }

    async onLoad() {
        this.personalList = [];
        this.init();
        this.getRecommentList();
        this.getRecommendGood();
        UserModule.getUserInfo();
    }

    async onRefresh() {
        this.init();
    }

    close() {
        this.show = false;
    }

    openShareWeXinCircle() {
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/selfCommunity/selfCommunity?creator=${this.userInfo.user_id}`;
        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };

        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            const params = {
                target: 'weixin_circle',
                type: 'web',
                content: {
                    url: res.data,
                    image: this.userInfo.avatar,
                    title: this.userInfo.nick + '的个人主页',
                },
            };
            if (UserModule.sdkVersion >= 15) {
                Utils.newMessageChannel('SHARE', 'shareWithoutUI', params);
            } else {
                Utils.newMessageChannel('SHARE', 'share', params);
            }
        });
        // this.show = true;
    }

    openShareWeXin() {
        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/selfCommunity/selfCommunity?creator=${this.userInfo.user_id}`;
        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };
        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            // 口令
            this.sharePassword = `加我DREAME APP好友，来这里看日常/发长文/一起聊天，还能解锁更多超值福利哦~` + res.data;
        });
        this.show = true;
    }

    copyPassword() {
        // 如果是sdk15
        if (UserModule.sdkVersion >= 15) {
            Utils.newMessageChannel('SHARE', 'shareWithoutUI', {
                target: 'wechat',
                type: 'text',
                content: {
                    title: this.sharePassword,
                },
            });
        } else {
            uni.setClipboardData({
                data: this.sharePassword,
                success: () => {
                    Utils.Toast('口令已复制');
                },
            });
        }
    }

    async init() {
        if (this.page > this.totalPages) {
            this.isLoading = false;
            return;
        }
        try {
            this.isLoading = true;
            const page_size: number = 10;
            const res = await searchFollowList({ keyword: '', page: this.page, page_size: this.page_size, type: [3] });
            console.log('res ===', res);
            if (res.success) {
                const { list = [] } = res.data;
                if (list === null || list.length < page_size) {
                    this.page = 1;
                }
                if (list.length > 0) {
                    this.personalList = this.personalList.concat(list);
                }
                this.canPullDown = false;
                console.log('personalList ==== ', this.personalList);
                this.page++;
            }
            this.canPullDown = true;
            this.isLoading = false;
        } catch (e) {
            this.canPullDown = true;
            this.isLoading = false;
            console.error('getWlist err=', e);
        }
    }

    async getRecommentList() {
        this.recommentList = [];
        const res = await getRecommentList({ user_id: '', page: this.page, page_size: 21 });
        if (res.success) {
            this.allCommentList = res.data.list;
            if (res.data.list) {
                if (res.data.list.length >= 3) {
                    for (let i = 0; i < 3; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                } else {
                    for (let i = 0; i < res.data.list.length; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                }
            }
        }
    }

    get userInfo() {
        return UserModule.userInfo;
    }

    async getRecommendGood() {
        const params = {
            page: 1,
            page_size: 4,
            single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
            type: -2,
            tid: -1,
        };
        const res = await goodsList(params);
        if (res.list) {
            for (let index = 0; index < res.list.length; index++) {
                const element = res.list[index];
                if (index <= 3) {
                    this.goodsList.push(element);
                }
            }
        }
    }

    goRefresh() {
        if (this.commentCount >= 10) {
            this.commentCount = 0;
            this.getRecommentList();
        } else {
            const count = this.allCommentList.length / 3;
            if (this.currentPage == count) {
                this.currentPage = 1;
            } else {
                this.currentPage++;
            }
            this.recommentList = this.allCommentList.slice((this.currentPage - 1) * 3, (this.currentPage - 1) * 3 + 3);
        }
    }

    goScan() {
        if (UserModule.sdkVersion < 15) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        Utils.newMessageChannel('QR', 'scanFriends', {}, (res) => {
            const { creator } = res.data;
            Utils.navigateTo(`/pagesC/selfCommunity/selfCommunity?creator=${creator}`);
        });
    }

    stringToBase64(str: string) {
        return btoa(str);
    }

    qrcodeUrl() {
        const url = {
            url: `pagesC/selfCommunity/selfCommunity?creator=${this.userInfo.user_id}`,
        };
        const base64 = this.stringToBase64(JSON.stringify(url));
        const escaped = encodeURIComponent(base64);
        return `https://app.dreame.tech/?mall_page=${escaped}`;
    }

    gotoGdetailPage(gid: string, name: string) {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
                id: Number(gid),
                name: name,
            },
            true,
        );
        Utils.reportEvent('product_click', { product_id: gid });
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}`);
    }

    public qrcodeConfig = {
        url: ``, // 二维码链接
        canvasWidth: 390,
        canvasHeight: 844,
        qrSize: 106,
        bgColor: '#ffffff',
        fgColor: '#2D0C00',
        ecLevel: 2,
    };

    async mounted(): Promise<void> {
        await UserModule.getUserInfo();
        await VIPModule.getBasicInfo();
        this.qrcodeConfig.url = this.qrcodeUrl();
        await this.generateCanvasWithQRCode();
    }

    generateCanvasWithQRCode(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.$nextTick(() => {
                setTimeout(async () => {
                    try {
                        const ctx = uni.createCanvasContext('qrcodeCanvas', this);
                        const systemInfo = uni.getSystemInfoSync();
                        const screenWidth = systemInfo.screenWidth || 390;
                        const rpxToPx = screenWidth / 750;

                        const canvasWidth = Math.round(302 * rpxToPx); // 假设 u-avatar 宽度为 100rpx
                        const canvasHeight = Math.round(302 * rpxToPx); // 假设 u-avatar 高度为 100rpx
                        const qrSize = Math.round(236 * rpxToPx); // 二维码大小

                        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                        QRCode.api.draw(
                            this.qrcodeConfig.url,
                            ctx,
                            (canvasWidth - qrSize) / 2,
                            (canvasHeight - qrSize) / 2,
                            qrSize,
                            qrSize,
                            this.qrcodeConfig.bgColor,
                            this.qrcodeConfig.fgColor,
                            this,
                            this.qrcodeConfig.ecLevel,
                        );

                        const avatarSize = Math.round(qrSize * 0.28); // 头像大小占二维码宽度的约28%
                        const centerX = Math.round(canvasWidth / 2);
                        const centerY = Math.round(canvasHeight / 2);
                        await this.drawCenterAvatar(ctx, centerX, centerY, avatarSize);
                        ctx.draw();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }, 300);
            });
        });
    }

    shareWeXinCircle() {
        this.openShareWeXinCircle();
    }

    goAddressBook() {
        Utils.newMessageChannel('PERMISSION', 'request', { permission: 'contacts' }, (res) => {
            if (res.code === 0) {
                Utils.newMessageChannel('CONTACT', 'contactList', {}, (res) => {
                    if (res.code === 0) {
                        // 获取所有手机号
                        const allPhones = res.data.reduce((acc, item) => {
                            return acc.concat(item.phones);
                        }, []);
                        // 一个人有多个手机号码就被拆分成了多个独立的对象
                        console.log(res.data, 'res.data')
                        const formatPhoneList = res.data.flatMap((item) =>
                            item.phones.length > 1
                                ? item.phones.map((phone) => ({ name: item.name, phone }))
                                : { name: item.name, phone: item.phones.join() },
                        );
                        console.log(formatPhoneList, allPhones, 'formatPhoneList');
                        Utils.navigateTo(
                          `/pages/contents/components/attention/attention?allPhones=${encodeURIComponent(JSON.stringify(allPhones),
                          )}&formatPhoneList=${encodeURIComponent(JSON.stringify(formatPhoneList))}`,
                        );
                    }
                });
            }
        })
    }

    goUserInfo(creator) {
        console.log('goUserInfo', creator);
        Utils.jumpPersonHome(creator);
    }

    handleUserFollow(item, index) {
        console.log(item);
        this.index = index;
        this.creator = item.user_id;

        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.user_id);
            if (item.follow_status === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    this.commentCount = +this.commentCount;
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        item.follow_status = res.data.follow_status;
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +this.creator });
                if (res.success) {
                    Utils.Toast('取消关注');
                    this.commentCount = -this.commentCount;
                    userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                }
                this.operContentOpen = false;
                setTimeout(() => {
                    this.operContent = { type: '' };
                }, 500);
            }
        }, 500);
    }

    changeFollowStatus(item, status) {
        this.recommentList.forEach((i) => {
            if (i.user_id === item.user_id) {
                i.follow_status = status;
            }
        });
    }

    gotoMoreGood(tid, title) {
        if (tid == 20) {
            Utils.reportEvent('parts_zone', {});
        }
        if (title) {
            Utils.reportEvent('see_all', {
                good_type: title,
            });
        }
        uni.setStorageSync('tid', tid);
        Utils.navigateTo('/pages/shop/shop?tid=' + tid);
    }

    clickFriend(item, index) {
        if (index !== -1) {
            this.$set(this.recommentList[index], 'follow_status', 1);
        }
    }

    goSearch() {
        Utils.navigateTo(`/pagesC/search/search?type=user`);
    }

    public canPullDown: boolean = true;

    backPage() {
        Utils.goBack();
    }
}
</script>

<style lang="scss" scoped>
@import './myFriendList.scss';
</style>
