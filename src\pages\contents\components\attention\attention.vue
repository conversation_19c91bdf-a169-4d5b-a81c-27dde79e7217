<template>
    <view class="contents-container" v-if="isNetworkConnect">
        <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
            <view class="u-flex u-row-between" style="width: 750rpx; padding-right: 30rpx; background: #ffffff">
                <view class="u-flex" @click="backPage">
                    <img src="@/static/goodDetail/back2.png"
                        style="width: 48rpx; height: 48rpx; margin-left: 20rpx" />
                    <view style="margin-left: 20rpx; font-size: 36rpx; font-weight: 600; color: #121212">关注通讯录好友</view>
                </view>
                <view class="community-search" @click="goInvite">
                  <img src="@/static/friend/icon_visit.png"/>
                </view>
            </view>
        </view>
        <view v-if="userList.length">
          <block>
              <view class="swiper-box">
                  <view class="swiper-item">
                      <scroll-view class="scroll-view" scroll-y>
                          <view class="u-good-box">
                              <view class="u-user-content" style="margin-bottom: 48rpx;" v-for="(item, index) in userList"
                                  :key="index">
                                  <view class="u-recommend-left">
                                      <view style="width: 96rpx; height: 96rpx; border-radius: 48rpx;">
                                          <img :src="item.avatar" style="width: 96rpx; height: 96rpx; border-radius: 48rpx;" />
                                      </view>
                                      <view class="u-flex-col" style="margin-left: 32rpx; width: 320rpx;">
                                          <view style="font-size: 32rpx; color: #121212;">{{ item.nick_name }}
                                          </view>
                                          <view style="font-size: 28rpx; color: #777777;">{{ item.uid }}</view>
                                      </view>
                                  </view>
                                  <view>
                                      <image class="attention" v-if="item.follow_status === 0" @click.stop="_ => toggleAttention(item)" src="@/static/friend/icon_attention.png" />
                                      <image class="attention" v-if="item.follow_status === 1" @click.stop="_ => toggleAttention(item)" src="@/static/friend/icon_Followed.png" />
                                      <image class="attention" v-if="item.follow_status === 2" @click.stop="_ => toggleAttention(item)" src="@/static/friend/icon_returnAttention.png" />
                                      <image class="attention" v-if="item.follow_status === 3" @click.stop="_ => toggleAttention(item)" src="@/static/friend/icon_all_attention.png" />
                                  </view>
                                  <!-- <view class="u-recomment-right" v-if="item.follow_status == 0"
                                      @click="handleUserFollow(item, index)">
                                      <img src="@/static/friend/icon_recomment_add.png"
                                          style="width: 32rpx; height: 32rpx;">
                                      关注
                                  </view>
                                  <view class="u-recomment-right-yes" v-if="item.follow_status == 1"
                                      @click="handleUserFollow(item, index)">
                                      已关注
                                  </view> -->
                              </view>
                          </view>
                      </scroll-view>
                  </view>
              </view>
          </block>
          <view class="allSelect bottom u-flex u-row-between u-col-center">
              <img class="allattention_bottom"  @click.stop="_ => toggleAllAttention()" src="@/static/friend/icon_allattention_bottom.png" />
          </view>
        </view>
        <view v-else class="empty-box u-flex u-flex-col u-row-center u-col-center empty-page"
            >
            <view class="none u-flex u-col-center">
                <image class="empty-icon" :style="{
                    width: 360 + 'rpx',
                    height: 360 + 'rpx',
                    marginBottom: 16 + 'rpx',
                }" src="@/static/friend/icon_empty.png" />
            </view>
            <text class="title">——暂时没有找到通讯录朋友——</text>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import { getPhoneUser, userFollow, userFollowDelete, userAllFollow } from '@/http/requestGo/community';

@Component({
    components: {
    },
})
export default class Contents extends Vue {
    // 网络状态
    get isNetworkConnect(): Boolean {
        return true;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    public scrollTop: number = 0; // 控制scroll-view滚动位置
    public flowList: any = [];
    public isLoading: Boolean = true;
    // public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成
    public isRefreshing: Boolean = false;
    // public page: number = 1;
    // public page_size: number = 10;
    // public totalPages: number = 1;
    public timer: any = null;
    public canPullDown: boolean = true;
    public userList: any = [];
    public checked: boolean = false;
    public allSelect: boolean = false;
    public allPhones: any = [];
    public formatPhoneList: any = [];
    onLoad(option) {
        // console.log(option.list, 'optionoptionoption');
        const arr1 = decodeURIComponent(option.allPhones)
        this.allPhones = JSON.parse(arr1);
        if (this.allPhones.length) {
          this.init()
        }
        this.formatPhoneList = option.formatPhoneList
    }

    // 列表查询
    async init() {
      console.log(this.allPhones, 'lhlhlh')
        // if (this.page > this.totalPages) {
        //     this.isLoading = false;
        //     return;
        // }
        this.userList = []
        try {
            this.isLoading = true;
            const res = await getPhoneUser({ phone: this.allPhones, user_id: +UserModule.user_id || 0, });
            console.log('res ===', res);
            if (res.success) {
              const { list = [] } = res.data;
              this.userList = list || []
            }
            this.isLoading = false;
        } catch (e) {
            this.isLoading = false;
            console.error('getWlist err=', e);
        }
    }

    get userInfo() {
      return UserModule.userInfo;
    }

    // 去邀请未注册的用户
    goInvite () {
      Utils.navigateTo(`/pages/contents/components/addressBook/addressBook?formatPhoneList=${this.formatPhoneList || []}`);
    }

    backPage() {
        Utils.goBack();
    }

    toggleSelected(userList, isSelected: boolean) {
      this.$set(userList, 'checked', isSelected)
    }

    // 全选
    onAllSelect() {
        this.userList.forEach(item => {
            this.$set(item, 'checked', !this.allSelect)
        })
        this.allSelect = !this.allSelect
    }

    // 添加联系人
    handleAddUser() {
        const checked = this.userList.some(item => {
          return item.checked;
        });
        if (!checked) {
          Utils.Toast('请至少选择一个联系人添加！')
        }
    }

    // 关注  1 关注 2 被关注 3 互关
    toggleAttention(item) {
        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.user_id);
            if (item.follow_status === 0 || item.follow_status === 2) {
                userFollow({ user_id, followed_user_id: other_id }).then((res) => {
                    Utils.Toast('关注成功');
                    if (item.follow_status === 0) {
                      item.follow_status = 1
                    } else {
                      item.follow_status = 3
                    }
                    // this.init()
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +item.user_id });
                if (res.success) {
                    Utils.Toast('取消关注');
                    if (item.follow_status === 1) {
                      item.follow_status = 0
                    } else {
                      item.follow_status = 2
                    }
                    // this.init()
                }
            }
        })
    }

    // 批量关注
    toggleAllAttention() {
        const followed_user_ids = this.userList.map(item => Number(item.user_id));
        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            userAllFollow({ user_id, followed_user_ids: followed_user_ids }).then((res) => {
                console.log(res, 'res222')
                if (res.success) {
                    Utils.Toast('关注成功');
                    this.init()
                }
            });
        })
    }
}
</script>

<style lang="scss" scoped>
@import './attention.scss'
</style>
