<template>
    <view class="tab-item-wrap">
        <image class="service_item_icon" :src="icon"></image>
         <!-- <image class="service_item_icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6878cc4640f532660010568.png"></image> -->
        <view class="service_item_name">{{ text }}</view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
        // uScrollList,
        // CoinPoints,
        // MemberLevel,
    }
})
export default class TabList extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: [Number, String], default: '99' })
    readonly icon!: any;

    @Prop({ type: [Number, String], default: '99' })
    readonly text!: any;

    async created() {
        // await this.fetchTagList();
    }
}
</script>

<style lang="scss" scoped>
 .tab-item-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
    width: calc((100% - 80rpx) / 5); /* 一行5个，减去4个间隔的宽度 */
    min-width: 120rpx;
    // padding-top: 24rpx;

    .service_item_icon{
        height: 100rpx;
        width: 100rpx;
    }
    .service_item_name{
        font-size: 24rpx;
        margin-top: 12rpx;
        font-weight: regular;
        color: #404040;
        /* 禁止换行 */
        white-space: nowrap;
        text-align: center;
    }
}
</style>
