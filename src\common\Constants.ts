export default class Constants {
    constructor() { }
    public static RET_OK = 1; // 登录成功
    public static RET_CUSTOM_JSON = 2; // 自定义json包
    public static RET_ERR = -1; // 请求失败
    public static RET_SESSION_EXP = -100; // 登录超时
    public static GO_RET_SESSION_EXP = 402;
    public static GO_RET_OK = 0; // 请求成功
    public static GO_RET_ERR = 10005; // 请求失败

    public static minLeft = 19; // 头像距离左侧最下距离

    public static maxLeft = 48; // 头像距离左侧最下距离

    public static ORDER_STATUS_WAIT_PAY = '0'; // 订单待付款状态
    public static ORDER_STATUS_WAIT_SEND = '300'; // 订单待发货状态
    public static ORDER_STATUS_TAKE_GOODS = '400'; // 订单待收货状态
    public static ORDER_STATUS_FINISH = '500'; // 订单已完成状态
    public static ORDER_STATUS_CLOSE = '100'; // 订单交易关闭状态
    public static ORDER_STATUS_REFUNDING = '10000000'; // 订单退款中状态
    public static ORDER_STATUS_REFUNDIED = '20000000'; // 订单退款成功状态
    public static ORDER_STATUS_CAN_REFUN = '10000'; // 订单申请退款状态
    public static ORDER_STATUS_REFUND_AUDIT = '1000'; // 订单退款平台待审核状态
    public static ORDER_STATUS_REFUND_P_PASS = '1010'; // 订单退款平台审核通过状态
    public static ORDER_STATUS_REFUND_P_REJECT = '1020'; // 订单退款平台审核拒绝状态

    public static USER_ORDER_TYPE_COMMON = 1; // 普通订单
    public static USER_ORDER_TYPE_TAIL = 2; // 尾款订单
    public static USER_ORDER_TYPE_DEPOSIT = 3; // 定金订单

    public static COUPON_TYPE_JYB = '6'; // 优惠券类型 兑换券

    public static PROD_STATUS_NONE_GOODS = '0';
    public static STATUS_SOLD_OUT = '1';
    public static STATUS_DEL = '1';
    public static SEARCH_HISTORY_LENGTH = 10;

    public static ALIPAY = {
        code: 4,
        name: ' 支付宝',
    };

    public static WECHAT_NATIVE = {
        code: 3,
        name: '微信',
    };

    public static WECHAT_MP = {
        code: 1,
        name: '微信',
    };

    public static WECHAT_H5 = {
        code: 2,
        name: '微信',
    };

    public static JDPAY_MP = {
        code: 9,
        name: '京东',
    };

    public static JDPAY_H5 = {
        code: 12,
        name: '京东白条支付',
    };

    public static POLLING_TIMEOUT = 3000;

    public static CUSTOM_BAR_HEIGHT = 108; // 自定义header高度

    public static ORDER_STATUS = {
        0: '待付款',
        300: '待发货',
        400: '待收货',
        500: '已完成',
        100: '交易关闭',
        10000: '退款',
        10000000: '退款中',
        20000000: '交易关闭',
    };

    public static SHOP_STAUTS = {
        10000000: '退款中',
        20000000: '退款成功',
    };

    public static TAB_BAR_LIST = [
        '/pages/index/index',
        '/pages/shop/shop',
        '/pages/contents/contents',
        '/pages/mine/mine',
        '/pages/vipCenter/vipCenter',
    ]; // 首页tabbar列表

    public static ERROR_ORDER_FAIL_E_REASON = '1000'; // 疫情地区不允许配送
    public static MINIPROGRAM = {
        AFTER_SALE: 'wx56734f71d3c629f5', // 售后服务/产品寄修
    };

    // 上报类型
    public static TYPE_REPORT_CONTENTS_PAGE = '10003'; // 内容模块 分享

    // 上下水
    public static DTD_TYPE_EXPLORE_BOOKING = '1';
    public static DTD_TYPE_INSTALL = '2';
    public static DTD_TYPE_SERVE = '3';
    public static DTD_SERVICE_TYPES_ARR = {
        [Constants.DTD_TYPE_EXPLORE_BOOKING]: '勘测预约',
        [Constants.DTD_TYPE_INSTALL]: '安装工单',
        [Constants.DTD_TYPE_SERVE]: '上门服务预约',
    };

    public static DTD_SERVICE_STATUS_WAIT = '0'; // 待服务状态
    public static DTD_SERVICE_STATUS_CANCEL = '1'; // 已取消状态
    public static DTD_SERVICE_STATUS_FINSHED = '2'; // 已完成状态
    public static DTD_SERVICE_STATUS_SERVEING = '3'; // 服务中状态
    public static DTD_SERVICE_STATUS_WAIT_PAY = '4'; // 待付款状态

    public static DTD_SERVICE_STATUS_ARR = {
        [Constants.DTD_SERVICE_STATUS_WAIT]: {
            value: Constants.DTD_SERVICE_STATUS_WAIT,
            name: '待服务',
            className: 'status-done',
        },
        [Constants.DTD_SERVICE_STATUS_CANCEL]: {
            value: Constants.DTD_SERVICE_STATUS_CANCEL,
            name: '已取消',
            className: '',
        },
        [Constants.DTD_SERVICE_STATUS_FINSHED]: {
            value: Constants.DTD_SERVICE_STATUS_FINSHED,
            name: '已完成',
            className: '',
        },
        [Constants.DTD_SERVICE_STATUS_SERVEING]: {
            value: Constants.DTD_SERVICE_STATUS_SERVEING,
            name: '服务中',
            className: 'status-done',
        },
        [Constants.DTD_SERVICE_STATUS_WAIT_PAY]: {
            value: Constants.DTD_SERVICE_STATUS_WAIT_PAY,
            name: '待付款',
            className: '',
        },
        [Constants.ORDER_STATUS_REFUND_AUDIT]: {
            value: Constants.ORDER_STATUS_REFUND_AUDIT,
            name: '等待商家处理',
            className: 'status-done',
        },
        [Constants.ORDER_STATUS_REFUND_P_PASS]: {
            value: Constants.ORDER_STATUS_REFUND_P_PASS,
            name: '退款中',
            className: 'status-done',
        },
        [Constants.ORDER_STATUS_REFUNDIED]: {
            value: Constants.ORDER_STATUS_REFUNDIED,
            name: '取消成功',
            className: '',
        },
        [Constants.ORDER_STATUS_REFUND_P_REJECT]: {
            value: Constants.ORDER_STATUS_REFUND_P_REJECT,
            name: '取消失败',
            className: '',
        },
    };

    public static DTD_SERVICE_CONTEXT_TYPE_ARR = {
        1: '毛坯房',
        2: '装修中',
        3: '已入住',
    };

    public static DTD_SERVICE_INSTALL_CASE_ARR = {
        1: '未安装',
        2: '安装失败',
        3: '已安装',
    };

    public static DTD_EXPLORE_CASE_STATUS = {
        1: '待跟进',
        2: '勘探失败',
        3: '勘探成功',
        4: '未勘探',
    };

    public static COMMUNITY_CONTENT_STATUS = {
        0: '作品审核中，审核通过后自动发布',
        1: '未通过审核，请重新编辑后再发布',
        2: '作品被举报，平台正在审核， 不可编辑/删除',
        3: '违规内容，已下架',
        4: '',
    };

    public static COMMUNITY_CONTENT_OTHER_STATUS = {
        0: ['作品审核中，', '审核通过后正常展示'],
        1: ['作品审核未通过，', '已下架'],
        2: '',
        3: ['作品审核未通过，', '已下架'],
        4: '',
    };

    public static DTD_CANCEL_TYPE_ARR = [
        {
            name: '我要取消',
            tips: '师傅还未出发，不需要服务了',
            type: '1',
            check: 0,
        },
        {
            name: '已购机退款',
            tips: '已购买扫地机器人，我要退款',
            type: '2',
            check: 0,
        },
        {
            name: '勘测失败退款',
            tips: '已勘测完成无法安装上下水，我要退款',
            type: '3',
            check: 0,
        },
    ];

    public static LIVE_STATUS_LIVE = 101; // 直播中
    public static LIVE_STATUS_TO_BEGIN = 102; // 未开始
    public static LIVE_STATUS_END = 103; // 已结束
    public static LIVE_STATUS_FODBID = 104; // 禁播
    public static LIVE_STATUS_PAUSE = 105; // 暂停
    public static LIVE_STATUS_FAIL = 106; // 异常
    public static LIVE_STATUS_EXPIRE = 107; // 过期

    // 618埋点相关
    public static LOG_TRACE_CACHE_LENGTH = 1;
    public static LOG_TRACE_MODULE_618 = 106;
    public static LOG_TRACE_MODULE_618_EVENTS = {
        LOG_TRACE_MODULE_618_EVENT_RULE: 1, // 点击活动规则时记录
        LOG_TRACE_MODULE_618_EVENT_TRADE_IN_BTN: 2, // 点击以旧焕新【去换新】按钮时记录
        LOG_TRACE_MODULE_618_EVENT_PART_TAB: 3, // 配件tab_^x^
        LOG_TRACE_MODULE_618_EVENT_BUY: 4, // 立即选购_^x^
        LOG_TRACE_MODULE_618_EVENT_COUPON: 5, // 立即领券
        LOG_TRACE_MODULE_618_EVENT_MAIN_TAB: 6, // 产品tab_^x^
        LOG_TRACE_MODULE_618_EVENT_MAIN: 7, // 产品_^x^
        LOG_TRACE_MODULE_618_EVENT_MORE: 8, // 查看更多
        LOG_TRACE_MODULE_618_EVENT_GUIDE: 9, // 专属秒杀点击指引
        LOG_TRACE_MODULE_618_EVENT_CONFIRM_ORDER: 10, // 确认订单
        LOG_TRACE_MODULE_REPORT_EVENT_SHARE: 11, // 分享
        // ---- 618埋点 新增 MidYearFestival埋点 ----
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_CHECKIN: 13, // 打卡
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_ORDER: 14, // 前往商城
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_AD: 14, // 前往广告地址
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_POINT_GOODS: 15, // 前往积分商品
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_RECOMEND_GOODS: 16, // 前往推荐商品
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_PARTS_GOODS: 17, // 前往配件商品
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_POINTS_EXCHANGE: 18, // 前往积分商城
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_INVITE_PERSON: 19, // 邀请入会
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_DRAW: 20, // 点击抽奖按钮
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_DRAW_RECORD: 21, // 中奖记录
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_DRAW_CHANCE: 22, // 获取更多抽奖次数
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_GO_COUPON: 23, // 前往领券中心
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_PICK_COUPON: 24, // 领取优惠券
        LOG_TRACE_MODULE_MIDYEARFESTIVAL_EVENT_PAGE_VISIT: 30, // 页面访问
    };

    // 年度报告埋点相关
    public static LOG_TRACE_MODULE_REPORT = 106;
    public static LOG_TRACE_MODULE_REPORT_EVENTS = {
        LOG_TRACE_MODULE_REPORT_EVENT_VIEW: 1, // 浏览
        LOG_TRACE_MODULE_REPORT_EVENT_SHARE: 2, // 分享
        LOG_TRACE_MODULE_REPORT_EVENT_NEW_USER: 3, // 新用户
        LOG_TRACE_MODULE_REPORT_EVENT_PAGE_VIEW: 4, // 页面访问
        LOG_TRACE_MODULE_REPORT_EVENT_SAVE_POSTER: 5, // 保存海报
    };

    // 以旧焕新埋点
    public static LOG_TRACE_MODULE_RENEW = 105;
    public static LOG_TRACE_MODULE_RENEW_EVENTS = {
        LOG_TRACE_MODULE_RENEW_EVENT_PAGE_VISIT: 1, // 页面访问
        LOG_TRACE_MODULE_RENEW_EVENT_RULE: 2, // 点击活动规则时记录
        LOG_TRACE_MODULE_RENEW_EVENT_BANNER: 3, // 头图
        LOG_TRACE_MODULE_RENEW_EVENT_TO_EXCHANGE: 4, // 前往换新_^x^
        LOG_TRACE_MODULE_RENEW_EVENT_SERVICE: 5, // 客服
        LOG_TRACE_MODULE_RENEW_EVENT_TO_REGISTER: 6, // 去注册
        LOG_TRACE_MODULE_RENEW_EVENT_TO_UPLOAD: 7, // 上传照片
        LOG_TRACE_MODULE_RENEW_EVENT_PICK_PHOTO: 8, // 选择照片
        LOG_TRACE_MODULE_RENEW_EVENT_UPLOAD_RULE_ICON: 9, // 上传照片规范icon
        LOG_TRACE_MODULE_RENEW_EVENT_UPLOAD_SUBMIT: 10, // 去上传
        LOG_TRACE_MODULE_RENEW_EVENT_EXCHANGE_SUBMIT: 11, // 立即换新_^x^
    };

    // 11.11埋点
    public static LOG_TRACE_MODULE_1111 = 103;
    public static LOG_TRACE_MODULE_1111_EVENTS = {
        LOG_TRACE_MODULE_1111_EVENT_PAGE_VISIT: 1, // 页面访问
        LOG_TRACE_MODULE_1111_EVENT_RULE: 2, // 点击活动规则时记录
        LOG_TRACE_MODULE_1111_EVENT_TAB: 3, // tab^x^
        LOG_TRACE_MODULE_1111_EVENT_PICK_COUPON: 4, // 领券^x^
        LOG_TRACE_MODULE_1111_EVENT_CHECKIN: 5, // 打卡
        LOG_TRACE_MODULE_1111_EVENT_EXTRA_RECEIVE: 6, // 点击额外获得积分奖励弹窗上「开心收下」按钮时记录
        LOG_TRACE_MODULE_1111_EVENT_LOTTERY: 7, // 点击抽奖按钮时记录
        LOG_TRACE_MODULE_1111_EVENT_MORE_CHANCE: 8, // 点击更多抽奖机会按钮时记录
        LOG_TRACE_MODULE_1111_EVENT_WINNING_RECORD: 9, // 中奖记录
        LOG_TRACE_MODULE_1111_EVENT_NO_CHANCE_BUTTON: 10, // 机会已用完弹窗_^x^ x=取消、做任务
        LOG_TRACE_MODULE_1111_EVENT_TO_GET_CHANCE: 11, // 去完成_^x^ x=任务名称（购买主机、购买配件、邀请有礼、添加客服），点击任务弹窗上去做某个任务按钮时记录
        LOG_TRACE_MODULE_1111_EVENT_POINT_MALL: 12, // 点击积分商城时记录
        LOG_TRACE_MODULE_1111_EVENT_BUY_GET_POINT: 13, // 购物返积分
        LOG_TRACE_MODULE_1111_EVENT_RECOMMAND: 14, // 邀请有礼
        LOG_TRACE_MODULE_1111_EVENT_TAB_PART: 15, // 配件tab_^x^
        LOG_TRACE_MODULE_1111_EVENT_BUY_PART: 16, // 立即购买_^x^
        LOG_TRACE_MODULE_1111_EVENT_TAB_MAIN: 17, // | 产品tab_^x^
        LOG_TRACE_MODULE_1111_EVENT_BUY_MAIN: 18, // 产品_^x^
        LOG_TRACE_MODULE_1111_EVENT_MORE: 19, // 查看更多_^x^
        LOG_TRACE_MODULE_1111_EVENT_SAVE_POSTER: 20, // 保存海报
        LOG_TRACE_MODULE_1111_EVENT_TRADE_IN_BTN: 21, // 点击以旧焕新【去换新】按钮时记录
        LOG_TRACE_MODULE_1111_EVENT_GUIDE: 22, // 专属秒杀点击指引
        LOG_TRACE_MODULE_1111_EVENT_CONFIRM_ORDER: 23, // 确认订单
        LOG_TRACE_MODULE_1111_EVENT_SHARE: 24, // 分享
        LOG_TRACE_MODULE_1111_EVENT_EXCHANGE_PART: 25, // 去积分详情兑换
        LOG_TRACE_MODULE_1111_EVENT_GO_ORDER: 26, // 去下单
        LOG_TRACE_MODULE_1111_INVITEBUY: 27, // 邀请购买
        LOG_TRACE_MODULE_1111_EVENT_MORE_CHANCE_CLICK: 28, // 点击更多抽奖机会按钮 单独抽奖
        LOG_TRACE_MODULE_1111_EVENT_WINNING_RECORD_CLICK: 29, // 点击中奖记录按钮 单独抽奖
        LOG_TRACE_MODULE_1111_EVENT_LOTTERY_CLICK: 30, // 点击抽奖按钮 单独抽奖
        LOG_TRACE_MODULE_1111_EVENT_RULE_CLICK: 31, // 点击活动规则按钮 单独抽奖
        LOG_TRACE_MODULE_1111_VIEW_STORE: 32, // 查看门店
        LOG_TRACE_MODULE_1111_VIEW_EXAMPLE: 33, // 查看案例
    };

    // 商城埋点
    public static LOG_TRACE_MODULE_DREAME = 107;
    public static LOG_TRACE_MODULE_DREAME_EVENTS = {
        // 全场三折购
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_EXPOSURE: 150, // 全场三折购曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_PRODUCT_CLICK: 151, // 全场三折商品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_PRODUCT_CLOSE_CLICK: 152, // 全场三折商品关闭点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_MORE_CLICK: 153, // 查看更多商品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHOP_CLICK: 154, // 邀请好友开店按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHARE_SHOP_CLICK: 155, // 分享小店任务按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_BUY_CLICK: 156, // 立即购买按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_BUY_CLICK: 157, // 抢购点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_CLICK: 158, // 邀请新用户助力按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_EXPOSURE: 159, // 助力页面曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHARE_CLICK: 160, // 邀请新用户助力分享点击
        LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHARE_RECORD_CLICK: 161, // 邀请新用户助力分享记录点击

        LOG_TRACE_MODULE_DREAME_EVENTS_PHONE_EXPOSURE: 1, // 手机号授权曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_PHONE_CLICK: 2, // 手机号授权点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CHECK_AGREEMENT: 3, // 勾选协议
        LOG_TRACE_MODULE_DREAME_EVENTS_SHOP_BUTTON: 4, // 商城导航栏按钮
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BANNER: 5, // 商城顶部banner
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BANNER_EXPOSURE: 6, // 商城顶部banner曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BUTTON: 7, // 探觅导航栏按钮
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER: 8, // 探觅顶部banner
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER_EXPOSURE: 9, // 探觅顶部banner曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BUTTON: 10, // 设备导航栏按钮
        LOG_TRACE_MODULE_DREAME_EVENTS_VIPCENTER_BUTTON: 11, // 会员导航栏按钮
        LOG_TRACE_MODULE_DREAME_EVENTS_VIPCENTER_WELFARE_CLICK: 12, // 会员专属福利点击
        LOG_TRACE_MODULE_DREAME_EVENTS_VIPCENTER_WELFARE_EXPOSURE: 13, // 会员专属福利曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_MINE_BUTTON: 14, // 我的导航栏按钮
        LOG_TRACE_MODULE_DREAME_EVENTS_MINE_ACTIVITY_CLICK: 15, // 我的活动点击
        LOG_TRACE_MODULE_DREAME_EVENTS_MINE_ACTIVITY_EXPOSURE: 16, // 我的活动曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_MINE_PRODUCT_CLICK: 17, // 我的产品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_ADD_PRODUCT_CLICK: 18, // 添加产品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_ADD_PRODUCT_EXPOSURE: 19, // 添加产品曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_SCAN_CODE_CAMERA: 20, // 扫码追觅产品SN码/相机按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CONFIRM_BUTTON_CLICK: 21, // 确定按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CONFIRM_BUTTON_EXPOSURE: 22, // 确定按钮曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_REGISTER_SUCCESS_CHECK: 23, // 注册成功查看按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_REGISTER_SUCCESS_RETURN: 24, // 注册成功返回按钮点击
        LOG_TRACE_MODULE_DREAME_EVENTS_OPEN_APP: 25, // 每个用户每天打开app的次数
        LOG_TRACE_MODULE_DREAME_EVENTS_FORWARD_BUTTON_CLICK: 26, // 用户的每天转发量
        LOG_TRACE_MODULE_DREAME_EVENTS_COUSTOMER_BUTTON_CLICK: 27, // 每个用户每天的点击客服的次数
        /** 社区创建作品时，调用uni.getlocation的记录 */
        LOG_TRACE_MODULE_DREAME_EVENTS_COUSTOMER_GET_LOCATION: 28,
        /** 首页点击事件定义 */
        // 包括但不限于：首页顶部搜索、首页tabs栏、首页分类、tabs下商品、tabs下配件、觅友圈、我的设备、我的管家、我的积分、购物车、会员中心、新人红包、CEO在线宠粉、CEO现金红包、点击商品跳转详情
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_SEARCH_CLICK: 29, // 首页顶部搜索点击
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_CLICK: 30, // 首页tabs栏点击
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_CATEGORY_CLICK: 31, // 首页分类点击
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_PRODUCT_CLICK: 32, // tabs下商品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_PARTS_CLICK: 33, // tabs下配件点击
        LOG_TRACE_MODULE_DREAME_EVENTS_MIYOUQUAN_CLICK: 34, // 觅友圈点击
        LOG_TRACE_MODULE_DREAME_EVENTS_MY_DEVICE_CLICK: 35, // 我的设备点击
        LOG_TRACE_MODULE_DREAME_EVENTS_MY_MANAGER_CLICK: 36, // 我的管家点击
        LOG_TRACE_MODULE_DREAME_EVENTS_MY_POINTS_CLICK: 37, // 我的积分点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CART_CLICK: 38, // 购物车点击
        LOG_TRACE_MODULE_DREAME_EVENTS_VIPCENTER_CLICK: 39, // 会员中心点击
        LOG_TRACE_MODULE_DREAME_EVENTS_SECKILLCLICK: 40, // 秒杀轻松购点击
        LOG_TRACE_MODULE_DREAME_EVENTS_GUOBU_CLICK: 50, // 国补点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CHUXING_CLICK: 51, // 出行点击
        LOG_TRACE_MODULE_DREAME_EVENTS_NEW_USER_RED_ENVELOPE_CLICK: 40, // 新人红包点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CEO_ONLINE_CLICK: 41, // CEO在线宠粉点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CEO_CASH_RED_ENVELOPE_CLICK: 42, // CEO现金红包点击
        LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK: 43, // 商品详情点击
        LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK: 44, // 首页头部积分点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CEO_GIFT_BAG_CLICK: 52, // ceo送福袋点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CEO_WELFARE_CLICK: 53, // ceo福利
        LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_CLICK: 54, // 苏超
        LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_SUZHOU_CLICK: 55, // 苏超苏州
        LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_NANTONG_CLICK: 56, // 苏超南通
        LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_SHARE_CLICK: 57, // 苏超活动分享

        /** 追觅大使 */
        LOG_TRACE_MODULE_DREAME_EVENTS_AMBASSADOR: 58,
        LOG_TRACE_MODULE_DREAME_EVENTS_RECOMMENDFRIEND_CLICK: 59, // 朋友推荐
        LOG_TRACE_MODULE_DREAME_EVENTS_WISH_CLICK: 60, // 我的心愿
        LOG_TRACE_MODULE_DREAME_EVENTS_PARTNER_CLICK: 61, // 追觅合伙人

        /** 一元购 */
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RETURN: 62, // 从一元购页面返回
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_PRODUCT_CLICK: 63, // 一元购商品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_INVITE_CLICK: 64, // 一元购邀请新用户助力
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_BUY_CLICK: 65, // 一元购立即购买
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_INDEX_BANNER_CLICK: 66, // 首页点击进入一元购
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ANOTHER_ORDER_CLICK: 67, // 再来一单
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RULE_CLICK: 68, // 查看活动规则
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_EXPOSURE: 122, // 一元购页面曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_SHARE_EXPOSURE: 123, // 一元购分享页面曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT: 132, // 一元购售罄弹窗
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT_CLOSE: 133, // 一元购商品售罄弹窗关闭
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT_LINK: 134, // 一元购商品售罄弹窗跳转三折购
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START: 135, // 一元购商品活动未开始强弹窗
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START_CLOSE: 136, // 一元购商品活动未开始弹窗关闭
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START_LINK: 137, // 一元购商品活动未开始弹窗跳转三折购
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ASSISTANE_BACK_BTN: 138, // 一元购邀请页返回按键
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_ASSISTANE_POPUP: 139, // 一元购邀请页三折购活动弹窗
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_ASSISTANE_LINK: 140, // 一元购邀请页三折购活动弹窗跳转三折购按键
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ASSISTANE_BACK: 141, // 一元购邀请页手机后退
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_PAY_SUCCESS: 142, // 一元购支付成功弹窗
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_PAY_SUCCESS_LINK: 143, // 一元购支付成功弹窗跳转去小店按键
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_BACK: 145, // 一元购活动页后退
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP: 146, // 一元购活动页强弹窗
        LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_LINK: 147, // 一元购活动页强弹窗跳转去团购按键

        // app底部导航栏购物 休闲 金币 朋友 个人
        LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_SHOP_CLICK: 45, // 导航栏购物点击
        LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_LEISURE_CLICK: 46, // 导航栏休闲点击
        LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_GOLD_CLICK: 47, // 导航栏金币点击
        LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_FRIEND_CLICK: 48, // 导航栏朋友点击
        LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_MINE_CLICK: 49, // 导航栏个人点击
        LOG_TRACE_MODULE_DREAME_EVENTS_CEO_POINT_CLICK: 69, // 会员商城
        LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK: 70, // 一元购
        LOG_TRACE_MODULE_DREAME_EVENTS_BANMONEY_CLICK: 80, // 半价购
        LOG_TRACE_MODULE_DREAME_EVENTS_FIVE_DISCOUNT_CLICK: 81, // 五折购

        /** 追觅合伙人 */
        LOG_TRACE_MODULE_DREAME_EVENTS_AMBASSADOR_INDEX_CLICK: 71, // 首页点击追觅合伙人入口
        LOG_TRACE_MODULE_DREAME_EVENTS_AMBASSADOR_INDEX_EXPOSURE: 126,

        // 半价购
        LOG_TRACE_MODULE_DREAME_EVENTS_HALF_PRICE_PIRCHASE: 72, // 半价购入口
        LOG_TRACE_MODULE_DREAME_EVENTS_INVITE_FRIENDS: 73, // // 邀好友，赚更多
        LOG_TRACE_MODULE_DREAME_EVENTS_GO_SHOP: 74, // 去购物
        LOG_TRACE_MODULE_DREAME_EVENTS_GO_GID: 75, // 去商品详情
        LOG_TRACE_MODULE_DREAME_EVENTS_GO_BACK: 76, // 返回
        LOG_TRACE_MODULE_DREAME_EVENTS_GO_SHOP_DETAIL_CLICK: 77, // 去查看详情记录

        // 赚钱花
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY: 82, // 赚钱
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_CLICK: 83, // 赚钱点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_EXPOSURE: 84, // 赚钱曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_CLICK: 85, // 赚钱邀请点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_EXPOSURE: 86, // 赚钱邀请曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_RECORD_CLICK: 87, // 赚钱邀请记录点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_RECORD_EXPOSURE: 99, // 赚钱邀请记录曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_SHARE_CLICK: 100, // 赚钱分享点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_SHARE_EXPOSURE: 101, // 赚钱分享曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_PRODUCT_DETAIL_CLICK: 102, // 赚钱花产品详情点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_RETURN: 103, // 赚钱花回退上个页面
        LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_RULE_CLICK: 104, // 赚钱花活动规则点击

        /** 八折购 */
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_ACTIVITY_CLICK: 88, // 八折购活动入口点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_PRODUCT_CLICK: 89, // 八折购商品点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_NEW_USER_CLICK: 90, // 八折购新人助力点击
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_BUY_CLICK: 91, // 八折购点击立即购买
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_RULE_CLICK: 92, // 八折购点活动规则
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_RETURN: 93, // 八折购页面返回
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_PRODUCT_DETAIL_LOAD: 110, // 商品详情页预览
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_PRODUCT_DETAIL_TO_CAR: 112, // 加入购物车

         /** 六折购 */
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_RETURN: 113, // 从六折页面返回
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_PRODUCT_CLICK: 114, // 六折购商品点击
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_INVITE_CLICK: 115, // 六折购邀请新用户助力
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_BUY_CLICK: 116, // 六折购立即购买
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_RULE_CLICK: 117, // 查看活动规则
         LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_ACTIVITY_CLICK: 118, // 六折购活动入口点击
         HOME_BANNER_BANNER_EXPOSURE: 119, // 首页banner曝光
         HOME_BANNER_CLICK: 120, // 首页banner点击

        /** 七夕活动 */

        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_EXPOSURE: 162, // 七夕活动曝光
        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_SHARE_FRIEND: 163, // 七夕活动分享好友
        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_THREE_COUPON: 164, // 七夕活动三折抢
        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_FREE_COUPON: 165, // 七夕活动随时抢
        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY__ORDER: 166, // 七夕活动订单
        LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_NEW_USER: 167, // 七夕活动新用户

        /** 团购活动入口点击 */
        LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_CLICK: 105,
        /** 团购活动曝光 */
        LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_EXPOSURE: 106,
        /** 团购分享曝光 */
        LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_SHARE_EXPOSURE: 121,
        /** 团购活动分享好友 */
        LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_SHARE_FRIEND: 107,
        LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_TO_ADVS: 108, // 首页推荐流插入

        /** 积分购页曝光 */
        LOG_TRACE_MODULE_DREAME_EVENTS_POINT_EXPOSURE: 124,

        /** 赚金币页曝光 */
        LOG_TRACE_MODULE_DREAME_EVENTS_GOLDCOINS_EXPOSURE: 125,

        /** 社区作品详情挂链商品点击 */
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENT_GOODS_CLICK: 127,
        /** 社区作品列表挂链商品点击 */
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENT_LIST_CLICK: 128,
        /** 社区发布带挂链商品创建 */
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENT_CREATE: 130,
        /** 社区发布带挂链商品更新 */
        LOG_TRACE_MODULE_DREAME_EVENTS_CONTENT_UPDATE: 131,

        /** 订单支付类型埋点 */
        LOG_TRACE_MODULE_DREAME_EVENTS_ORDER_TYPE_PAY: 129,
        LOG_TRACE_MODULE_DREAME_EVENTS_GOODS_CLICK: 126, // 商品点击

    };

    public static PARTCATE_MAP = {
        100001: {
            id: '48',
            name1: '扫地机配件',
            name2: '上下水',
        },
        100002: {
            id: '39',
            name1: '扫地机配件',
            name2: '滚刷',
        },
        100003: {
            id: '',
            name1: '扫地机配件',
            name2: '滤芯',
        },
        100004: {
            id: '40',
            name1: '扫地机配件',
            name2: '尘袋',
        },
        100005: {
            id: '42',
            name1: '扫地机配件',
            name2: '清洁液',
        },
        100006: {
            id: '38',
            name1: '扫地机配件',
            name2: '边刷',
        },
        100007: {
            id: '51',
            name1: '扫地机配件',
            name2: '尘盒滤网',
        },
        100008: {
            id: '52',
            name1: '扫地机配件',
            name2: '银离子除菌模块',
        },
        100009: {
            id: '53',
            name1: '扫地机配件',
            name2: '主刷罩',
        },
        100010: {
            id: '54',
            name1: '扫地机配件',
            name2: '越障门槛条',
        },
        100011: {
            id: '37',
            name1: '扫地机配件',
            name2: '抹布',
        },
        100012: {
            id: '50',
            name1: '扫地机配件',
            name2: '清洁礼盒',
        },
        200001: {
            id: '32',
            name1: '洗地机配件',
            name2: '清洁液',
        },
        200002: {
            id: '33',
            name1: '洗地机配件',
            name2: '滚刷',
        },
        200003: {
            id: '34',
            name1: '洗地机配件',
            name2: '滤芯',
        },
    };

    // public static REQUEST_GO_CODE = {
    //     10000: {
    //         label: 'FAILURE',
    //         value: '业务异常'
    //     },
    //     10001: {
    //         label: 'MSG_NOT_READABLE',
    //         value: '消息不能读取',
    //     },
    //     10002: {
    //         label: 'METHOD_NOT_SUPPORTED',
    //         value: '不支持当前请求方法',
    //     },
    //     10003: {
    //         label: 'MEDIA_TYPE_NOT_SUPPORTED',
    //         value: '不支持当前媒体类型',
    //     },
    //     10004: {
    //         label: 'REQ_REJECT',
    //         value: '请求被拒绝',
    //     },
    //     10005: {
    //         label: 'PARAM_MISS',
    //         value: '缺少必要的请求参数',
    //     },
    //     10006: {
    //         label: 'PARAM_TYPE_ERROR',
    //         value: '请求参数类型错误',
    //     },
    //     10007: {
    //         label: 'PARAM_BIND_ERROR',
    //         value: '请求参数绑定错误',
    //     },
    //     10008: {
    //         label: 'PARAM_VALID_ERROR',
    //         value: '参数校验失败',
    //     },
    //     10010: {
    //         label: 'TENANT_MISS',
    //         value: 'tenant missing',
    //     },
    //     10011: {
    //         label: 'TENANT_NOT_FOUND',
    //         value: 'tenant not found',
    //     },
    //     10012: {
    //         label: 'TENANT_USER_EXCEED_LIMIT',
    //         value: 'tenant user exceed limit',
    //     },
    //     10013: {
    //         label: 'IP_BANNED',
    //         value: 'ip banned',
    //     },
    //     10014: {
    //         label: 'SOCIAL_OAUTH_FAIL',
    //         value: 'social oauth fail',
    //     },
    //     10015: {
    //         label: 'SOCIAL_UUID_NOT_FOUNT',
    //         value: 'socialUuid not found',
    //     },
    //     10016: {
    //         label: 'ALREADY_BIND_OTHER_UUID',
    //         value: 'already_bind_other_uuid',
    //     },
    //     10017: {
    //         label: 'SOCIAL_UID_UNBIND',
    //         value: 'social_uid_unbind',
    //     },
    //     10018: {
    //         label: 'NO_DATABASE_CONNECTION_EXIST',
    //         value: 'TOKEN IS EXPIRED',
    //     },
    //     10019: {
    //         label: 'UPLOAD_FILE_FAILED',
    //         value: '请求未授权',
    //     },
    //     10020: {
    //         label: 'LOAD_FILE_FAILED',
    //         value: '客户端请求未授权',
    //     },
    //     10021: {
    //         label: 'FILE_SIZE_EXCEED_LIMIT',
    //         value: 'timestamp expired',
    //     },
    //     10022: {
    //         label: 'DUPLICATE_RECORDS',
    //         value: 'sign wrong',
    //     },
    //     10111: {
    //         label: 'UN_AUTHORIZED',
    //         value: '404 没找到请求',
    //     },
    //     401: {
    //         label: 'NO_AUTHORIZED',
    //         value: '服务器异常',
    //     },
    //     10112: {
    //         label: 'CLIENT_UN_AUTHORIZED',
    //         value: '密码不符合规则',
    //     },
    //     10120: {
    //         label: 'TIMESTAMP_EXPIRED_AUTHORIZED',
    //         value: '不存在数据库连接',
    //     },
    //     10121: {
    //         label: 'SIGN_ERROR_AUTHORIZED',
    //         value: '上传文件失败',
    //     },
    //     10400: {
    //         label: 'NOT_FOUND',
    //         value: '加载文件失败',
    //     },
    //     10500: {
    //         label: 'INTERNAL_SERVER_ERROR',
    //         value: '文件大小超限',
    //     },
    //     10009: {
    //         label: 'PASSWORD_NOT_RULE_ERROR',
    //         value: '重复记录',
    //     },
    //     402: {
    //         label: 'LOGIN_FAILURE',
    //         value: '登录失效',
    //     },
    //     50023: {
    //         label: 'NO_PERMISSION',
    //         value: '没有权限',
    //     },
    //     50024: {
    //         label: 'NOT_EXIST_PHONE',
    //         value: '手机号不存在',
    //     },
    //     50025: {
    //         label: 'ERROR_REQUEST',
    //         value: '请求报错',
    //     },
    //     50026: {
    //         label: 'CREATION_FAILURE',
    //         value: '创建失败',
    //     },
    //     50027: {
    //         label: 'SENSITIVE_WORDS',
    //         value: '你输入的内容包含敏感词，请删除后重试',
    //     },
    //     50028: {
    //         label: 'REVIEW_DELETE',
    //         value: '回复失败，评论已删除或不存在',
    //     },
    //     50029: {
    //         label: 'REVIEW_DELETE',
    //         value: '文章已下架，不能点赞或收藏',
    //     },
    // }

    // Tabbar切换监听
    public static TABBAR_CHANGE = 'tabbarChange';

    // genShareLink 分享类型
    public static GEN_SHARE_LINK_TYPE = {
        // header参数
        bizParam: {
            xxxx: 'yyyy',
        },
        // 最终拼接到url
        shareSource: '-1',
        // 最终拼接到url
        shareTarget: 'dreameApp',
        // 打开类型
        type: 'mall',
        // 是否在url拼接session、userid
        auth: true,
        // 跳转地址 商城/App页面/第三方网页
        jumpLink: '',
    };
}
