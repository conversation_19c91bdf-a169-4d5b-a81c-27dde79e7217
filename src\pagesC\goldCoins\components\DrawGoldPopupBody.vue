<template>
    <view class="reward-popup-wrapper">
        <view class="title-icon"></view>
        <view class="tiger-machine-icon" :class="{ 'tiger-machine-icon-end': false }">
            <view class="band-icon"></view>
            <view class="star-icon"></view>
            <view class="tiger-machine-title-icon"></view>
            <view class="tiger-machine-hint">
                <vue-seamless-scroll
                    :data="drawGoldPopupUserList"
                    :style="{
                        height: '48rpx',
                        overflow: 'hidden',
                        position: 'relative',
                    }"
                    :class-option="scrollOption"
                >
                    <view class="notice-item" v-for="(item, index) in drawGoldPopupUserList" :key="index">
                        <view class="notice-icon" :style="{ backgroundImage: `url(${item.avatar})` }"></view>
                        <view class="notice-text"> ****抽到{{ item.numValue }}金币 </view>
                    </view>
                </vue-seamless-scroll>
            </view>
            <view v-if="drawGoldResult === 0">
                <view class="tiger-machine-gold-icon one"></view>
                <view class="tiger-machine-gold-icon two"></view>
                <view class="tiger-machine-gold-icon three"></view>
                <view class="tiger-machine-gold-icon four"></view>
            </view>
            <view v-else-if="drawGoldResult > 0 && !isDrawingEnd">
                <view
                    class="tiger-machine-result-item"
                    :class="`item-${index}`"
                    v-for="(item, index) in drawGoldResultArr"
                    :key="index"
                >
                    {{ item }}
                </view>
            </view>
            <view :class="[drawGoldResult > 0 && isDrawingEnd ? '' : 'invisible']">
                <view class="center-bg"></view>
                <view class="result-wrapper">
                    <view class="tiger-machine-gold-icon result-gold-icon"></view>
                    <view class="final-result"> +{{ drawGoldResult }} </view>
                </view>
                <view class="tomorrow-hint">
                  {{showYesterdayView ? '— 现在可立即领取 —' : '— 明天0点后解锁领取 —'}}
                </view>
            </view>
        </view>
        <view class="btn" @click="gainYesterdayDraw" v-if="showYesterdayView">
            <view class="btn-text">立即领取</view>
        </view>
        <view class="btn" @click="drawGold" v-else-if="!isDrawingEnd">
            <view class="btn-text" v-if="!isDrawing">立即抽奖</view>
            <view class="btn-loading-icon" v-else> </view>
        </view>
        <view class="btn btn-end" v-else @click="closeDrawGoldPopup"></view>
        <view class="signIn-popup-close" @click="closeDrawGoldPopup">
            <image
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889012760f323970031451.png"
            >
            </image>
        </view>
        <view class="rule-wrapper" @click="handleRecordClick">
            <view class="rule-title" >记录</view>
            <view>/</view>
            <view class="rule-content">规则</view>
        </view>
    </view>
</template>

<script>
import { drawGoldCoins, collectTaskGold } from '@/http/checkIn';
import checkDate from '../helpers/date';
import vueSeamlessScroll from 'vue-seamless-scroll';
import Utils from '@/common/Utils';

export default {
    data() {
        return {
            isDrawing: false,
            scrollOption: {
                step: 0.3, // 数值越大速度滚动越快
                limitMoveNum: 2, // 开始无缝滚动的数据量
                hoverStop: false, // 是否开启鼠标悬停stop
                direction: 1, // 0向下 1向上 2向左 3向右
                waitTime: 0, // 单步运动停止的时间(默认值1000ms)
            },
            drawGoldResult: 0,
            isDrawingEndValue: false,
        };
    },
    components: {
        vueSeamlessScroll,
    },
    props: {
        goldPrizeDrawItem: {
            type: Object,
            default: () => {},
        },
        drawGoldPopupUserList: {
            type: Array,
            default: () => [],
        },
    },
    created() {},
    watch: {
        goldPrizeDrawItem: {
            handler(newVal) {
                this.drawGoldResult = newVal?.numValue || 0;
            },
            immediate: true,
            deep: true,
        },
    },
    computed: {
        isToday() {
            const { isToday } = checkDate(this.goldPrizeDrawItem?.eventTime || 0) || {};
            return isToday;
        },
        isYesterday() {
            const { isYesterday } = checkDate(this.goldPrizeDrawItem?.eventTime || 0) || {};
            return isYesterday;
        },
        isDrawingEnd() {
            return ((this.isToday || this.isYesterday) && this.drawGoldResult > 0) || this.isDrawingEndValue;
        },

        showYesterdayView() {
            return this.isYesterday && this.drawGoldResult > 0;
        },

        drawGoldResultArr() {
            return String(this.drawGoldResult).split('');
        },
    },
    methods: {
        handleRecordClick() {
            Utils.navigateTo('/pagesC/goldCoins/coinRecord');
            this.$emit('showRecordPopup');
        },
        handleRuleClick() {
            this.$emit('showRulePopup');
        },
        closeDrawGoldPopup() {
            this.$emit('closeDrawGoldPopup');
        },
        async drawGold() {
            this.isDrawing = true;

            const res = await drawGoldCoins();
            this.drawGoldResult = res;

            setTimeout(() => {
                this.isDrawing = false;
                this.isDrawingEndValue = true;
            }, 500);
        },
        // 领取昨日抽奖结果
        async gainYesterdayDraw() {
            const res = await collectTaskGold({
                group_code: 'mall',
                type_code: 'dreame',
                code: 'gold_prize_draw',
            });

            this.closeDrawGoldPopup();
            this.$emit('showRewardPopup', res || 0);
        },
    },
};
</script>

<style lang="scss" scoped>
.signIn-popup-close {
    width: 52rpx;
    height: 52rpx;
    opacity: 1;
    box-sizing: border-box;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -90rpx;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        width: 40rpx;
        height: 40rpx;
    }
}

.reward-popup-wrapper {
    position: relative;

    .title-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a30fdd4f2fc3240012665.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 454rpx;
        height: 213rpx;
        margin: 0 auto;
    }

    .tiger-machine-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a30fd9c3dc98020017992.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 663rpx;
        height: 506rpx;
        margin: 36rpx auto 0;
        position: relative;

        &.tiger-machine-icon-end {
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a31bdd7feec5240010876.png');
        }
    }

    .btn {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a315bdbb1307660011673.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 400rpx;
        height: 112rpx;
        margin: 40rpx auto 0;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: MiSans;
        font-size: 48rpx;
        font-weight: 600;
        line-height: 112rpx;
        text-align: center;
        color: #ffffff;

        &.btn-end {
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a31bdd74a234780014876.png');
            height: 121rpx;
            margin-top: 30rpx;
        }
    }

    .btn-loading-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a315bdb6a737480011475.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 56rpx;
        height: 56rpx;
        position: absolute;
        top: 28rpx;
        left: 173rpx;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    .band-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a314536256b4030010899.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 95rpx;
        height: 75rpx;
        position: absolute;
        top: 248rpx;
        left: -24rpx;
    }

    .star-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a31455279cb1620017935.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 70rpx;
        height: 83rpx;
        position: absolute;
        top: 45rpx;
        right: -30rpx;
    }

    .invisible {
        visibility: hidden;
    }

    .center-bg {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a3e47ddcc4c9040011358.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 509rpx;
        height: 221rpx;
        position: absolute;
        top: 74rpx;
        left: 50%;
        transform: translate(-50%, 0);
    }

    .tiger-machine-title-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a31295126270750011828.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 224rpx;
        height: 42rpx;
        position: absolute;
        top: 24rpx;
        left: 50%;
        transform: translateX(-50%);
    }

    .tiger-machine-hint-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a312978245c5340047167.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 280rpx;
        height: 48rpx;
        position: absolute;
        top: 300rpx;
        left: 50%;
        transform: translateX(-50%);
    }

    .notice-item {
        width: 100%;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notice-icon {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #ffffff;
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 16rpx;
        border-radius: 50%;
    }

    .notice-text {
        font-family: MiSans;
        font-size: 24rpx;
        height: 32rpx;
        line-height: 32rpx;
        font-weight: normal;
        text-align: center;
        color: #ffffff;
    }

    .tiger-machine-hint {
        width: 280rpx;
        height: 48rpx;
        position: absolute;
        top: 300rpx;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 36rpx;
        opacity: 1;

        background: rgba(250, 63, 17, 0.32);

        box-shadow: inset 0px -1rpx 0px 0px rgba(255, 255, 255, 0.48), inset 0px 2rpx 6rpx 0px rgba(202, 76, 0, 0.4);
    }

    .tiger-machine-gold-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a3129d1d37f1200010745.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 68rpx;
        height: 68rpx;
        position: absolute;
        top: 148rpx;

        &.one {
            left: 127rpx;
        }

        &.two {
            left: 240rpx;
        }

        &.three {
            left: 353rpx;
        }

        &.four {
            left: 466rpx;
        }
    }

    .tiger-machine-result-item {
        position: absolute;
        width: 49rpx;
        height: 72rpx;
        top: 150rpx;
        font-family: MiSans;
        font-size: 72rpx;
        font-weight: 600;
        line-height: 72rpx;
        text-align: center;
        color: #ff2300;

        &.item-0 {
            left: 140rpx;
        }

        &.item-1 {
            left: 251rpx;
        }

        &.item-2 {
            left: 362rpx;
        }

        &.item-3 {
            left: 473rpx;
        }
    }

    .result-gold-icon {
        left: 193rpx;
        top: 136rpx;
    }

    .final-result {
        position: absolute;
        font-size: 56rpx;
        height: 56rpx;
        line-height: 56rpx;
        font-weight: 600;
        text-align: center;
        color: #ff2300;
        left: 289rpx;
        top: 142rpx;
    }

    .tomorrow-hint {
        height: 24rpx;
        font-family: MiSans;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 24rpx;
        text-align: center;
        color: #ca6d36;
        position: absolute;
        left: 50%;
        top: 220rpx;
        transform: translateX(-50%);
    }

    .rule-wrapper {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -190rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: MiSans;
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        text-align: center;
        color: #ffffff;
    }
}
</style>
