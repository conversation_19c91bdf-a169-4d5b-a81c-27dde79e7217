<template>
     <view class="product-list-y">
        <view
            class="product-card-y"
            v-for="(product, index) in productList"
            :key="index"
            @click="goToProduct(product)"
        >
            <view class="product-image-y-container">
                <lazy-image class="product-image-y" :src="product.goods_image"></lazy-image>
            </view>
            <view class="product-info-y">
                <view class="product-name-y">{{ product.show_name }}</view>
                <view class="product-price-content-y">
                    <view class="product-price-y">
                        <view class="product-price-y-icon"></view>
                        <text class="product-price-y-text">¥</text>
                        <text class="product-price-y-price">{{ product.half_price&&product.half_price.split('.')[0] }}</text>
                        <text class="product-price-y-price1">.{{ product.half_price&&product.half_price.split('.')[1] }}</text>
                    </view>
                    <view class="original_price-y">优惠前：{{ product.sale_price }}元</view>
                    <view class="product-button-y">
                        <view class="LineProgress">
                            <view class="content_title_text_content_progresss">
                                <view
                                    class="progress"
                                    :style="{
                                        width:
                                            Number(product.user_shop_money) > Number(product.half_price)
                                                ? '100%'
                                                : 100 -
                                                    ((Number(product.half_price) -
                                                        Number(product.user_shop_money)) /
                                                        Number(product.half_price)) *
                                                        100 +
                                                    '%',
                                    }"
                                >
                                </view>
                            </view>
                        </view>
                        <view class="half_price_y">
                            <view class="half_price_y_text">距半价还有</view>
                            <view class="half_price_y_price"
                                >{{
                                    Number(product.user_shop_money) > Number(product.half_price)
                                        ? '0'
                                        : Number(product.half_price) - Number(product.user_shop_money)
                                }}元</view
                            >
                        </view>
                        <view class="qiang"></view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Object, default: () => {} })
    readonly Item!: any;

    @Prop({ type: Array, default: () => [] })
    readonly productList!: any;

    goToProduct(product) {
        this.$emit('goToProduct', product);
    }
}
</script>
<style lang="scss" scoped>
@import "./ListHorizontal.scss";
.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}
</style>
