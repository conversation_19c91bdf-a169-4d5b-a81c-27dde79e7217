.group-goods {
  background: linear-gradient(311deg, #FF1C7C -6%, #FF4242 140%);
  height: 100vh;

  &.bg {
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/304482-1755518335236.png');

  }

  .header {
    display: flex;
    align-items: center;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    background: linear-gradient(311deg, #FF1C7C -6%, #FF4242 140%);

    .back {
      padding: 24rpx 12rpx 24rpx 16rpx;
      width: 48rpx;
      height: 48rpx;
    }

    &-title {
      font-size: 36rpx;
      color: #FFFFFF;
      margin-right: 12rpx;
    }

    .icon {
      width: 396rpx;
      height: 48rpx;
      margin-right: auto;
    }

    .share {
      width: 48rpx;
      height: 48rpx;
      margin-right: 12rpx;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6897212c5e6ff3870024130.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .count-down {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 18rpx 0 18rpx 15rpx;

    img {
      width: 26rpx;
      height: 32rpx;
      margin-right: 32rpx;
    }

    .time {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 24rpx;
      color: #FFFFFF;

      .time__item {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &_text {
          width: 32rpx;
          height: 32rpx;
          background: white;
          color: #FF256E;
          border-radius: 6rpx;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .group-goods-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .share {
      width: 136rpx;
      height: 40rpx;
      margin-right: 16rpx;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688e03cd08ad10360011733.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  &-title {
    font-size: 28rpx;
    color: #FFFFFF;
    padding: 12rpx 0 16rpx 20rpx;
  }

  .group-task {
    padding: 8rpx 8rpx 6rpx 18rpx;
    background: white;
    font-size: 28rpx;
    color: #000000;

    margin: 0 20rpx 20rpx 20rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-radius: 16rpx;

    .icon {
      width: 46rpx;
      height: 46rpx;
      margin-right: 26rpx;
    }

    .text {
      margin-right: auto;
    }

    .task {
      padding: 10rpx 58rpx 10rpx 69rpx;
      position: relative;
      font-size: 28rpx;
      font-weight: 500;
      color: #FFFFFF;
      background: #FF0039;
      border-radius: 16rpx;
    }
  }

  &-container {
    background: linear-gradient(180deg, #FDEEF3 0%, #F6F6F6 100%);
    border-radius: 32rpx 32rpx 0 0;
    padding-bottom: 16rpx;
    min-height: 100vh;
  }

  .container-tip {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 28rpx 0 24rpx 26rpx;

    img {
      width: 58rpx;
      height: 58rpx;
      margin-right: 14rpx;
    }

    &__title {
      font-size: 30rpx;
      font-weight: 500;
      color: #FF3755;
      margin-right: auto;
    }

    &__rule {
      padding: 4rpx 8rpx 4rpx 12rpx;
      background: #FF3755;
      color: white;
      font-size: 24rpx;
      font-weight: 500;
      border-radius: 40rpx 0 0 40rpx;
    }
  }

  .container-rank {
    margin: 0 16rpx 16rpx 16rpx;
    width: 718rpx;
    height: 346rpx;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a568c9a90ff6920010419.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    // display: grid;
    // grid-template-columns: repeat(3, 1fr);
    // gap: 70rpx;
    // justify-content: space-between;
    display: flex;
    justify-content: space-between;
    padding: 78rpx 60rpx 0 60rpx;

    .rank-item {
      // display: flex;
      // flex-direction: column;
      // justify-content: flex-start;
      flex: 0 0 148rpx;

      img {
        width: 148rpx;
        height: 148rpx;
        margin-bottom: 44rpx;
      }

      view {
        font-size: 16rpx;
        text-align: center;
        color: #121212;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .rank-item__name {
      margin-top: 16rpx;
      font-size: 16rpx;
      font-weight: 500;
      color: #121212;
      text-align: center;
    }


  }

  .goods-tab {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    z-index: 2;
    padding: 8rpx 16rpx 24rpx 16rpx;
    gap: 20rpx;

    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;

    position: sticky;
    top: 200rpx;
    left: 0;
    background-color: #fceff4;

    .goods-item {
      flex: 0 0 auto;
      margin-right: 20rpx;
      padding: 10rpx 0 6rpx 0;
      margin: 0 24rpx;
      font-size: 28rpx;
      color: #121212;
      position: relative;

      &.active {
        font-weight: 500;
        color: #FF3754;

        .slider {
          height: 4rpx;
          background: #FF3754;
          border-radius: 10rpx;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 0;
        }
      }

      &.isIconTag {
        .slider {
          width: 56rpx !important;
        }
      }
    }
  }

  // .slider {
  //   height: 4rpx;
  //   background: #FF3754;
  //   border-radius: 10rpx;
  // }

  .group-list {
    // margin: 24rpx 20rpx 0 20rpx;
    // border-radius: 16rpx;
    // background: linear-gradient(129deg, #1A1815 17%, #4D3E27 117%);

    // &__header {
    //   padding: 24rpx 20rpx;
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: center;
    //   font-size: 36rpx;
    //   font-weight: 500;
    //   line-height: 48rpx;
    //   color: #E0C49B;

    //   img {
    //     width: 40rpx;
    //     height: 40rpx;
    //   }
    // }

    &__scroll {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      padding: 0 20rpx 20rpx 20rpx;

      // #ifdef H5
      ::v-deep .uni-scroll-view-content {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-items: flex-start;
      }

      // #endif

      // &-more {
      //   display: flex;
      //   flex-direction: column;
      //   align-items: center;
      //   margin: auto 0 auto 36rpx;
      //   padding-right: 40rpx;

      //   view {
      //     font-size: 28rpx;
      //     color: #E0C49B;
      //     text-align: center;
      //   }

      //   img {
      //     width: 36rpx;
      //     height: 36rpx;
      //     margin-top: 12rpx;
      //   }
      // }
    }
  }

  .group-goods-bg {
    width: 100%;
    height: 680rpx;
  }

  .search-bar {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &__input {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: #ffffff;
      padding: 14rpx 24rpx;
      border: 2rpx solid rgba(203, 175, 134, 0.22);
      border-radius: 16rpx;

      &-name {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #a6a6a6;
        margin: 0 auto 0 16rpx;
      }

      &-btn {
        padding: 12rpx 40rpx;
        border-radius: 182rpx;
        background: #e8dec1;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 40rpx;
        color: #8c6533;
      }

      img {
        width: 48rpx;
        height: 48rpx;
      }
    }

    &__group {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 52rpx;
      font-size: 28rpx;
      font-weight: 500;
      line-height: 40rpx;
      color: #3d3d3d;

      img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
    }
  }

  .rule-popup-content {
    width: 664rpx;
    height: 824rpx;
    max-height: 824rpx;
    background-size: 100% 100%;
    border-radius: 32rpx;
    overflow: hidden;

    .rule-popup-header {
      padding: 94rpx 36rpx 30rpx;
      position: relative;
      text-align: center;

      .rule-title {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          width: 352rpx;
          height: 66rpx;
        }
      }

      .rule-popup-close {
        position: absolute;
        right: 36rpx;
        top: 24rpx;
        width: 46rpx;
        height: 46rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;

        .close-icon {
          width: 46rpx;
          height: 46rpx;
          background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png') no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }

    .rule-container {
      padding: 0 50rpx 40rpx 50rpx;
      font-size: 22rpx;
      line-height: 42rpx;
      color: #3D3D3D;

      view {
        max-height: 600rpx;
        overflow-y: auto;
      }
    }
  }

  .group-empty {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #A6A6A6;
    padding-top: 308rpx;
    text-align: center;
  }
}