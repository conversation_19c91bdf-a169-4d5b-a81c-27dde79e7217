/* eslint-disable no-redeclare */
import http from './index';
/*
校验卡片
 */
export const validateCard = (params) => http.post('main/gift-card/validate-card', params);

/*
激活卡片
 */
export const activateCard = (params) => http.post('main/gift-card/activate-card', params);

/*
激活卡片
 */
export const userCardList = (params) => http.post('main/gift-card/user-card-list', params);

/*
我的礼品卡详情
 */
export const userCardInfo = (params) => http.post('main/gift-card/user-card-info', params);

/*
敏感词校验
 */
export const checkContent = (params) => http.post('main/gift-card/check-content', params);
/*
礼品卡分享
 */
export const shareCard = (params) => http.post('main/gift-card/share-card', params);

/*
礼品卡分享详情
 */
export const shareCardInfo = (params) => http.post('main/gift-card/share-card-info', params);

/*
领取卡片
 */
export const receiveCard = (params) => http.post('main/gift-card/receive-card', params);

/*
分享卡片列表
 */
export const shareCardList = () => http.post('main/gift-card/share-card-list');

/*
礼品卡消费记录
 */
export const expendRecord = (params) => http.post('main/gift-card/expend-record', params);

// 获取半价活动的消费记录
export const expendList = (params) => http.post('/main/my/shop-money-log', params);

export const amountMoney = (params) => http.post('/main/my/shop-money-balance', params);
