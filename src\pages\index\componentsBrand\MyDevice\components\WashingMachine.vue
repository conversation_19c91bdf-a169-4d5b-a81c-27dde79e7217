<template>
    <view class="clean-card">
        <view class="info">
            <view class="info-title" @click="gotoDevice('detail')">
                <view class="title">{{ currentDevice.customName || currentDevice.displayName }}</view>
                <view class="status" v-if="status">{{ status }}</view>
                <view class="battery-wrap" v-if="battery">
                    <text class="battery-text">{{ battery }}</text>
                    <view class="battery-unit-wrap">
                        <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d4849a78fc6860096749.png" alt="电量" class="device-info-battery" />
                        <text class="battery-unit-text">%</text>
                    </view>
                </view>
            </view>
            <view class="info-image">
                <image  class="info-image-icon" :src="image" alt="设备图片" mode="aspectFit" />
            </view>
        </view>
        <view class="more" @click="gotoDevice('list')">
            查看更多
        </view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage = 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png'

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage
        },
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true
        }
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber()
            const bind_id = bindDomain.split('.')[0]
            const data = { did, id, from: 'mapp', method: 'get_properties', params: [{ did, siid: 2, piid: 1 }, { did, siid: 3, piid: 1 }, { did, siid: 4, piid: 38 }, { did, siid: 4, piid: 83 }, { did, siid: 4, piid: 48 }] }
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data)
            }
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter(r => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter(r => r.siid === 3 && r.piid == 1);
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel('DEVICE', 'getStatusStr', {
                                model: model,
                                latestStatus: onlineStatus[0].value
                            }, (res) => {
                                newStatus.status = res.data;
                                this.currentDeviceStatus = { ...newStatus };
                            });
                        }
                    }
                }
            };
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: body
            }, commandResultCB);
        },
        // 进入设备详情，插件
        // gotoDevice() {
        //   Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        //   //  Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        // },
        gotoDevice(type) {
          if (type === 'detail') {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
          } else {
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
          }
            // Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    }
}

</script>

<style lang="scss" scoped>
.clean-card {
  width: 250rpx;
  height: 201rpx;
  background-color: rgb(218, 228, 240);
  padding: 20rpx;
  border-radius: 40rpx;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

  .info {
    // width: 250rpx;
    width: 100%;
    height: 70%;
    display: flex;
    justify-content: space-between;
    .info-title {
      font-size: 24rpx;
      font-weight: bold;
      color: #121212;;
      margin-bottom: 6px;
      width: 70%;
      .title{
        font-size: 20rpx;
        white-space: nowrap;    /* 不换行 */
        overflow: hidden;      /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
        width: 100%;
        max-width: 80rpx;
      }
      .status{
        font-size: 22rpx;
      }
      .battery-wrap{
        display: flex;
        align-items: center;
        .battery-unit-wrap{
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: last baseline;
          padding-top: 10rpx;
          .device-info-battery{
            width: 38rpx;
            height: 36rpx;
          }
        }
        .battery-unit-text{
          font-size: 22rpx;
          color: #fff;
        }
        .battery-text{
          font-size: 48rpx;
          color: #fff;
        }
      }
    }

    .info-image {
      // width: 100rpx;
      height: 90%;
      width: 30%;
      min-width: 100rpx;
      .info-image-icon{
        width: 100%;
        height: 100%;
      }
    }
  }

  .more {
    width: 208rpx;
    height: 38rpx;
    color: #AAC4E4;
    font-size: 24rpx;
    text-align: center;
    line-height: 38rpx;
    border-radius: 40rpx;
    background-color: #fff;
    margin: 0 auto;
    margin-top: 20rpx;
  }
}
</style>
