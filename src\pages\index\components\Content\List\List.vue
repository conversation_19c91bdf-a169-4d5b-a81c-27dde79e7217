<template>
     <view class="class-item">
        <view class="class-item-top">
            <view
                v-if="Item.atmosphere_img"
                class="atmosphereImg"
                :style="{
                    backgroundImage: `url(${Item.atmosphere_img})`,
                }"
            ></view>
               <!-- <block v-if="Item.is_presale === 1">
                            <view  class="yushouImg" ></view>
                        </block>
                         <block v-else>
                            <block v-if="Item.tids&&Item.tids.includes('2')">
                             <view  class="hotProImg" ></view>
                        </block>
                        <block v-else-if="Item.tids&&Item.tids.includes('1')">
                             <view  class="newProImg" ></view>
                        </block>
                        </block> -->
            <!-- #ifdef H5 -->
            <LazyImage :src="Item.cover_image" class="lazyImg"> </LazyImage>
            <!-- #endif -->
        </view>
        <view class="class-item-bottom">
            <view class="name u-line-2">
                <!-- <text class="uni-presale-tag tag" style="line-height: 1" v-if="Item.is_presale === 1"
                    >预售</text
                > -->
                    <!-- <template v-if="itm.goods_tag_name==='品牌'">
                        <text class=" brand tag" style="line-height: 1"></text>
                    </template>
                    <template v-if="itm.goods_tag_name==='生态'">
                        <text class="strictly-selected  tag" style="line-height: 1">生态</text>
                    </template>
                    <template v-if="itm.goods_tag_name==='严选'">
                        <text class=" ecology tag" style="line-height: 1">严选</text>
                    </template> -->
                    <!-- <block v-if="Item.is_presale === 1">
                        <text class="uni-presale-tag tag" style="line-height: 1">预售</text>
                    </block>
                    <block v-else>
                        <block v-if="Item.tids && Item.tids.includes('2')">
                            <text class="uni-presale-tag tag" style="line-height: 1">爆款</text>
                        </block>
                        <block v-else-if="Item.tids && Item.tids.includes('1')">
                            <text class="uni-presale-tag tag" style="line-height: 1">新品</text>
                        </block>
                    </block> -->
                     <block v-if="Item.is_presale === 1">
                        <text class=" ysIcon tag" style="line-height: 1"></text>
                    </block>
                    <block v-else>
                        <block v-if="Item.tids && Item.tids.includes('2')">
                            <text class=" htIcon tag" style="line-height: 1"></text>
                        </block>
                        <block v-else-if="Item.tids && Item.tids.includes('1')">
                            <text class=" newIcon tag" style="line-height: 1"></text>
                        </block>
                    </block>
                    <view class="text titleW" :style="{
                            textIndent: Item.is_presale === 1 || Item.tids.includes('2') || Item.tids.includes('1') ? '76rpx' : '0rpx',
                        }">{{ Item.name }}</view>
            </view>
            <view class="item_desc">{{ Item.introduce }}</view>
            <view class="price">
                <view
                    style="
                        color: #a6a6a6;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                        height: 58rpx;
                    "
                >    <text class="unit">￥</text>
                    <text
                        class="optimizePrice"
                        v-if="String((Number(Item.price || 0) - Number(Item.subsidy_price ||
                        0)).toFixed(2)).indexOf('.') !== -1"
                        style="font-size: 42rpx; line-height: 1; align-self: flex-end"
                    >
                        {{ String((Number(Item.price || 0) - Number(Item.subsidy_price ||
                        0)).toFixed(2)).split('.')[0] }}
                    </text>
                    <text
                    class="optimizePrice"
                        v-if="String((Number(Item.price || 0) - Number(Item.subsidy_price ||
                        0)).toFixed(2)).indexOf('.') !== -1"
                        style="font-size: 22rpx; line-height: 1; align-self: flex-end"
                    >
                        .{{ String((Number(Item.price || 0) - Number(Item.subsidy_price ||
                        0)).toFixed(2)).split('.')[1] }}
                    </text>
                     <!-- <text class="originPrice">￥{{ Item.price }}</text> -->
                    <!-- <text class="unit">￥</text>
                    <text class="optimizePrice">{{ (Number(Item.price || 0) - Number(Item.subsidy_price || 0)).toFixed(2) }}</text> -->

                </view>
                 <view class="content_title_text_grab_text">抢</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Object, default: { }})
    readonly Item!: any;
}
</script>
<style lang="scss" scoped>
@import "./List.scss";
.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}
</style>
