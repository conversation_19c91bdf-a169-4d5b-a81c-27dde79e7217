<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <custom-bar background="#f6f6f6" title="查看更多" />
            <view class="title" :style="{ 'padding-top': pagePaddingTop + 'rpx' }">
                <block v-for="(item, index) in tabsList" :key="index">
                    <view class="swiper-box">
                        <view class="swiper-item swiper-box-has-data">
                            <scroll-view
                                class="scroll-view"
                                scroll-y
                                using-sticky
                                :refresher-enabled="canPullDown"
                                :upper-threshold="0"
                                :lower-threshold="300"
                                :refresher-triggered="isRefreshing"
                                @scrolltolower="onScrollToLower"
                                @refresherrefresh="refresherrefresh"
                                @touchstart="touchStart"
                                @touchmove="touchMove"
                                @touchend="touchEnd"
                                :scroll-top="scrollTop"
                            >
                                <view v-if="index === 0 && bannerList.length" class="swiper-image">
                                    <view class="carousel-map">
                                        <custom-swiper
                                            bgColor="#ffffff"
                                            height="417rpx"
                                            :circular="true"
                                            :autoplay="autoplay"
                                            :loop="true"
                                            :interval="3000"
                                            :duration="400"
                                            :fullScreenBtn="false"
                                            :isAutoPlayVideo="true"
                                            :showControls="false"
                                            @change="change"
                                            @click="jumpDetailPage"
                                            :list="bannerList"
                                            :indicatorStyle="indicatorStyle"
                                        >
                                            <view
                                                slot="indicator"
                                                class="indicator-row u-flex u-col-center u-row-center"
                                            >
                                                <view
                                                    v-for="(item, i) in bannerList"
                                                    class="indicator__dot"
                                                    :key="i"
                                                    :class="{ active: i === currIndex }"
                                                ></view>
                                            </view>
                                        </custom-swiper>
                                    </view>
                                </view>
                                <!-- 邂逅觅事 -->
                                <view class="jiemi-zhuanqu">
                                    <view
                                        class="official-accounts-list"
                                        v-for="(item, i) in officialAccountsList"
                                        :key="i"
                                    >
                                        <view
                                            style="display: flex; align-items: center; justify-content: space-between"
                                        >
                                            <view class="comment_item_top_left" @click="jumpHome(item)">
                                                <view class="avatar">
                                                    <img
                                                        :src="item.author_avatar"
                                                        mode="aspectFill"
                                                        class="comment_item_top_left_img"
                                                    />
                                                    <img
                                                        v-if="item.author_tag"
                                                        :src="item.author_tag"
                                                        class="comment_item_top_left_tag"
                                                    />
                                                </view>
                                                <view class="comment_item_top_left_name u-line-1">{{
                                                    item.author
                                                }}</view>

                                                <!-- <img
                                                    v-if="item.creator == '3587481'"
                                                    class="custom_imag"
                                                    style="
                                                        margin-left: 10rpx;
                                                        width: 128rpx;
                                                        height: 36rpx;
                                                        border-radius: 0rpx !important;
                                                    "
                                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png"
                                                    alt=""
                                                /> -->
                                            </view>
                                            <view
                                                class="time"
                                                style="color: #a6a6a6; font-size: 24rpx; padding-right: 24rpx"
                                                >{{ formatDate(item.publish_at) }}</view
                                            >
                                        </view>
                                        <img
                                            style="
                                                width: 100%;
                                                height: 296rpx;
                                                object-fit: cover;
                                                object-position: center;
                                            "
                                            :src="item.cover_image"
                                            alt=""
                                        />
                                        <view
                                            @click="viewDetail(item)"
                                            style="
                                                font-size: 31rpx;
                                                color: #404040;
                                                margin-top: 25rpx;
                                                padding-left: 31rpx;
                                                font-weight: 600;
                                            "
                                            >{{ item.title }}</view
                                        >
                                    </view>
                                    <view v-show="!isLoading" class="my-waterfall">
                                        <view class="community_content">
                                            <Comment
                                                :topicId="currActiveId"
                                                :flowList="flowList"
                                                :finished="finished"
                                                @changeFollowStatus="changeFollowStatus"
                                            ></Comment>
                                        </view>
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </block>

                <!-- #ifdef MP-WEIXIN -->
                <view class="add-publish" @click="handlePublish">
                    <img src="https://wpm-cdn.dreame.tech/images/202412/575817-1735020536436.png" />
                </view>

                <Publish :open="publishOpen" @close="handlepublishClose" />
                <!-- #endif -->

                <!-- #ifdef H5 -->
                <H5Publish :open="h5PublishOpen" @close="handleH5PublishClose" :rect="h5PublishRect" />
                <!-- #endif -->
            </view>
        </view>
        <NoNetwork v-else :isNetworkConnect="isNetworkConnect" @onRefresh="onRefresh" />
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <WxLogin></WxLogin>
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import Comment from './components/community/components/comment.vue';
import Consultation from './components/community/components/consultation.vue';
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from './components/CustomSwiper.vue';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
// import { wnewList } from '@/http/contents';
import { bannerList } from '@/http/home';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import ExploreLife from './components/exploreLife/exploreLife.vue';
import Community from './components/community/community.vue';
 // appShare,

import {
    getContentList,
    getTopicList,
    getContentTagList,
    TagItem,
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
} from '@/http/requestGo/community';
import Publish from './components/publish/Publish.vue';
import H5Publish from './components/publish/H5Publish.vue';
import WxLogin from '@/components/WxLogin/WxLogin.vue';

const FOLLOW_BTN_OPTION = [
    { value: 0, label: '关注' },
    { value: 1, label: '已关注' },
    { value: 2, label: '互相关注' },
];

enum JumpType {
    Page = 1, // 小程序内页面
    MiniProgram, // 第三方小程序
    WebView, // 网页
}

@Component({
    components: {
        CustomSwiper,
        NoNetwork,
        TabsSwiper,
        ExploreLife,
        Community,
        Publish,
        H5Publish,
        WxLogin,
        Comment,
        Consultation,
    },
    filters: {
        clicks(click) {
            if (Number(click) < 1000) {
                const item = click;
                return item;
            } else {
                const item = (Number(click) / 1000).toFixed(1);
                return item + 'k';
            }
        },
    },
})
export default class Contents extends Vue {
    $refs: {
        exploreRef;
        CommunityRef;
    };

    public tabKey: any = 0;

    get platform(): string {
        return AppModule.platform;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    // 网络状态
    get isNetworkConnect(): Boolean {
        return AppModule.isNetworkConnect;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get navstyle(): Object {
        const height: any = Number(this.statusBarHeight) + Number(108);
        return {
            height: `${height}rpx`,
            'padding-top': `${this.statusBarHeight}rpx`,
        };
    }

    get bannerList(): Array<any> {
        if (this.banners && this.banners.length > 0) {
            const newList = [];
            this.banners.forEach((itm) => {
                if (itm.image) {
                    const item = { url: '', title: '', type: 'image' };
                    item.url = itm.image;
                    item.title = itm.title;
                    newList.push(item);
                }
                if (itm.video) {
                    const item = { url: '', title: '', type: 'image', poster: '' };
                    item.url = itm.video;
                    item.title = itm.title;
                    item.poster = `${itm.video}?x-oss-process=video/snapshot,t_1000,m_fast`;
                    item.type = 'video';
                    newList.push(item);
                }
            });
            return newList;
        }
        return [];
    }

    get relationText() {
        return FOLLOW_BTN_OPTION.find((v) => v.value === this.relationStatus)?.label || '关注';
    }

    get mallVersion(): number {
        return UserModule.mallVersion;
    }

    public serveList: any = {
        name: '门店查询',
        icon: 'https://wpm-cdn.dreame.tech/images/202206/601646-*************.png',
        url: '/pagesA/nearShop/nearShop',
        jump: JumpType.Page,
        isPhone: true,
    };

    public officialAccountsId: any = 10; // 公众号id uat环境2 线上环境 10
    public officialAccountsList: any = [];

    public productId: any = 1; // 产品id uat 暂无 线上1
    public productIdList: any = [];

    public consultationId: any = 34;

    public scrollTop: number = 0; // 控制scroll-view滚动位置

    public relationStatus = 0;

    public timer: any = null;

    public indicatorStyle: any = { bottom: '34rpx', right: '28rpx' };

    public flowList: any = [];

    public isRefreshing: Boolean = false;
    public isLoading: Boolean = true;
    public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成

    public page: number = 1;
    public page_size: number = 10;
    public Pid: number = 0;

    public content_id: String = '';
    public isShowCoCreation = false;

    public productList: any = [];

    /** 当前的话题id */
    public currActiveId = '';
    public currTagActiveId = [];
    public currActiveIndex = 0;
    public isMsg = 0; // app消息新增返回按钮
    public show: Boolean = false;
    public handlerBack: Boolean = false;
    private showLoading: boolean = false; // 加载toast
    public tabsList: any = [{ name: '解觅专区' }, { name: '邂逅觅事' }, { name: '觅享家圈子' }];
    public tabsIndex: number = 1;
    public currIndex: number = 0;
    public autoplay: any = true;
    public banners: any = [];
    public bannersThing: any = [];
    public list1: any = [];
    public creator: any = -1;
    public indexTopicId: any = '';
    public exchangeTab: boolean = false; // 是否操作过一级话题
    public contentTagOption: TagItem[] = [];
    /** 选中的二级话题是否为 扫地机 */
    public isSweeper = false;
    /** 记录二级话题为扫地机的话题id */
    public sweeperTopicId = null;
    /** 记录二级话题为扫地机的一级话题的tabindex */
    public sweeperTabIndex = 3;
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    /** hardcode-点击首页产品tab下的官方教程跳转社区对应产品的话题id的映射 */
    public productIdToTopicIdMap = {
        11: 6,
        10: 4,
        12: 2,
        13: 5,
        16: 3,
    };

    async handleOperFlow(item, type) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                await this.handleOper(item, type);
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
        Utils.reportEvent('give_like', { title: item.title });
        // #ifdef H5
        await this.handleOper(item, type);
        // #endif
    }

    async handleOper(item, type) {
        let flag, res;
        // const defaultPraise = item.is_praise;
        if (item[type === 'praise' ? 'is_praise' : 'is_favorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = flag;
            item[type === 'praise' ? 'praise' : 'favorite'] += +flag ? 1 : -1;
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = res.data.id;
        }
        // this.$emit('praise', id);
    }

    clicks(val, index) {
        if (Number(val) === 0) {
            return ['点赞', '收藏', '评论'][index];
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    formatDate(date) {
        return Utils.timeAgoFormat(date, 'comment');
    }

    public isTimeout: any = null;

    changeFollowStatus(item, status) {
        this.flowList.forEach((i) => {
            if (i.creator === item.creator) {
                i.follow_status = status;
            }
        });
    }

    jumpHome(item) {
        Utils.jumpPersonHome(item.creator);
    }

    // tabbar 切换
    onTabItemTap(item) {
        uni.$emit(Constants.TABBAR_CHANGE, item);
    }

    animationfinish(e) {
        const current: number = e.detail.current;
        this.tabsIndex = current;
    }

    // async handleShare(item) {
    //     // #ifdef H5
    //     await createShare({ content_id: item.content_id });
    //     const { url } = await appShare({
    //         page:
    //             item.type === 1
    //                 ? `/pagesC/contentDetails/contentDetails`
    //                 : `/pagesC/contentVideoDetails/contentVideoDetails`,
    //         content_id: item.content_id,
    //     });
    //     const params = {
    //         target: 'wechat',
    //         type: 'web',
    //         content: {
    //             url: url,
    //             share_image: item.cover_image + '?x-oss-process=image/resize,w_200',
    //             share_title: '邀请您加入追觅会员官方商城小程序社区 ' + item.title,
    //         },
    //     };
    //     Utils.messageChannel('share', params);
    //     // #endif
    // }

    // 获取话题列表
    async getTlist(pid = '') {
        try {
            let param = {};
            if (pid) {
                param = { level: 2, parent_id: +pid, page_size: 999 };
            } else {
                param = { level: 1, page_size: 999 };
            }
            const result = await getTopicList(param);
            const { list = [] } = result.data;
            if (uni.$u.test.isEmpty(list)) return;
            if (pid) {
                this.productList[this.tabsIndex] = [...list];
                this.tabsList[this.tabsIndex].currentTopicId = list[0].id;
                if (!this.currActiveId) {
                    this.currActiveId = list[0].id;
                    this.currActiveIndex = 0;
                }

                // 二级话题首个话题为扫地机，且当前currActiveId等于该话题id，才初始化
                if (list[0].name === '扫地机' && this.currActiveId === list[0].id) {
                    this.sweeperTopicId = list[0].id;
                    this.sweeperTabIndex = this.tabsIndex;
                    this.currTagActiveId = [this.contentTagOption[0].id];
                    this.isSweeper = true;
                }
            } else {
                if (list.length > 1) {
                    this.tabsList = [
                        ...list.map((i, index) => ({ ...i, currentTopicId: index === 0 ? list[0].id : '' })),
                    ];
                    await this.getTlist(list[this.tabsIndex].id);
                }
            }
        } catch (e) {}
    }

    refresherrefresh() {
        this.isRefreshing = true;
        this.page = 1;
        this.officialAccountsList = [];
        this.tabLoading = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
            this.tabLoading = false;
        }, 200);
    }

    // banner跳转
    jumpDetailPage() {
        const item = this.banners[this.currIndex];
        const path = item.jump_url.split('?')[0];
        const jump_type = item.jump_type + '';
        // #ifdef MP-WEIXIN
        switch (jump_type) {
            case '1':
                uni.navigateTo({
                    url: `/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`,
                });
                break;
            case '2':
                uni.navigateTo({
                    url: `/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`,
                });
                break;
            case '3':
                if (Constants.TAB_BAR_LIST.includes(path)) {
                    uni.reLaunch({
                        url: `${item.jump_url}`,
                    });
                } else {
                    uni.navigateTo({ url: `${item.jump_url}` });
                }
                break;
            case '4':
                uni.navigateToMiniProgram({
                    appId: item.appid,
                    path: item.jump_url,
                });
        }
        // #endif

        // #ifdef H5
        switch (jump_type) {
            case '1':
                Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`);
                break;
            case '2':
                Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`);
                break;
            case '3':
                if (Constants.TAB_BAR_LIST.includes(path)) {
                    if (path === '/pages/index/index') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                    } else if (path === '/pages/vipCenter/vipCenter') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/vip' });
                    } else if (path === '/pages/contents/contents') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/explore' });
                    } else if (path === '/pages/shop/shop') {
                        Utils.navigateTo(`${item.jump_url}`);
                    } else {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                    }
                } else {
                    Utils.navigateTo(`${item.jump_url}`);
                }
                break;
            case '4':
                Utils.openMiniProgram({
                    id: item.appid,
                    path: item.jump_url,
                });
                break;
        }
        // #endif
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER,
        });
    }

    onScrollToLower() {
        this._getListFactory();
    }

    // 获取内容列表
    async getWlist() {
        try {
            if (!this.currActiveId) return;
            const topic_id: number = +this.currActiveId || 0;
            const page: number = this.page;
            // page === 1 && (this.isLoading = true);
            const params = { topic_id, page, page_size: this.page_size, tag_ids: this.currTagActiveId };
            const res = await getContentList(params, this.showLoading);
            if (res.success) {
                const { list = [], total = 1 } = res.data;
                if (this.page >= Math.ceil(total / this.page_size)) {
                    this.finished = true;
                    this.page = 1;
                }
                // if (page === 1) {
                //     this.flowList = list;
                // } else {
                this.officialAccountsList = this.officialAccountsList.concat(list);
                // }
                this.page++;
                setTimeout(() => {
                    this.isLoading = false;
                }, 300);
            }
            console.log('getWlist--------------', this.page);
        } catch (e) {
            console.error('getWlist err=', e);
            this.isLoading = false;
        }
    }

    async onLoad(options) {
        if (options) {
            if (options.tabsIndex) {
                this.tabsIndex = +options.tabsIndex;
                this.sweeperTabIndex = this.tabsIndex;
            }
            if (options.isMsg) {
                this.isMsg = +options.isMsg;
            }
            if (options.indexTopicId) {
                this.currActiveId = this.productIdToTopicIdMap[Number(options.indexTopicId)];
            }
        }

        const result = await getContentTagList({ type: 4 });
        this.contentTagOption = result.data.list.flatMap((v) => v.children);

        await this.getTlist();

        // #ifdef H5
        await this.init();
        // const res = await wnewList({ pid: '1' });
        // this.list1 = res;
        this.show = true;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER_EXPOSURE,
        });
        // #endif
    }

    async onShow() {
        // #ifdef H5
        const win: any = window;
        win.onAppPageShow = async () => {
            // this.$nextTick(async () => {
            this.isLoading = true;
            clearTimeout(this.isTimeout);
            this.isTimeout = setTimeout(async () => {
                const del_content_id = await Utils.sharedStorage('get', 'del_content_id');
                // await this.init();
                if (this.tabsIndex === 0) {
                    this.$refs.CommunityRef[0].flowList = this.$refs.CommunityRef[0].flowList.filter(
                        (i) => i.content_id !== del_content_id,
                    );
                } else if (this.tabsIndex === 1) {
                    this.flowList = this.flowList.filter((i) => i.content_id !== del_content_id);
                } else if (this.tabsIndex === 2) {
                    this.$refs.CommunityRef[0].flowList = this.$refs.CommunityRef[0].flowList.filter(
                        (i) => i.content_id !== del_content_id,
                    );
                } else if (this.tabsIndex === 3) {
                    // this.$refs.exploreRef[0].flowList = this.$refs.exploreRef[0].flowList.filter(
                    //     (i) => i.content_id !== del_content_id,
                    // );
                    this.$refs.CommunityRef[0].flowList = this.$refs.CommunityRef[0].flowList.filter(
                        (i) => i.content_id !== del_content_id,
                    );
                }
                this.isLoading = false;

                Utils.sharedStorage('remove', 'del_content_id');
            }, 200);
            // });
            // const res = await wnewList({ pid: '1' });
            // this.list1 = res;
        };
        // #endif
        // #ifdef MP-WEIXIN
        await this.init();
        // const res = await wnewList({ pid: '1' });
        // this.list1 = res;
        // #endif
        if (this.publishOpen || this.h5PublishOpen) {
            uni.hideTabBar();
        } else {
            uni.showTabBar();
        }
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BUTTON,
        });
    }

    async onRefresh() {
        await this.getTlist();
        this.currActiveId = this.productList[this.tabsIndex][0].id;
        this.currActiveIndex = 0;
        this.tabsList[this.tabsIndex].currentTopicId = this.currActiveId;
        await this.init();
        // const res = await wnewList({ pid: '1' });
        // this.list1 = res;
        this.show = true;
    }

    async init(winoutBanner = false) {
        this.clear();
        if (this.tabsIndex === 1) {
            if (!winoutBanner) {
                const result = await bannerList({ drop_position: 3, banner_version: 2 });
                this.banners = result.data;
                this.currActiveId = this.tabsList[this.tabsIndex].currentTopicId;
                this.currActiveIndex = this.tabsIndex;
            }

            this.currActiveId = this.officialAccountsId;
            await this._initPageData();
            await this._getListFactory();
        }
    }

    async initTopicList() {
        await this.getTlist(this.tabsList[this.tabsIndex].id);
        if (
            !this.currActiveId ||
            this.productList[this.tabsIndex].some((i: any) => i.id === this.currActiveId) === false
        ) {
            this.currActiveId = this.productList[this.tabsIndex][0].id;
            this.currActiveIndex = 0;
        }
    }

    // 初始化页面的数据
    _initPageData() {
        this.finished = false;
        this.page = 1;
        this.flowList = [];
        this.showLoading = false;
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        if (this.finished) {
            this.page = 1;
            this.finished = false;
        }
        await this.getWlist();
    }

    change(e) {
        this.currIndex = e.current;
    }

    async swiperChange(e) {
        const current = e.detail.current;
        this.tabsIndex = current;
        this.tabLoading = true;
        this.currActiveId = this.tabsList[this.tabsIndex].currentTopicId;
        // 切换非扫地机tab隐藏筛选
        if (this.sweeperTabIndex !== this.tabsIndex) {
            this.isSweeper = false;
            this.currTagActiveId = [];
        } else {
            // console.log(this.currActiveId, 3938939)
            // console.log(this.sweeperTopicId, 2334449)
            // 切换扫地机tab，但是选中的二级话题不是扫地机清空并隐藏筛选
            if (this.currActiveId !== this.sweeperTopicId) {
                this.isSweeper = false;
                this.currTagActiveId = [];
            } else {
                this.currTagActiveId = [this.contentTagOption[0].id];
                this.isSweeper = true;
            }
        }

        if (current === 0) {
            await this.init();
        } else if (current === 1) {
            await this.init();
        } else if (current === 2) {
            await this.init();
        } else if (current === 3) {
            await this.init();
        }
        this.tabLoading = false;
        current === 1 && (this.exchangeTab = true);
    }

    async changeMainTabs(event) {
        this.tabsIndex = event;
    }

    async changeTabs(item, _index) {
        if (this.currActiveId == item.id) return true;

        // 记录一级tab和二级topicid
        if (item.name === '扫地机') {
            this.sweeperTopicId = item.id;
            this.sweeperTabIndex = this.tabsIndex;
            this.currTagActiveId = [this.contentTagOption[0].id];
            this.isSweeper = true;
        } else {
            this.isSweeper = false;
            this.currTagActiveId = [];
        }

        this.tabLoading = true;
        this.currActiveId = item.id;
        this.tabsList[this.tabsIndex].currentTopicId = item.id;
        this.currActiveIndex = _index > 0 ? _index - 1 : _index;
        this.flowList = [];
        await this.init(true);
        this.tabLoading = false;
    }

    async changeTags(item, index) {
        this.tabLoading = true;
        if (this.currTagActiveId.includes(item.id)) {
            this.currTagActiveId = [];
        } else {
            this.currTagActiveId = [item.id];
        }
        await this.init(true);
        this.tabLoading = false;
    }

    goSearch() {
        Utils.navigateTo(`/pagesC/search/search`);
    }

    viewMore() {
        Utils.navigateTo(`/pagesA/contents/officialAccount`);
    }

    viewDetail(item) {
        const { content_id = '', type } = item;
        this.content_id = item.content_id;
        // Utils.navigateTo(`/pagesC/contentDetails/contentDetails?content_id=${content_id}`);
        // Utils.navigateTo(
        //     type === 1
        //         ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
        //         : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this
        //               .currActiveId}`,
        // );
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this
                      .officialAccountsId}`,
        );
    }

    memberCenter() {
        // #ifdef MP-WEIXIN
        Utils.navigateTo('/pages/vipCenter/vipCenter');
        // #endif
        // #ifdef H5
        if (this.mallVersion > 4) {
            Utils.navigateTo('/pages/vipCenter/vipCenter');
        } else {
            Utils.messageChannel('navigation', { type: 'mall', path: 'home/vip' });
        }
        // #endif
    }

    storeInquiry(service) {
        this.handleJump(service);
    }

    handleJump(service) {
        switch (service.jump) {
            case JumpType.Page:
                Utils.navigateTo(service.url);
                break;
            // case JumpType.MiniProgram:
            //     uni.navigateToMiniProgram({
            //         appId: service.url,
            //     });
            //     break;
            case JumpType.WebView:
                Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(service.url)}`);
                break;
        }
    }

    goCocreation() {
        if (!this.isPhone) {
            UserModule.authFlow({ target: 'goCocreation' });
        } else {
            Utils.navigateTo('/pagesB/superApp/index');
        }
    }

    clear() {
        this.$nextTick(() => {
            // this.tabsIndex === 1 && this.exchangeTab && (this.$refs.exploreRef as any)?.[0]?.clear();
            // this.tabsIndex === 0 && this.$refs.CommunityRef && (this.$refs.CommunityRef as any)?.[0]?.clear();
        });
    }

    public startX: any = 0;
    public startY: any = 0;
    public canPullDown: boolean = true;
    public isLeftMove: boolean = true;

    touchStart(e) {
        this.startX = e.touches[0].pageX;
        this.startY = e.touches[0].pageY;
    }

    touchMove(e) {
        if (!this.isLeftMove) return;
        const moveX = e.touches[0].pageX;
        const moveY = e.touches[0].pageY;
        const diffX = moveX - this.startX;
        const diffY = moveY - this.startY;

        // 如果是左右滑动，禁用下拉刷新
        if (Math.abs(diffX) > Math.abs(diffY)) {
            this.canPullDown = false;
        } else {
            this.isLeftMove = false;
        }
    }

    touchEnd() {
        // 在滑动结束后，启用下拉刷新
        this.canPullDown = true;
        this.isLeftMove = true;
    }

    public publishOpen: boolean = false;
    public h5PublishOpen: boolean = false;

    // #ifdef MP-WEIXIN
    handlePublish() {
        if (!this.isPhone) {
            UserModule.authFlow({ target: 'handlePublish' });
        } else {
            uni.hideTabBar();
            this.$nextTick(() => {
                this.publishOpen = true;
            });
        }
    }

    handlepublishClose() {
        this.publishOpen = false;
        this.$nextTick(() => {
            uni.showTabBar();
        });
    }
    // #endif

    // #ifdef H5
    public h5PublishRect = {};
    handleH5Publish() {
        uni.createSelectorQuery()
            .select('.community-publish')
            .boundingClientRect((rect) => {
                this.h5PublishRect = rect;
            })
            .exec();
        this.h5PublishOpen = true;
    }

    handleH5PublishClose() {
        this.h5PublishOpen = false;
    }
    // #endif

    backPage() {
        Utils.goBack();
    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view-refresher {
    background-color: #f6f6f6 !important;
}
.swiper-box {
    height: 100vh;
    margin-bottom: 228rpx;
    // #ifdef H5
    ::v-deep .uni-swiper-wrapper {
        overflow: hidden !important;
    }
    // #endif
    .swiper-item {
        width: 100%;
        height: 100%;
        .scroll-view {
            height: 100%;
            background-color: rgb(245, 245, 245);
            // #ifdef H5
            ::v-deep .uni-scroll-view {
                .uni-scroll-view-content {
                    height: fit-content;
                }
            }
            // #endif
        }
    }
}

.contents-container {
    position: relative;
    background: #f6f6f6;

    ::v-deep .uni-swiper-wrapper {
        overflow: auto;
    }

    .title {
        display: flex;
        flex-direction: column;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        z-index: 3;
        color: $color-2A2A2A;
        display: flex;
        background: #f6f6f6;
        align-items: unset;
        // #ifdef H5
        justify-content: center;
        // #endif
        // #ifdef MP-WEIXIN
        justify-content: flex-start;
        // #endif

        .status-bar-left {
            flex-direction: column;
            margin-right: auto;
            justify-content: start;
            align-items: start;
            .text {
                margin-top: 10rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 27rpx;
                color: #121212;
                line-height: 38rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }

        .status-bar-image {
            padding: 2rpx 30rpx 0 38rpx;
            background: #f6f6f6;
            display: flex;
            align-items: center;
            .left-bar {
                .back_bar {
                    padding-right: 20rpx;
                    img,
                    image {
                        height: 50rpx;
                    }
                }
            }

            .title-image {
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202502/720574-1740109971236.png')
                    no-repeat;
                background-size: cover;
                width: 212rpx;
                height: 29.3rpx;
            }

            .text {
                font-size: 25rpx;
            }

            text {
                margin-left: 14rpx;
                font-family: MiSans;
                font-size: 40rpx;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0px;
                font-variation-settings: 'opsz' auto;
                color: #040000;
            }
        }

        .status-bar {
            width: 100%;
        }
    }

    .swiper-image {
        background-color: #f6f6f6;
        width: 100%;
        height: 417rpx;
        padding: 8rpx 8rpx;

        .carousel-map {
            height: 100%;
            border-radius: 8rpx;
            overflow: hidden;
        }
    }

    .scroll-view-look {
        white-space: nowrap;
        ::v-deep .uni-scroll-view-content {
            display: flex;
            align-items: center;
        }
        .item {
            display: inline-block;
            padding-right: 75rpx;
            &:last-child {
                padding-right: 30rpx;
            }
        }
    }

    .stickyContent {
        width: 100%;
        z-index: 2;
        position: sticky;
        top: -1rpx;
        padding-left: 32rpx;
        z-index: 100;
        transition: background 0.1s ease-in-out;
        padding-top: 24rpx;
        .tab-view {
            -webkit-overflow-scrolling: touch;
            width: 100%;
            /* height: 92rpx; */
            overflow-x: scroll;
            align-items: flex-start;
            // #ifdef H5
            ::v-deep .uni-scroll-view-content {
                display: flex;
                flex-wrap: nowrap;
                justify-content: flex-start;
                align-items: flex-start;
            }
            // #endif
            .tab-item {
                /* margin-top: 28rpx; */
                flex-shrink: 0;
                font-size: 28rpx;
                color: $text-color-regular;
                /* line-height: 38rpx; */
                position: relative;
                transition: all 0.1s ease-in-out;
                &:last-child {
                    // margin-right: 0;
                }
                .icon {
                    position: absolute;
                    top: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 58rpx;
                    height: 58rpx;
                    background-image: url('https://wpm-cdn.dreame.tech/mova/images/202408/66c554a26c39c4430209153.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }
            .tab-item-active {
                font-weight: 600;
            }
        }

        .filter-tag {
            -webkit-overflow-scrolling: touch;
            width: 100%;
            display: flex;
            overflow-x: scroll;
            align-items: flex-start;
            // #ifdef H5
            ::v-deep .uni-scroll-view-content {
                display: flex;
                flex-wrap: nowrap;
                justify-content: flex-start;
                align-items: flex-start;
            }
            // #endif
            &__item {
                flex-shrink: 0;
                font-size: 28rpx;
                color: $text-color-regular;
                position: relative;
                transition: all 0.1s ease-in-out;
            }
        }
    }

    .jiemi-zhuanqu {
        overflow: auto;
        padding: 0rpx 0rpx 0;
        background-color: rgb(245, 245, 245);

        .official-accounts-list {
            height: 524rpx;
            background: #ffffff;
            border-radius: 16rpx;
            width: calc(100% - 48rpx);
            margin: 0 auto 24rpx;
            .comment_item_top_left {
                padding-left: 23rpx;
                height: 88rpx;
                display: flex;
                align-items: center;
                position: relative;
                .avatar {
                    height: 38rpx;
                    position: relative;
                }
                img,
                image {
                    object-fit: cover;
                    width: 38rpx;
                    height: 38rpx;
                    border-radius: 50%;
                    vertical-align: top;
                }
                .comment_item_top_left_tag {
                    position: absolute;
                    width: 18rpx;
                    height: 18rpx;
                    left: 24rpx;
                    bottom: 0;
                }

                ::v-deep .custom_imag {
                    object-fit: contain;
                    border-radius: 0rpx !important;
                }

                &_name {
                    margin-left: 15rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 23rpx;
                    color: #777777;
                    line-height: 40rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    max-width: 380rpx;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        .more-official-accounts {
            height: 130rpx;
            margin-top: 30rpx;
            font-size: 27rpx;
            color: #a6a6a6;
            text-align: center;
            line-height: 38rpx;
        }
        .product-list-container {
            .product-list {
                display: flex;
                justify-content: space-between;
                padding: 0rpx 24rpx;
                margin-bottom: 24rpx;
                .product-list-item {
                    background-color: #fff;
                    display: inline-block;
                    width: calc(50% - 9rpx);
                    padding-bottom: 30rpx;
                    overflow: hidden;
                    border-radius: 8rpx;
                    .comment_item_imgs_item {
                        width: 100%;
                        height: 420rpx;
                        object-fit: cover;
                        object-position: center;
                    }
                    .comment_item_video_icon {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 77rpx;
                        height: 77rpx;
                        image,
                        img {
                            width: 77rpx;
                            height: 77rpx;
                        }
                    }

                    .product-list-item-title {
                        padding: 0rpx 23rpx 0rpx 21rpx;
                        padding-top: 22rpx;
                        font-size: 27rpx;
                        color: #404040;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: normal;
                        text-align: left;
                        font-style: normal;
                        font-weight: 500;
                        margin-bottom: 20rpx;
                    }

                    .comment_item_top_left {
                        padding-left: 23rpx;
                        display: flex;
                        align-items: center;
                        position: relative;
                        .avatar {
                            height: 38rpx;
                            position: relative;
                        }
                        img,
                        image {
                            object-fit: cover;
                            width: 38rpx;
                            height: 38rpx;
                            border-radius: 50%;
                            vertical-align: top;
                        }
                        .comment_item_top_left_tag {
                            position: absolute;
                            width: 18rpx;
                            height: 18rpx;
                            left: 24rpx;
                            bottom: 0;
                        }

                        ::v-deep .custom_imag {
                            object-fit: contain;
                            border-radius: 0rpx !important;
                        }

                        &_name {
                            margin-left: 15rpx;
                            font-family: MiSans, MiSans;
                            font-weight: 400;
                            font-size: 23rpx;
                            color: #777777;
                            line-height: 40rpx;
                            text-align: left;
                            font-style: normal;
                            text-transform: none;
                            max-width: 380rpx;
                        }
                    }
                    .oper_item {
                        display: flex;
                        align-items: center;
                        &:not(:last-child) {
                            padding-right: 32rpx;
                        }
                        img,
                        image {
                            width: 31rpx;
                            height: 31rpx;
                            margin-right: 8rpx;
                            object-fit: contain;
                        }
                        .oper_num {
                            min-width: 25px;
                            font-family: MiSans, MiSans;
                            font-weight: 400;
                            font-size: 23rpx;
                            color: #777777;
                            line-height: 40rpx;
                            text-align: center;
                            font-style: normal;
                            text-transform: none;
                        }
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
        }

        .my-waterfall {
            z-index: 1;
            .community_content {
                min-height: calc(100vh - 795rpx);
                padding: 0rpx 0rpx 70rpx;
                background-color: rgb(245, 245, 245);
            }
        }
    }

    .compare {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 48rpx;
        margin-top: 60rpx;
        margin-bottom: 28rpx;

        .selection-guide {
            padding-left: 8rpx;
        }

        .selection-guide,
        .selection-guide1 {
            font-size: 36rpx;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 600;
            color: #121212;
            line-height: 46rpx;
        }

        .selection-guide1 {
            padding-left: 16rpx;
        }

        .suitable-product {
            padding-right: 8rpx;
        }

        .suitable-product,
        .suitable-product1 {
            font-size: 28rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #777777;
            height: 38rpx;
            line-height: 38rpx;
        }

        .suitable-product1 {
            padding-right: 8rpx;
        }
    }

    .compare1 {
        margin-bottom: 26rpx;
    }

    .search-message {
        display: flex;
        justify-content: space-between;

        .search-content {
            width: 338rpx;
            height: 192rpx;
        }
    }

    .description {
        display: inline-block;
        margin-right: 32rpx;
        /* margin-top: 32rpx; */
        margin-bottom: 16rpx;
        text-align: center;
        padding: 12rpx 32rpx;
        height: 56rpx;
        border-radius: 160rpx;
        font-family: MiSans;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 1 !important;
        letter-spacing: 0em;
        box-sizing: border-box;
        border: 1px solid #121212;
        font-variation-settings: 'opsz' auto;
        /* Text 文字/text_1 */
        /* 样式描述：强调/标题文字 */
    }

    .isActive {
        border-color: #d8d8d8;
        color: #6a6a6a;
    }

    .active {
        color: #121212;
        border-color: #121212;
    }

    .Co-creation_image {
        background: url('https://wpm-cdn.dreame.tech/images/202312/6569b98fcf3ea8493122758.png') no-repeat;
        background-size: 100% 100%;
        width: 688rpx;
        height: 230rpx;
        border-radius: 15rpx 15rpx 15rpx 15rpx;
        position: relative;
    }

    .compare-image {
        background: url('https://wpm-cdn.dreame.tech/images/202305/64745e3e72a204693616143.png') no-repeat;
        background-size: 100% 100%;
        height: 220rpx;
        width: 100%;
        position: relative;

        .goCompare {
            position: absolute;
            width: 132rpx;
            height: 54rpx;
            line-height: 54rpx;
            text-align: center;
            background: #1d1e20;
            border-radius: 138rpx;
            font-weight: 600;
            color: #ffffff;
            left: 54rpx;
            bottom: 28rpx;
            font-size: 24rpx;
        }
    }

    .scroll-view_H {
        white-space: nowrap;
        display: flex;
        padding-left: 32rpx;
        padding-top: 24rpx;
        padding-bottom: 2rpx;

        .tanmi-shenghuo {
            margin-right: 16rpx;
            width: 300rpx;
            height: 346rpx;
            display: inline-block;
            position: relative;
            overflow: hidden;

            .view-num {
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 106rpx;
                top: 12rpx;
                left: 12rpx;
                position: absolute;
                min-height: 38rpx;
                padding: 6rpx 14rpx;
                background: rgba(35, 35, 35, 0.3);
                border-radius: 108px;

                .view-num-image {
                    width: 28rpx;
                    height: 28rpx;
                }

                .text {
                    margin-left: 10rpx;
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #ffffff;
                }
            }

            .view-content {
                position: absolute;
                width: 100%;
                min-height: 112rpx;
                padding: 18rpx 22rpx 18rpx 24rpx;
                background: rgba(20, 20, 20, 0.2);
                backdrop-filter: blur(32rpx);
                bottom: 0rpx;
                border-bottom-left-radius: 12rpx;
                border-bottom-right-radius: 12rpx;

                .view-content-text {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 38rpx;
                    white-space: normal;
                    word-break: break-all;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    color: #ffffff;
                    font-size: 28rpx;
                    font-weight: 500;
                }
            }
        }

        .view-more {
            margin-right: 32rpx;
            width: 170rpx;
            height: 346rpx;
            display: inline-block;
            background: #f6f6f6;
            border-radius: 12rpx;
            position: relative;
            color: #121212;
            font-weight: 500;
            font-size: 28rpx;

            .view-more-content {
                position: absolute;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 170rpx;
                height: 346rpx;

                .jump {
                    width: 42rpx;
                    height: 42rpx;
                    margin-top: 12rpx;
                }
            }
        }
    }

    .indicator-row {
        width: 100%;
        height: 2rpx;

        .indicator__dot {
            width: 70rpx;
            height: 4rpx;
            right: 28rpx;
            background-color: rgba(38, 38, 38, 0.4);

            &.active {
                height: 4rpx;
                background-color: #ffffff;
            }
        }
    }
}

.community-search,
.community-publish {
    margin-left: 30rpx;
    img {
        display: block;
        height: 46rpx;
        width: 46rpx;
    }
}

.add-publish {
    width: 104rpx;
    height: 104rpx;
    position: fixed;
    right: 30rpx;
    bottom: 30rpx;
    border-radius: 50%;

    img {
        width: 100%;
        height: 100%;
    }
}
</style>
