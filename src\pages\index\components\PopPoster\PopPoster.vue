<template>
    <u-popup :show="isShow" mode="top" bgColor="transparent" :round="12">
        <view class="pop-poster">
            <painter v-if="paintPallette" :palette="paintPallette" @imgOK="onImgOK" :widthPixels="750" />
            <image id="popImg" v-if="image" class="image" mode="heightFix" :draggable="false" :src="image" @longpress="savePoster" />
            <view v-else class="image"></view>
        </view>
    </u-popup>
</template>
<script lang="ts">
import { Prop, Vue, Component } from 'vue-property-decorator';
import Card from './card';
import Utils from '@/common/Utils';
import { UserInfo, UserModule } from '@/store/modules/user';

const SCOPE_WRITE_PHOTOS_ABLUM = 'scope.writePhotosAlbum';

@Component
export default class PopPoster extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({
        type: Object,
        default: { nick: '', cover: '', keyword: '' },
    })
    readonly posterData: {};

    public paintPallette: Object = {}; // 海报数据
    public image: string = '';
    public timer: any = null;
    public saveImgState: boolean = false;

    get userInfo(): UserInfo {
        return UserModule.userInfo;
    }

    created() {
        this.handleShowLoading();
        this.handleCreatePainter();
    }

    generateImage(string: String, {
        font = '25vw Arial',
        fillStyle = '#000000'
    }) {
            const canvas: any = document.createElement('canvas');
            canvas.height = '300'
            canvas.width = '1000'
            const context = canvas.getContext('2d');
            context.fillStyle = '#FFFFFF';
            // 设置文本样式
            context.font = font;
            context.fillStyle = fillStyle;
            // 绘制文本
            context.fillText(string, 0, 250);
            // 将Canvas转换为Base64图片
            const dataURL = canvas.toDataURL('image/png');
            // 将Base64图片展示出来
            return dataURL
    }

    // 生成海报
    handleCreatePainter() {
        let imgList = [];
        // #ifdef H5
        // painter h5bug text生成base64显示
        imgList = [this.generateImage(this.userInfo.nick.length > 15 ? this.userInfo.nick.substr(0, 8) + '...\u3000' : this.userInfo.nick, { font: '100px Arial', fillStyle: '#000000' }),
            this.generateImage('向你推荐', { font: '80px Arial', fillStyle: 'rgba(34, 34, 34, 0.5)' }),
            this.generateImage('长按保存图片', { font: '80px Arial', fillStyle: 'rgba(34, 34, 34, 0.5)' })];
        // #endif
        console.log(imgList, 'imgList');
        this.paintPallette = new Card().palette(this.posterData, this.userInfo, imgList);
    }

    handleShowLoading() {
        uni.showLoading({
            title: '加载中',
            mask: true,
        });
    }

    onImgOK(e) {
        if (this.timer) return
        this.timer = setTimeout(async() => {
            this.image = e.detail.path;
            Utils.handleHideLoading();
            this.saveImgState && (await this.savePoster());
            clearTimeout(this.timer)
        }, 300)
    }

    async setSavePoster() {
        if (this.image) {
            await this.savePoster();
        } else {
            this.saveImgState = true;
        }
    }

    async savePoster() {
        const that = this;
        // #ifdef H5
        // const getPolicyRes = await Utils.getPolicy();
        // const imageList: any = await Utils.handleImageUpload([{ url: this.image, name: 'haibao' + +new Date() + '.png' }], getPolicyRes);
        // const msg = await Utils.saveImage(this.image, 'base64');
        const msg = await Utils.saveImage(this.image.replace('data:image/png;base64,', ''), 'base64');
        Utils.Toast(msg);
        // #endif
        // #ifdef MP-WEIXIN
        wx.getSetting({
            success(res) {
                if (res.authSetting[SCOPE_WRITE_PHOTOS_ABLUM]) {
                    console.log('saveImage');
                    that.saveImage();
                } else if (res.authSetting[SCOPE_WRITE_PHOTOS_ABLUM] === undefined) {
                    console.log('authSetting');
                    try {
                        uni.authorize({
                            scope: SCOPE_WRITE_PHOTOS_ABLUM,
                            success() {
                                console.log('authorize');
                                that.saveImage();
                            },
                            fail() {
                                console.log('fail');
                                that.authConfirm();
                            },
                        });
                    } catch (e) {
                        console.log(e);
                    } finally {
                        this.saveImgState = false;
                    }
                } else {
                    console.log('authConfirm');
                    that.authConfirm();
                }
            },
        });
        // #endif
    }

    // 授权拒绝后，再次授权提示弹窗
    authConfirm() {
        const that = this;
        uni.showModal({
            content: '检测到您没打开保存图片权限，是否去设置打开？',
            confirmText: '确认',
            cancelText: '取消',
            success: async function (res) {
                if (res.confirm) {
                    console.log(this.image, 'this.image');
                    wx.openSetting({
                        success(res) {
                            if (res.authSetting[SCOPE_WRITE_PHOTOS_ABLUM]) {
                                that.saveImage();
                            } else {
                                Utils.Toast('您没有授权，无法保存到相册');
                            }
                        },
                    });
                } else {
                    Utils.Toast('您没有授权，无法保存到相册');
                }
            },
        });
    }

    // 长按保存图片
    saveImage() {
        uni.saveImageToPhotosAlbum({
            filePath: this.image,
            success(res) {
                console.log('saveImg res=', res);
                Utils.Toast('保存成功~');
            },
            fail(err) {
                console.log('saveImg err=', err);
                Utils.Toast('保存失败~');
            },
            complete() {
                uni.vibrateShort({});
            },
        });
    }
}
</script>

<style lang="scss" scoped>
.pop-poster {
    margin: 0 auto;
    margin-top: 120rpx;
    // width: 500rpx !important;
    height: calc(100vh - 500rpx - 156rpx);
    border-radius: 22rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .image {
        // width: 500rpx !important;
        height: calc(100vh - 500rpx - 156rpx);
        border-radius: 22rpx;
        -webkit-user-drag: none; // 禁止拖拽
        user-select: none;
    }
}
</style>
