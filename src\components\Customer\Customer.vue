<template>
    <view class="customer" @click="handleContact" :style="customerStyle">
        <image src="https://wpm-cdn.dreame.tech/images/202309/64f18e97e86e19521398153.png"></image>
        <view class="customer-text">客服</view>
        <customer-service-picker/>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import { BuType } from '../CustomerServicePicker/customer-butype';

@Component
export default class Customer extends Vue {
    @Prop({
        type: String,
        default: ''
    })
    readonly customerStyle;

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    handleContact(e) {
        this.$emit('onClick')
        if (this.wxAuth && this.isPhone) {
            Utils.decryptContact(e, BuType.BuType_NONE);
        } else {
            const target = 'handleContact';
            UserModule.authFlow({ target });
        }
    }
}
</script>

<style lang="scss" scoped>
.customer {
    position: fixed;
    right: 42rpx;
    bottom: 218rpx;
    width: 96rpx;
    height: 96rpx;
    @include flex(column, center, center, none);
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.11999999731779099);
    border: 2rpx solid #E2E2E2;
    border-radius: 50%;
    z-index: 1;

    image {
        width: 42rpx;
        height: 42rpx;
    }

    .customer-text {
        font-size: 20rpx;
        color: $text-color-primary;
        line-height: 28rpx;
    }
}
</style>
