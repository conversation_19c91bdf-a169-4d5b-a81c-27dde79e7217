<template>
    <view class="search-history-container">
        <view class="goldCoins" v-if="fromPage == 'goldCoins' && !searchStatus">
            <image
                class="goldCoinsIcon"
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fddf20fae10640080168.png"
            />
            <text class="goldCoinsTextTitle">搜商品浏览金币</text>
            <text class="goldCoinsTextNum">+{{ goldCoin }}</text>
        </view>
        <view class="search-history-new" v-if="searchHistoryList.length > 0 || searchPointHistoryList.length > 0">
            <view class="title">
                <view class="history_title">
                    <!-- <image
                        class="history_icon"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870ac125c0a93770011077.png"
                    >
                    </image> -->
                    <text>搜索历史</text>
                </view>
                <image
                    v-if="!deleteShow"
                    class="detele"
                    @click="clearHistory"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870ae2c1bc621140019938.png"
                />
                <view
                    v-else
                    style="display: flex; align-items: center; font-size: 24rpx; color: #777777; padding-right: 32rpx"
                >
                    <view style="margin-right: 2rpx" @click="deleteAllData">全部删除</view>
                    <image
                        style="width: 32rpx; height: 32rpx"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870b82273b914740026249.png"
                    />
                    <text style="margin-left: 2rpx" @click="deleteFinish">完成</text>
                </view>
            </view>
            <view :class="{ delete_item: deleteShow }" class="history-list" v-if="search_type == '1'">
                <view class="item" v-for="(h, i) in searchHistoryList" :key="i" @click="triggerSearch(h, i)"
                    >{{ h }}
                    <image
                        class="delete_icon"
                        v-if="deleteShow"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870ba6720c5e1340019897.png"
                    >
                    </image>
                </view>
            </view>
            <view class="history-list" v-if="search_type == '2'">
                <view class="item" v-for="(h, i) in searchPointHistoryList" :key="i" @click="triggerSearch(h, i)">{{
                    h
                }}</view>
            </view>
            <CustomModal
                :show="isDelete"
                @confirm="confirmDetale"
                :showCancelButton="true"
                buttonSize="large"
                @cancel="cancelDetele"
                :content="titleDetele"
                :zoom="true"
            />
        </view>
        <view class="exploration_discovery" v-if="recommendList.length > 0">
            <view class="title">
                <view class="history_title">
                    <!-- <image
                        class="history_icon"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870b0e85290e3380011995.png"
                    >
                    </image> -->
                    <text>探索发现</text>
                </view>
            </view>
            <view class="history-list">
                <view class="item" v-for="(h, i) in recommendList" :key="i" @click="triggerSearch(h.name, i)">{{
                    h.name
                }}</view>
            </view>
        </view>
        <view class="history-search" v-if="hotSearchList.length > 0">
            <view class="history-search-title">今日热搜</view>
            <view>
                <HotSearch v-for="(h, i) in hotSearchList" :key="i" :hotSearchItem="h" :index="i + 1" @handleHotSearchItemClick="handleHotSearchItemClick"/>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import HotSearch from './HotSearch/HotSearch.vue';
import { getSearchHistory, getHotSearch } from '@/http/SearchHistory';
import { VIPModule } from '@/store/modules/vip';
import { getCoin } from '@/http/vip';

@Component({
    components: { HotSearch },
})
export default class Serve extends Vue {
    @Prop({ type: String, default: '0' })
    readonly search_type;

    public title: String = '搜索';
    public bgColor: String = 'transparent'; // 导航栏背景
    public isLoaded: boolean = false;
    public isDelete: boolean = false;
    public keyword: string = '';
    public titleDetele: string = '确定删除所有记录吗？';
    public searchHistoryList = [];
    public searchPointHistoryList = [];
    public recommendList = [];
    public fromPage: string = '';
    public hotSearchList = [
    //    {
    //     name: '邀好友 赚佣金',
    //     jump_url: '/pagesC/ambassador/login/index',
    //     jump_type: 1,
    //     hot_num: 5484758
    //    },
    //    {
    //     name: '积分当钱花',
    //     jump_url: '/pagesA/point/new_point',
    //     jump_type: 1,
    //     hot_num: 567575757
    //    },
    //    {
    //     name: '一元购好物',
    //     jump_url: '/pagesC/oneYuanFlashSale/oneYuanFlashSale',
    //     jump_type: 1,
    //     hot_num: 7575757
    //    },
    //    {
    //     name: '国补立减15%',
    //     jump_url: '/pagesB/nationalSubsidy/index',
    //     jump_type: 1,
    //     hot_num: 575757
    //    },
    //    {
    //     name: '注册产品',
    //     jump_url: '/product_list',
    //     jump_type: 4,
    //     hot_num: 7578568
    //    }
    ];

    public deleteShow: boolean = false;

    get searchStatus(): Boolean {
        return VIPModule.taskList.find((item) => item?.code === 'search_view_goods')?.completed;
    }

    async mounted() {
        this.getGoldCoin()
        const historyUserId = uni.getStorageSync('historyUserId');
        if (historyUserId != uni.getStorageSync('uInfo').user_id) {
            uni.removeStorageSync('searchHistoryList');
            uni.removeStorageSync('searchPointHistoryList');
        }
        const searchHistoryList = uni.getStorageSync('searchHistoryList');
        if (searchHistoryList && this.search_type == '1') {
            this.searchHistoryList = JSON.parse(searchHistoryList);
        }

        const searchPointHistoryList = uni.getStorageSync('searchPointHistoryList');
        if (searchPointHistoryList && this.search_type == '2') {
            this.searchPointHistoryList = JSON.parse(searchPointHistoryList);
        }
        const res: any = await getSearchHistory({ limit: 10 });
        this.recommendList = res.slice(0, 10);
        const hotSearchRes: any = await getHotSearch({ type: 9, page: 1, pageSize: 10 });
        this.hotSearchList = hotSearchRes.data.list.reverse();
        uni.$emit('updateHotSearchList', this.hotSearchList);

        // 获取页面参数
        this.getPageOptions();
    }

    public goldCoin: number = 0;
    getGoldCoin() {
        const params = {
            group_code: 'mall',
            type_code: 'dreame',
            code: 'search_view_goods',
            taskCode: 'mall/dreame/search_view_goods',
        };
        getCoin(params).then((res) => {
            this.goldCoin = res.gold;
        });
    }

    getPageOptions() {
        // 在组件中获取页面参数的方法
        try {
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            if (currentPage && (currentPage as any).options) {
                const options = (currentPage as any).options;
                console.log('options', options);
                this.fromPage = options.from || '';
            }
        } catch (error) {
            console.log('获取页面参数失败:', error);
        }
    }

    updateSearchHistory(keyword) {
        if (this.search_type == '1') {
            const index = this.searchHistoryList.indexOf(keyword);
            if (index > -1) {
                this.searchHistoryList.splice(index, 1);
            }
            this.searchHistoryList.unshift(keyword);
            if (this.searchHistoryList.length > Constants.SEARCH_HISTORY_LENGTH) {
                this.searchHistoryList.pop();
            }
            const info = uni.getStorageSync('uInfo');
            uni.setStorageSync('historyUserId', info.user_id);
            uni.setStorageSync('searchHistoryList', JSON.stringify(this.searchHistoryList));
        } else {
            const pointIndex = this.searchPointHistoryList.indexOf(keyword);
            if (pointIndex > -1) {
                this.searchPointHistoryList.splice(pointIndex, 1);
            }
            this.searchPointHistoryList.unshift(keyword);
            if (this.searchPointHistoryList.length > Constants.SEARCH_HISTORY_LENGTH) {
                this.searchPointHistoryList.pop();
            }
            const info = uni.getStorageSync('uInfo');
            uni.setStorageSync('historyUserId', info.user_id);
            uni.setStorageSync('searchPointHistoryList', JSON.stringify(this.searchPointHistoryList));
        }
    }

    deleteFinish() {
        this.deleteShow = false;
    }

    triggerSearch(val, index) {
        if (this.deleteShow) {
            const index = this.searchHistoryList.indexOf(val);
            if (index > -1) {
                this.searchHistoryList.splice(index, 1);
            }
            if (this.searchHistoryList.length == 0) {
                this.deleteShow = false;
            }
            return;
        }
        if (this.search_type == '1') {
            this.searchHistoryList.splice(index, 1);
        } else {
            this.searchPointHistoryList.splice(index, 1);
        }
        this.$emit('onSearch', val);
    }

    handleHotSearchItemClick(val, index) {
        console.log('%c val: ', 'font-size:16px;background-color: #42b983;color:#fff;', val.jump_url)
        // this.$emit('onSearch', val);
        // Utils.navigateTo(`${val.jump_url}`);
        switch (Number(val.jump_type)) {
            case 1:
                Utils.navigateTo(`${val.jump_url}`);
                break;
            case 4:
                Utils.newMessageChannel('PAGE', 'push', { path: val.jump_url });
                break;
        }
    }

    deleteAllData() {
        this.isDelete = true;
        Utils.reportEvent('clear_history_search', {});
    }

    clearHistory() {
        this.deleteShow = true;
    }

    confirmDetale() {
        if (this.search_type == '1') {
            this.searchHistoryList = [];
            this.isDelete = false;
            this.deleteShow = false;
            uni.removeStorageSync('searchHistoryList');
        } else {
            this.searchPointHistoryList = [];
            uni.removeStorageSync('searchPointHistoryList');
            this.isDelete = false;
            this.deleteShow = false;
        }
    }

    cancelDetele() {
        this.isDelete = false;
    }
}
</script>
<style lang="scss" scoped>
@font-face {
    /* 定义名称 */
    font-family: 'DouyinSansBold';
    /* 引入字体文件 */
    src: url('@/static/fonts/DouyinSansBold.otf') format('opentype');
}
.search-history-container {
    overflow-y: auto;
    height: calc(100vh - 138rpx);
    background: #FFFFFF;
    border-radius: 32rpx 32rpx 0px 0px;
    margin-top: 28rpx;
}
.search-history-new {
    margin-top: 34rpx !important;

    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .history_title {
            display: flex;
            align-items: center;
            padding-left: 32rpx;

            .history_icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 8rpx;
            }

            text {
                // font-size: 24rpx;
                // font-weight: 400;
                // color: #a6a6a6;
                font-family: MiSans;
                font-size: 28rpx;
                font-weight: 500;
                color: #404040;
            }
        }
    }

    .detele {
        width: 32rpx;
        height: 32rpx;
        margin-right: 28rpx;
    }

    .history-list {
        margin-top: 14rpx;
        margin-left: 32rpx;
        margin-right: 32rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .item {
            flex: 1;
            min-width: calc(50% - 20rpx);
            max-width: calc(50% - 20rpx);
            padding-left: 12rpx;
            height: 62rpx;
            margin-right: 0;
            margin-bottom: 0;
            padding: 12rpx 24rpx;
            box-sizing: border-box;
            background: #f6f6f6;
            color: #555555;
            border-radius: 12rpx;
            font-size: 24rpx;
            line-height: 38rpx;
            font-weight: 400;
            color: $theme-text-color;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .delete_item {
        .item {
            padding-right: 56rpx;
            position: relative;
            color: #555555;

            .delete_icon {
                position: absolute;
                right: 12rpx;
                top: 50%;
                transform: translateY(-50%);
                width: 32rpx;
                height: 32rpx;
                margin-left: 6rpx;
            }
        }
    }
}

.exploration_discovery {
    margin-top: 30rpx;
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .history_title {
            display: flex;
            align-items: center;
            padding-left: 32rpx;

            .history_icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 8rpx;
            }

            text {
                // font-size: 24rpx;
                // font-weight: 400;
                // color: #a6a6a6;
                font-family: MiSans;
                font-size: 28rpx;
                font-weight: 500;
                color: #404040;
            }
        }
    }

    .detele {
        width: 32rpx;
        height: 32rpx;
        margin-right: 28rpx;
    }

    .history-list {
        margin-top: 14rpx;
        margin-left: 32rpx;
        margin-right: 32rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .item {
            flex: 1;
            min-width: calc(50% - 24rpx);
            max-width: calc(50% - 24rpx);
            height: 32rpx;
            line-height: 32rpx;
            box-sizing: border-box;
            color: #555555;
            border-radius: 12rpx;
            font-size: 24rpx;
            font-family: MiSans;
            line-height: 38rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
        }
    }
}
.history-search {
    margin-left: 32rpx;
    margin-right: 24rpx;
    margin-top: 30rpx;
}
.history-search-title {
    margin-bottom: 16rpx;
    font-family: DouyinSansBold;
    font-size: 28rpx;
    color: #f53f3f;
}
.goldCoins {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 28rpx;
    width: 702rpx;
    height: 64rpx;
    border-radius: 8rpx;
    margin: 0 auto;
    margin-top: 35rpx;
    margin-bottom: 20rpx;
    /* Function功能色/Red/4 */
    background: rgba(253, 205, 197, 0.3);
    .goldCoinsIcon {
        width: 32rpx;
        height: 32rpx;
        opacity: 1;
    }
    .goldCoinsTextTitle {
        margin-left: 12rpx;
        width: 182rpx;
        opacity: 1;

        font-family: MiSans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: 32rpx;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #3d3d3d;
    }
    .goldCoinsTextNum {
        width: 49rpx;
        opacity: 1;
        font-family: MiSans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: 32rpx;

        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ff2300;
    }
}
</style>
