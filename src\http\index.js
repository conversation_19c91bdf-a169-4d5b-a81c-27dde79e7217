/* eslint-disable import/first */
let request = null;
// #ifdef H5
import h5 from './request_h5';
request = h5;
// #endif
// #ifdef MP-WEIXIN
import mp from './request_mp';
request = mp;
// #endif
export default request;
export const getRequest = (url) => {
    return new Promise((resolve, reject) => {
        uni.request({
            url,
            method: 'GET',
            success: (res) => {
                resolve(res.data);
            },
            fail: (err) => {
                reject(err);
            },
        });
    });
};
