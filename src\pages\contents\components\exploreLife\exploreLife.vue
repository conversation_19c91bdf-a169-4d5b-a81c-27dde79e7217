<template>
    <view class="container">
        <view class="my-waterfall">
            <view class="community_content">
                <Comment
                    :finished="finished"
                    :flowList="flowList"
                    ref="uWaterfall"
                    :topicId="currActiveId"
                    @share="handleShare"
                    @changeFollowStatus="changeFollowStatus"
                ></Comment>
            </view>
        </view>
        <custom-toast ref="customToast" />
    </view>
</template>

<script lang="ts">
import Comment from '../community/components/comment.vue';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getContentList, appShare, createShare } from '@/http/requestGo/community.ts';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';

@Component({
    components: { Comment },
})
export default class Contents extends Vue {
    @Prop({ type: Array })
    public banner: any;

    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    public relationStatus = 0;
    public creator: any = -1;

    public productList: any = [];
    public currActiveId: any = '';
    public sortList: any = ['发布时间 近-远', '热度 高-低'];
    public finished: Boolean = false; // 是否加载完成
    public page: number = 1;
    private showLoading: boolean = false; // 加载toast
    public sortId: any = 1;
    public flowList: any = [];
    public isShow: boolean = true;
    public content_id: String = '';
    public currentTab: string = 'tab-0'; // tab定位锚点
    public isLoading: Boolean = true;

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    async init(id) {
        this.currActiveId = id;
        await this._initPageData();
        await this._getListFactory();
    }

    async _getListFactory(id = this.currActiveId) {
        // 如果已经全部加载完成直接终止执行
        console.log(this.finished, '8888888');
        if (this.finished) return;
        await this.getWlist(id);
    }

    async handleShare(item) {
        // #ifdef H5
        await createShare({ content_id: item.content_id });
        const { url } = await appShare({
            page:
                item.type === 1
                    ? `/pagesC/contentDetails/contentDetails`
                    : `/pagesC/contentVideoDetails/contentVideoDetails`,
            content_id: item.content_id,
        });
        const params = {
            target: 'wechat',
            type: 'web',
            content: {
                url: url,
                share_image: item.cover_image + '?x-oss-process=image/resize,w_200',
                share_title: item.title,
            },
        };
        Utils.messageChannel('share', params);
        // #endif
    }

    // 获取内容列表
    async getWlist(id) {
        console.log(763637377);
        try {
            const topic_id: number = +this.currActiveId || id || 0;
            const page: number = this.page;
            page === 1 && (this.isLoading = true);
            const params = { topic_id, page };
            const res = await getContentList(params, this.showLoading);
            if (res.success) {
                const { list = [], total = 0 } = res.data;
                if (this.page >= Math.ceil(total / 10)) {
                    console.log(73773737);
                    this.finished = true;
                }
                if (page === 1) {
                    if (this.banner && this.banner.length) {
                        this.flowList = [{ type: 'banner', bannerList: this.banner }, ...list];
                    } else {
                        this.flowList = [...list];
                    }
                } else {
                    if (list.length > 0) {
                        this.flowList = this.flowList.concat(list);
                    }
                }
                this.page++;
                setTimeout(() => {
                    this.isLoading = false;
                }, 300);
            }
        } catch (e) {
            console.error('getWlist err=', e);
            this.isLoading = false;
        }
    }

    // 初始化页面的数据
    _initPageData() {
        this.finished = false;
        this.page = 1;
        this.showLoading = false;
    }

    clear() {
        this.flowList = [];
    }

    onReachBottom() {
        console.log('onReachBottom');
        this._getListFactory();
    }

    changeFollowStatus(item, status) {
        this.flowList.forEach((i) => {
            if (i.creator === item.creator) {
                i.follow_status = status;
            }
        });
    }

    viewDetail(item) {
        const { content_id = '', type } = item;
        this.content_id = item.content_id;
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this
                      .currActiveId}`,
        );
        // uni.navigateTo({
        //     url: type === 1
        //         ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
        //         : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this.currActiveId}`,
        // });
    }
}
</script>

<style lang="scss">
.container {
    height: auto;
    min-height: calc(100vh - 228rpx);
    // margin-top: 116rpx;
    // padding: 116rpx 0 0 0rpx;
    background-color: #fff;

    .my-waterfall {
        background-color: #fff;
        z-index: 1;
        padding: 8rpx 8rpx 0;
        margin-bottom: 30rpx;
    }

    .sort-content {
        display: inline-block;
        margin-left: 16rpx;
        margin-right: 22rpx;
        margin-bottom: 42rpx;
        font-size: 24rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
    }

    .sortActive {
        color: #1d1e20;
    }

    .sortIsActive {
        color: rgba(29, 30, 32, 0.4);
    }
    .community_content {
        min-height: calc(100vh - 795rpx);
        padding: 24rpx 30rpx 54rpx;
        background-color: #fff;
    }
}
</style>
