import { IGoodsAttrCnf } from '../modules/goods';
import { IPresale } from '@/http/interface/IPresale';

export enum OrderStauts {
    WaitPay = '0',
    Cancel = '100',
    WaitSend = '300',
    WaitGet = '400',
    Complete = '500',
    Back = '600',
}

/** 订单子类型，表示该订单的来源是哪个活动 */
export enum IUnionSource {
  /** 无来源 */
  EMPTY='',
  /** 一元购 */
  ONE_YUAN_PURCHASE='one_yuan_purchase',
  /** 追觅小店 */
  DREAME_SMALL_SHOP='dreame_small_shop',
  /** 团购 */
  GROUP_PURCHASE='group_purchase',
}

export interface IOrderGoods {
    status: string;
    check?: number;
    id: string;
    sku: string;
    name: string;
    cover_image: string;
    tids: Array<string>;
    num: string;
    oprice: string;
    price: string;
    uprice: string;
    attr_cnf: Array<IGoodsAttrCnf>;
    presale_info?: IPresale;
    gid: number;
    // 商品类型
    type: string;
    // 积分商品子类型
    subtype?: string;
}

export interface IOrderList {
    order_no: string;
    status: OrderStauts;
    oprice: string;
    price: string;
    goods: Array<IOrderGoods>;
    remain_time?: string;
    last_time?: string;
}

export interface ICoinData {
    coin_type: string | number; // 积分类型 0 展示可用积分 1使用积分 2不适用积分
    can_coin: number; // 可使用积分
    rate: number; // 比率
    tcoin: number; // 积分
    coin_price: string | number; // 积分金额
    coin?: number; // 使用了积分
}

export interface IOrderInfo {
    id: string;
    user_id: string;
    order_no: string;
    oprice: string;
    price: string;
    cprice: string;
    consume_price: string;
    subsidy_price?: string;
    fprice: string;
    coin: string;
    coin_price: string;
    status: string;
    ctime: string;
    note: string;
    goods: Array<IOrderGoods>;
    remain_time: number; // 倒计时
    can_refund: number; // 是否可以退款
    cr_type?: string; // 是否是系统取消
    will_coin?: string; // 将获得积分数
    real_price?: string; // 实付款
    card_type?: string; // 优惠券类型
    shopping_price?: string; // 购物金
    address: {
        id?: any;
        aid?: any;
        nick: string;
        phone: string;
        province: string;
        city: string;
        area: string;
        detail: string;
    };
    gift_card: {
        can_number: number | string;
        use_amount: number | string;
        gift_card_ids: any;
    };
    is_presale_order: number;
    is_trade_in: number;
    user_order_type: number;
    use_coin?: ICoinData;
    has_opay?: number;
    deposit_price?: string;
    acdeprice: string;
    tradeInOrder?: any;
    pay_type?: string;
}

export interface IOrderRtList {
    id: any;
    check?: number;
    rid: string;
    name: string;
    code: string;
}

interface IOrderBuyspec {
    id: string;
    gid: string;
    sku: string;
    price: string;
    av_ids: string;
    image: string;
    attr_cnf: Array<IGoodsAttrCnf>;
}

interface IOrderBuyList {
    id: string;
    sku: string;
    name: string;
    status: string;
    version: string;
    ctime: string;
    gid: string;
    price: string;
    fprice: string;
    cover_image: string;
    atype: string;
    limit_num: string;
    spec: IOrderBuyspec;
    tprice: string;
    num: string;
}

interface IOrderBuyGoods {
    gid: string;
}

export interface IOrderBuyInfo {
    coupon: any;
    list: Array<IOrderBuyList>;
    goods: Array<IOrderBuyGoods>;
    total: {
        tprice: string; // 总价
        fprice: string | number; // 运费
        coin: ICoinData; // 积分
        coupon: {
            coupon_id: string | number;
            cprice: string | number;
            card_type?: string;
        }; // 优惠券
        consume: {
            consume_id: string | number;
            consume_price: string | number;
            consume_type?: string;
        }; // 消费券
        gift_card: {
            can_number: string | number;
            use_amount: string | number;
            gift_card_ids: Array<string | number>;
        }; // 觅码
        shopping_price: {
            can_use_shopping_price: string;
            is_use_shopping_price: number;
            total_shopping_price: string;
        };
        consume_money: {
            can_use_consume_money: string;
            is_use_consume_money: number;
            total_consume_money: string;
        };
        price: string; // 实付款
        real_price?: string;
        acdeprice: string; // 以旧焕新
        subsidy_price?: string; // 国家补贴金额
        is_yjhx: number;
    };
    gcombines: Array<string | number>;
    cart_ids: Array<string>;
    usage_coupon_num: any;
    usage_consume_num: any;
}

export interface IOrderBuyParams {
    storekeeper?: string; // 店铺user_id
    one_yuan_seckill_id?: number; // 一元秒杀id
    gcombines: string; // 购买json数据【必要】
    cart_ids: string; // 购物车id
    coupon_id: string | number; // 优惠券id
    consume_id: string | number; // 消费券id
    coin: string | number; // 使用积分数
    aid: string | number; // 收货地址id
    note: string; // 订单备注
    live_mark?: String; // 直播备注
    ac_data: any;
    trade_in_remark: string;
    is_trade_in: string; // 是否以旧换新
    cate_code: string; // 以旧换新模型报价id
    item_brand: string; // 以旧换新品牌
    item_cates: string; // 以旧换新品类
    item_model: string; // 以旧换新型号
    in_express_time: string; // 以旧换新取件时间
    trade_in_aid: string; // 以旧换新地址
    gift_card_ids: any;
    is_internal_purchase: number; // 是否内购
    group_activity_id: number; // 拼团活动id
    group_purchase_id: number; // 拼团id
    pay_type?: number; // 支付方式
    type?: number; // 订单类型
    shopping_price?: string; // 购物金
    consume_money?: string; // 消费金
    relation_type?: string; // 订单下单活动类型
    union?: IUnionSource;
    relation_id?: number; // 订单下单活动id
    inviter_id?: number | string; // 邀请人id
}

export interface IWxPayParams {
    coin: string;
    timeStamp: string;
    nonceStr: string;
    package: string;
    signType: string;
    paySign: string;
    order_no?: string;
}

export interface IOrderRefundInfo {
    data?: {
        order_no: string;
        price?: string;
        deposit_price?: string;
        fprice: string;
        goods: Array<IOrderGoods>;
        user_order_type?: any;
    }; // 订单信息
    m_data?: {
        type: string; // 1：退款 2：退货退款
        name: string;
    }; // 退款内容
    r_data?: {
        rid: string | number; // 退款原因 ID
        name: string;
    };
}

export interface IOrderScouponInfo {
    id: string; // 选中的优惠券id
    cprice: string; // 优惠金额
    ids: Array<string>; // 可使用优惠券ID
}

export interface ICouponInfo {
    condition: string;
    discount: number;
    expire_time: string;
    id: string;
    images: string;
    market_id: string;
    name: string;
    num: number;
    resource_type: string;
    start_time: string;
    type: string;
    type_name: string;
    status: string;
    canUse: Boolean;
}

export interface IOrderCouponInfo {
    select: IOrderScouponInfo;
    list: Array<ICouponInfo>;
    consume: IOrderScouponInfo;
}
