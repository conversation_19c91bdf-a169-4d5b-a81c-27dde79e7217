<template>
    <view class="tasks">
        <!-- 列表整体盒子 -->
        <view class="task-list">
            <!-- 每块的盒子 -->
            <view class="task-item u-flex u-row-between u-col-center" v-for="(item, index) in taskList.slice(0, limit)" :key="index">
                <view style="display: flex; align-items: center">
                    <image :src="item.picture" style="width: 58rpx; height: 58rpx; margin-right: 24rpx" />
                    <view class="task-left">
                        <view class="name-box u-flex u-row-between u-col-center">
                            <!-- 左侧标题，可换行 -->
                            <text class="title-text">{{ item.name }}</text>

                            <!-- 右侧计数和提示图标 -->
                            <view class="count-box u-flex u-row-center u-col-center" v-if="item.eventLimitNum">
                                <text class="week">({{ item.currentPeriodEventNum }}/{{ item.eventLimitNum }})</text>
                                <image
                                    @click="showDetail(item)"
                                    style="height: 38rpx; width: 38rpx; margin-left: 12rpx"
                                    src="https://wpm-cdn.dreame.tech/images/202307/709391-1689660485571.png"
                                    alt=""
                                ></image>
                            </view>
                            <image
                                v-else
                                @click="showDetail(item)"
                                style="height: 38rpx; width: 38rpx; margin-left: 12rpx"
                                src="https://wpm-cdn.dreame.tech/images/202307/709391-1689660485571.png"
                                alt=""
                            ></image>
                        </view>
                        <view class="integra" v-if="item.gold > 0">
                            <view class="integral">
                                <image
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a618cb03547220012265.png"
                                />
                                <text>+{{ item.gold }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view> </view>
                <view class="task-btn">
                    <view v-if="item.haveUnCollect" class="unCollect-btn" @click="getcollectTaskGoldHandle(item)"> 领取 </view>
                    <view v-else :class="{ 'task-btn-completed': item.completed }" @click="doTask(item)">
                        {{ item.completed ? '已完成' : '去完成' }}
                    </view>
                </view>
            </view>
        </view>
        <CustomModal
            :show="isShow"
            width="616rpx"
            contentStyle="color:#404040;font-weight:400;font-size: 32rpx;text-align: center;"
            confirmStyle="width: 524rpx; color: #8C6533; margin-bottom: -20rpx;"
            :content="taskDetail"
            confirmText="知道了"
            @confirm="isShow = false"
        >
        </CustomModal>
        <Live :isShow="false" ref="live"></Live>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { VIPModule } from '@/store/modules/vip';
import Live from '@/components/Live/Live.vue';
import Utils from '@/common/Utils';
import { getcollectTaskGold } from '@/http/coin';

@Component({
    components: {
        Live,
    },
})
export default class MemberTasks extends Vue {
    $refs!: {
        live: Live;
    };

    @Prop({ type: Boolean, default: false })
    readonly isMore!: Boolean;

    @Prop({ type: Number, default: 99999999 })
    readonly limit;

    public isShow: Boolean = false; // 规则弹窗
    public taskDetail: String = ''; // 规则内容

    get taskList(): Array<any> {
        // 移除数量限制，全部展示任务
        return JSON.parse(JSON.stringify(VIPModule.taskList));
    }

    showDetail(task) {
        this.taskDetail = task.descri;
        this.isShow = true;
    }

    async getcollectTaskGoldHandle(item) {
        await getcollectTaskGold({ code: item.code, group_code: item.groupCode, type_code: item.typeCode });
        this.$emit('refresh'); // 通知父组件刷新
        VIPModule.getCoinTaskList();
    }

    doTask(item) {
        if (item.completed) return;
        switch (item.code) {
            case 'view_goods':
                this.goToShop();
                break;
            case 'goods_add_cart':
                this.goToShop();
                break;
            case 'buy_new_products':
                this.goToShop();
                break;
            case 'buy_appoint_goods':
                this.goToShop();
                break;
            case 'buy_main_machine':
                this.goToShop();
                break;
            case 'buy_parts':
                this.goToShop(20);
                break;
            case 'invite_buy':
                Utils.navigateTo('/pagesA/recommend/recommend');
                break;
            case 'reg_sn':
                Utils.navigateTo('/pagesC/serve/serve');
                break;
            case 'evaluate_main_machine':
                Utils.navigateTo('/pagesB/evaluate/myEvaluate');
                break;
            case 'evaluate_parts':
                Utils.navigateTo('/pagesB/evaluate/myEvaluate');
                break;
            case 'share_goods':
                this.goToShop();
                break;
            case 'invite_reg':
                Utils.navigateTo('/pagesA/recommend/recommend');
                break;
            case 'follow_official_account':
                Utils.navigateTo(
                    `/pages/webView/webView?web_url=${encodeURIComponent(
                        'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=**********&idx=1&sn=69a3db2d520c0151d8d6e5a9a82f8ef6&chksm=ce51b09ef92639880531af2392c71e94e559b96dfecf1ee98812a9319654e002224ab6ab2060#rd',
                    )}`,
                );
                break;
            case 'add_wechat':
                Utils.navigateTo(`/pagesA/memberShip/memberShip?typeCode=exclusive`);
                break;
            case 'f_pair': // 跳转设备页
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'start_clean': // 跳转设备页
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'login': // app登录
                break;
            case 'clean_area': // 清扫面积
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'f_fill': // 完善个人信息
                // #ifdef H5
                Utils.messageChannel('navigation', { type: 'mine', path: 'mine/accountSetting' });
                // #endif
                // #ifdef MP-WEIXIN
                Utils.navigateTo('/pagesA/userInfo/userInfo');
                // #endif
                break;
            case 'signin': // 签到
                Utils.navigateTo('/pagesA/daliyCheckIn/daliyCheckIn');
                break;
            case 'watch_live': // 直播
                this.$refs.live.getLiveList(true);
                break;
            case 'f_video': // 使用视频管家
                Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' });
                break;
            case 'accessory_usage': // 查看耗材计时
                Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' });
                break;
            default:
                break;
        }
    }

    goToShop(tid?: any) {
        // #ifdef MP-WEIXIN
        uni.switchTab({ url: `/pages/shop/shop${tid ? `?tid=${tid}` : ''}` });
        // #endif
        // #ifndef MP-WEIXIN
        Utils.navigateTo(`/pages/shop/shop${tid ? `?tid=${tid}` : ''}`);
        // #endif
    }

    created() {
        VIPModule.getCoinTaskList();
    }

    // 页面显示时(包括用户返回当前页面或切换 Tab) 重新拉取任务列表，确保状态实时刷新
    onPageShow() {
        VIPModule.getCoinTaskList();
    }
}
</script>

<style lang="scss">
$text-color-more: #7d7d7d;

.tasks {
    width: auto;
    /* 统一与其他白色卡片风格 */
    background-color: $fill-color-bg-white;
    margin: 32rpx 0rpx;
    .title-container {
        width: 100%;
        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: $text-color-primary;
        }

        .more {
            font-size: 28rpx;
            color: $text-color-more;
            line-height: 38rpx;

            .arrow {
                height: 38rpx;
                width: 38rpx;
            }
        }
    }

    .task-list {
        overflow-y: scroll;
        margin-top: 24rpx;
        max-height: calc(100vh - 596rpx);
        /* 外层已是白色卡片，这里去掉背景和圆角以避免双层 */
        border-radius: 0;
        background-color: transparent;
        padding-bottom: 38rpx;

        .task-item {
            height: 118rpx;
            padding: 38rpx 0 0 0;

            .task-left {
                /* 名称整体盒子：左右结构 */
                .name-box {
                    font-size: 28rpx;
                    font-weight: 600;
                    color: $text-color-primary;
                    line-height: 38rpx;
                    width: 100%;

                    .title-text {
                        max-width: 9em; /* 一行容纳约8个汉字 */
                        word-break: break-all;
                        /* 避免被拉伸 */
                        flex: none;
                    }

                    .count-box {
                        flex-shrink: 0;
                        white-space: nowrap;

                        .week {
                            margin-left: 0;
                        }
                    }
                }

                .integra {
                    display: flex;
                    align-items: center;
                    margin-top: 8rpx;

                    .integral {
                        @include flex(row, center, center, none);

                        & + .integral {
                            margin-left: 24rpx;
                        }

                        color: $text-color-more;
                        font-size: 24rpx;

                        image {
                            width: 28rpx;
                            height: 28rpx;
                            margin-right: 8rpx;
                        }
                    }

                    .separate {
                        display: inline-block;
                        width: 32rpx;
                        color: #d8d8d8;
                        font-size: 20rpx;
                        text-align: center;
                    }
                }
            }

            .task-btn {
                width: 132rpx;
                height: 60rpx;
                line-height: 60rpx;
                border-radius: 96rpx;
                color: white;
                font-weight: 500;
                font-size: 28rpx;
                text-align: center;
                background: linear-gradient(116deg, #fee2a0 7%, #fdc847 52%, #fdbe22 95%);
                .task-btn-completed {
                    background: linear-gradient(115deg, #cecece 7%, #acacac 89%);
                    border-radius: 96rpx;
                }
                .unCollect-btn {
                    background: linear-gradient(116deg, #ff8d50 7%, #f9580d 52%, #ff2e20 95%);
                    border-radius: 96rpx;
                }
            }
        }
    }
}
</style>
