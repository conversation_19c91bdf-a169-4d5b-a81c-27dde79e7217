<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <title><%= htmlWebpackPlugin.options.title %></title>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px';
            });
            var coverSupport =
                'CSS' in window &&
                typeof CSS.supports === 'function' &&
                (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
            document.write(
                '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
                    (coverSupport ? ', viewport-fit=cover' : '') +
                    '" />',
            ); // 埋点
            (function (w, d, s, q) {
                w[q] = w[q] || [];
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s);
                j.async = true;
                j.id = 'beacon-aplus';
                j.src = 'https://o.alicdn.com/QTSDK/quicktracking-sdk/qt_web.cjs.js';
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'aplus_queue');
            //集成应用的appKey
            aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['appKey', 'b0no53b9c1s3bn80ojw2hngn'] });

            //如果是私有云部署还需要在上面那段JS后面紧接着添加日志域名埋点b0no53b9c1s3bn80ojw2hngn
            //通常私有云日志服务端域名类似于：quickaplus-web-api.xxx.com.cn, 具体域名要找交付同学要
            aplus_queue.push({
                action: 'aplus.setMetaInfo',
                arguments: ['trackDomain', 'https://quickaplus-he-api-cn-shanghai.aliyuncs.com'],
            });
            aplus_queue.push({
                // 埋点日志开关
                action: 'aplus.setMetaInfo',
                arguments: ['DEBUG', true],
            });
            aplus_queue.push({
                // 全埋点开关
                action: 'aplus.setMetaInfo',
                arguments: ['aplus-autotrack-enabled', true],
            });
            aplus_queue.push({
                // 全局空间埋点测试
                action: 'aplus.setMetaInfo',
                arguments: [
                    'aplus-autotrack-config',
                    {
                        collect_tags: {
                            li: true, //采集<li/>控件
                            img: true, //采集<img/>控件
                            svg: true, //采集<svg/>控件
                            div: true, //采集<div/>控件
                            span: true, //采集<span/>控件
                            path: true, //采集<path/>控件
                            p: true, //采集<p/>控件
                        },
                        collect_input: true, //采集input框输入的内容，默认不采集,
                        element_capture_enable: true, //全埋点控件点击支持事件捕获模式，默认为冒泡模式，值为false
                    },
                ],
            });
        </script>
        <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
    </head>

    <body>
        <noscript>
            <strong>Please enable JavaScript to continue.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
