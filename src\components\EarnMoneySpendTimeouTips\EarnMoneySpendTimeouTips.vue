<template>
  <view class="earn-money-spend-timeout-tips" :style="{ ...position }" v-if="interfaceRequest && !isCompleted">
      <view>
            <view class="earn-money-spend-timeout-tips-rounded" :class="isShow ? 'has-border' : 'border'">
                <svg v-if="isShow" class="border-svg" viewBox="0 0 118 118">
                    <circle
                        cx="59"
                        cy="59"
                        r="56"
                        fill="none"
                        stroke="#ff5020"
                        stroke-width="6"
                        stroke-linecap="round"
                        :stroke-dasharray="circumference"
                        :stroke-dashoffset="strokeDashoffset"
                        transform="rotate(-90 59 59)"
                    />
                </svg>
                <image class="gold-icon" :src="imgMap[fromPage] || imgMap.money" />
            </view>
            <view class="earn-money-spend-timeout-tips-text">
                <view class="performing" v-if="isShow">
                    <view class="performing-text"> 逛{{ Math.ceil(time / 1000) }}秒得 </view>
                    <view class="performing-text">
                        {{ fromPage == 'goldCoins' ? goldCoin + '金币' : money + '元' }}
                    </view>
                </view>
                <view class="complete" v-else @click="goToEarnMoney">
                    <image
                        class="complete-icon"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689bf81369b024330015941.png"
                    />
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getTaskInfo, doGoldTask } from '@/http/vip';
import { viewVideo15s } from '@/http/requestGo/community';
import { VIPModule } from '@/store/modules/vip';

@Component
export default class EarnMoneySpendTimeoutTips extends Vue {
  @Prop({ type: Object, default: () => ({ top: '200rpx' }) }) position: object;
  @Prop({
      type: String,
      default: '',
  })
  readonly taskCode;

  @Prop({
        type: String,
        default: '',
    })
    readonly fromPage;

    @Prop({
        type: Number,
        default: 30000,
    })
    readonly watchTime;

    public isShow: boolean = false;
    public timer: any = null;
    public time: number = 15000;
    public initialTime: number = 15000;
    public isCompleted: boolean = true;
    public interfaceRequest: boolean = false;
    public taskInfoMap: any = {
        viewGoodsMoney: 'mall/dreame/view_goods_money', // 逛商城15秒
        viewGoodsOneYuanMoney: 'mall/dreame/view_goods_one_yuan_money', // 逛1元购15秒
        viewGoodsGroup: 'mall/dreame/view_goods_group', // 逛拼团活动15秒
        viewGoodsRichPlanMoney: 'mall/dreame/view_goods_rich_plan_money', // 逛暴富计划15秒
        viewGoodsThreeBuy: 'mall/dreame/view_goods_three_buy', // 逛3折购15秒
        viewVideo: 'friend/user_event_app/watch_video', // 观看视频15秒
        richPlan: 'mall/dreame/view_goods_rich_plan', // 逛暴富计划15秒来自赚金币
        oneYuanPurchase: 'mall/dreame/view_goods_one_yuan', // 逛一元购15秒来自赚金币
        viewGoodsGold: 'mall/dreame/view_goods_gold', // 逛商城60秒来自赚金币
        viewGoodsGroupGold: 'mall/dreame/view_goods_group_gold', // 逛拼团好物来自赚金币
        searchViewGoods: 'mall/dreame/search_view_goods', // 搜商品赚金币来自赚金币
        pointsShopping: 'mall/dreame/view_goods_points_shopping', // 积分购物
        viewGoodsThreeGold: 'mall/dreame/view_goods_three_gold', // 三折购
    };

    public imgMap: any = {
        goldCoins:
            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fddf20fae10640080168.png',
        money: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c39d8b75be7510010581.png',
    };

  public money: string = '0.00';
  public goldCoin: string = '0.00';

  get uid(): any {
      return VIPModule.basicInfo.uid;
  }

  get circumference() {
      return 2 * Math.PI * 56; // 圆的周长 = 2πr
  }

  get strokeDashoffset() {
      if (!this.isShow) {
          return this.circumference;
      }
      const progress = (this.initialTime - this.time) / this.initialTime;
      return this.circumference * (1 - progress);
  }

  get borderStyle() {
      if (!this.isShow) {
          return {};
      }
      const progress = (this.initialTime - this.time) / this.initialTime;
      const angle = progress * 360;
      return {
          '--border-angle': `${angle}deg`,
      };
  }

  get computedTime(): number {
        return this.fromPage === 'goldCoins' ? this.watchTime || 15000 : 15000;
    }

    @Watch('time', { immediate: true })
    timeChange(newVal: number, oldVal: number) {
        if (this.time <= 0) {
            this.isShow = false;
            this.clearTimer();
            this.setViewGoodsTaskStatus();
        }
    }

  @Watch('isShow', {})
  isShowChange(newVal: number, oldVal: number) {
      if (this.isShow == false) {
          setTimeout(() => {
              this.timer = null;
          }, 17);
      }
  }

  created() {
        this.time = this.computedTime;
        this.initialTime = this.computedTime;
    }

    async getViewGoodsTaskStatus() {
        this.clearTimer();
        this.interfaceRequest = false;
        getTaskInfo({ taskCode: this.taskInfoMap[this.taskCode] }).then((res) => {
            console.log('res', typeof res.completed);
            this.interfaceRequest = true;
            if (!res.completed) {
                this.isShow = true;
                this.time = this.initialTime; // 重置时间
                this.isCompleted = false;
                this.money = (res.money / 100).toFixed(2);
                this.goldCoin = res.gold;
                // 使用基于实际时间的倒计时
                const startTime = Date.now();
                this.timer = setInterval(() => {
                    const elapsed = Date.now() - startTime;
                    this.time = Math.max(0, this.initialTime - elapsed);

                  if (this.time <= 0) {
                      this.clearTimer();
                      this.isShow = false;
                  }
              }, 100); // 每100ms更新一次，减少性能消耗
          } else {
              this.isShow = false;
              this.isCompleted = true;
          }
      });
  }

  async setViewGoodsTaskStatus() {
      if (this.taskCode == 'viewVideo') {
          await viewVideo15s({ type: 5, uid: this.uid });
      } else {
          await doGoldTask({ type: this.taskCode });
      }
      this.clearTimer();
  }

  clearTimer() {
      this.timer && clearInterval(this.timer);
      this.timer = null;
      this.isShow = false;
  }

  goToEarnMoney() {
      console.log('goToEarnMoney');
      if (this.fromPage == 'goldCoins') {
            uni.redirectTo({
                url: '/pagesC/goldCoins/goldCoins',
            });
        } else {
            uni.redirectTo({
                url: '/pagesC/earnMoneySpend/earnMoneySpend',
            });
        }
    }

  clearInterfaceRequest() {
      this.interfaceRequest = false;
  }

  onHide() {
      this.clearTimer();
      this.interfaceRequest = false;
  }
}
</script>
<style lang="scss">
.earn-money-spend-timeout-tips {
  width: 118rpx;
  height: 152rpx;
  position: fixed;
  right: 16rpx;
  z-index: 4;
  .earn-money-spend-timeout-tips-rounded {
      width: 118rpx;
      height: 118rpx;
      border-radius: 50%;
      background-color: rgba(255, 80, 32, 0.15);
      position: relative;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      transform: translateZ(0);
      backface-visibility: hidden;

      .border-svg {
          position: absolute;
          top: 0;
          left: 0;
          width: 118rpx;
          height: 118rpx;
          z-index: -1;
      }

      .gold-icon {
          width: 88rpx;
          height: 88rpx;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
      }
  }
  .earn-money-spend-timeout-tips-text {
      width: 100%;
      height: 62rpx;
      position: absolute;
      bottom: 0;
      left: 0;
      border-radius: 16rpx;
      background: linear-gradient(359deg, #ffa218 3%, #ff6200 36%, #ff4242 71%);
      opacity: 0.9;
      color: #fff;
      .performing {
          width: 100%;
          height: 100%;
          text-align: center;
          font-size: 22rpx;
          .performing-text {
              line-height: 1;
              margin-top: 8rpx;
          }
      }
      .complete {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .complete-icon {
              width: 84rpx;
              height: 28rpx;
          }
      }
  }
  .border {
      box-shadow: 0 0 0 7rpx #ff5020 inset;
  }
}
</style>
