<template>
    <view class="coin-points">
        <!-- <view class="coin-points-item" v-if="coin > 0">
            <image style="width: 48rpx; height: 48rpx;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870cea3038ed0150012285.png"></image>
            <view class="coin-points-item-text">
                <view class="coin-points-item-text-value">{{ coin }}</view>
                <view class="coin-points-item-text-title">金币</view>
            </view>
        </view> -->
        <view class="coin-points-item" @click="handlePointsClick">
            <image class="point_icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870cebb5e9c53880022036.png"></image>
            <view class="coin-points-item-text">
                <view class="coin-points-item-text-value">{{ points }}</view>
                <!-- <view class="coin-points-item-text-title">积分</view> -->
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component
export default class CoinPoints extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    handlePointsClick() {
        Utils.navigateTo('/pagesA/point/new_point');
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }
}
</script>
<style lang="scss" scoped>
@keyframes flip {
  0% {
    transform: rotateY(0deg); /* 开始状态，不旋转 */
  }
  50% {
    transform: rotateY(180deg); /* 中间状态，旋转180度 */
  }
  100% {
    transform: rotateY(360deg); /* 结束状态，回到原始状态（或再次旋转180度） */
  }
}
.coin-points {
    display: flex;
    align-items: center;
    gap: 30rpx;
    height: 114rpx;
    .point_icon{
        width: 48rpx;
        height: 48rpx;
       // animation: flip 2s infinite; /* 动画名称、持续时间、无限循环 */
    }
}
.coin-points-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.coin-points-item-text{
    margin-left: 8rpx;
    text-align: center;
}
.coin-points-item-text-value{
   font-family: MiSans;
   font-size: 24rpx;
   font-weight: 500;
   letter-spacing: normal;
   color: #D5BA89;
   line-height: 1;
}
.coin-points-item-text-title{
   font-family: MiSans;
   font-size: 20rpx;
   letter-spacing: normal;
   color: #777777;;
   line-height: 1;
}
</style>
