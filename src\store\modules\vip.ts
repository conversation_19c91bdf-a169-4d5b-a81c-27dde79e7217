import { Module, VuexModule, Mutation, getModule, Action } from 'vuex-module-decorators';
import store from '@/store';
import { getTaskList, getBasicInfo, getEmployeeTaskList, smileInfo, getNoUserIdTaskList } from '@/http/vip';
interface Task {
    awardType: Number;
    completed: Boolean;
    currentPeriodEventNum: Number;
    descri: string;
    eventLimitNum: Number;
    score: Number;
    grow: Number;
    name: string;
    period: Number;
    point: Number;
    pointCollectType: String;
    timePeriodUnit: String;
    timePeriodValue: Number;
    code?: string; // 任务代码，可选属性
}

export interface CurrentLevelInfo {
    name: String;
    level: String;
    maxGrow: Number;
    minGrow: Number;
    order: Number;
    picture: String;
}

export interface CurrentStatusInfo {
    continueSignDays: Number;
    todaySignedIn: Boolean;
}

export interface MineBasicInfo {
    level: CurrentLevelInfo;
    benefitRuleList: Array<any>;
    showLevelUpgradePrompt: Boolean;
    is_show_birth_card: Boolean;
    totalGrow: Number;
    totalPoints: Number;
    unCollectPoint: Number;
    uid: String;
    signInStatusVO: CurrentStatusInfo;
}

export interface SmileVipInfo {
    avg_score_3_months: number;
    employee_status: number;
    employee_status_name: String;
    is_blacklist: number;
    is_employee: number;
    level: number;
    level_name: String;
    score: number;
    uid: String;
    employee_uid?: String;
    is_tip: number;
    tips: String;
    task_tips: String;
}
@Module({ name: 'VIPModule', namespaced: true, dynamic: true, store })
export default class VIPStore extends VuexModule {
    public taskList: Array<Task> = [];
    public coinTaskList: Array<Task> = [];

    public basicInfo: MineBasicInfo = {
        level: {
            level: 'v1',
            maxGrow: 0,
            minGrow: 0,
            name: '',
            order: 1,
            picture: '',
        },
        signInStatusVO: {
            continueSignDays: 0,
            todaySignedIn: false,
        },
        benefitRuleList: [],
        showLevelUpgradePrompt: false,
        is_show_birth_card: false,
        totalGrow: 0,
        totalPoints: 0,
        unCollectPoint: 0,
        uid: '',
    };

    public smileInfo: SmileVipInfo = {
        avg_score_3_months: 0,
        employee_status: 0,
        employee_status_name: '',
        is_blacklist: 0,
        is_employee: 0,
        level: 0,
        level_name: '',
        score: 0,
        uid: '',
        is_tip: 0,
        tips: '',
        task_tips: '',
    };

    public employTaskList: Array<Task> = [];
    public moneyTaskList: Array<Task> = [];

    @Mutation
    public clearVip(): void {
        this.taskList = [];
        this.employTaskList = [];
        this.moneyTaskList = [];
        this.smileInfo = {
            avg_score_3_months: 0,
            employee_status: 0,
            employee_status_name: '',
            is_blacklist: 0,
            is_employee: 0,
            level: 0,
            level_name: '',
            score: 0,
            uid: '',
            is_tip: 0,
            tips: '',
            task_tips: '',
        };
        this.basicInfo = {
            level: {
                level: 'v1',
                maxGrow: 0,
                minGrow: 0,
                name: '',
                order: 1,
                picture: '',
            },
            signInStatusVO: {
                continueSignDays: 0,
                todaySignedIn: false,
            },
            benefitRuleList: [],
            showLevelUpgradePrompt: false,
            is_show_birth_card: false,
            totalGrow: 0,
            totalPoints: 0,
            unCollectPoint: 0,
            uid: '',
        }
    }

    @Mutation
    public setTaskList(list: Array<Task>): void {
        this.taskList = list;
    }

    @Mutation
    public setMoneyTaskList(list: Array<Task>): void {
        this.moneyTaskList = list;
    }

    @Mutation
    public setEmployTaskList(list: Array<Task>): void {
        this.employTaskList = list;
    }

    @Mutation
    public setSmileInfo(info: SmileVipInfo): void {
        this.smileInfo = info;
    }

    @Mutation
    public setLevelUpgradePrompt(bool: Boolean): void {
        this.basicInfo.showLevelUpgradePrompt = bool;
    }

    @Mutation
    public setHappyBirthdayPrompt(bool: Boolean): void {
        this.basicInfo.is_show_birth_card = bool;
    }

    @Mutation
    public setBasicInfo(basicInfo: any): void {
        const info = {
            benefitRuleList: basicInfo.currentLevelInfo.benefitRuleList,
            level: basicInfo.currentLevelInfo.level,
            showLevelUpgradePrompt: basicInfo.showLevelUpgradePrompt,
            is_show_birth_card: basicInfo.is_show_birth_card,
            totalGrow: basicInfo.totalGrow,
            totalPoints: basicInfo.totalPoints,
            unCollectPoint: basicInfo.unCollectPoint,
            uid: basicInfo.uid,
            signInStatusVO: basicInfo.signInStatusVO,
        };
        this.basicInfo = info;
    }

    @Action({ rawError: true })
    public getTaskList() {
        return new Promise((resolve) => {
            getTaskList({}).then((res) => {
                if (res) {
                    this.context.commit('setTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getNoUserIdTaskList() {
        return new Promise((resolve) => {
            getNoUserIdTaskList({}).then((res) => {
                if (res) {
                    this.context.commit('setTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getCoinTaskList() {
        return new Promise((resolve) => {
            getTaskList({ type: 3 }).then((res) => {
                console.log('%c res111: ', 'font-size:16px;background-color: #4b4b4b;color:#fff;', res)
                if (res) {
                    this.context.commit('setTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public noUserIdGetCoinTaskList() {
        return new Promise((resolve) => {
            getNoUserIdTaskList({ type: 3 }).then((res) => {
                console.log('%c res222: ', 'font-size:16px;background-color: #F5CE50;color:#fff;', res)
                if (res) {
                    this.context.commit('setTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getMoneyTaskList() {
        return new Promise((resolve) => {
            getTaskList({ type: 4 }).then((res) => {
                console.log('%c res333: ', 'font-size:16px;background-color: #4b4b4b;color:#fff;', res)
                if (res) {
                    this.context.commit('setMoneyTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public noUserIdMoneyTaskList() {
        return new Promise((resolve) => {
            getNoUserIdTaskList({ type: 4 }).then((res) => {
                if (res) {
                    this.context.commit('setMoneyTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getEmployTaskList() {
        return new Promise((resolve) => {
            getEmployeeTaskList({}).then((res) => {
                if (res) {
                    this.context.commit('setEmployTaskList', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getBasicInfo() {
        return new Promise((resolve) => {
            getBasicInfo({ single: 1 }).then((res) => {
                if (res) {
                    this.context.commit('setBasicInfo', res);
                }
                resolve(res);
            });
        });
    }

    @Action({ rawError: true })
    public getSmileInfo() {
        return new Promise((resolve) => {
            smileInfo().then((res) => {
                if (res) {
                    this.context.commit('setSmileInfo', res);
                }
                resolve(res);
            });
        });
    }
}

export const VIPModule = getModule(VIPStore);
