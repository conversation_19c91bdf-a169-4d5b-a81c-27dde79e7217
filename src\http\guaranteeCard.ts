import http from './index';
import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
let env = process.env.NODE_ENV;
let BASE_URL = process.env.VUE_APP_BASE_URL;

/*
 *以下代码仅在开发阶段执行
 *发行时需要删除此部分代码
 */

// start
if (process.env.NODE_ENV === 'development') {
    try {
        const node_env = uni.getStorageSync('node_env');
        if (node_env) {
            env = node_env.env;
            BASE_URL = node_env.BASE_URL;
        } else {
            uni.setStorage({
                key: 'node_env',
                data: {
                    env,
                    BASE_URL,
                },
            });
        }
    } catch (e) {
        console.error(`当前环境是${process.env.NODE_ENV}`);
    }
} else {
    // 生产环境 避免出现环境缓存
    try {
        uni.removeStorageSync('node_env');
    } catch (e) {
        console.error('没有环境缓存');
    }
}
// end

// 激活电子保修卡
export const activeGuaranteeCard = (params) => http.post('main/warranty-apply/active', params);

// 电子保修卡- 平台列表
export const platformList = () => http.post('main/warranty-apply/platform-list');

// 申请电子保修卡- 相关说明
export const warrantyApplyRules = () => http.post('main/warranty-apply/rules');

// 申请电子保修卡
export const warrantyApplyAdd = (params) => http.post('main/warranty-apply/add', params);

// 电子保修卡-隐藏申请信息
export const warrantyApplyHide = (params) => http.post('main/warranty-apply/hide', params);

function getSign(obj, security_key) {
    // @ts-ignore
    obj.sign_time = parseInt(new Date().getTime() / 1000);
    const newObj = {
        ...obj,
    };
    newObj.security_key = security_key;
    let sortArr: any = [];
    const keysSorted = Object.keys(newObj).sort(); // 排序名
    for (let i = 0; i < keysSorted.length; i++) {
        sortArr.push(keysSorted[i] + '=' + newObj[keysSorted[i]]);
    }
    sortArr = sortArr.join('&');
    return md5Libs.md5(sortArr);
}

// 申请电子保修卡 - 上传购买凭证

export const warrantyApplyUpload = (imgPath, params = {}) => {
    return new Promise((resolve, reject) => {
        const BASE_DATA = {
            user_id: UserModule.user_id || 0,
            sessid: UserModule.sessid || '',
            api: AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268',
            version: AppModule.version,
            ...params,
        };
        const security_key = AppModule.platform == 'ios' ? 'b_m3h^jWfA9jp' : 'b_m3I6PiPgYX#';
        // @ts-ignore
        BASE_DATA.sign = getSign(BASE_DATA, security_key);
        // fetch(`${BASE_URL}main/warranty-apply/upload`)
        uni.uploadFile({
            url: `${BASE_URL}main/warranty-apply/upload`,
            filePath: imgPath,
            name: 'file',
            formData: BASE_DATA,
            success: (res) => {
                const result = JSON.parse(res.data);
                if (result.iRet == 1) {
                    resolve(result.data);
                } else if (result.iRet == 2) {
                    resolve(result);
                } else if (result.iRet == -1) {
                    if (result.sMsg) {
                        uni.showToast({
                            title: result.sMsg,
                            icon: 'none',
                            duration: 2300,
                        });
                    }
                    reject(result);
                } else if (result.iRet == -100) {
                    // 清除登录信息重新登录
                    UserModule.setSessId('');
                    UserModule.setUserId('');
                    UserModule.setIsLogin(false);
                    UserModule.asyncWxLogin();
                    reject(res);
                } else {
                    reject(res);
                }
            },
            fail: (err) => {
                // @ts-ignore
                if (err.sMsg) {
                    uni.showToast({
                        title: '上传图片失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
                reject(err);
            },
            complete: () => {},
        });
    });
};
