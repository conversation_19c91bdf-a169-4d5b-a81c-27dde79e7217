<template>
    <view class="personal-code_container">
        <CustomBar title="" :background="'transparent'" :bottomHeight="76" :isShowBack="true"></CustomBar>
        <view class="personal-code_content">
            <!-- 二维码 -->
            <view class="personal-code_content_qrcode">
                <canvas id="qrcodeCanvas" canvas-id="qrcodeCanvas" style="width: 100%; height: 100%;"></canvas>
            </view>
            <!-- 用户信息 -->
            <view class="personal-code_content_userInfo">
                <view class="personal-code_content_userInfo_name">
                    {{ userInfo.nick }}
                </view>
                <view class="personal-code_content_userInfo_uid">
                    追觅号：{{ Uid }}
                </view>
            </view>
        </view>
        <view class="personal-code_operation">
            <view class="personal-code_operation_item" @click="goScan">
                <view class="personal-code_operation_item_icon">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689316afe10c69220093257.png" />
                </view>
                <view class="personal-code_operation_item_text">
                    扫一扫
                </view>
            </view>
            <view class="personal-code_operation_item" @click="saveQrcode">
                <view class="personal-code_operation_item_icon">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689316f50cae40520014866.png" />
                </view>
                <view class="personal-code_operation_item_text">
                    保存
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';
import { VIPModule } from '@/store/modules/vip';
import * as QRCode from '@/wxcomponents/painter/lib/qrcode';
import Utils from '@/common/Utils';

@Component({})
export default class PersonalCode extends Vue {
    get userInfo() {
        return UserModule.userInfo;
    }

    get Uid() {
        return VIPModule.basicInfo.uid;
    }

    stringToBase64(str: string) {
        return btoa(str);
    }

    public qrcodeConfig = {
        url: '', // 二维码链接
        canvasWidth: 390,
        canvasHeight: 844,
        qrSize: 106,
        bgColor: '#ffffff',
        fgColor: '#2D0C00',
        ecLevel: 2,
    };

    public ctx: any = null;
    public ossUrl: string = '';
    public baseUrl: string = '';

    onLoad() {
        UserModule.getUserInfo();
        VIPModule.getBasicInfo()
    }

    onShow() {
        UserModule.getUserInfo();
        VIPModule.getBasicInfo();
    }

    // 保存二维码
    async saveQrcode() {
        this.ctx.draw(true, async () => {
            const filePath = await this.getCanvasLocalPath(this.qrcodeConfig.canvasWidth, this.qrcodeConfig.canvasHeight);
            const ossUrl = await this.uploadCanvasToOss(filePath);
            Utils.saveImage(ossUrl, 'url').then((res) => {
                Utils.Toast(res);
            });
        });
    }

    goScan() {
        Utils.goBack()
    }

    qrcodeUrl() {
        const url = {
            url: `pagesC/selfCommunity/selfCommunity?creator=${this.userInfo.user_id}`,
        };
        const base64 = this.stringToBase64(JSON.stringify(url));
        const escaped = encodeURIComponent(base64);
        return `https://app.dreame.tech/?mall_page=${escaped}`;
    }

    async mounted(): Promise<void> {
        await UserModule.getUserInfo();
        await VIPModule.getBasicInfo();
        this.qrcodeConfig.url = this.qrcodeUrl();
        this.generateCanvasWithQRCode();
    }

    // 在二维码中心绘制圆形头像（带白色底圈）
    private drawCenterAvatar(ctx: any, centerX: number, centerY: number, avatarSize: number): Promise<void> {
        const avatarUrl = this.userInfo?.avatar || 'https://wpm-cdn.dreame.tech/images/202301/185295-1673921873152.png';
        return new Promise<void>((resolve) => {
            uni.getImageInfo({
                src: avatarUrl,
                success: (info) => {
                    const size = Math.floor(avatarSize);
                    const border = Math.max(2, Math.floor(size * 0.06));
                    const x = Math.floor(centerX - size / 2);
                    const y = Math.floor(centerY - size / 2);
                    const radius = Math.floor(size * 0.30); // 圆角半径（更大）

                    const roundedRectPath = (px: number, py: number, w: number, h: number, r: number) => {
                        const rr = Math.min(r, Math.floor(Math.min(w, h) / 2));
                        ctx.beginPath();
                        ctx.moveTo(px + rr, py);
                        ctx.lineTo(px + w - rr, py);
                        ctx.arc(px + w - rr, py + rr, rr, -Math.PI / 2, 0);
                        ctx.lineTo(px + w, py + h - rr);
                        ctx.arc(px + w - rr, py + h - rr, rr, 0, Math.PI / 2);
                        ctx.lineTo(px + rr, py + h);
                        ctx.arc(px + rr, py + h - rr, rr, Math.PI / 2, Math.PI);
                        ctx.lineTo(px, py + rr);
                        ctx.arc(px + rr, py + rr, rr, Math.PI, 1.5 * Math.PI);
                        ctx.closePath();
                    };

                    // 白色圆角底，增强可识别性
                    ctx.save();
                    roundedRectPath(x - border, y - border, size + border * 2, size + border * 2, radius + border);
                    ctx.setFillStyle('#FFFFFF');
                    ctx.fill();
                    ctx.restore();

                    // 圆角裁剪并绘制头像
                    ctx.save();
                    roundedRectPath(x, y, size, size, radius);
                    ctx.clip();
                    // 以cover方式裁切：居中裁剪，保证铺满且不变形
                    const imgW = info.width || size;
                    const imgH = info.height || size;
                    let sX = 0;
                    let sY = 0;
                    let sW = imgW;
                    let sH = imgH;
                    if (imgW / imgH > 1) {
                        // 图片偏宽，左右裁切
                        sH = imgH;
                        sW = imgH;
                        sX = Math.floor((imgW - sW) / 2);
                        sY = 0;
                    } else if (imgW / imgH < 1) {
                        // 图片偏高，上下裁切
                        sW = imgW;
                        sH = imgW;
                        sX = 0;
                        sY = Math.floor((imgH - sH) / 2);
                    }
                    ctx.drawImage(info.path, sX, sY, sW, sH, x, y, size, size);
                    ctx.restore();
                    resolve();
                },
                fail: () => resolve(),
            });
        });
    }

    getCanvasLocalPath(canvasWidth: number, canvasHeight: number): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            uni.canvasToTempFilePath(
                {
                    canvasId: 'qrcodeCanvas',
                    width: canvasWidth,
                    height: canvasHeight,
                    success: (res) => {
                        // 直接返回临时文件路径
                        resolve(res.tempFilePath);
                    },
                    fail: (err) => {
                        reject(err);
                    },
                },
                this,
            );
        });
    }

    async uploadCanvasToOss(filePath: string): Promise<string> {
        const getPolicyRes = await Utils.getPolicy();
        const imageList = await Utils.handleImageUpload([{ url: filePath, name: 'qrcode' }], getPolicyRes);
        this.ossUrl = imageList[0];
        return this.ossUrl;
    }

    generateCanvasWithQRCode(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.$nextTick(() => {
                setTimeout(async () => {
                    try {
                        const ctx = uni.createCanvasContext('qrcodeCanvas', this);
                        this.ctx = ctx;
                        const systemInfo = uni.getSystemInfoSync();
                        const screenWidth = systemInfo.screenWidth || 390;
                        const rpxToPx = screenWidth / 750;

                        const canvasWidth = Math.round(528 * rpxToPx); // 假设 u-avatar 宽度为 100rpx
                        const canvasHeight = Math.round(528 * rpxToPx); // 假设 u-avatar 高度为 100rpx
                        const qrSize = Math.round(400 * rpxToPx); // 二维码大小

                        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                        QRCode.api.draw(
                            this.qrcodeConfig.url,
                            ctx,
                            (canvasWidth - qrSize) / 2,
                            (canvasHeight - qrSize) / 2,
                            qrSize,
                            qrSize,
                            this.qrcodeConfig.bgColor,
                            this.qrcodeConfig.fgColor,
                            this,
                            this.qrcodeConfig.ecLevel,
                        );

                        // 在二维码中心叠加用户头像
                        const avatarSize = Math.round(qrSize * 0.28); // 头像大小占二维码宽度的约28%
                        const centerX = Math.round(canvasWidth / 2);
                        const centerY = Math.round(canvasHeight / 2);
                        await this.drawCenterAvatar(ctx, centerX, centerY, avatarSize);

                        ctx.draw();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }, 500);
            });
        });
    }
}
</script>
<style lang="scss" scoped>
.personal-code_container {
    width: 100vw;
    height: 100vh;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6893134e85fb75490010999.png') no-repeat center center;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;

    .personal-code_content {
        .personal-code_content_qrcode {
            width: 528rpx;
            height: 528rpx;
            margin: 0 auto;
            margin-top: 100rpx;
            border-radius: 40rpx;
            overflow: hidden;
            background: #ffffff;
            border: 4rpx solid #E1CEAB;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .personal-code_content_userInfo {
            margin-top: 64rpx;
            text-align: center;

            .personal-code_content_userInfo_name {
                font-size: 32rpx;
                color: #ffffff;
            }

            .personal-code_content_userInfo_uid {
                font-size: 26rpx;
                color: #ffffff;
            }
        }
    }

    // 操作栏
    .personal-code_operation {
        margin-top: 64rpx;
        text-align: center;
        position: fixed;
        bottom: 124rpx;
        left: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 136rpx;

        .personal-code_operation_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: column;

            .personal-code_operation_item_icon {
                width: 102rpx;
                height: 102rpx;

                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .personal-code_operation_item_text {
                font-size: 28rpx;
                color: #ffffff;
                margin-top: 16rpx;
            }
        }
    }
}
</style>
