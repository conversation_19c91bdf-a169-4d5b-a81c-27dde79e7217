/* eslint-disable no-redeclare */
import http from './index';

/** 获取团购活动id */
export const getGroupActivityId = (): Promise<{ max_id: string }> =>
    http.post('main/group-purchase/get-id', null, { custom: { hideErrorToast: true }});

interface GoodListParams {
    activity_id: string;
    goods_name?: string;
    page?: number;
    page_size?: number;
    /** 列表类型 1:正常列表 2:随机模式 */
    type?: number;
    gid?: string;
    tag_id?: string
}

export enum GoodItemStatusEnum {
    /** 待抢 */
    WAIT = 0,
    /** 已抢光 */
    EMPTY = 1,
    /** 邀请好友 */
    SHARE = 2,
    /** 已下架 */
    OFF = 3,
    /** 团员已满（拼团成功） */
    FULL = 4,
    /** 团未拼满时间结束（拼团失败） */
    OVER = 5,
}

export const GoodItemStatusMap: Record<GoodItemStatusEnum, string> = {
    [GoodItemStatusEnum.WAIT]: '拼',
    [GoodItemStatusEnum.EMPTY]: '暂无库存',
    [GoodItemStatusEnum.SHARE]: '邀请好友拼团',
    [GoodItemStatusEnum.OFF]: '已下架',
    [GoodItemStatusEnum.FULL]: '拼团成功',
    [GoodItemStatusEnum.OVER]: '拼团失败',
};

export interface GoodItem {
    /** 商品关联ID */
    id: number;
    /** 商品ID */
    gid: number;
    member: number;
    /** 团购成团人数 */
    max_members: number;
    name: string;
    /** 商品团购价格 */
    price: number;
    /** 商品市场原价 */
    mprice: number;
    rate: string;
    /** 商品状态 0:正常 1:下架 */
    status: number;
    cover_image: string;
    /** 商品属性（0：统一规格；1：多规格；2：自定义） */
    atype: number;
    /** 按钮状态 0：抢 1：已抢光 2：邀请好友 3：已下架 4：团员已满 5：拼团已结束 */
    button_status: GoodItemStatusEnum;
    /** 已废弃 */
    selected?: boolean;
    /** 折扣活动id */
    discount_activity_id: string;
    /** 折扣商品id */
    discount_gid: string;
}

interface GoodListRes {
    title: string;
    goods: GoodItem[][];
    total_page: number;
}

/** 获取团购商品列表 */
export const getGroupGoodList = (params: GoodListParams): Promise<GoodListRes> =>
    http.post('main/group-purchase/goods-list', params);

export interface GroupingItem {
    gid: number;
    name: string;
    cover_image: string;
    leader_id?: string;
    /** 团购成团人数 */
    max_members: number;
    price: number;
    mprice: number;
    /** 商品状态 0:正常 1:下架 */
    status: number;
    button_status: GoodItemStatusEnum;
    /** 还差多少人成团 */
    remaining_members: number;
    /** 拼团ID */
    group_purchase_id: number;
    /** 活动结束时间戳 */
    end_time: number;
    /** 团购结束时间戳 */
    group_end_time: number;
    rate: string;
    /** 成员头像列表 */
    member_avatars: {
        avatar: string;
        nick_name: string;
    }[];
}

interface GroupingListRes {
    title: string;
    list: GroupingItem[];
    page: number;
    page_size: number;
    total: number;
}

/** 获取团购正在拼列表 */
export const getGroupingGoodList = (params: GoodListParams): Promise<GroupingListRes> =>
    http.post('main/group-purchase/in-progress-list', params);

interface MyGroupListParams {
    type: number;
    page: number;
    page_size?: number;
}

export interface MyGroupItem extends GoodItem {
    group_purchases_id: string;
    group_end_time?: number;
}

interface MyGroupListRes {
    list: MyGroupItem[];
    page: number;
    page_size: number;
    total: number;
}

/** 获取我的拼团列表 */
export const getMyGroupList = (params: MyGroupListParams): Promise<MyGroupListRes> =>
    http.post('main/group-purchase/my', params);

interface InitGroupPurchaseParams {
    activity_id: string;
    gid: number;
}

/** 发起拼团 */
// export const initGroupPurchase = (params: InitGroupPurchaseParams): Promise<{ id: number }> =>
//   http.post('main/group-purchase/initiate-group-purchase', params);

/** 检查是否满足拼团前置条件 */
export const checkGroupPurchase = (params: InitGroupPurchaseParams): Promise<any> =>
    http.post('main/group-purchase/check', params, { custom: { hideErrorToast: true }});

// 拼团活动-弹窗记录
export const getPopupRecord = () => http.post('main/group-purchase/popup');

// 校验用户是否有未支付订单
export const checkUnPayOrder = (params): Promise<any> =>
    http.post('main/group-purchase/check-un-pay-order', params, { custom: { hideErrorToast: true }});

// 榜单商品
export const realTimeLeaderboard = (params): Promise<any> =>
    http.post('main/group-purchase/real-time-leaderboard', params);

// 榜单商品
export const groupPurchaseTags = (): Promise<any> =>
    http.post('main/group-purchase/tags');

