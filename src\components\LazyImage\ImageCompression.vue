<!-- eslint-disable vue/no-multiple-template-root -->
<template>
    <!-- #ifdef MP-WEIXIN -->
    <image
        :src="compressedSrc"
        :lazy-load="lazyLoad"
        :mode="mode"
        :style="customStyle"
        @click="onConfirm"
        class="lazy-image-container"
    ></image>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <img
        :src="compressedSrc"
        loading="lazy"
        :width="width"
        :height="height"
        :style="customStyle"
        @click="onConfirm"
        class="lazy-image-container"
    />
    <!-- #endif -->
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { compressImageUrl, CompressionOptions, getUltraCompressionOptions } from '@/utils/imageCompression';

@Component
export default class LazyImage extends Vue {
    @Prop({ type: String, default: '' })
    readonly src!: string;

    @Prop({ type: String, default: '' })
    readonly width!: string;

    @Prop({ type: String, default: '' })
    readonly height!: string;

    @Prop({ type: Boolean, default: true })
    readonly lazyLoad!: boolean;

    @Prop({ type: String, default: 'aspectFit' })
    readonly mode!: string;

    @Prop({ type: String, default: '' })
    readonly customStyle!: string;

    @Prop({ type: Boolean, default: true })
    readonly enableCompression!: boolean;

    @Prop({ type: Number, default: 0 })
    readonly quality!: number;

    @Prop({ type: Number, default: 0 })
    readonly maxWidth!: number;

    @Prop({ type: Number, default: 0 })
    readonly maxHeight!: number;

    @Prop({ type: String, default: 'auto' })
    readonly format!: 'webp' | 'jpeg' | 'png' | 'auto';

    @Prop({ type: Boolean, default: false })
    readonly ultraCompression!: boolean;

    get compressedSrc(): string {
        if (!this.src || !this.enableCompression) {
            return this.src;
        }

        const options = this.getCompressionOptions();
        return compressImageUrl(this.src, options);
    }

    private getCompressionOptions(): CompressionOptions {
        // 如果启用了超强压缩
        if (this.ultraCompression) {
            return getUltraCompressionOptions();
        }

        // 使用传入的参数或默认值
        return {
            quality: this.quality || 75,
            maxWidth: this.maxWidth || 0,
            maxHeight: this.maxHeight || 0,
            format: this.format,
            progressive: true,
        };
    }

    onConfirm(event: Event) {
        // 阻止事件冒泡，防止穿透到父元素
        event.stopPropagation();
        this.$emit('click', event);
    }
}
</script>

<style lang="scss" scoped>
::v-deep image {
    vertical-align: baseline;
}
.lazy-image-container {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}
.lazy-image-container {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-image-container {
    opacity: 1;
}
</style>
