<template>
    <view class="reward-popup-wrapper">
        <view class="bg-light"></view>
        <view class="title"> 金币到账 +{{ rewardNum }} </view>
        <view class="big-icon"> </view>
        <view class="btn" @click="closeSignInPopup"> 开心收下 </view>
        <view class="signIn-popup-close" @click="closeSignInPopup">
            <image
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889012760f323970031451.png"
            >
            </image>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    props: {
        rewardNum: {
            type: [Number, String],
            default: 0,
        },
    },
    created() {},
    watch: {},
    computed: {},
    methods: {
        closeSignInPopup() {
            this.$emit('closeRewardPopup');
        },
    },
};
</script>

<style lang="scss" scoped>
.signIn-popup-close {
    width: 52rpx;
    height: 52rpx;
    opacity: 1;
    box-sizing: border-box;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -90rpx;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        width: 40rpx;
        height: 40rpx;
    }
}

.reward-popup-wrapper {
    position: relative;

    .bg-light {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ff8d403e9d0160428644.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 1188rpx;
        height: 1013rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: rotate-light 8s linear infinite;
    }

    @keyframes rotate-light {
        from {
            transform: translate(-50%, -50%) rotate(0deg);
        }
        to {
            transform: translate(-50%, -50%) rotate(360deg);
        }
    }

    .title {
        height: 83rpx;
        line-height: 83rpx;
        font-family: YouSheBiaoTiHei;
        font-size: 64rpx;
        font-weight: normal;
        text-align: center;
        background: linear-gradient(82deg, #ffe9a4 0%, #fffce2 32%, #fff4ba 56%, #fffce4 78%, #ffe4b0 99%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }

    .big-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689f1e8394bce6090011294.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 456rpx;
        height: 298rpx;
        margin: 94rpx auto 0;
    }

    .btn {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689f1e839435d6070013024.png');
        background-size: contain;
        background-repeat: no-repeat;
        width: 407rpx;
        height: 126rpx;
        line-height: 116rpx;
        text-align: center;
        margin: 71rpx auto 0;
        font-size: 48rpx;
        font-weight: 500;
        color: #ffffff;
        text-shadow: 0px 5rpx 12rpx rgba(211, 0, 28, 0.5);
        position: relative;
    }
}
</style>
