<template>
    <view class="featured-category">
        <view class="category-title">{{ title }}</view>
        <view class="category-grid" :style="gridStyle">
            <view
                class="category-item"
                v-for="(item, index) in categoryList"
                :key="item.id"
                v-show="item.visible !== false"
                @tap="handleCategoryClick(item, index)"
            >
                <view class="item-image-wrapper">
                    <image class="item-image" :src="item.image" mode="aspectFit" :lazy-load="true" />
                </view>
                <view class="item-name">{{ item.name }}</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { CategoryItem, CategoryClickEvent, DEFAULT_CATEGORIES } from './types';

@Component({
    name: 'FeaturedCategory',
})
export default class FeaturedCategory extends Vue {
    // Props定义
    @Prop({
        type: Array,
        default: () => DEFAULT_CATEGORIES,
    })
    readonly categoryList!: CategoryItem[];

    @Prop({ type: String, default: '精选分类' })
    readonly title!: string;

    @Prop({ type: Number, default: 3 })
    readonly columns!: number;

    // 计算属性：动态网格样式
    get gridStyle(): Record<string, string> {
        return {
            'grid-template-columns': `repeat(${this.columns}, 1fr)`,
        };
    }

    // 处理分类点击事件
    handleCategoryClick(item: CategoryItem, index: number): void {
        // 检查分类是否可见
        if (item.visible === false) {
            return;
        }

        // 构建点击事件数据
        const clickEvent: CategoryClickEvent = { item, index };

        // 发送点击事件给父组件
        this.$emit('category-click', clickEvent);

        // 根据类型进行不同的跳转处理
        this.navigateToCategory(item);

        // 埋点统计
        this.trackCategoryClick(item, index);
    }

    // 导航到分类页面
    private navigateToCategory(item: CategoryItem): void {
        if (item.url) {
            Utils.navigateTo(item.url);
        } else if (item.tid) {
            // 跳转到商品分类页面
            Utils.navigateTo(`/pagesA/brandType/brandType?tid=${item.tid}`);
        }
    }

    // 埋点统计
    private trackCategoryClick(item: CategoryItem, index: number): void {
        Utils.reportEvent('category_click', {
            category_id: item.id,
            category_name: item.name,
            category_type: item.type || 'product',
            position: index,
        });
    }
}
</script>

<style lang="scss" scoped>
.featured-category {
    width: 524rpx;
    height: 502rpx;
    border-radius: 16rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin-bottom: 16rpx;
    position: absolute;
    border: 1rpx solid #ccc;

    .category-title {
        position: absolute;
        left: 24rpx;
        top: 24rpx;
        width: 112rpx;
        height: 38rpx;
        opacity: 1;

        font-family: MiSans;
        font-size: 28rpx;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0rpx;

        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #3d3d3d;
    }

    .category-grid {
        position: absolute;
        left: 24rpx;
        top: 80rpx;
        right: 24rpx;
        bottom: 24rpx;

        /* #ifndef MP-WEIXIN */
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24rpx 16rpx;
        align-content: start;
        /* #endif */

        /* #ifdef MP-WEIXIN */
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: flex-start;
        /* #endif */

        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16rpx 8rpx;
            border-radius: 12rpx;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;

            /* #ifdef MP-WEIXIN */
            width: 30%;
            margin-bottom: 24rpx;
            box-sizing: border-box;
            /* #endif */

            /* #ifndef MP-WEIXIN */
            width: 100%;
            /* #endif */

            &:active {
                background: $uni-bg-color-hover;
                transform: scale(0.98);
            }

            .item-image-wrapper {
                width: 80rpx;
                height: 80rpx;
                border-radius: 12rpx;
                background: #f8f8f8;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 8rpx;
                overflow: hidden;
                border: 1rpx solid #e8e8e8;

                .item-image {
                    width: 64rpx;
                    height: 64rpx;
                    border-radius: 8rpx;
                }
            }

            .item-name {
                font-size: 24rpx;
                color: #333333;
                text-align: center;
                line-height: 32rpx;
                font-weight: 400;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100rpx;
            }
        }
    }
}

// 响应式适配 - 小屏设备优化
@media screen and (max-width: 750rpx) {
    .featured-category {
        padding: 24rpx;

        .category-grid {
            /* #ifndef MP-WEIXIN */
            gap: 24rpx 16rpx;
            /* #endif */

            .category-item {
                padding: 20rpx 12rpx;

                /* #ifdef MP-WEIXIN */
                width: 30%;
                margin-bottom: 24rpx;
                /* #endif */

                .item-image-wrapper {
                    width: 100rpx;
                    height: 100rpx;

                    .item-image {
                        width: 80rpx;
                        height: 80rpx;
                    }
                }

                .item-name {
                    font-size: 26rpx;
                }
            }
        }
    }
}
</style>
