import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';

let env = process.env.NODE_ENV;
let BASE_URL = process.env.VUE_APP_BASE_URL;

let api, security_key;
// #ifdef H5
api = AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268';
security_key = AppModule.platform == 'ios' ? 'b_m3h^jWfA9jp' : 'b_m3I6PiPgYX#';
// #endif
// #ifdef MP-WEIXIN
api = AppModule.platform == 'ios' ? 'i_1643178026' : 'a_1643178000';
security_key = AppModule.platform == 'ios' ? '*&^$%#%!#$&@' : '*&^@!!#$&#@%';
// #endif

/*
 *以下代码仅在开发阶段执行
 *发行时需要删除此部分代码
 */

// start
if (process.env.NODE_ENV === 'development') {
    try {
        const node_env = uni.getStorageSync('node_env');
        if (node_env) {
            env = node_env.env;
            BASE_URL = node_env.BASE_URL;
        } else {
            uni.setStorage({
                key: 'node_env',
                data: {
                    env,
                    BASE_URL,
                },
            });
        }
    } catch (e) {
        console.error(`当前环境是${process.env.NODE_ENV}`);
    }
} else {
    // 生产环境 避免出现环境缓存
    try {
        uni.removeStorageSync('node_env');
    } catch (e) {
        console.error('没有环境缓存');
    }
}
// end

function getSign(obj, security_key) {
    obj.sign_time = parseInt(new Date().getTime() / 1000);
    const newObj = {
        ...obj,
    };
    newObj.security_key = security_key;
    let sortArr = [];
    const keysSorted = Object.keys(newObj).sort(); // 排序名
    for (let i = 0; i < keysSorted.length; i++) {
        sortArr.push(keysSorted[i] + '=' + newObj[keysSorted[i]]);
    }
    sortArr = sortArr.join('&');
    return md5Libs.md5(sortArr);
}

export const upload = (imgPath, params = {}) => {
    return new Promise((resolve, reject) => {
        const BASE_DATA = {
            user_id: UserModule.user_id || 0,
            sessid: UserModule.sessid || '',
            api: api,
            version: AppModule.version,
            ...params,
        };

        BASE_DATA.sign = getSign(BASE_DATA, security_key);
        uni.uploadFile({
            url: `${BASE_URL}main/order/rupload`,
            filePath: imgPath,
            name: 'file',
            formData: BASE_DATA,
            success: (res) => {
                const result = JSON.parse(res.data);
                if (result.iRet == 1) {
                    resolve(result.data);
                } else if (result.iRet == 2) {
                    resolve(result);
                } else if (result.iRet == -1) {
                    if (result.sMsg) {
                        uni.showToast({
                            title: result.sMsg,
                            icon: 'none',
                            duration: 2300,
                        });
                    }
                    reject(result);
                } else if (result.iRet == -100) {
                    // 清除登录信息重新登录
                    UserModule.setSessId('');
                    UserModule.setUserId('');
                    UserModule.setIsLogin(false);
                    UserModule.asyncWxLogin();
                    reject(res);
                } else {
                    reject(res);
                }
            },
            fail: (err) => {
                if (err.sMsg) {
                    uni.showToast({
                        title: '上传图片失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
                reject(err);
            },
            complete: () => {},
        });
    });
};

// 上传文件
export const uploadFile = (imgPath, params = {}) => {
    return new Promise((resolve, reject) => {
        const BASE_DATA = {
            user_id: UserModule.user_id || 0,
            sessid: UserModule.sessid || '',
            api: api,
            version: AppModule.version,
            ...params,
        };
        BASE_DATA.sign = getSign(BASE_DATA, security_key);

        uni.uploadFile({
            url: `${BASE_URL}main/data/upload-file`,
            filePath: imgPath,
            name: 'file',
            formData: BASE_DATA,
            success: (res) => {
                const result = JSON.parse(res.data);
                if (result.iRet == 1) {
                    resolve(result.data);
                } else if (result.iRet == 2) {
                    resolve(result);
                } else if (result.iRet == -1) {
                    if (result.sMsg) {
                        uni.showToast({
                            title: result.sMsg,
                            icon: 'none',
                            duration: 2300,
                        });
                    }
                    reject(result);
                } else if (result.iRet == -100) {
                    // 清除登录信息重新登录
                    UserModule.setSessId('');
                    UserModule.setUserId('');
                    UserModule.setIsLogin(false);
                    UserModule.asyncWxLogin();
                    reject(res);
                } else {
                    reject(res);
                }
            },
            fail: (err) => {
                if (err.sMsg) {
                    uni.showToast({
                        title: '上传图片失败',
                        icon: 'none',
                        duration: 2000,
                    });
                }
                reject(err);
            },
            complete: () => {},
        });
    });
};
