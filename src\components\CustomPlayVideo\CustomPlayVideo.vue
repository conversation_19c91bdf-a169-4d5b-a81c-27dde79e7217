<template>
	<u-overlay :show="show" v-prevent-scroll="show" :opacity="1" :duration="200"
		style="display: flex; justify-content: center; align-items: center;inset: 0;">
		<view class="video-wrap" :class="{ 'video-wrap-safeAreaInsetBottom': safeAreaInsetBottom }">
			<image @click="closeMask" style="height:64rpx;width:64rpx;position: absolute; right: 38rpx;z-index:1"
				:style="{ top: top + 'rpx' }" src="https://wpm-cdn.dreame.tech/images/202308/245558-1693387834622.png">
			</image>
			<!-- video player -->
			<view @click="handleControls" style="width:100%;height:100%">
				<video class="video-player" :id="videoId" ref="myVideo" style="width:100%;height:100%"
					:poster="video.poster" webkit-playsinline="true" playsinline="true" x-webkit-airplay="allow"
					x5-video-player-type="h5" x5-video-player-orientation="portrait" :src="video.url"
					:initial-time="initialTime" :controls="showContros" :show-center-play-btn="false" :autoplay="true"
					:muted="isMute" :preload="false" @play="videoPlay" @pause="videoPause" @ended="videoEnded"
					@timeupdate="videoTimeUp" @loadedmetadata="videoLoaded" @seeked="videoSeeked"
					@seeking="videoSeeking" @waiting="videoWaiting" @error="videoError"
					@fullscreenchange="onFullScreen"></video>
			</view>
			<view class="abs-center">
				<!-- 中心播放按钮 -->
				<image src="https://wpm-cdn.dreame.tech/images/202309/64f2ceafc58938092020542.png" mode=""
					class="play-btn" v-if="!isVideoPlay && !showLoading" @click="videoPlayCenter"></image>
				<!--  加载中 -->
				<div class="video-loading" v-if="showLoading">
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2ceef618043992020538.png" mode=""
						class="loading-btn"></image>
				</div>
			</view>

			<!-- 控制条 -->
			<view :class="['controls-bar', controls ? 'show' : 'hide']">
				<!-- 播放 -->
				<view class="play-box" @click="videoPlayClick">
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2cf012a87c1741594349.png" mode=""
						class="play-icon" v-if="isVideoPlay"></image>
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2cf1377c324901992820.png" mode=""
						class="play-icon" v-else></image>
				</view>
				<!-- 进度 -->
				<view class="progress">
					<view class="currtime">{{ currentTimeStr }}</view>
					<view class="slider-container">
						<slider @change="sliderChange" @changing="sliderChanging" :step="step" :value="sliderValue"
							backgroundColor="#9f9587" activeColor="#d6d2cc" block-color="#FFFFFF" block-size="12" />
					</view>
					<view class="druationTime">{{ druationTimeStr }}</view>
				</view>
				<!-- 声音 -->
				<view class="mute-box" @click="videoMuteClick" v-if="showVoice">
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2cf24522993362020539.png" mode=""
						class="mute-icon" v-if="!isMute"></image>
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2cf31488f02971594352.png" mode=""
						class="mute-icon" v-else></image>
				</view>
				<!-- 倍速 -->
				<view class="play-rate" @click="videoPlayRate" v-if="showRate">
					{{ playbackRate }}x
				</view>
				<!-- 全屏 -->
				<view class="play-full" v-if="isPlayFull" @click="videoFull">
					<image src="https://wpm-cdn.dreame.tech/images/202309/64f2cf3d8bf2b5731992840.png" mode=""
						class="play-icon"></image>
				</view>
				<!-- 倍速菜单 -->
				<ul class="play-rate-menu" :style="{ height: `${height}` }" v-if="showRateMenu">
					<li v-for="item in playbackRates" :key="item"
						:class="[{ activeRate: playbackRate === item }, 'play-rate-item']"
						@click="changePlayRate(item)">
						{{ item }}x
					</li>
				</ul>
			</view>
		</view>
	</u-overlay>
</template>

<script>
import { AppModule } from '@/store/modules/app';

export default {
	name: 'PlayVideo',
	props: {
		isPlayFull: {
			type: Boolean,
			default: true,
		},
		showVoice: {
			type: Boolean,
			default: true,
		},
		// 视频地址
		videoId: {
			type: String,
			default: 'myVideo'
		},
		safeAreaInsetBottom: {
			type: Boolean,
			default: true,
		},
		// 视频地址
		src: {
			type: String,
		},
		// 自动播放
		autoplay: {
			type: Boolean,
			default: true,
		},
		// 封面
		poster: {
			type: String,
		},
		// 步长，表示占比，取值必须大于0且为整数
		step: {
			type: Number,
			default: 1,
		},
		// 初始播放进度，表示占比
		progress: {
			type: Number,
		},
		// 视频宽度
		width: {
			type: String,
			default: '100%',
		},
		// 视频高度
		height: {
			type: String,
			default: '',
		},
		// 播放错误提示
		errorTip: {
			type: String,
			default: '播放错误',
		},
		// 是否展示倍速
		showRate: {
			type: Boolean,
			default: true,
		},
		// 是否暂停
		isPause: {
			type: Boolean,
			default: false,
		},
		// 是否展示倍速
		controlBottom: {
			type: String,
			default: '0rpx',
		},
		// 播放速率
		playbackRates: {
			type: Array,
			default: () => [0.8, 1, 1.25, 1.5, 2],
		},
		// 是否全屏播放
		isFullscreenPlay: {
			type: Boolean,
			default: false,
		},
		// 显示播放控件
		show: {
			type: Boolean,
			default: false,
		},
		video: {
			type: Object,
			default() {
				return { src: '', poster: '' }
			}
		}
	},
	data() {
		return {
			controls: false, // 显示播放控件
			isVideoPlay: false, // 是否正在播放
			isMute: true, // 是否静音
			isVideoEnd: false, // 是否播放结束
			showPoster: true, // 是否显示视屏封面
			showLoading: false, // 加载中
			durationTime: 0, // 总播放时间 时间戳
			currentTime: 0, // 当前播放时间 时间戳
			druationTimeStr: '00:00', // 总播放时间 字符串 计算后
			currentTimeStr: '00:00', // 当前播放时间 字符串 计算后
			sliderValue: 0, // 进度条的值 百分比
			isSeeked: true, // 防止进度条拖拽失效
			playbackRate: 1, // 初始播放速率
			showRateMenu: false, // 显示播放速率
			initialTime: 0, // 初始播放时间
			showContros: false, // 是否横屏-小程序
		}
	},
	computed: {
		top() {
			let top = 0
			// #ifdef H5
			top = AppModule.statusBarHeight + 22
			// #endif
			// #ifdef MP-WEIXIN
			top = AppModule.pagePaddingTop + 22
			// #endif
			return top
		}
	},
	watch: {
		show(val) {
			if (val) {
				this.$nextTick(() => {
					// #ifdef H5
					this.videoPlayer = this.$refs.myVideo
					// #endif
					// #ifdef MP-WEIXIN
					this.videoPlayer = uni.createVideoContext('myVideo', this);
					// #endif
					this.videoPlayer?.play()
				})
			}
		}
	},
	created() {
	},
	methods: {
		// 自动隐藏控制条
		hideControls() {
			this.timer = setTimeout(() => {
				this.controls = false;
			}, 5000);
		},
		// 点击显示/隐藏控制条
		handleControls() {
			this.controls = !this.controls;
		},
		// 根据秒获取时间
		formatSeconds(second) {
			second = Math.round(second);
			let hh = parseInt(second / 3600);
			let mm = parseInt((second - hh * 3600) / 60);
			if (mm < 10) mm = '0' + mm;
			let ss = parseInt((second - hh * 3600) % 60);
			if (ss < 10) ss = '0' + ss;
			if (hh < 10) hh = hh == 0 ? '' : `0${hh}:`;
			const length = hh + mm + ':' + ss;
			if (second > 0) {
				return length;
			} else {
				return '00:00';
			}
		},
		// 缓冲
		videoWaiting(e) {
			// 没有缓冲结束事件，所以在不播放的情况触发loading
			if (!this.isVideoPlay) this.showLoading = true;
		},
		// 视频信息加载完成
		videoLoaded(e) {
			this.durationTime = e.detail.duration;
			this.druationTimeStr = this.formatSeconds(this.durationTime);
			this.initialTime = this.progress * this.durationTime;
			this.sliderValue = this.progress * 100;
			this.videoPlayer?.seek(this.initialTime);
			this.currentTimeStr = this.formatSeconds(this.initialTime);
			this.controls = true;
			this.showLoading = false;
			this.$emit('loadeddata', e.detail.duration);
		},
		// 播放进度更新,触发频率 250ms 一次
		videoTimeUp(e) {
			const sliderValue = Math.round(
				(e.detail.currentTime / this.durationTime) * 100
			);
			if (this.isSeeked) {
				// 判断拖拽完成后才触发更新，避免拖拽失效
				if (sliderValue % this.step === 0) {
					// 比例值能被步进值整除
					this.sliderValue = sliderValue;
				}
			}
			this.currentTimeStr = this.formatSeconds(e.detail.currentTime);
			this.$emit('timeupdate', e.detail.currentTime);
		},
		// 正在拖动slider
		sliderChanging(e) {
			this.isSeeked = false; // 拖拽过程中，不允许更新进度条
			this.showLoading = true;
			this.videoPlayer.pause();
			this.$emit('seeking');
		},
		// 拖动slider完成后
		sliderChange(e) {
			this.sliderValue = e.detail.value;
			const currentTime = (this.sliderValue / 100) * this.durationTime;
			this.showLoading = false;
			this.isSeeked = true; // 完成拖动后允许更新滚动条
			this.videoPlayer?.seek(currentTime);
			if (this.sliderValue < 100) {
				this.videoPlayer?.play();
			} else {
				this.videoPlayer.pause();
				this.videoEnded();
			}
			this.hideControls();
			this.$emit('seeked', this.sliderValue);
		},

		// 点击中心播放
		videoPlayCenter() {
			// 如果播放完成
			if (!this.videoPlayer) {
				// #ifdef H5
				this.videoPlayer = this.$refs.myVideo
				// #endif
				// #ifdef MP-WEIXIN
				this.videoPlayer = uni.createVideoContext('myVideo', this);
				// #endif
				this.videoPlayer.currentTime = 0;
			}
			setTimeout(() => {
				this.videoPlayer?.play();
			}, 100);
			this.$emit('play');
		},

		// 关闭遮罩
		closeMask() {
			this.videoEnded();
			this.$emit('hide')
		},

		// 点击左下角播放/暂停,会触发原始播放/暂停事件,分开写，防止重复触发
		videoPlayClick() {
			if (this.isVideoPlay) {
				this.videoPlayer.pause();
			} else {
				if (!this.videoPlayer) {
					// #ifdef H5
					this.videoPlayer = this.$refs.myVideo
					// #endif
					// #ifdef MP-WEIXIN
					this.videoPlayer = uni.createVideoContext('myVideo', this);
					// #endif
					this.videoPlayer.currentTime = 0;
				}
				this.videoPlayer?.play();
				this.$emit('play');
			}
		},
		// 原始播放
		videoPlay() {
			if (this.pauseTimer) {
				clearTimeout(this.pauseTimer);
			}
			this.isVideoPlay = true;
			this.isVideoEnd = false;
			this.showLoading = false;
			this.hideControls();
		},
		// 原始暂停
		videoPause() {
			// 处理播放结束和拖动会先触发暂停的问题
			this.pauseTimer = setTimeout(() => {
				if (this.isVideoEnd) return;
				if (!this.isSeeked) return;
				this.isVideoPlay = false;
				this.$emit('pause');
			}, 100);
		},
		// 静音
		videoMuteClick() {
			this.isMute = !this.isMute;
		},
		// 播放结束
		videoEnded() {
			// 重置状态
			this.isVideoPlay = false;
			this.showPoster = true;
			this.isVideoEnd = true;
			this.videoPlayer?.seek(0);
			this.videoPlayer = null;
			this.$emit('ended');
		},
		// 播放错误
		videoError(e) {
			uni.showToast({
				title: this.errorTip,
				icon: 'none',
			});
			this.$emit('error');
		},
		// 显示倍速
		videoPlayRate() {
			this.showRateMenu = true;
		},
		// 点击倍速
		changePlayRate(rate) {
			this.playbackRate = rate;
			this.videoPlayer.playbackRate(rate);
			this.showRateMenu = false;
			this.hideControls();
		},
		// 点击全屏
		videoFull() {
			this.videoPlayer.requestFullScreen();
		},
		// 监听原生全屏事件
		onFullScreen({ detail }) {
			if (detail.fullScreen) {
				this.showContros = true
			} else {
				this.showContros = false
			}
		}
	},

	mounted() {
	}
}
</script>

<style lang="scss" scoped>

::v-deep .u-overlay {
	position: fixed;
	top: 0;
	height: 100vh;
	width: 100vw;
}

.show {
	opacity: 1 !important;
}

.hide {
	opacity: 0 !important;
	pointer-events: none;
}

.abs-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.video-wrap-safeAreaInsetBottom {
	height: calc(100vh - env(safe-area-inset-bottom)) !important;
	height: calc(100vh - constant(safe-area-inset-bottom)) !important;
}

.video-wrap {
	position: relative;
	box-sizing: border-box;
	width: 100vw;
	height: 100vh;

	.mask {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 84rpx;
		left: 0;
	}

	.play-btn {
		width: 120rpx;
		height: 120rpx;
	}

	@keyframes run {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.loading-btn {
		width: 120rpx;
		height: 120rpx;
		animation: run 0.8s linear 0s infinite;
	}

	.controls-bar {
		width: 100%;
		padding: 1% 1% 1% 0;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 99;
		display: flex;
		align-items: center;
		background: rgba(59, 57, 57, 0.2);
		color: #fff;
		opacity: 1;
		transition: opacity 1s;
		height: 84rpx;

		.play-box,
		.mute-box,
		.play-full {
			width: 84rpx;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.mute-icon {
			width: 40rpx;
			height: 40rpx;
		}

		.play-icon {
			width: 34rpx;
			height: 34rpx;
		}

		.progress {
			display: flex;
			align-items: center;
			flex: 1;
			font-size: 24rpx;
			margin-left: 16rpx;

			.slider-container {
				flex: 1;
				max-width: 65%;
			}

			.currtime {
				color: #ffffff;
				width: 11%;
				height: 100%;
				text-align: center;
				margin-right: 20rpx;
			}

			.druationTime {
				color: #ffffff;
				width: 12%;
				height: 100%;
				text-align: center;
			}
		}

		.play-rate {
			font-size: 32rpx;
			margin-right: 24rpx;
		}

		.play-rate-menu {
			list-style-type: none;
			background-color: rgba(0, 0, 0, 0.7);
			width: 24%;
			padding-top: 26rpx;
			position: absolute;
			right: 0;
			bottom: 0;
			padding-left: 0;
			box-sizing: border-box;
		}

		.play-rate-item {
			line-height: 70rpx;
			font-size: 28rpx;
			text-align: center;
		}

		.play-rate-item:first-child {
			margin-top: 30rpx;
		}

		.activeRate {
			color: #5785e3;
		}
	}
}
</style>
