<template>
    <view class="icon-close-layout u-flex u-col-center u-row-center" :class="[typeData.className]" @click="popClose">
        <image :src="typeData.url" class="icon-close" />
    </view>
</template>

<script lang="ts">
import { Vue, Component, Emit, Prop } from 'vue-property-decorator';
interface ITypeParams {
    className: string;
    url: string;
}
@Component({})
export default class CustomAmount extends Vue {
    @Prop({ type: String, default: '1' })
    readonly isType;

    get typeData(): ITypeParams {
        const TYPE_ONE = '1';
        const TYPE_TWO = '2';
        if (this.isType === TYPE_ONE) {
            return {
                className: '',
                url: 'https://wpm-cdn.dreame.tech/images/202207/824922-1656990927014.png',
            };
        }
        if (this.isType === TYPE_TWO) {
            return {
                className: 'icon-close-layout-2',
                url: 'https://wpm-cdn.dreame.tech/images/202203/621ddb22f3edd9991574776.png',
            };
        }
    }

    @Emit('popClose')
    popClose() {}
}
</script>

<style lang="scss" scoped>
.icon-close-layout {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 50rpx;
    height: 50rpx;
    z-index: 99;
    .icon-close {
        width: 50rpx;
        height: 50rpx;
    }
}
.icon-close-layout-2 {
    position: absolute;
    top: 18rpx;
    right: 14rpx;
    width: 38rpx;
    height: 38rpx;
    .icon-close {
        width: 38rpx;
        height: 38rpx;
    }
}
</style>
