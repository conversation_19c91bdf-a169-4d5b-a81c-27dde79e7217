<template>
    <view>
        <APPSHARE :link="link"></APPSHARE>
        <view class="contentDetails-container" :style="{ paddingBottom: pagePaddingBottom + 'rpx' }">
            <custom-bar
                :isFixed="true"
                :backIcon="icon"
                :background="'rgba(0,0,0,0)'"
                @back="toHome"
                :customBack="true"
            />

            <view
                v-if="isContentCreator && currentAuditStatus"
                class="status-bar"
                :style="{
                    top: `${pagePaddingTop}rpx`,
                    color: `${currentAuditStatus.color}`,
                    background: `${currentAuditStatus.bgColor}`,
                }"
                >{{ currentAuditStatus.label }}</view
            >

            <videoPlay
                style="display: unset !important"
                v-show="!isLoading && data.is_report === 0"
                ref="videoPlayRef"
                @share="handleShare"
                @longpress="handleLongpress"
                @videoPlay="handleVideoPlay"
                @swiperChange="handleswiperChange"
                :permissionTypeProp="permissionType"
                :fromPage="fromPage"
            ></videoPlay>
        </view>
        <!-- 退出挽留弹窗 -->
        <u-popup
            :show="awardPopup"
            mode="center"
            :round="30"
            class="award-popup"
            v-prevent-scroll="awardPopup"
            catchtouchmove
            :customStyle="{ backgroundColor: 'transparent', height: '800rpx' }"
        >
            <view class="award-popup-content">
                <view class="award-popup-header">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a295a7592b53650010560.png"
                        class="title-img"
                    ></image>
                </view>
                <view class="award-title"> 再看{{ remainTime }}s后可领取奖励 </view>
                <view class="award-popup-body" @click="existawardPopup">
                    <text class="award-num-front">+</text>
                    <text class="award-num-mid">20</text>
                    <text class="award-num-back">金币</text>
                </view>
                <view class="award-continue" @click="continueWatch">继续观看</view>
                <view class="award-exit-box">
                    <view class="award-exit" @click="existawardPopup">坚持退出</view>
                    <view class="award-change" @click="changeVideo">换一个</view>
                </view>
                <view class="award-popup-close" @click="continueWatch">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889012760f323970031451.png"
                    >
                    </image>
                </view>
            </view>
        </u-popup>
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin v-if="isWxLogin"></WxLogin>
        <!-- #endif -->
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
        <EarnMoneySpendTimeoutTips
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '300rpx' }"
            :task-code="'viewVideo'"
        />
        <VideoIntegralTimeoutTips
            ref="videoIntegralTimeoutTipsRef"
            @isCompleted="
                (e) => {
                    isCompleted = e;
                }
            "
            @remain="remain"
        />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from '@/components/CustomSwiper/CustomSwiper.vue';
import { AppModule } from '@/store/modules/app';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import Utils from '@/common/Utils';
// import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
import videoPlay from './components/videoPlay/videoPlay.vue';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';
import {
    ContentPermissionType,
    createBrowse,
    detailContent,
    detailContentNoLogin,
    getContentPer,
    getrandomVideoList,
} from '@/http/requestGo/community';
import Constants from '@/common/Constants';
import { AUDIT_STATUS_OPTION, REPORT_STATUS_OPTION } from '../contentDetails/components/contant';
import VideoIntegralTimeoutTips from '@/components/VideoIntegralTimeoutTips/VideoIntegralTimeoutTips.vue';
import { CheckAppJump } from '@/common/decorators';

type ReportType = 'comment' | 'detail';

@Component({
    components: {
        CustomSwiper,
        WxLogin,
        videoPlay,
        EarnMoneySpendTimeoutTips,
        VideoIntegralTimeoutTips,
    },
})
export default class Contents extends Vue {
    $refs: {
        videoPlayRef;
        EarnMoneySpendTimeoutTips;
        videoIntegralTimeoutTipsRef;
    };

    public link: string = '';
    public awardPopup = false;
    public canLeave = false;
    public remainTime = 0;
    public delayTime: boolean = false;
    @CheckAppJump()
    openawardPopup() {
        this.awardPopup = true;
        setTimeout(async () => {}, 1000);
    }

    closeawardPopup() {
        this.awardPopup = false;
        // this.getawardStatus();
        // 更新分数
    }

    // onBackPress(e) {
    //     console.log('onBackPress', e);
    //     if (!this.awardPopup) {
    //         this.awardPopup = true;
    //         return true; // 阻止页面退出
    //     }
    //     if (this.canLeave) {
    //         return false; // 允许页面退出
    //     }
    //     return true; // 阻止页面退出
    // }

    existawardPopup() {
        console.log('existawardPopup');
        this.awardPopup = false;
        Utils.goBack();
    }

    // 恢复VideoIntegralTimeoutTips的动画和计时
    resumeAnimationAndTimer() {
        if (this.$refs.videoIntegralTimeoutTipsRef) {
            this.$refs.videoIntegralTimeoutTipsRef.resumeAnimationAndTimer();
        }
    }

    continueWatch() {
        this.awardPopup = false;
        this.canLeave = true;

        this.handleVideoPlay(this.isPlaying);
        // this.changeToNextVideo();
    }

    remain(res) {
        this.remainTime = res;
    }

    public isPlaying: boolean = false;

    handleVideoPlay(isPlaying: boolean) {
        this.isPlaying = isPlaying;
        if (isPlaying) {
            this.$refs.videoIntegralTimeoutTipsRef.pauseAnimationAndTimer();
        } else {
            this.resumeAnimationAndTimer();
        }
    }

    public pushTimer: number = 0;

    handleswiperChange() {
        this.resumeAnimationAndTimer();
        clearTimeout(this.pushTimer);
        this.pushTimer = setTimeout(() => {
            this.$refs.videoIntegralTimeoutTipsRef.pauseAnimationAndTimer();
            console.log('handleswiperChange');
        }, 65000);
    }

    beforeDestroy(): void {
        clearTimeout(this.pushTimer);
    }

    changeVideo() {
        this.awardPopup = false;
        this.canLeave = true;

        // 调用子组件的方法切换到下一个视频
        if (this.$refs.videoPlayRef && this.$refs.videoPlayRef.switchToNextVideo) {
            this.$refs.videoPlayRef.switchToNextVideo();
        }
        this.resumeAnimationAndTimer();
        // else {
        //     console.log('changeVideo');
        //     // 如果是从金币页面跳转过来的，需要重新获取一个新的视频
        //     if (this.fromPage === 'goldCoins') {
        //         this.changeToNextVideo();
        //     } else {
        //         // 原有的逻辑
        //         uni.navigateBack({ delta: 1 });
        //     }
        // }
    }

    // 切换到下一个视频
    // async changeToNextVideo() {
    //     console.log('changeToNextVideo');
    //     try {
    //         // 直接调用子组件的向下获取数据方法，模拟手动上划
    //         if (this.$refs.videoPlayRef && this.$refs.videoPlayRef.getMoreData) {
    //             // 调用子组件的向下获取数据方法 (direction = 0)
    //             await this.$refs.videoPlayRef.getMoreData(0);

    //             // 显示成功提示
    //             Utils.Toast('已切换到新视频');
    //         } else {
    //             Utils.Toast('组件未准备好');
    //         }
    //     } catch (error) {
    //         console.error('切换视频失败:', error);
    //         Utils.Toast('切换视频失败');
    //     }
    // }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get pagePaddingBottom(): number {
        return AppModule.pagePaddingBottom;
    }

    get icon(): String {
        const icon = 'https://wpm-cdn.dreame.tech/images/2024010/290671-1728727353625.png';
        // if (!this.showIsImage && this.banners.length && !this.isTop) {
        //     icon = 'https://wpm-cdn.dreame.tech/images/202302/693755-1676372106062.png';
        // } else {
        //     icon = 'https://wpm-cdn.dreame.tech/images/202302/508756-1676371936507.png';
        // }
        return icon;
    }

    // #ifdef MP-WEIXIN

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    get clicks(): number | string {
        if (Number(this.data.clicks) < 1000) {
            const item = this.data.clicks;
            return item;
        } else {
            const item = (Number(this.data.clicks) / 1000).toFixed(1);
            return item + 'k';
        }
    }

    get getContentApi() {
        return Utils.isInApp() ? detailContent : detailContentNoLogin;
    }

    get currentAuditStatus() {
        // 举报下架 > 审核下架 > 举报中 > 审核中
        if (this.data.report_status === 2) {
            return REPORT_STATUS_OPTION.find((v) => v.value === this.data?.report_status);
        }
        if (this.data.audit_status === 3) {
            return AUDIT_STATUS_OPTION.find((v) => v.value === this.data?.audit_status);
        }
        if (this.data.report_status === 1) {
            return REPORT_STATUS_OPTION.find((v) => v.value === this.data?.report_status);
        }
        if (this.data.audit_status === 1) {
            return AUDIT_STATUS_OPTION.find((v) => v.value === this.data?.audit_status);
        }
    }

    public content_id: string = '';
    public topic_id: string = '';
    public paddingLeft: number = 19;
    public isWxLogin: any = false;
    public isLoaded: any = false;
    public isTop: any = false;
    public data: any = {};
    public item: any = {};
    public currentNum: number = 0;
    public share: number = 0;
    public autoplay: any = false;
    public isLoading: any = true;
    public isContentCreator: any = false;
    public fromPage: string = '';

    async onLoad(options) {
        console.log('options', options);

        this.isLoading = true;
        this.link = options.link || '';
        this.share = +options.share || 0;
        this.content_id = options.content_id || '';
        this.topic_id = options.topic_id || '';
        this.fromPage = options.from || '';

        if (this.fromPage === 'goldCoins') {
            // 从金币页面跳转过来，需要自己获取 content_id
            await this.getContentIdFromGoldCoins();
            this.handleswiperChange();
        } else {
            // 原有逻辑：使用传递过来的 content_id
            this.init();
        }
    }

    async getContentIdFromGoldCoins() {
        try {
            const params = {
                page_size: 10,
                type: 2,
            };

            const res = await getrandomVideoList(params, false);

            if (res.success && res.data.list && res.data.list.length > 0) {
                this.content_id = res.data.list[0].content_id;
                this.topic_id = res.data.list[0].topic_id || '';

                // 获取到 content_id 后继续初始化
                await this.init();
            } else {
                Utils.Toast('暂无推荐视频');
                Utils.goBack();
            }
        } catch (error) {
            console.error('获取推荐视频失败:', error);
            Utils.Toast('获取推荐视频失败');
            Utils.goBack();
        }
    }

    public permissionType = ContentPermissionType.PUBLIC;
    onShow() {
        const createContentData = uni.getStorageSync('createContent');
        this.permissionType = Number(createContentData.selectPermission) || this.permissionType;
        // #ifdef MP-WEIXIN
        this.$nextTick(() => {
            (this.$refs.videoPlayRef as any).showView();
        });
        // #endif
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
    }

    onHide() {
        // 页面隐藏时停止视频播放
        if (this.$refs.videoPlayRef && this.$refs.videoPlayRef.stopAllVideos) {
            this.$refs.videoPlayRef.stopAllVideos();
        }
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onUnload() {
        // 页面卸载时停止视频播放
        if (this.$refs.videoPlayRef && this.$refs.videoPlayRef.stopAllVideos) {
            this.$refs.videoPlayRef.stopAllVideos();
        }
        this.data = {};
        uni.removeStorageSync('createContent');
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    async init() {
        await this.getDetailData();
        this.$refs.videoPlayRef.getMoreData(0);
        this.isLoading = false;
        this.isWxLogin = true;
    }

    async getDetailData() {
        const res = await this.getContentApi({ content_id: this.content_id });
        if (res.success) {
            Utils.setPageTitle(res.data.title);
            this.isContentCreator = UserModule.user_id == res.data.creator;
            // #ifdef MP-WEIXIN
            if (this.wxAuth && this.isPhone) {
                // #endif
                if (Utils.isInApp()) {
                    const viewRes = await getContentPer({ content_id: this.content_id });
                    if (viewRes.data.is_view === 0 && !this.isContentCreator) {
                        return uni.redirectTo({
                            url: `/pagesC/contentDetails/contentDetails?content_id=${this.content_id}&is_view=${viewRes.data.is_view}`,
                        });
                    }
                }
                // #ifdef MP-WEIXIN
            }
            // #endif
            if (!this.isContentCreator && (res.data.report_status === 2 || res.data.audit_status === 3)) {
                uni.redirectTo({ url: `/pagesC/contentDetails/contentDetails?content_id=${this.content_id}` });
            }
            if (res.data.is_delete === 1) {
                return uni.redirectTo({
                    url: `/pagesC/contentDetails/contentDetails?content_id=${this.content_id}&is_delete=${res.data.is_delete}`,
                });
            }

            this.data = {
                ...res.data,
                isPraise: res.data.is_praise,
                praiseNum: res.data.praise,
                isFavorite: res.data.is_favorite,
                favoriteNum: res.data.favorite,
            };
            this.data.audit_status === 2 && createBrowse({ content_id: this.content_id });

            (this.$refs.videoPlayRef as any).init(this.data, this.topic_id);
        }
    }

    // 举报和举报弹窗
    public reportType: ReportType = 'detail';
    public operatePopupOpen: boolean = false;

    handleReport(type: ReportType): void {
        this.reportType = type;
        this.operatePopupOpen = true;
    }

    handleLongpress() {
        this.reportType = 'comment';
        this.operatePopupOpen = true;
    }

    handleOperatePopupConfirm() {
        uni.navigateTo({ url: `/pagesA/contentReport/contentReport?reportType=${this.reportType}` });
        this.operatePopupOpen = false;
    }

    async handleShare(item) {
        // #ifdef H5
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FORWARD_BUTTON_CLICK,
        });

        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/contentVideoDetails/contentVideoDetails?content_id=${item.content_id}`;

        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };
        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            const params = {
                target: 'wechat,weixin_circle,qq,sina',
                type: 'web',
                content: {
                    url: url + '&link=' + res.data,
                    share_image: item.cover_image + '?x-oss-process=image/resize,w_200',
                    share_title: item.title,
                    share_desc: item.body,
                },
            };
            Utils.messageChannel('share', params);
        });
        // #endif
    }

    public isCompleted: boolean = false;

    toHome() {
        // 暂停VideoIntegralTimeoutTips的动画和计时
        if (this.$refs.videoIntegralTimeoutTipsRef) {
            this.$refs.videoIntegralTimeoutTipsRef.pauseAnimationAndTimer();
        }

        if (!this.awardPopup) {
            console.log('isCompleted', this.isCompleted);
            if (this.isCompleted !== true) {
                this.awardPopup = true;
                return;
            }
        }
        if (this.share) {
            uni.reLaunch({
                url: '/pages/contents/contents',
            });
        } else {
            Utils.goBack();
        }
    }
}
</script>

<style lang="scss" scoped>
.contentDetails-container {
    height: 100vh;
    width: 100vw;
    position: relative;
    // background-color: #000;

    // background: $background-ffffff;

    ::v-deep .narBar {
        width: 50vw;

        /* .content {
        } */
    }

    .head-menu {
        position: fixed;
        background: $background-ffffff;
        width: 100vw;
        top: 0rpx;
        z-index: 2;
        transition: all ease-in-out 0.1s;
    }

    .status-bar {
        width: 100vw;
        height: 92rpx;
        @include flex(row, center, center);
        position: fixed;
        z-index: 100;
        font-size: 27rpx;
    }

    .content {
        width: 100vw;

        .indicator-num {
            width: 146rpx;
            height: 66rpx;
            border-radius: 54rpx;
            background: $contentDetails-indicator-num;
            color: $background-ffffff;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 66rpx;
            font-size: 28rpx;

            .indicator-num__text-num {
                color: #ffffff;
                font-size: 28rpx;
                font-weight: 500;
            }

            .line {
                font-size: 22rpx;
                height: 38rpx;
                overflow: hidden;
                color: #ffffff;
                width: 16rpx;
                line-height: 38rpx;
                transform: translateY(-2rpx);
                font-weight: 500;
            }

            .indicator-num__text {
                color: #ffffff;
                margin-right: 4rpx;
                font-size: 28rpx;
                font-weight: 500;
            }
        }
    }

    .detail-desc {
        background: $background-ffffff;

        .title {
            padding: 46rpx 30rpx 30rpx 30rpx;
            font-size: 30rpx;
            word-break: break-all;
            font-weight: 500;
            color: $text-color-primary;
            line-height: 40rpx;
        }

        .message {
            padding: 0rpx 42rpx 30rpx 30rpx;
            font-size: 26rpx;
            word-break: break-all;
            line-height: 42rpx;
            color: #777777;
            letter-spacing: 0;
            text-align: justify;
            height: max-content;

            ::v-deep ._root {
                overflow: visible !important;
            }
        }

        .operate {
            padding: 0 30rpx;
            font-size: 23rpx;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            color: $text-color-secondary;

            text {
                margin-right: auto;
            }

            img {
                width: 38rpx;
                height: 38rpx;
            }

            .share-btn {
                margin: 0 38rpx 0 0;
                padding: 0;
                border: none;
                @include flex($justify: center, $align: center);
            }
        }
    }

    .comment {
        padding: 62rpx 30rpx 0 30rpx;

        &-empty {
            padding: 130rpx 0 104rpx 0;

            &-title {
                font-size: 28rpx;
                color: $text-color-disable;
                line-height: 38rpx;
                text-align: center;
            }

            &-review {
                font-size: 28rpx;
                color: $brand-color-btn-text;
                line-height: 38rpx;
                text-align: center;
                margin-top: 54rpx;
            }
        }

        &-title {
            font-weight: 500;
            font-size: 32rpx;
            color: $text-color-regular;
            line-height: 40rpx;
        }

        &-type {
            @include flex($justify: flex-start, $align: center);
            margin: 30rpx 0;

            &--item {
                width: 142rpx;
                height: 54rpx;
                border-radius: 154rpx 154rpx 154rpx 154rpx;
                border: 2rpx solid $uni-border-color-gray;
                font-size: 28rpx;
                color: $text-color-gray;
                margin-right: 30rpx;
                @include flex;

                &.active {
                    color: $text-color-primary;
                    border: 2rpx solid $text-color-primary;
                }
            }
        }

        &-item {
            padding: 20rpx 0 34rpx 0;

            .user {
                @include flex($justify: flex-start, $align: center);
                font-size: 28rpx;
                margin-bottom: 12rpx;

                &-avatar {
                    width: 46rpx;
                    height: 46rpx;
                    border-radius: 50%;
                }

                &-name {
                    color: $text-color-regular;
                    margin: 0 12rpx 0 16rpx;
                }

                &-auther {
                    width: 66rpx;
                    height: 38rpx;
                }
            }

            .text {
                font-size: 26rpx;
                color: $text-color-secondary;
                line-height: 38rpx;
                word-break: break-all;
                margin-bottom: 16rpx;
            }

            .operate {
                font-size: 24rpx;
                margin-top: 20rpx;
                @include flex($justify: flex-start, $align: center);

                &-day,
                &-time,
                &-like {
                    color: $text-color-secondary;
                }

                &-time {
                    margin-left: 10rpx;
                }

                &-reply {
                    color: $text-color-primary;
                    margin: 0 auto 0 30rpx;
                }

                img {
                    width: 30rpx;
                    height: 30rpx;
                    margin-right: 8rpx;
                }
            }
        }

        &-end {
            padding: 130rpx 0 104rpx 0;
            font-size: 24rpx;
            color: $text-color-disable;
            text-align: center;
        }
    }

    .bottom {
        height: calc(122rpx + env(safe-area-inset-bottom) / 2);
        padding: 0 30rpx calc(env(safe-area-inset-bottom) / 2) 30rpx;
        position: sticky;
        bottom: 0;
        border-top: 2rpx solid $uni-bg-color-modal;
        width: 100%;
        background: $background-ffffff;
        @include flex($justify: flex-start, $align: center);

        &-publish {
            width: 246rpx;
            height: 69rpx;
            background: #f4f4f4;
            border-radius: 194rpx;
            @include flex($justify: center, $align: center);
            margin-right: auto;

            img {
                width: 36rpx;
                height: 36rpx;
            }

            text {
                font-size: 28rpx;
                color: $text-color-disable;
                margin-left: 16rpx;
            }
        }

        &-operate {
            @include flex($justify: flex-start, $align: center);
            margin-left: 38rpx;
            font-size: 28rpx;
            color: $text-color-regular;

            img {
                width: 46rpx;
                height: 46rpx;
                margin-right: 8rpx;
            }
        }
    }
}
.award-popup-content {
    width: 646rpx;
    height: 640rpx;
    border-radius: 40rpx;
    opacity: 1;
    background: linear-gradient(152deg, #fff5cd 9%, #ffffff 92%);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .award-title {
        width: 429rpx;
        height: 58rpx;
        font-family: MiSans;
        font-size: 44rpx;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ca6d36;
        margin-top: 160rpx;
    }
    .award-popup-header {
        width: 100%;
        height: 90rpx;
        position: absolute;
        left: 50%;
        top: -174rpx;
        transform: translateX(-50%);

        .title-img {
            width: 402rpx;
            height: 352rpx;
            position: absolute;
            left: 50%;
            top: -20rpx;
            transform: translateX(-50%);
        }

        .line-img {
            width: 260rpx;
            height: 60rpx;
            position: absolute;
            right: 140rpx;
            bottom: 0;
        }
    }
    .award-popup-body {
        display: flex;
        align-items: flex-end;
        gap: 12rpx;
        margin-top: 56rpx;
        font-family: MiSans;
        font-size: 56rpx;
        font-weight: 500;
        line-height: 100%;
        text-align: center;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ff2300;
        .award-num-front {
            width: 35rpx;
            height: 56rpx;
        }
        .award-num-mid {
            height: 92rpx;
            font-size: 92rpx;
        }
        .award-num-back {
            width: 112rpx;
            height: 56rpx;
        }
        text {
            display: flex;
            align-items: flex-end;
        }
    }
    .award-continue {
        margin-top: 48rpx;
        width: 496rpx;
        height: 106rpx;
        border-radius: 90px;
        background: linear-gradient(302deg, #fd5922 17%, #ff8826 42%, #ffb547 65%);
        font-family: MiSans;
        font-size: 38rpx;
        font-weight: 500;
        line-height: 106rpx;
        text-align: center;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ffffff;
    }
    .award-exit-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 195rpx;
        margin-top: 40rpx;
    }
    .award-exit {
        height: 34rpx;
        font-family: MiSans;
        font-size: 26rpx;
        font-weight: 500;
        line-height: 34rpx;
        text-align: center;
        background: none;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #a6a6a6;
    }
    .award-change {
        opacity: 1;
        font-family: MiSans;
        font-size: 26rpx;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ff2300;
    }
    .award-popup-close {
        width: 52rpx;
        height: 52rpx;
        opacity: 1;
        box-sizing: border-box;
        border: 2rpx solid #ffffff;
        border-radius: 50%;
        position: absolute;
        left: 50%;
        bottom: -90rpx;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        justify-content: center;

        image {
            width: 40rpx;
            height: 40rpx;
        }
    }
}
</style>
