<template>
  <!-- 活动区域 -->
    <!-- 折扣活动区域 -->
    <view class="discount-section">
      <view class="section-header" @click="handleMoreDiscounts">
        <text class="section-title" >浏览更多折购活动</text>
        <view class="more-icon">
          <image class="more-icon-img" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689af235c41358030083579.png" mode="widthFix"></image>
        </view>
      </view>
      <!-- 折扣类型 -->
      <view class="discount-types">
        <u-scroll-list
          :indicator="false"
          :indicatorActiveColor="'#DBC49A'"
          :indicatorInactiveColor="'#EEEEEE'"
          :indicatorWidth="46.15"
          :indicatorHeight="7.69"
          :indicatorBarRadius="98.08"
          :indicatorBarStyle="{ margin: '16rpx auto 0' }"
          :showScrollbar="false"
        >
          <view
              @tap.stop="navigateToPageSale(service)"
              @click.stop="navigateToPageSale(service)"
              class="discount-item"
              v-for="service in ServerList"
              :key="service.type"
          >
              <view class="discount-icon">
                <image class="discount-icon-img" :src="service.icon"></image>
              </view>
              <view class="discount-text">{{ service.text }}</view>
              <image v-if="service.hover_icon" class="service_item_hover_icon" :src="service.hover_icon"></image>
          </view>
        </u-scroll-list>
      </view>
      <!-- 产品分类 -->
      <view class="product-categories">
        <u-scroll-list
            :indicator="false"
            :indicatorActiveColor="'#DBC49A'"
            :indicatorInactiveColor="'#EEEEEE'"
            :indicatorWidth="46.15"
            :indicatorHeight="7.69"
            :indicatorBarRadius="98.08"
            :indicatorBarStyle="{margin: '16rpx auto 0'}"
            :showScrollbar="false"
        >
            <view
              @tap.stop="navigateToPageCategory(index, item, 'image')"
              @click.stop="navigateToPageCategory(index, item, 'image')"
              class="category-item" v-for="(item, index) in BrandTypeLists" :key="item.tid"
            >
              <view class="category-icon">
                <image class="category-icon-img" :src="item.tag_img" mode="contain"></image>
              </view>
              <view class="category-text">{{ item.name }}</view>
              <image v-if="item.hover_icon" class="service_item_hover_icon" :src="item.hover_icon"></image>
            </view>
        </u-scroll-list>
      </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import { getTagList } from '@/http/requestGo/community';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import { mineInfo } from '@/http/mine';

@Component({
    components: {
    }
})
export default class tabListBar extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Array, default: 0 })
    readonly BrandTypeLists!: Array<any>;

    public ServerList = []

    async created() {
      await this.fetchTagList();
    }

    // 获取折扣列表
    async fetchTagList() {
      try {
          const version = AppModule.VersionCode;
          const res: any = await getTagList({ type: 42, page_size: 100, is_show: 1, tag_version: `${version}` });
          // 根据接口返回的数据结构取 list
          const tagList = (res?.data?.list || res?.data || []) as any[];
          // 仅取 item.version 不超过当前版本的条目
          const validList = tagList.filter((item) => {
              const v = Number(item.version || 0);
              return v !== 0 && v <= Number(version);
          });

          // 找到 <= version 的最大版本号
          const maxVersion = validList.reduce((max, item) => {
              const v = Number(item.version);
              return v > max ? v : max;
          }, -Infinity);

          const extraList = validList
              .filter((item) => Number(item.version) === maxVersion)
              .map((item) => ({
                  icon: item.icon || '',
                  text: item.name || '',
                  url: item.jump_url || '',
                  type: item.jump_type || '',
                  version: item.version || '',
                  // 接口返回 is_show 值为 "1" 或 "2"，此处统一转成字符串便于后续比较
                  isShow: String(item.is_show || ''),
              }));
          // 仅保留 isShow 为 "1" 的数据
          const visibleExtraList = extraList.filter((extra) => extra.isShow === '1');
          // 过滤掉名称(text)已存在的服务项，避免重复追加
          const uniqueExtraList = visibleExtraList.filter(
              (extra) => !this.ServerList.some((service) => service.text === extra.text),
          );
          this.ServerList = [...this.ServerList, ...uniqueExtraList];
      } catch (error) {
          /* eslint-disable no-console */
          console.error('获取服务列表失败', error);
      }
    }

    // 切换全部活动和产品
    handleMoreDiscounts() {
      this.$emit('changeMoreActivity');
    }

    // 跳转折扣页面
    navigateToPageSale(item) {
        const { type, url } = item;
         Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .HOME_BANNER_BANNER_EXPOSURE,
            name: item.text,
        });

        if (['manager', 6].includes(type)) { // 在线客服
            Utils.decryptContact('', BuType.BuType_NONE);
        } else if (['device', 10].includes(type)) { // 设备
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        } else if (['chuxing', 9].includes(type)) { // 出行
            Utils.newMessageChannel('PAGE', 'external', { path: `${url}` });
        } else if (['content', 8].includes(type)) { // 觅友空间
            Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' });
        } else if (type === 'ambassador') {
            mineInfo().then(response => {
                const { is_employee } = response
                const isEmployee = is_employee === 1
                if (isEmployee) {
                    Utils.navigateTo(`/pagesC/ambassador/detail/index`);
                } else {
                    Utils.navigateTo(`/pagesC/ambassador/login/index`);
                }
            })
        } else {
            Utils.navigateTo(`${url}`);
        }
    }

    // 跳转产品分类页面
    navigateToPageCategory(index: number, item: any, falg?: string) {
      this.$emit('switchMenu', index, item, falg);
    }
}
</script>

<style lang="scss" scoped>
.discount-section {
  background: rgba(255, 255, 255, 0.86);
  border-radius: 20rpx;
  padding: 30rpx 30rpx 0rpx 30rpx;
  margin: 0 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);

    .section-title {
      font-size: 32rpx;
      color: #121212;
    }

    .more-icon {
      display: flex;
      align-items: center;

      .more-icon-img {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .discount-types {
    // display: flex;
    // justify-content: space-between;
    margin-bottom: 30rpx;

    .discount-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 126rpx;
      // height: 126rpx;
      gap: 12rpx;
      margin-right: 6rpx;

      .discount-icon {
        width: 88rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .discount-icon-img {
          width: 88rpx;
          height: 88rpx;
          object-fit: cover;
        }
      }

      .discount-text {
        width: 126rpx;
        color: #333;
        font-size: 24rpx;
        text-align: center;
      }
    }
  }

  .product-categories {

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;
      width: 126rpx;
      // height: 126rpx;
      margin-right: 6rpx;

      .category-icon {
        width: 88rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .category-icon-img {
          width: 88rpx;
          height: 88rpx;
          object-fit: cover;
        }
      }

      .category-text {
        width: 126rpx;
        color: #333;
        font-size: 24rpx;
        text-align: center;
      }
    }
  }
}
</style>

