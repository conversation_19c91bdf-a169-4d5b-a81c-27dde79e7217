<template>
    <u-popup
        :show="isShow"
        class="claimPrizePopup"
        mode="center"
        :round="24"
        @close="closePopup"
        @open="clearData"
        :bgColor="'transparent'"
        :closeOnClickOverlay="false"
    >
        <view class="claimPrizeContent">
            <view class="claimPrizeContent-title">
                <text class="title">报名信息填写</text>
                <text class="subTitle">请确认信息填写无误，提交后信息将不能修改</text>
            </view>
            <u--form
                class="form"
                labelPosition="top"
                labelWidth="120"
                :model="receiptData"
                :rules="rules"
                ref="uForm"
                :labelStyle="{
                    color: '#3d3d3d',
                    fontSize: '24rpx',
                    marginLeft: '12rpx',
                    marginBottom: '12rpx',
                }"
                errorType="toast"
            >
                <u-form-item prop="userInfo.name" label="姓名" required>
                    <u--input
                        customStyle="background: #ffffff; border-radius: 12rpx;height: 84rpx;padding:24rpx;"
                        class="input"
                        v-model="receiptData.userInfo.name"
                        placeholder="请输入您的姓名"
                        fontSize="24rpx"
                        border="none"
                    ></u--input>
                </u-form-item>
                <u-form-item prop="userInfo.phone" label="手机号" labelPosition="top" required>
                    <u--input
                        customStyle="background: #ffffff; border-radius: 12rpx;height: 84rpx;padding:24rpx;"
                        class="input"
                        v-model="receiptData.userInfo.phone"
                        placeholder="请输入您的手机号"
                        fontSize="24rpx"
                        border="none"
                    ></u--input>
                </u-form-item>
                <u-form-item prop="userInfo.address" label="地址" labelPosition="top" required>
                    <u--input
                        customStyle="background: #ffffff; border-radius: 12rpx;height: 84rpx;padding:24rpx;"
                        class="input"
                        v-model="receiptData.userInfo.address"
                        placeholder="请输入您的地址"
                        fontSize="24rpx"
                        border="none"
                    ></u--input>
                </u-form-item>
            </u--form>
            <view class="btn" @click="submit"> </view>
            <view class="closeBtn" @click="closePopup"> </view>
        </view>
    </u-popup>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { submitgolddraw } from '@/http/doubleEleven';
import Utils from '@/common/Utils';

@Component({
    name: 'claimPrizePopup',
})
export default class ClaimPrizePopup extends Vue {
    $refs!: {
        uForm: any;
    };

    @Prop({ default: false }) isShow: boolean;
    @Prop({ default: '' }) couponId: string;
    public receiptData: any = {
        userInfo: {
            name: '',
            phone: '',
            address: '',
        },
    };

    // 修正：rules结构需为扁平结构，prop为'userInfo.name'等
    public rules = {
        'userInfo.name': [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        'userInfo.phone': [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
                validator: (rule: any, value: any, callback: any) => this.validatePhone(rule, value, callback),
                trigger: 'blur',
            },
        ],
        'userInfo.address': [{ required: true, message: '请输入地址', trigger: 'blur' }],
    };

    // 自定义验证方法
    validatePhone(rule: any, value: any, callback: any) {
        if (!/^1[3-9]\d{9}$/.test(value)) {
            callback(new Error('请输入正确的手机号'));
        } else {
            callback();
        }
    }

    clearData() {
        this.receiptData = {
            userInfo: {
                name: '',
                phone: '',
                address: '',
            },
        };
    }

    mounted() {
        // uView2.x 需在 mounted 后设置 rules
        this.$nextTick(() => {
            if (this.$refs.uForm && this.$refs.uForm.setRules) {
                this.$refs.uForm.setRules(this.rules);
            }
        });
    }

    @Watch('isShow')
    onPopupShow(newVal: boolean) {
        if (newVal) {
            this.$nextTick(() => {
                if (this.$refs.uForm && this.$refs.uForm.setRules) {
                    this.$refs.uForm.setRules(this.rules);
                }
            });
        }
    }

    async submit() {
        if (!this.$refs.uForm) return;
        this.$refs.uForm.validate().then(async (valid: boolean) => {
            if (valid) {
                await submitgolddraw({
                    dpr_id: this.couponId,
                    realname: this.receiptData.userInfo.name,
                    phone: this.receiptData.userInfo.phone,
                    address: this.receiptData.userInfo.address,
                    status: 0, // 未领取
                    type: 2, // 2 七夕
                });
                Utils.Toast('提交成功');
                setTimeout(() => {
                    this.closePopup();
                }, 200);
            } else {
                Utils.Toast('请填写完整信息');
            }
        });
    }

    closePopup() {
        this.$emit('closePopup');
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-form-item__body {
    padding: 10rpx 0 !important; /* 增加上下内边距 */
}
::v-deep .u-form-item__body__left__content__required {
    transform: translate(16rpx, -10rpx) !important;
    color: #f53f3f;
    font-size: 24.58rpx;
}
.claimPrizePopup {
    .claimPrizeContent {
        width: 610rpx;
        height: 860rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a84c2f1cc2c1180010754.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        position: relative;
        .claimPrizeContent-title {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            gap: 12rpx;
            padding: 66rpx 24rpx 20rpx;

            .title {
                color: #121212;
                font-size: 31.62rpx;
                font-weight: 500;
            }
            .subTitle {
                color: #ff3654;
                font-size: 21.08rpx;
            }
        }
        .form {
            padding: 0 24rpx;
            .form-item {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
                gap: 12rpx;
                .label {
                    color: #3d3d3d;
                    font-size: 24.58rpx;
                    text {
                        color: #f53f3f;
                    }
                }
                .input {
                    width: 100%;
                    height: 80rpx;
                    background: #ffffff;
                    border-radius: 12rpx;
                }
            }
        }
        .btn {
            position: absolute;
            bottom: 34px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 234px;
            height: 45px;
        }
        .closeBtn {
            position: absolute;
            right: 18rpx;
            top: 22rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            width: 44rpx;
            height: 44rpx;
        }
    }
}
</style>
