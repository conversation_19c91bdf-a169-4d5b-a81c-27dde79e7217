<template>
    <view v-if="!isProd">
        <image class="btn_img" @click="showPop" :src="currEnvImg" mode=""></image>
        <u-popup :show="show" v-prevent-scroll="show" mode="center" round="20" @close="close">
            <view class="content u-flex u-flex-col u-col-center u-row-left">
                <view class="title"> 请选择环境 </view>
                <u-radio-group placement="column" v-model="select">
                    <view class="u-flex u-col-center u-row-left">
                        <u-radio name="dev"> </u-radio>
                        <view class="tb u-flex">
                            <view class="td_left"> dev </view>
                            <view class="td_right">
                                <u-input border="none" fontSize="28rpx" v-model="devUrl" />
                            </view>
                        </view>
                    </view>
                    <view class="u-flex u-col-center u-row-left">
                        <u-radio name="test"> </u-radio>
                        <view class="tb u-flex">
                            <view class="td_left"> test </view>
                            <view class="td_right disable">
                                <u-input border="none" fontSize="20rpx" v-model="testUrl" disabled />
                            </view>
                        </view>
                    </view>
                </u-radio-group>
                <u-button :custom-style="{ marginTop: '35rpx', width: '300rpx' }" type="primary" @click="change">
                    确认
                </u-button>
            </view>
        </u-popup>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator';

@Component
export default class ChangeEnv extends Vue {
    public isProd: Boolean = true;
    private currEnv: String = process.env.NODE_ENV;
    public devUrl: String = 'http://************:8088';
    public testUrl: String = process.env.VUE_APP_BASE_URL;
    public select: String = '';
    public show: Boolean = false;
    private currUrl: String = this.testUrl;
    get currEnvImg(): String {
        let url = 'https://wpm-cdn.dreame.tech/images/202207/836046-1658979888276.png';

        switch (this.currEnv) {
            case 'development':
                url = 'https://wpm-cdn.dreame.tech/images/202207/050874-1658979954592.png';
                break;
            case 'dev':
                url = 'https://wpm-cdn.dreame.tech/images/202207/030939-1658979918657.png';
                break;
            case 'test':
                url = 'https://wpm-cdn.dreame.tech/images/202207/050874-1658979954592.png';
                break;
            case 'production':
                url = 'https://wpm-cdn.dreame.tech/images/202207/836046-1658979888276.png';
                break;
            default:
                url = 'https://wpm-cdn.dreame.tech/images/202207/836046-1658979888276.png';
                break;
        }
        return url;
    }

    @Watch('select')
    async selectChange(newVal: String, oldVal: Boolean) {
        if (newVal) {
            switch (newVal) {
                case 'dev':
                    this.currUrl = this.devUrl;
                    break;
                case 'test':
                    this.currUrl = this.testUrl;
                    break;
                default:
                    this.currUrl = this.testUrl;
                    break;
            }
        }
    }

    created() {
        this.isProd = process.env.NODE_ENV != 'development';
        const node_env = uni.getStorageSync('node_env');
        if (node_env) {
            this.currEnv = node_env.env === 'development' ? 'test' : node_env.env;
            if (this.currEnv === 'dev') {
                this.devUrl = node_env.BASE_URL;
            }
            this.currUrl = node_env.BASE_URL
        }
        this.select = this.currEnv;
    }

    showPop() {
        this.show = true;
    }

    change() {
        this.currEnv = this.select;
        uni.setStorage({
            key: 'node_env',
            data: {
                env: this.currEnv,
                BASE_URL: this.currUrl,
            },
            success: () => {
                uni.showToast({
                    title: '环境切换成功, 请重启小程序',
                    icon: 'none',
                    duration: 2500,
                    mask: true,
                });
            },
        });
    }

    close() {
        this.show = false;
    }
}
</script>

<style lang="scss">
.btn_img {
    position: fixed;
    top: 150rpx;
    right: 15rpx;
    z-index: 9999999;
    width: 100rpx;
    height: 100rpx;
}

.content {
    width: 660rpx;
    padding: 75rpx 45rpx 45rpx;

    .title {
        font-size: 42rpx;
        color: #121212;
        line-height: 56rpx;
        text-align: center;
        margin-bottom: 45rpx;
    }

    .tb {
        width: 100%;
        font-size: 28rpx;
        text-align: center;
        line-height: 66rpx;
        margin: 15rpx 0 15rpx 10rpx;

        .td_left {
            width: 80rpx;
            height: 66rpx;
            line-height: 64rpx;
            border: 1rpx solid #ccc;
            border-right: none;
        }
        .td_right {
            width: 420rpx;
            height: 66rpx;
            border: 1rpx solid #ccc;
            padding: 6rpx 10rpx 0;

            &.disable {
                background-color: #f6f6f6;
            }
        }
    }
}
</style>
