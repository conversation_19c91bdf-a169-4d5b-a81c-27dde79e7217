import http from './index';

/**
 * 修改购物车信息
 * id
 * num
 */
export function modifyCart(params: { id: number; num: number }) {
    return http.post('main/cart/modify', params);
}

/*
 * 添加购物车
 */
export function addShopCart(params) {
    return http.post('main/cart/add', { ...params });
}

/*
 * 组合购买添加购物车
 */
export function batchAddShopCart(params) {
    return http.post('main/cart/batch-add', params);
}
/*
 * 扫地机插件组合购买添加购物车
 */
export function pluginBatchAddShopCart(params) {
    return http.post('main/cart/plug-batch-add', params);
}

/**
 * 获取购物车列表
 */
export function getCartList() {
    return http.post('main/cart/list', {});
}

/**
 * 删除
 */
export function delShopCart(ids: string) {
    return http.post('main/cart/del', { ids });
}

/**
 * 查询选中商品总价
 */
export function getTotalPrice(gcombines) {
    return http.post('main/cart/use-card', { gcombines });
}

/**
 * 获取商品规格列表
 * @param gid
 * @returns
 */
export function getGoodsAtt(gid) {
    return http.post('main/goods/attr-info', { gid });
}

/**
 * 购物车校验商品
 * @param gid
 * @returns
 */
export function checkGoods(params) {
    return http.post('main/cart/check-goods', params);
}
