<template>
  <view class="total-layout u-flex u-col-bottom">
    <view class="u-flex u-col-center" :class="iniData.is_presale == 1 ? 'total-right-presale' : 'total-right'"
      :style="[totalStyle]">
      <text class="unit"
        :style="{ 'font-size': `${unitSize}rpx`, 'padding-right': `${unitRight}rpx`, 'font-weight': `${unitWeight}`, 'color': `${uniColor}`, 'line-height': `${unitLineHeight}rpx` }">￥</text>
      <text v-if="!cashierShow" :style="{ color: color ? color : (iniData.is_presale == 1 ? '#8C6533' : '#AB8C5E') }">{{
        totalPrice }}</text>
      <view v-if="cashierShow" class="u-flex u-col-bottom">
        <text style="color: #AB8C5E;font-size: 62rpx">{{
          totalPrice.split('.')[0] }}.</text>
        <text style="margin-bottom: 6rpx;">{{ totalPrice.split('.')[1] }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
@Component({
  filters: {
    priceStyle: (price: string, index: number) => {
      const p = Number(price).toFixed(2);
      let _arr = [p, ''];
      try {
        _arr = p.split('.');
        _arr[_arr.length - 1] = `.${_arr[_arr.length - 1]}`;
      } catch (e) { }
      return _arr[index];
    },
  },
})
export default class CustomAmount extends Vue {
  @Prop({
    type: Object,
    default: () => ({
      is_ini: 0,
      gini_tag: '',
      price: 0,
      gini_etime: '',
      is_presale: 0,
    }),
  })
  readonly iniData;

  @Prop({ type: Object, default: null })
  readonly totalStyle!: Object;

  @Prop({ type: String, default: '' })
  readonly color!: String;

  @Prop({ type: [String, Number], default: '0.00' })
  readonly totalPrice!: String;

  @Prop({ type: String, default: '24' })
  readonly unitSize!: String;

  @Prop({ type: String, default: '0' })
  readonly unitRight!: String;

  @Prop({ type: String, default: '' })
  readonly unitWeight!: String;

  @Prop({ type: String, default: '' })
  readonly unitLineHeight!: String;

  @Prop({ type: String, default: '' })
  readonly uniColor!: String;

  @Prop({ type: Boolean, default: false })
  readonly cashierShow;
}
</script>

<style lang="scss" scoped>
.total-layout {
  .total-right {
    font-size: 46rpx;
    font-weight: bold;
    color: $fill-color-primary-active;
  }

  .total-right-presale {
    font-size: 46rpx;
    font-weight: bold;
    color: $brand-color-btn-text;
  }
}
</style>
