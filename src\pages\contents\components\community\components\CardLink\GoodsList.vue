<template>
    <u-popup :show="show" mode="bottom" safeAreaInsetBottom :customStyle="{ 'border-radius': '20rpx 20rpx 0 0' }">
        <view class="pop-title">
            {{ goods_ids.length }}件商品
            <image @click="toVisible()" class="pop-close" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889f905dd7269070012111.png" />
        </view>
        <scroll-view scroll-y style="max-height: calc(100vh - 400rpx);">
            <view v-for="(goods, key) in linkGoodsList" :key="key" class="goods-container u-flex">
                <view class="image-container">
                    <image class="goods-image" :src="goods.cover_image" />
                </view>
                <view class="info-container">
                    <view class="goods-name">{{ goods.name }}</view>
                    <view class="u-flex u-flex-between">
                        <view class="goods-price">
                            <text class="goods-price-icon">¥</text>
                            <text class="goods-price-amount">{{ goods.price }}</text>
                            <text class="goods-sales">已售 {{  goods.sales }}</text>
                        </view>
                        <view class="btn-buy" @click="buyGoods(goods)">去购买</view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </u-popup>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
import { goodsInfo } from '@/http/goods';
import { IGoods } from '@/store/modules/goods'

@Component
export default class goodsList extends Vue {
    public show = false
    public linkGoodsList: Array<IGoods> = []
    public goods_ids: Array<number> = []

    async toggleVisible(ids) {
        if (ids) {
            this.goods_ids = ids
        }
        if (!this.show) {
            await this.getLinkGoodsList()
        }
        this.show = !this.show
    }

    toVisible() {
        this.show = !this.show
    }

    async getLinkGoodsList() {
        const linkGoodsList = await Promise.all(
            this.goods_ids.map(goodsId => goodsInfo({ gid: goodsId }))
        )
        this.linkGoodsList = linkGoodsList.map(({ goods }) => goods)
    }

    buyGoods(goods: IGoods) {
        this.$emit('buyGoods', { gid: goods.gid, from: 'GoodsList' })
    }
}
</script>

<style lang="scss" scoped>
.pop-title {
    position: relative;
    margin-top: 24rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #121212;
    text-align: center;

    .pop-close {
        position: absolute;
        width: 48rpx;
        height: 48rpx;
        top: 16rpx;
        right: 16rpx;
    }
}
.goods-container {
    margin-top: 46rpx;
    padding: 0 32rpx 24rpx;

    + .margin-top {
        margin-top: 48rpx;
    }

    .image-container {
        width: 208rpx;
        height: 208rpx;
        border-radius: 16rpx;
        overflow: hidden;
        background: #EFEFEF;

        .goods-image {
            width: 208rpx;
            height: 208rpx;
        }
    }

    .info-container {
        margin-left: 24rpx;
        flex: 1;
        display: flex;
        height: 208rpx;
        flex-direction: column;
        justify-content: space-between;
    }

    .goods-name {
        font-size: 28rpx;
        color: #121212;
    }

    .goods-price {
        margin-right: auto;

        .goods-price-icon {
            font-size: 24rpx;
            line-height: 32rpx;
            color: #404040;
        }
        .goods-price-amount {
            margin-right: 12rpx;
            font-size: 32rpx;
            line-height: 44rpx;
        }

        .goods-sales {
            font-size: 24rpx;
            line-height: 32rpx;;
            color: #A6A6A6;
        }
    }

    .btn-buy {
        width: 136rpx;
        height: 56rpx;
        font-size: 28rpx;
        color: #8C6533;
        line-height: 52rpx;
        text-align: center;
        border-radius: 56rpx;
        border: 2rpx solid #C2A271;
    }
}
</style>
