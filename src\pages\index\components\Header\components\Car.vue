<template>
    <view class="car-wrap">
        <view class="car-item" @click="handleCar">
            <image style="width: 40rpx; height:40rpx;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689adf291ba961130011724.png"></image>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component
export default class CoinPoints extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    handleCar() {
        Utils.navigateTo('/pagesA/cart/cart');
        // Utils.navigateTo('/pagesC/purchase/purchase');
        // Utils.navigateTo('/pagesC/goldCoins/goldCoins');
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }
}
</script>
<style lang="scss" scoped>
.car-wrap {
    display: flex;
    align-items: center;
    gap: 30rpx;
    height: 114rpx;
}
.car-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 28rpx;
}

</style>
