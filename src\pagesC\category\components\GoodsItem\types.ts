/**
 * 商品卡片组件相关类型定义
 */

// 商品信息接口
export interface GoodsItemInfo {
    /** 商品ID */
    id: string | number;
    /** 商品标题 */
    title: string;
    /** 商品图片URL */
    image: string;
    /** 商品规格描述 */
    specs?: string;
    /** 当前价格 */
    currentPrice: number | string;
    /** 原价（可选） */
    originalPrice?: number | string;
    /** 优惠券文本（可选） */
    couponText?: string;
    /** 跳转链接（可选） */
    url?: string;
    /** 商品ID（用于跳转商品详情） */
    gid?: string | number;
    /** 商品名称（兼容现有接口） */
    name?: string;
    /** 商品价格（兼容现有接口） */
    price?: number | string;
    /** 商品封面图（兼容现有接口） */
    cover_image?: string;
    /** 是否显示（可选） */
    visible?: boolean;
    /** 排序权重（可选） */
    sort?: number;
    /** 扩展数据 */
    extra?: Record<string, any>;
}

// 点击事件参数
export interface ProductCardClickEvent {
    /** 商品信息 */
    product: GoodsItemInfo;
    /** 事件类型 */
    type: 'card' | 'image' | 'price';
    /** 点击位置索引（可选） */
    index?: number;
}

// 组件Props接口
export interface GoodsItemProps {
    /** 商品信息 */
    productInfo: GoodsItemInfo;
    /** 是否可点击 */
    clickable: boolean;
}

// 默认商品数据示例
export const DEFAULT_GOODS_ITEM: GoodsItemInfo = {
    id: 1,
    title: '系列玩偶kawaii',
    image: 'https://example.com/product.jpg',
    specs: '尺寸：XS、S、M',
    currentPrice: 299,
    originalPrice: 499,
    couponText: '起券后价',
    gid: '1001'
};

// 埋点事件类型
export interface GoodsItemTrackEvent {
    /** 商品ID */
    product_id: string | number;
    /** 商品标题 */
    product_title: string;
    /** 当前价格 */
    current_price: number | string;
    /** 原价 */
    original_price: string;
    /** 点击位置 */
    position?: number;
    /** 页面来源 */
    page_source?: string;
}
