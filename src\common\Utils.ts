import Vue from 'vue';
import { UserModule, ILog } from '@/store/modules/user';
import { phoneDecode } from '@/http/serve';
import { getRequest } from '@/http/index';
import { AppModule } from '@/store/modules/app';
import { dataCallback } from '@/http/user';
import { getTaskInfo, doGoldTask } from '@/http/vip';
import Constants from '@/common/Constants';
import md5Libs from '@/utils/md5.js';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import { getShortLink } from '@/http/goods';
// import Countly from 'countly-sdk-web'
interface phoneParam {
    iv: string;
    encryptedData: string;
    code: string;
}

interface ShareContent {
    url: string;
    image: string;
    title: string;
    desc: string;
}

interface ShareGoods {
    name: string;
    desc: string;
    image: string;
    price: string;
    priceColor?: string;
    descColor?: string;
    imageBg?: string; // 卡片背景图
    badge?: string; // 角标
}

interface ShareExtras {
    id?: string;
    type: 'goods' | 'activity' | 'content' | 'other';
    copyText?: string;
    goods?: Partial<ShareGoods>;
}

interface ShareConfig {
    target: string;
    link: string;
    jumpLink: string;
}

interface shareConfigOption {
    key: string;
    icon:string;
    text:string;
}

interface CardShareData {
    content: ShareContent;
    extras?: ShareExtras;
    options?: shareConfigOption[];
}

interface ShareLinkConfig {
    bizParam: Record<string, any>; // header参数
    shareSource: string; // 最终拼接到url
    shareTarget: string; // 最终拼接到url
    type: string; // 打开类型
    auth: boolean; // 是否在url拼接session、userid
    jumpLink: string; // 跳转地址 商城/App页面/第三方网页
}

let addressCache = null;
export default class Utils {
    constructor() {}

    // 时间转换
    public static timeFormat(time: any = Number(new Date().getTime()), fmt = 'yyyy/mm/dd', AaddZero = false) {
        if (/^\d+$/.test(time)) {
            // 如果为纯数字则为时间戳
            // 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
            if (time.toString().length == 10) time *= 1000;
        }
        const date = new Date(time);
        let ret;
        const opt = {
            'y+': date.getFullYear().toString(), // 年
            'm+': (date.getMonth() + 1).toString(), // 月
            'd+': date.getDate().toString(), // 日
            'h+': date.getHours().toString(), // 时
            'M+': date.getMinutes().toString(), // 分
            's+': date.getSeconds().toString(), // 秒
            'q+': Math.floor((date.getMonth() + 3) / 3).toString(), // 季度
            S: date.getMilliseconds().toString(), // 毫秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (const k in opt) {
            ret = new RegExp('(' + k + ')').exec(fmt);
            if (ret) {
                if (AaddZero) {
                    fmt = fmt.replace(ret[1], opt[k]);
                } else {
                    fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));
                }
            }
        }
        return fmt;
    }

    // 获取微信code
    public static getWxCode(): Promise<string> {
        return new Promise((resolve, reject) => {
            uni.login({
                provider: 'weixin',
                success: (res) => {
                    resolve(res.code);
                },
                fail: (err) => {
                    reject(err.authErrorScope);
                },
            });
        });
    }

    /**
     * 手机号授权: 可选择是否进行会员注册操作, 默认进行
     * @param { phoneParam } phoneParam 手机号授权加密信息
     * @param { boolean } isRegister 是否进行会员注册, 默认为true
     * @returns
     */
    public static async getUserPhone(phoneParam: phoneParam, isRegister: boolean = true): Promise<any> {
        try {
            const params: any = {
                iv: encodeURIComponent(phoneParam.iv),
                encryptedData: encodeURIComponent(phoneParam.encryptedData),
                openudid: phoneParam.code,
                r_code: AppModule.recEncrypt || '',
                shop_code: AppModule.shopCode || '',
            };
            if (isRegister) {
                // 执行会员注册操作
                await UserModule.asyncWxPhone(params);
                return Promise.resolve();
            } else {
                const { phone } = await phoneDecode(params);
                return Promise.resolve(phone);
            }
        } catch (error) {
            return Promise.reject(error);
        }
    }

    // 获取用户信息
    public static getUserInfo(): Promise<any> {
        return new Promise((resolve, reject) => {
            (wx as any).getUserProfile({
                desc: '登录',
                success: async (res) => {
                    resolve(res.userInfo);
                },
                fail: (err) => {
                    reject(err);
                },
            });
        });
    }

    public static reportEvent(event, obj = {}) {
        // #ifdef MP-WEIXIN
        const params = Object.assign({ euid: '', union: '' }, obj);
        wx.reportEvent(event, params);
        // #endif
    }

    // 检查登录是否过期
    public static checkSession() {
        return new Promise((resolve, reject) => {
            wx.checkSession({
                success(res) {
                    if (res.errMsg == 'checkSession:ok') {
                        resolve(true);
                    }
                    resolve(false);
                },
                fail(err) {
                    reject(err);
                },
            });
        });
    }

    // 获取任务信息
    public static async getTaskInfo(code: String) {
        if (!UserModule.isPhone) return Promise.resolve(true);
        try {
            const res = await getTaskInfo({ taskCode: code });
            if (!res.completed) {
                this.Toast(
                    '完成任务，获得' +
                        `${code == 'mall/dreame/view_goods' ? '0.50元消费金,' : ''}${
                            res.grow ? `${res.grow}觅享分` : ''
                        }${res.gold ? `,${res.gold}金币` : ''}`,
                );
                return Promise.resolve(false);
            } else {
                return Promise.resolve(true);
            }
        } catch (error) {
            return Promise.resolve(true);
        }
    }

    // 提示
    public static Toast(message, duration = 2000, icon = null, style = '') {
        // #ifdef H5
        const ComponentClass = Vue.component('CustomToast');
        const component = new ComponentClass();
        component.$mount();
        document.body.appendChild(component.$el);
        icon = icon === 'none' ? false : icon;
        // @ts-ignore
        component.show(message, duration, icon, style);
        component.$on('hide', () => {
            component.$destroy();
        });
        // // #endif

        // #ifdef MP-WEIXIN
        message = { message, duration, icon, style };
        AppModule.setToast({ isShow: true, message });
        // #endif
    }

    // 客服选择框
    public static CustomerServicePicker() {
        // #ifdef H5
        // 获取所有 class 为 'customer-service-picker' 的元素
        const popups = document.querySelectorAll('.customer-service-picker');
        // 遍历所有元素并从 document.body 中移除
        popups.forEach((popup) => {
            popup.parentNode.removeChild(popup);
        });
        const ComponentClass = Vue.component('CustomerServicePicker');
        const component = new ComponentClass();
        component.$mount();
        document.body.appendChild(component.$el);
        // @ts-ignore
        component.show();
        component.$on('hide', () => {
            component.$destroy();
        });
        // #endif

        AppModule.setCustomerService({ isShow: true });
    }

    // 图片处理url拼接
    public static getThumbImg(str, w, h = '') {
        return `${str}?x-oss-process=image/resize,m_fill,w_${w},h_${h || w}`;
    }

    /**
     * 文件流写入本地文件
     * arrayBuffer: 文件流
     * path: 文件路径
     * fileNmae: 文件名称
     * */
    public static writeFile(arrayBuffer, path = 'wx_code', fileName = 'code.png') {
        return new Promise((resolve, reject) => {
            const fs = wx.getFileSystemManager();
            try {
                fs.accessSync(`${wx.env.USER_DATA_PATH}/${path}`);
            } catch (e) {
                // 文件不存在创建文件
                fs.mkdirSync(`${wx.env.USER_DATA_PATH}/${path}`, false);
            }
            // 写入文件
            fs.writeFile({
                filePath: `${wx.env.USER_DATA_PATH}/${path}/${fileName}`,
                data: arrayBuffer,
                encoding: 'utf8',
                success(res) {
                    // 回调文件路径
                    resolve(`${wx.env.USER_DATA_PATH}/${path}/${fileName}`);
                },
                fail(err) {
                    console.error('writeFile err=', err);
                    reject(err);
                },
            });
        });
    }

    public static async getAddress() {
        const getP = getRequest('https://wpm-cdn.dreame.tech/files/202501/677629e07eee05200009862.json');
        const getC = getRequest('https://wpm-cdn.dreame.tech/files/202501/677629ce11fd00740009632.json');
        const getA = getRequest('https://wpm-cdn.dreame.tech/files/202501/677629bd0b3760460377091.json');
        // 生命周期内多次进入地址选择页面时, 使用缓存以避免重复请求
        if (!addressCache) {
            const [p, c, a] = await Promise.all([getP, getC, getA]);
            addressCache = { p, c, a };
        }
        return addressCache;
    }

    // 客服
    public static decryptContact(e, buType = BuType.BuType_OTHER, params = {}) {
        const serviceUrl = process.env.VUE_APP_CONTACT_TIANRUN;
        const userInfo = UserModule.userInfo;
        const customerFields = `{"客户名称":"${userInfo.nick || ''}","autoUpdate":1}`;
        const _url = `${serviceUrl}&tel=${userInfo.phone || ''}&customerFields=${encodeURIComponent(customerFields)}`;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_COUSTOMER_BUTTON_CLICK,
        });
        let url = '';
        const win: any = window;
        if (win.webkit || win.jsBridge || win.messageChannel || win.flutter_inappwebview) {
            // Utils.messageChannel('openOuterPage', _url);
            this.newMessageChannel('CUSTOMER', 'openCustomer', params);
        } else {
            url = `/pages/webView/webView?title=${encodeURIComponent('客服')}&web_url=${encodeURIComponent(
                encodeURIComponent(_url),
            )}`;
            this.navigateTo(url);
        }
    }

    // 根据商品获取bu客服分类
    public static getBuType(goods) {
        let buTye = BuType.BuType_NONE;
        // 产品判断
        const tids = [];
        for (let i = 0; i < goods.length; i++) {
            let itemIds = goods[i].tids || [];
            if (itemIds.includes('11')) {
                itemIds = ['11'];
            } else if (itemIds.includes('10')) {
                itemIds = ['10'];
            } else if (itemIds.includes('12')) {
                itemIds = ['12'];
            }
            tids.push(...itemIds);
        }

        // 剔除掉 非 11  10  12
        const tidsNotIncludeFilter = tids.filter((item) => !['11', '10', '12'].includes(item));

        // 获取 11 10  12
        const tidsIncludeFilter = tids.filter((item) => ['11', '10', '12'].includes(item));

        if (tids.length == 0) {
            buTye = BuType.BuType_NONE;
        } else if (tidsNotIncludeFilter.length > 0) {
            buTye = BuType.BuType_NONE;
        } else if (tidsIncludeFilter.length > 0) {
            // 都是11
            if (tidsIncludeFilter.every((item) => item === '11')) {
                buTye = BuType.BuType_SDJ;
            } else if (tidsIncludeFilter.every((item) => item === '10')) {
                // 都是10
                buTye = BuType.BuType_XDJ;
            } else if (tidsIncludeFilter.every((item) => item === '12')) {
                // 都是12
                buTye = BuType.BuType_XCQ;
            } else {
                buTye = BuType.BuType_NONE;
            }
        }
        return buTye;
    }

    // 获取bu客服分类
    public static getBuTypeByTids(tid) {
        let buTye = BuType.BuType_NONE;
        if (tid == '11') {
            buTye = BuType.BuType_SDJ;
        } else if (tid == '10') {
            buTye = BuType.BuType_XDJ;
        } else if (tid == '12') {
            buTye = BuType.BuType_XCQ;
        }
        return buTye;
    }

    // 分享上报
    public static getDataCallback(data) {
        try {
            dataCallback(data);
            console.log('内容详情分享上报成功');
        } catch (e) {
            console.error('内容详情分享上报失败 err=', e);
        }
    }

    public static scanCode() {
        this.messageChannel('navigation', { type: 'device', path: 'device/barcodeScan' });
        return new Promise((resolve, reject) => {
            const win: any = window;
            win.onBarCodeScan = (data) => {
                if (data.code === 0) {
                    resolve(data.result);
                } else {
                    reject({ data });
                }
            };
        });
    }

    public static getLocation() {
        if (AppModule.platform === 'ios') {
            this.messageChannel('getLocation', {});
            return new Promise((resolve, reject) => {
                const win: any = window;
                win.onAppMessage = (data) => {
                    if (data.type === 2 && data.code === 0) {
                        resolve(data.data);
                    } else {
                        reject({ data });
                    }
                    win.onAppMessage = null;
                };
            });
        } else {
            return new Promise((resolve, reject) => {
                uni.getLocation({
                    type: 'gcj02', // 返回可以用于wx.openLocation的经纬度
                    success: function (res) {
                        resolve({ lon: res.longitude, lat: res.latitude });
                    },
                    fail: (err) => {
                        reject(err);
                    },
                });
            });
        }
    }

    public static handleHideLoading(time: number = 0) {
        if (!time) {
            uni.hideLoading();
            return;
        }
        setTimeout(() => {
            uni.hideLoading();
        }, time);
    }

    public static goBack(backTrigger = undefined) {
        const pages = getCurrentPages();
        if (pages.length === 1) {
            // #ifdef MP-WEIXIN
            uni.reLaunch({ url: '/pages/index/index' });
            // #endif
            // #ifdef H5
            this.messageChannel('closeWebView', '');
            // #endif
            return true;
        } else {
            if (backTrigger) {
                uni.$emit(backTrigger);
                return true;
            } else {
                uni.navigateBack({});
                return true;
            }
        }
    }

    // 开启关闭h5键盘监听
    public static keyboardForObserver(val) {
        this.messageChannel('keyboardForObserver', String(val));
    }

    public static saveImage(tempFilePath, type = 'url') {
        this.messageChannel('saveImage', {
            type,
            content: tempFilePath,
        });
        return new Promise((resolve, reject) => {
            const win: any = window;
            win.onSaveImage = (data) => {
                if (data.code === 0) {
                    resolve('保存成功');
                } else {
                    resolve(`保存失败，${data.result} `);
                }
                win.onSaveImage = null;
            };
        });
    }

    // native支付
    public static nativePay(payload) {
        this.messageChannel('pay', payload);
        return new Promise((resolve) => {
            const win: any = window;
            win.onPayResponse = (data) => {
                resolve(data);
                win.onPayResponse = null;
            };
        });
    }

    public static getRequestParams(search): Object {
        // const search: string = location.search;
        const requestParams: object = {};
        if (search.indexOf('?') !== -1) {
            const str: string = search.substr(search.indexOf('?') + 1); // 截取?后面的内容作为字符串
            const strs = str.split('&'); // 将字符串内容以&分隔为一个数组
            for (let i = 0; i < strs.length; i++) {
                requestParams[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
                // 将数组元素中'='左边的内容作为对象的属性名，'='右边的内容作为对象对应属性的属性值
            }
        }
        return requestParams;
    }

    public static navigateTo(url) {
        // #ifdef H5
        const win: any = window;
        if (win.webkit || win.jsBridge || win.messageChannel || win.flutter_inappwebview) {
            if (url[0] === '/') {
                url = url.replace(/\//, '');
            }
            this.messageChannel('openWebview', url);
        } else {
            uni.navigateTo({ url });
        }
        // #endif

        // #ifdef MP-WEIXIN
        const path = url.split('?')[0];
        if (Constants.TAB_BAR_LIST.includes(path)) {
            if (path === url) {
                uni.switchTab({ url });
            } else {
                uni.reLaunch({ url });
            }
        } else {
            uni.navigateTo({ url });
        }
        // #endif
    }

    public static refreshSession() {
        this.messageChannel('refreshSession', '');
    }

    public static openMapNavigation(data) {
        this.messageChannel('mapNavigation', data);
    }

    public static openMiniProgram(data: { id?: string; path?: string } = {}) {
        data.id = data.id || process.env.VUE_APP_ORIGIN_WX_APPID;
        data.path = data.path || '/pages/index/index';
        this.messageChannel('openMiniProgram', data);
    }

    // h5与原生app通信
    public static messageChannel(event: string, data: any) {
        if (event === 'share' && UserModule.sdkVersion && UserModule.sdkVersion >= 13) {
            this.shareAdapter(data);
            return;
        }
        const win: any = window;
        if (win.flutter_inappwebview) {
            // flutter通信
            if (win.flutter_inappwebview.callHandler) {
                win.flutter_inappwebview.callHandler('messageChannel', JSON.stringify({ type: event, data }));
            } else {
                win.flutter_inappwebview._callHandler('messageChannel', null, JSON.stringify({ type: event, data }));
            }
        } else {
            // RN通信
            if (AppModule.platform === 'ios') {
                win.webkit &&
                    win.webkit.messageHandlers.messageChannel.postMessage(JSON.stringify({ type: event, data }));
                if (event === 'share') {
                    win.webkit &&
                        win.webkit.messageHandlers.messageChannel.postMessage(
                            JSON.stringify({ type: event, data: JSON.stringify(data) }),
                        );
                }
            } else {
                win.jsBridge && win.jsBridge.messageChannel(JSON.stringify({ type: event, data }));
            }
        }
    }

    // h5与原生app通信 新方法
    public static newMessageChannel(type: string, action: String = '', params: any = {}, callback?: Function) {
        const win: any = window;
        const id = new Date().getTime() + Math.floor(Math.random() * 1000); // 生成唯一id
        const callbackName = `${type}${action}${id}`; // 生成回调函数名
        win[callbackName] = (data) => {
            callback && callback(data);
            delete win[callbackName]; // 删除回调函数
        };
        if (win.flutter_inappwebview) {
            // flutter通信
            if (win.flutter_inappwebview.callHandler) {
                win.flutter_inappwebview.callHandler('messageChannel', JSON.stringify({ type, id, action, params }));
            } else {
                win.flutter_inappwebview._callHandler(
                    'messageChannel',
                    null,
                    JSON.stringify({ type, id, action, params }),
                );
            }
        } else {
            // RN通信
            if (AppModule.platform === 'ios') {
                win.webkit &&
                    win.webkit.messageHandlers.messageChannel.postMessage(JSON.stringify({ type, id, action, params }));
                if (type === 'share') {
                    win.webkit &&
                        win.webkit.messageHandlers.messageChannel.postMessage(
                            JSON.stringify({ type, data: JSON.stringify({ type, id, action, params }) }),
                        );
                }
            } else {
                win.jsBridge && win.jsBridge.messageChannel(JSON.stringify({ type, id, action, params }));
            }
        }
    }

    // 分享适配器
    public static shareAdapter(params) {
        const event = 'share';
        // const id = Number(new Date().getTime());

        function oldParamsToNewParams(params) {
            const { target, type, content } = params;
            const newContent = { ...content };

            // 把content中所以带 share_ 的key 去掉 share_, eg：share_title => title
            Object.keys(newContent).forEach((key) => {
                if (key.startsWith('share_')) {
                    const newKey = key.replace('share_', '');
                    newContent[newKey] = newContent[key];
                    delete newContent[key];
                }
            });
            return {
                target,
                type,
                content: newContent,
            };
        }

        const id = new Date().getTime() + Math.floor(Math.random() * 1000); // 生成唯一id
        const data = {
            id,
            type: 'SHARE',
            action: 'share',
            params: oldParamsToNewParams(params),
        };

        console.log(JSON.stringify(data));
        const win: any = window;
        if (win.flutter_inappwebview) {
            // flutter通信
            if (win.flutter_inappwebview.callHandler) {
                win.flutter_inappwebview.callHandler('messageChannel', JSON.stringify(data));
            } else {
                win.flutter_inappwebview._callHandler('messageChannel', null, JSON.stringify(data));
            }
        } else {
            // RN通信
            if (AppModule.platform === 'ios') {
                win.webkit && win.webkit.messageHandlers.messageChannel.postMessage(JSON.stringify(data));
                if (event === 'share') {
                    win.webkit && win.webkit.messageHandlers.messageChannel.postMessage(JSON.stringify(data));
                }
            } else {
                win.jsBridge && win.jsBridge.messageChannel(JSON.stringify(data));
            }
        }
    }

    public static logTrace(data: ILog, immediate = true) {
        UserModule.asyncLogTrace({ data, immediate });
    }

    public static generateShareLink() {
        const data = {
            // header参数
            bizParam: {
                xxxx: 'yyyy',
            },
            // 最终拼接到url
            shareSource: 'xxx',
            // 最终拼接到url
            shareTarget: 'xxx',
            // 打开类型
            type: 'app/web/mall',
            // 是否在url拼接session、userid
            auth: true,
            // 跳转地址 商城/App页面/第三方网页
            jumpLink: 'pages/coin/coin',
        };
        console.log(data);
        return data;
    }

    // public static logTrace(data: ILog, immediate = true) {
    //     UserModule.asyncDataBurialPoint({ data, immediate });
    // }

    public static pxToRpx(pxValue) {
        const systemInfo = uni.getSystemInfoSync();
        const screenWidthPx = systemInfo.windowWidth;
        const screenWidthRpx = screenWidthPx / 750; // 根据设计稿的宽度进行调整
        const rpxValue = pxValue / screenWidthRpx;
        return rpxValue;
    }

    public static rpxToPx(rpx) {
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        return rpx * (screenWidth / 750);
    }

    public static convertTimezone(utcTimeString) {
        utcTimeString = utcTimeString.replace(/-/g, '/');
        const utcDate = new Date(utcTimeString);
        const currentDate = new Date();
        const timeZoneOffset = currentDate.getTimezoneOffset();
        const timeZoneOffsetMs = timeZoneOffset * 60 * 1000;
        const convertedTime = new Date(utcDate.getTime() - timeZoneOffsetMs);
        return this.timeFormat(convertedTime, 'yyyy-mm-dd hh:MM:ss');
    }

    public static async upload(params) {
        const {
            count = 1,
            sizeType = ['original', 'compressed'],
            sourceType = ['album', 'camera'],
            compress = true, // 新增参数，默认需要压缩
            maxSize = 512, // 新增参数，默认最大大小为512KB
        } = params;

        const imgArr: Array<{ url: string; name: string }> = [];

        try {
            const res: any = await this.chooseImage({ count, sizeType, sourceType });
            const isImage = this.validateImages(res.tempFiles);
            if (isImage) return [];

            uni.showLoading({ title: '图片加载中', mask: true });

            for (const file of res.tempFiles) {
                const { url, name } = await this.processImage(file, compress, maxSize);
                imgArr.push({ url, name });
            }

            const getPolicyRes = await this.getPolicy();
            const imageList = await this.handleImageUpload(imgArr, getPolicyRes);

            return imageList;
        } catch (error) {
            if (error.message !== 'chooseImage:fail cancel') {
                this.Toast('选择图片失败', 2000, '', 'width: 300rpx');
            }
            return [];
        } finally {
            uni.hideLoading();
        }
    }

    public static chooseImage(params) {
        return new Promise((resolve, reject) => {
            uni.chooseImage({
                ...params,
                success: resolve,
                fail: reject,
            });
        });
    }

    public static validateImages(files) {
        return files.some((item) => {
            // #ifdef H5
            if (!this.isImageUrl(item.name)) {
                this.Toast('请选择图片进行上传', 2000, '', 'width: 300rpx');
                return true;
            }
            // #endif
            // #ifdef MP-WEIXIN
            if (!this.isImageUrl(item.path)) {
                this.Toast('请选择图片进行上传', 2000, '', 'width: 300rpx');
                return true;
            }
            // #endif
        });
    }

    public static async processImage(file, compress, maxSize) {
        let url;
        let name;
        // #ifdef H5
        name = file.name;
        // #endif
        // #ifdef MP-WEIXIN
        name = file.path;
        // #endif

        if (compress && Math.ceil(file.size / 1024) > maxSize) {
            // #ifdef H5
            url = (await this.compressImage(file)) as string;
            // #endif
            // #ifdef MP-WEIXIN
            url = await new Promise((resolve, reject) => {
                wx.compressImage({
                    src: file.path,
                    quality: 80,
                    success: (compressRes) => resolve(compressRes.tempFilePath),
                    fail: reject,
                });
            });
            // #endif
        } else {
            url = file.path;
        }

        return { url, name };
    }

    // 压缩图片
    public static compressImage(urlData) {
        return new Promise((resolve) => {
            uni.getImageInfo({
                src: urlData.path,
                success: (res) => {
                    const originWidth: any = res.width; // 图片原始宽
                    const originHeight: any = res.height; // 图片原始高
                    const maxWidth = 800;
                    const maxHeight = 800;
                    const img = new Image();
                    img.src = res.path;
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    // 目标尺寸
                    let targetWidth = originWidth;
                    let targetHeight = originHeight;
                    if (originWidth > maxWidth || originHeight > maxHeight) {
                        if (originWidth / originHeight > maxWidth / maxHeight) {
                            // 更宽，按照宽度限定尺寸
                            targetWidth = maxWidth;
                            targetHeight = Math.round(maxWidth * (originHeight / originWidth));
                        } else {
                            targetHeight = maxHeight;
                            targetWidth = Math.round(maxHeight * (originWidth / originHeight));
                        }
                    }

                    canvas.width = targetWidth;
                    canvas.height = targetHeight;
                    // 图片压缩
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
                    // canvas对图片进行缩放 0.3是我定义的图片质量，
                    const base64: any = canvas.toDataURL(urlData.type, 0.3);
                    const blob: any = this.dataURLtoBlob(base64);
                    const url = URL.createObjectURL(blob);

                    resolve(url);
                },
                fail: () => {},
            });
        });
    }

    public static dataURLtoBlob(dataurl) {
        const arr = dataurl.split(','); // 分割为数组，分割到第一个逗号
        const mime = arr[0].match(/:(.*?);/)[1]; // 获取分割后的base64前缀中的类型
        const bstr = window.atob(arr[1]);
        let n: any = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
    }

    public static isVideoUrl(url: string): boolean {
        if (typeof url !== 'string' || url.trim() === '') {
            return false;
        }
        // 移除URL参数部分
        const baseUrl = url.split('?')[0];
        // 定义视频文件的扩展名列表
        const videoExtensions = ['mp4', 'mov', 'wmv', 'flv', 'avi', 'mkv', 'webm'];
        // 提取URL的扩展名
        const extension = baseUrl.split('.').pop().toLowerCase();
        // 检查扩展名是否在视频扩展名列表中
        return videoExtensions.includes(extension);
    }

    public static isImageUrl(url: string): boolean {
        if (typeof url !== 'string' || url.trim() === '') {
            return false;
        }
        // 移除URL参数部分
        const baseUrl = url.split('?')[0];
        return /\.(jpg|jpeg|png|gif|bmp|svg)$/i.test(baseUrl);
    }

    public static getSign(obj, security_key) {
        // @ts-ignore
        obj.sign_time = parseInt(new Date().getTime() / 1000);
        const newObj = {
            ...obj,
        };
        newObj.security_key = security_key;
        let sortArr: any = [];
        const keysSorted = Object.keys(newObj).sort(); // 排序名
        for (let i = 0; i < keysSorted.length; i++) {
            sortArr.push(keysSorted[i] + '=' + newObj[keysSorted[i]]);
        }
        sortArr = sortArr.join('&');
        return md5Libs.md5(sortArr);
    }

    public static request(url, method, data) {
        return new Promise((resolve, reject) => {
            const BASE_DATA = {
                user_id: UserModule.user_id || 0,
                sessid: UserModule.sessid || '',
                api: AppModule.platform == 'ios' ? 'i_1643178026' : 'a_1643178000',
                version: AppModule.version,
                union: UserModule.union || uni.getStorageSync('union') || '',
                euid: UserModule.euid || uni.getStorageSync('euid') || 0,
                is_internal_purchase: UserModule.isInternalPurchase,
                cps: UserModule.cps,
                referer: uni.getStorageSync('referer') || '',
            };
            const security_key = AppModule.platform == 'ios' ? '*&^$%#%!#$&@' : '*&^@!!#$&#@%';
            const data2 = {
                ...data,
                ...BASE_DATA,
            };

            data2.sign = Utils.getSign(data2, security_key);
            uni.request({
                url: url,
                method: method,
                data: {
                    ...data2,
                },
                header: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'wx-version': 'v1.0.0',
                },
                success: (res) => {
                    const response = res.data;
                    resolve(response);
                },
                fail: (err: any) => {
                    console.log('err', err);
                    reject(err);
                },
                complete: () => {},
            });
        });
    }

    public static getPolicy = async () => {
        let result;
        // #ifdef H5
        const security_key = AppModule.platform == 'ios' ? '*&^$%#%!#$&@' : '*&^@!!#$&#@%';
        const BASE_DATA = {
            user_id: UserModule.user_id || 5435,
            sessid: UserModule.sessid || '********************************',
            api: AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268',
            version: AppModule.version,
        };
        const sign = Utils.getSign(BASE_DATA, security_key);
        const params = {
            ...BASE_DATA,
            sign,
        };
        const url = new URL('https://cn-cms.dreame.tech/dreame-cms/api/v2/common/get-policy');
        Object.keys(params).forEach((key) => url.searchParams.append(key, params[key]));
        const res = await fetch(url.toString(), {
            method: 'get',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const response: any = await res.json();
        result = response.data;
        // #endif

        // #ifdef MP-WEIXIN
        const resultMp: any = await Utils.request(
            'https://cn-cms.dreame.tech/dreame-cms/api/v2/common/get-policy',
            'GET',
            {},
        );
        result = resultMp.data;
        // #endif

        return result;
    };

    public static random_string(len) {
        len = len || 32;
        const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        const maxPos = chars.length;
        let pwd = '';
        let i;
        for (i = 0; i < len; i++) {
            pwd += chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return pwd;
    }

    public static handleImageUpload = async (arr, getPolicyRes: any = {}) => {
        const promises = arr.map((item) => {
            const name = Utils.random_string(10) + '.' + item.name.split('?')[0].split('.').pop();
            const params = {
                name: name,
                key: `${getPolicyRes.policy_token.dir}` + name,
                policy: getPolicyRes.policy_token.policy,
                OSSAccessKeyId: getPolicyRes.policy_token.accessid,
                success_action_status: '200',
                callback: getPolicyRes.policy_token.callback,
                signature: getPolicyRes.policy_token.signature,
            };
            return Utils.aliyuncsUpload(getPolicyRes.url, item.url, params).then((result: any) => {
                return {
                    ...result,
                    file_url: getPolicyRes.absulte_path + params.name, // 添加新的字段
                };
            });
        });
        const results: any = await Promise.all(promises);
        return results.map((item) => item.file_url);
    };

    public static aliyuncsUpload = async (domain, filePath, params) => {
        return new Promise((resolve, reject) => {
            uni.uploadFile({
                url: domain,
                filePath: filePath,
                name: 'file',
                formData: {
                    ...params,
                },
                success: (uploadFileRes) => {
                    resolve(uploadFileRes);
                },
                fail: (error) => {
                    reject(error);
                },
            });
        });
    };

    /**
     * 轮询
     * @param promiseMaker {Function} 生成Promise的函数 resovle时返回true时终止循环
     * @param options  {Object} 配置项 immediate: 是否立即执行 timeout: 轮询间隔时间
     * @returns  {Promise} 返回一个resolved的Promise
     */
    public static promiseSequence(promiseMaker: Function, options?: { immediate?: boolean; timeout?: number }) {
        const { immediate = false, timeout = 300 } = options || {};
        const handleNextInput = (status: boolean) => {
            if (status) {
                return status;
            } else {
                return this.sleep(timeout || 300).then(() => promiseMaker().then(handleNextInput));
            }
        };
        if (immediate) {
            return promiseMaker().then(handleNextInput);
        } else {
            return Promise.resolve(false).then(handleNextInput);
        }
    }

    public static sleep(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    /** 发布时间格式化 */
    public static timeAgoFormat(date = 0, type?: 'publish' | 'comment') {
        const now = new Date();
        const timestamp = String(date).length <= 10 ? date * 1000 : date;
        const inputDate = new Date(timestamp);

        const timestampDiff = now.getTime() - inputDate.getTime();

        const secondsDiff = Math.floor(timestampDiff / 1000);
        const minutesDiff = Math.ceil(secondsDiff / 60);
        const hoursDiff = Math.floor(minutesDiff / 60);
        const daysDiff = Math.floor(hoursDiff / 24);

        const year = inputDate.getFullYear();
        const month = String(inputDate.getMonth() + 1).padStart(2, '0');
        const day = String(inputDate.getDate()).padStart(2, '0');

        switch (type) {
            /**
             * 发布于当天，且尚未超过1小时，显示---XX分钟前
             * 发布于当天，且超过1小时，显示---XX小时前
             * 超过当天，则展示年月日，不需要精确到时分秒，显示---2024-09-12
             */
            case 'publish':
                if (now.toDateString() === inputDate.toDateString()) {
                    if (minutesDiff < 60) {
                        return `${minutesDiff}分钟前`;
                    } else {
                        return `${hoursDiff}小时前`;
                    }
                } else {
                    return `${year}-${month}-${day}`;
                }

            /**
             * 几秒:如果评论是在当前时间的几秒内发布，显示为“刚刚“或“几秒前”
             * 几分钟:如果评论是在1分钟到59分钟内发布，通常会显示为“X分钟前”(例如“5分钟前”)
             * 几小时:如果评论是在1小时到23小时内发布，通常会显示为“X小时前(例如“2小时前”)
             * 几天:如果评论是在1天到6天内发布，通常会显示为“X天前”(例如“3天前”)
             * 特定日期:如果评论发布超过6天，则会显示为具体的日期格式，比如”MM月DD日”，并且在年份不同的情况下，可能会显示完整的年份(如“2022年MM月DD日”)
             */
            case 'comment':
                if (secondsDiff < 60) {
                    return '刚刚';
                } else if (minutesDiff < 60) {
                    return `${minutesDiff}分钟前`;
                } else if (hoursDiff < 24) {
                    return `${hoursDiff}小时前`;
                } else if (daysDiff < 7) {
                    return `${daysDiff}天前`;
                } else {
                    return `${year}-${month}-${day}`;
                }

            default:
                return `${year}-${month}-${day}`;
        }
    }

    public static sharedStorage(type: 'get' | 'set' | 'remove', key?: string, value?: any) {
        this.messageChannel('sharedStorage', { type, data: { key, value }});
        return new Promise((resolve, reject) => {
            const win: any = window;
            win.onSharedStorage = (data) => {
                // type value key code
                if (data.code === 0) {
                    resolve(data.value);
                } else {
                    this.Toast('获取数据失败，请稍后再试');
                }
                win.onSharedStorage = null;
            };
        });
    }

    public static jumpPersonHome(creator, redirect = false) {
        // #ifdef MP-WEIXIN
        try {
            if (UserModule.wxAuth && UserModule.isPhone) {
                // #endif
                let user_id = creator;
                if (UserModule.user_id == creator) user_id = -1;
                if (redirect) {
                    uni.redirectTo({
                        url: `/pagesC/selfCommunity/selfCommunity?creator=${user_id}`,
                    });
                } else {
                    Utils.navigateTo(`/pagesC/selfCommunity/selfCommunity?creator=${user_id}`);
                }
                // #ifdef MP-WEIXIN
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
    }

    public static requestPermission() {
        this.messageChannel('requestPermission', 'camera');

        return new Promise((resolve, reject) => {
            const win: any = window;
            win.requestPermission = (data) => {
                if (data.code === 0) {
                    resolve(data.result);
                } else {
                    reject(false);
                }
                win.requestPermission = null;
            };
        });
    }

    public static backBlockForObserver(intercept) {
        this.messageChannel('backBlockForObserver', intercept);
    }

    /**
     * 判断设备是否为折叠屏
     * @returns {boolean} 是否为折叠屏设备
     */
    public static isFoldableDevice(): boolean {
        try {
            const systemInfo = uni.getSystemInfoSync();
            const { screenWidth, screenHeight } = systemInfo;

            // 获取屏幕宽高比
            const aspectRatio = Math.max(screenWidth, screenHeight) / Math.min(screenWidth, screenHeight);

            // 检测异常的屏幕比例（折叠屏通常有特殊的宽高比）
            const isUnusualAspectRatio = aspectRatio > 2.5 || aspectRatio < 1.2;
            console.log('isUnusualAspectRatio', isUnusualAspectRatio);
            return isUnusualAspectRatio;
        } catch (error) {
            console.error('检测折叠屏设备失败:', error);
            return false;
        }
    }

    /*
    定义一个方法，通过传递的url，从分享承接页跳转到dreame app对应的界面
    */
    public static jumpToDreameApp(url: string): boolean {
        if (!UserModule.user_id) {
            window.location.href = url;
            return true; // 返回 true 表示执行了 window.location.href
        }
        return false; // 返回 false 表示没有执行 window.location.href
    }

    /* 定义一个方法，通过的userId参数，判断是不是处于APP环境*/
    public static isInApp() {
        const isDev = process.env.NODE_ENV === 'development';
        const hasUserId = !!UserModule.user_id || isDev;
        return hasUserId;
    }

    public static setPageTitle(title: string) {
        document.title = title;
    }

    // 写入日历提醒
    public static callCalendar(paramsTmp?: any) {
        console.log('callCalendar===paramsTmp===', paramsTmp)
        const params = {
            // 开始时间
            time: Date.now(),
            // 时间段
            durationMinutes: 60,
            // 持续多少天
            days: 30,
            // 链接
            url: 'http://www.baidu.com',
            // 提前几分钟提醒
            reminderMinutes: 10,
            // 标题
            title: '设备联动检查',
            // 备注描述
            description: '每日例行巡检',
            ...(paramsTmp || {}),
        };
        Utils.newMessageChannel('DEVICE', 'calendar', params, (res) => {
            console.log(res);
        })
    }

    // {
    //     'target': 'download,wechat,qq,sina,weixin_circle',
    //     'type': 'image',
    //     'content': {
    //       'url': 'https://www.baidu.com',
    //       'image':
    //           'https://oss.iot.dreame.tech/dreame-public/prod/products/dreame.hold.w2322/images/d75c94c4f8fd791aa937c5aeffa19351.png',
    //       'title': '追觅好物，限时五折购',
    //       'desc': 'XXX（到手价）+ XXX（商品名称）+一句话文案',
    //     },
    //     'extras': {
    //       'id': '112233',
    //       'type': 'goods',
    //       'goods': {
    //         'name': 'NAVEE代步出行电动滑板...',
    //         'desc': '轻便出行轻便出行',
    //         'image':
    //             'https://oss.iot.dreame.tech/dreame-public/prod/products/dreame.hold.w2322/images/d75c94c4f8fd791aa937c5aeffa19351.png',
    //         'price': '1999',
    //         'priceColor': '#FF7D01',
    //         'descColor': '#C59245',
    //        'imageBg': 卡片背景图,
    //        'badge': 角标
    //       }
    //     }
    // 卡片分享
    public static cardShare(
        type: 'image' | 'web',
        shareType?: 'goods' | 'activity',
        clickCallback?: (res:any) => void,
    ): (config: ShareConfig, cardData?: Partial<CardShareData>) => void {
        return (config: ShareConfig, cardData?: Partial<CardShareData>) => {
            const data: ShareLinkConfig = {
                ...Constants.GEN_SHARE_LINK_TYPE,
                jumpLink: config.link,
            };

            Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
                const long_link = this.buildLongLink(config.jumpLink, res.data);

                if (Number(UserModule.sdkVersion) < 15) {
                    this.handleLegacyShare(config, cardData, type, shareType, long_link);
                } else {
                    this.handleModernShare(config, cardData, type, long_link, clickCallback);
                }
            });
        };
    }

    // 构建长链接
    private static buildLongLink(jumpLink: string, shareData: string): string {
        if (!jumpLink) return shareData;

        const symbol = jumpLink.includes('#') ? (jumpLink.split('#')[1].includes('?') ? '&' : '?') : '?';

        return `${jumpLink}${symbol}link=${shareData}`;
    }

    // 处理旧版本分享逻辑
    private static handleLegacyShare(
        config: ShareConfig,
        cardData: Partial<CardShareData>,
        type: 'image' | 'web',
        shareType: 'goods' | 'activity',
        long_link: string,
    ) {
        const processShareData = (url: string) => {
            this.prepareShareData(cardData, type, shareType);
            const params = {
                target: config.target,
                type,
                ...cardData,
};
            this.executeShare('SHARE', 'share', params);
        };

        if (config.jumpLink.includes('#')) {
            getShortLink({ long_link }).then((res1) => {
                const short_link = res1.short_link[0].ShortUrl;
                cardData.content.url = short_link;
                processShareData(short_link);
            });
        } else {
            cardData.content.url = long_link;
            processShareData(long_link);
        }
    }

    // 处理新版本分享逻辑
    private static async handleModernShare(
        config: ShareConfig,
        cardData: Partial<CardShareData>,
        type: 'image' | 'web',
        long_link: string,
        clickCallback?: (res:any) => void,
    ) {
        cardData.content.url = long_link;
        console.log('%c long_link: ', 'font-size:16px;background-color: #F5CE50;color:#fff;', long_link)
        const params = {
            target: config.target + ',copy_link',
            type,
            ...cardData,
        };
        if (Number(UserModule.sdkVersion) > 15) {
          const copyText = cardData.extras.copyText ? JSON.parse(JSON.stringify(cardData.extras.copyText)) : '';
          const res = await getShortLink({ long_link });
          const short_link = res.short_link[0].ShortUrl;
          params.extras.copyText = `【DREAME】 ${short_link} ${copyText} 点击链接直接打开`
        }
        this.executeShare('SHARE', 'shareDM', params, clickCallback);
    }

    // 准备分享数据
    private static prepareShareData(
        cardData: Partial<CardShareData>,
        type: 'image' | 'web',
        shareType: 'goods' | 'activity',
    ) {
        if (type === 'web') {
            delete cardData.extras;
        }

        // 截取标题
        if (cardData.content.title) {
            cardData.content.title = cardData.content.title.slice(0, 12) + '...';
        }

        // 截取商品名称
        if (cardData?.extras?.goods?.name && shareType !== 'goods') {
            cardData.extras.goods.name = cardData.extras.goods.name.slice(0, 12) + '...';
        }
    }

    // 执行分享
    private static executeShare(channel: string, method: string, params: any, clickCallback?: (res: any) => void) {
        Utils.newMessageChannel(channel, method, params, (res) => {
            console.log('卡片分享结果:', res);
            if (clickCallback) {
              console.log(JSON.stringify(res));
                clickCallback(res);
            } else if (res.code === 0) {
                this.handleShareResult(res.media);
            }
        });
    }

    // 处理分享结果
    private static handleShareResult(media: number) {
        const toastConfig = 'background: rgba(0, 0, 0, 0.8);color: #ffffff;';

        switch (media) {
            case 12:
                Utils.Toast('图片已保存至相册', 2000, '', `${toastConfig} width: 300rpx;`);
                break;
            case 13:
                Utils.Toast('复制成功，快去粘贴吧~', 2000, '', `${toastConfig} width: 360rpx;`);
                break;
        }
    }

     /* 任务完成 */
     public static taskComplete(taskCode: string) {
        /*
        taskCode枚举值
          分享商品给好友：shareGoodsFriendMoney
          分享拼团商城商品：shareGroupGoods
          分享小店商品给好友：shareShopGoodsFriend
          分享暴富计划商品给好友：shareRichGoodsFriend
          参与盲盒抽奖：addBlindPrizeDraw
        */
        doGoldTask({ type: taskCode });
    }

    /**
 * 拼接字符串：文案 + 链接 + 文案
 * @param {string} beforeText 前文案
 * @param {string} link 链接
 * @param {string} afterText 后文案
 * @returns {string} 拼接后的字符串
 */
    public static async concatText(beforeText: string, link: string, afterText: string) {
        try {
            const res = await getShortLink({ long_link: link });
            const short_link = res.short_link[0].ShortUrl;
            return `${beforeText} ${short_link} ${afterText}`;
        } catch (error) {
            console.error('获取短链接失败:', error);
            // 如果获取短链接失败，使用原始链接
            return `${beforeText} ${link} ${afterText}`;
        }
    }
}
// inviteNewUser() {
//     const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/new_point?sharerUid=${this.user_id}`;
//     const data = {
//         ...Constants.GEN_SHARE_LINK_TYPE,
//         jumpLink: url,
//     };
//     //  `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/share_undertaking_page?link=` + res.data
//     Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
//         console.log(res.data);
//         const params = {
//             target: 'wechat,qq,sina',
//             type: 'web',
//             content: {
//                 url:
//                     `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/inner_share_undertaking_page?link=` +
//                     res.data,
//                 share_image:
//                     'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688220c82fb331950011310.png',
//                 share_title: `一起追觅好物，共享10倍积分`,
//                 share_desc: `上线秒送500积分`,
//             },
//         };
//         Utils.messageChannel('share', params);
//     });
// }
