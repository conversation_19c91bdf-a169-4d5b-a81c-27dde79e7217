const args = process.argv.splice(2);
let versionCode, versionName;
try {
    args.forEach((val) => {
        if (val.indexOf('versionCode') > -1) {
            versionCode = val.split('-')[1];
        } else if (val.indexOf('versionName') > -1) {
            versionName = val.split('-')[1];
        }
    });
    if (isNaN(versionCode)) {
        throw 'versionCode error';
    }
} catch (error) {
    console.log(error);
    return;
}
const fs = require('fs');
const Hjson = require('hjson');
const { delFile, mkdirsSync } = require('./util/fsUtil');
const zipper = require('zip-local');
const crypto = require('crypto');
const fsHash = crypto.createHash('md5');
const manifestPath = './src/version.json';
const manifest = fs.readFileSync(manifestPath, 'utf-8');
const manifestInfo = Hjson.parse(manifest);
const versionPath = './src/version_h5.json';
// 读取版本号记录文件
const versionInfo = JSON.parse(fs.readFileSync(versionPath, 'utf-8'));

// 获取当前日期
const currentDate = new Date();
const dateStr = `${currentDate.getFullYear().toString().slice(2)}${(currentDate.getMonth() + 1)
    .toString()
    .padStart(2, '0')}${currentDate.getDate().toString().padStart(2, '0')}`;

// 更新版本号
if (versionInfo.date === dateStr) {
    versionInfo.buildCount += 1;
} else {
    versionInfo.date = dateStr;
    versionInfo.buildCount = 1;
}

// 更新版本号记录文件
fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 4));

if (versionCode && versionName) {
    manifestInfo.versionCode = versionCode;
    manifestInfo.versionName = versionName;
    fs.writeFileSync(manifestPath, JSON.stringify(manifestInfo, null, 4));
}
const execSync = require('child_process').execSync;
const outPath = 'out/';
const outDistPath = `${outPath}dist/`;
const outTmp = `${outPath}tmp.zip`;
delFile('dist/build/h5');
delFile(outPath);
mkdirsSync(outDistPath);
console.log('--------------------开始打包h5--------------------');
execSync('npm run build:h5');
console.log(`--------------------开始移动h5资源到${outDistPath}--------------------"`);
execSync(`mv dist/build/h5/* ${outDistPath}`);
execSync(`cp app_config.json ${outDistPath}`);
execSync(`cp ${manifestPath} ${outDistPath}`);
console.log(`---------------------开始生成压缩包--------------------`);
zipper.sync.zip(outDistPath).compress().save(outTmp);
const stream = fs.readFileSync(outTmp);
fsHash.update(stream);
const md5 = fsHash.digest('hex');
console.log(`---------------------重命名压缩包--------------------`);
fs.renameSync(outTmp, `out/${manifestInfo.appid}.zip`);
console.log(`---------------------生成md5文件--------------------`);
const md5Info = {
    versionCode: manifestInfo.versionCode,
    md5,
};
fs.writeFileSync(`${outPath}md5.json`, JSON.stringify(md5Info));
console.log(`****************** 打包成功 ******************`);
