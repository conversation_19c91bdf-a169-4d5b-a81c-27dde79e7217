/* eslint-disable no-redeclare */
import http from './index';
let env = process.env.NODE_ENV;
let BASE_URL = process.env.VUE_APP_BASE_URL;

// start
if (process.env.NODE_ENV === 'development') {
    try {
        const node_env = uni.getStorageSync('node_env');
        if (node_env) {
            env = node_env.env;
            BASE_URL = node_env.BASE_URL;
        } else {
            uni.setStorage({
                key: 'node_env',
                data: {
                    env,
                    BASE_URL,
                },
            });
        }
    } catch (e) {
        console.error(`当前环境是${process.env.NODE_ENV}`);
    }
} else {
    // 生产环境 避免出现环境缓存
    try {
        uni.removeStorageSync('node_env');
    } catch (e) {
        console.error('没有环境缓存');
    }
}
// end

/**
 * 评价列表
 * @parma {Number} page 页数
 * @parma {Number} page_size 条数
 * @parma {String} has_append 1主评（主评返回的数据包含追评和回复）、2追评（只展示追评数据）
 * @parma {String} has_media 有图片/视频
 * @parma {String} gid 商品gid
 * @returns
 */
export const goodsReviewList = (params) => http.post('main/goods-review/list', params);

/**
 * 评价详情
 * @parma {Number} review_id 评价id
 * @returns
 */
export const goodsReviewDetail = (review_id: number) => http.post('main/goods-review/detail', { review_id });

/**
 * 我的订单商品评价列表
 * @parma {Number} type 1待评价、2已评价、3可追评
 * @parma {Number} page 已评价当前页数
 * @parma {Number} page_size 条数
 * @returns
 */
export const myReviewList = (params) => http.post('main/goods-review/my-list', params);

/**
 * 删除评价
 * @parma {String} review_id 评价id
 * @returns
 */
export const goodsReviewDelete = (params: any) => http.post('main/goods-review/delete', params);

/**
 * 商城评价
 */
export const goodsReviewReview = (params: any) => http.post('main/goods-review/review', params);

/**
 * 举报原因
 */

export const goodsReviewReasonList = () => http.post('main/goods-review/reason-list');

/**
 * 举报评价
 */

export const goodsReviewReport = (params: any) => http.post('main/goods-review/report', params);
