<template>
    <view class="pointProducts-horizontal" :style="pointProductStyle" @click="toGoodsDetail(pointProductsInfo)">
        <!-- 左边商品图片 -->
        <view class="img-container">
            <view :style="pointProductsInfo.stock <= 0 ? 'filter: opacity(0.5)' : 'filter: opacity(1)'">
                <lazy-image
                    :customStyle="`width: 100%; height: 100%; border-radius: 16rpx`"
                    :lazyLoad="true"
                    :src="pointProductsInfo.cover_image"
                    mode="scaleToFill"
                ></lazy-image>
            </view>
        </view>
        <!-- 右边商品信息 -->
        <view class="content-container">
            <!-- 第一行：商品名称 -->
            <view class="title">
               <view class="title-icon">
                 会员
               </view>
                <view class="title-text">{{ pointProductsInfo.name }}</view>
            </view>

         <view class="member-price-container">
               <!-- 第二行：会员价 -->
               <view class="member-price">
              <view class="member-price-content">
                <!-- <text class="unit">￥</text> -->
                <text class="price">{{ pointProductsInfo.point }}积分 + ￥{{ pointProductsInfo.price  }}</text>
                <!-- <text class="price">{{ (pointProductsInfo.price - ( Number(pointProductsInfo.can_use_coin) / Number(pointProductsInfo.deduction_rate))- Number(pointProductsInfo.subsidy_price)).toFixed(2) }}</text>
                <text class="flag">会员价</text> -->
              </view>
                <!-- 已售出 -->

            </view>

            <!-- 第三行：积分抵扣金额 -->
            <view class="point-deduction">
                <text class="originPrice">￥{{ pointProductsInfo.mprice }}</text>
                <!-- <text class="sold-out">已售{{ pointProductsInfo.sales }}</text> -->
                <!-- <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f6f413b9642440010926.png"
                    class="point-icon"
                ></image> &nbsp;
                 <view class="point-text">
                    &nbsp;{{ pointProductsInfo.point }}积分
                </view>
                <view class="point-text" v-if="pointProductsInfo.can_use_coin > 100">
                    积分抵扣 ￥{{ (Number(pointProductsInfo.can_use_coin) / Number(pointProductsInfo.deduction_rate)).toFixed(2) }}
                </view>
                <view class="point-text" v-else>
                    无积分可抵扣
                </view> -->
            </view>
         </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator';
import { CheckAppJump } from '@/common/decorators';

@Component({
    components: {},
})
export default class pointProductsHorizontal extends Vue {
    // 商品样式
    @Prop({ type: String, default: '' })
    readonly pointProductStyle;

    // 数据
    @Prop({
        type: Object,
        default: () => ({
            cover_image: '', // 图片链接
            levels_tag: '', // 等级说明
            label_tag: '', // 标签
            online_time_tag: '', // 上架时间
            name: '', // 标题文本
            introduce: '', // 卖点文本
            point: 0, // 积分
            price: 0, // 优惠价格
            mprice: 0, // 原始价格
            can_use_coin: 0, // 可用积分
            deduction_rate: 1, // 抵扣比例
            subsidy_price: 0, // 补贴价格
            stock: 0, // 库存
            gid: '', // 商品ID
        }),
    })
    readonly pointProductsInfo;

    @CheckAppJump()
    toGoodsDetail(item) {
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&type=` + 'pointsMall&orderType=POINT_SHOPPING',
        });
    }
}
</script>

<style lang="scss" scoped>
.pointProducts-horizontal {
    background: #ffffff;
    width: 100%;
    height: 248rpx;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 24rpx;
    box-sizing: border-box;

    .img-container {
        width: 208rpx;
        height: 208rpx;
        flex-shrink: 0;
        border-radius: 15rpx;
        overflow: hidden;
        margin-right: 24rpx;
    }

    .content-container {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        line-height: 40rpx;
        font-size: 28rpx;
        padding: 8rpx 0;
        font-weight: normal;
        justify-content: space-between;
        color: #121212;

        .title {
            display: flex;
            position: relative;

            .title-icon {
                width: 52rpx;
                height: 25rpx;
                margin-right: 16rpx;
                border-radius: 4rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #FFE5EE;
                color: #F3558C;
                font-size: 20rpx;
                font-weight: 500;
                flex-shrink: 0;
                position: absolute;
                top:20rpx;
                line-height: 25rpx;
                transform: translateY(-50%);
            }

            .title-text {
                font-size: 28rpx;
                text-indent: 60rpx;
                font-weight: normal;
                color: #333333;
                word-break: break-all;
                line-height: 40rpx;
                // 2行
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                flex: 1;
            }
        }

        .member-price-container {
        }

        .member-price {
            display: flex;
            align-items: baseline;
            justify-content: space-between;
            font-weight: 500;
            .unit {
                font-size: 26rpx;
                color: #F3558C;
            }

            .price {
                font-size: 32rpx;
                font-weight: 500;
                line-height: 44rpx;
                color: #F3558C;
                margin-right: 8rpx;
            }

            .flag {
                font-size: 24rpx;
                font-weight: 500;
                color: #F3558C;
            }
            .sold-out {
                font-size: 24rpx;
                font-weight: normal;
                margin-right: 16rpx;
                color: #A6A6A6;
            }
        }

        .point-deduction {
            display: flex;
            align-items: center;
            .originPrice{
                font-size: 24rpx;
                font-weight: normal;
                margin-right: 16rpx;
                color: #A6A6A6;
                 text-decoration: line-through;
            }

            .point-icon {
                width: 32rpx;
                height: 32rpx;
            }

            .point-text {
                font-size: 20rpx;
                line-height: 32rpx;
                font-weight: normal;
                color: #F3558C;
            }
        }
    }
}
</style>
