<template>
    <u-popup :show="isShow" v-prevent-scroll="isShow" mode="center" :round="round" @close="onClose" :safeAreaInsetBottom="false"
        :closeOnClickOverlay="false">
        <view class="rule-box u-flex u-flex-col u-row-left u-col-center" :style="ruleBoxCustomStyle">
            <view class="title" :style="titleCustomStyle">{{ title }}</view>
            <view class="rule u-flex-1">
                <u-parse v-if="isRenew" :content="rules"></u-parse>
                <text v-else style="word-break: break-all;" :decode="true">{{ rules }}</text>
            </view>
            <slot name="confirmButton"></slot>
            <view class="icon-close u-flex u-col-center u-row-center" :style="iconCustomStyle">
                <image class="icon" :src="icon"
                    @click="onClose" />
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
    components: {},
})
export default class PopRule extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isRenew!: Boolean;

    @Prop({ type: String, default: '' })
    readonly rules;

    @Prop({ type: String, default: '规则说明' })
    readonly title;

    @Prop({ type: String, default: 'https://wpm-cdn.dreame.tech/images/202206/796191-1655952214518.png' })
    readonly icon;

    @Prop({ type: String, default: '' })
    readonly iconCustomStyle

    @Prop({ type: String, default: '' })
    readonly titleCustomStyle

    @Prop({ type: String, default: '' })
    readonly ruleBoxCustomStyle

    @Prop({ type: Number, default: 32 })
    readonly round

    onClose() {
        this.$emit('update:isShow', false);
    }
}
</script>

<style lang="scss" scoped>
.rule-box {
    position: relative;
    width: 666rpx;
    max-height: 795rpx;
    background: #ffffff;
    border-radius: 32rpx;
    padding-top: 42rpx;
    padding-bottom: 54rpx;

    .title {
        color: #000000;
        line-height: 50rpx;
        font-size: 36rpx;
        margin-bottom: 56rpx;
    }

    .rule {
        width: 578rpx;
        font-size: 26rpx;
        color: #686868;
        line-height: 36rpx;
        overflow-y: auto;
    }

    .icon-close {
        position: absolute;
        top: 28rpx;
        right: 42rpx;
        width: 32rpx;
        height: 32rpx;

        .icon {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
