/* eslint-disable object-curly-spacing */
import http from './index';

/*
 * 推荐有礼页
 * @returns
 */
export const recommendGift = () => http.post('main/my/recommend-gift');

/*
 * 累计邀请注册页
 * @returns
 */
export const inviteReg = () => http.post('main/my/invite-reg', {}, { custom: { loading: true } });

/*
 * 累计邀请购买页
 * @parma {*} year  年（2022-2030） int类型
 * @parma {*} is_valid 订单是否有效（0：无效 1：有效）int类型
 * @returns
 */
interface inviteBuyParams {
    year: string;
}
export const inviteBuy = (params: inviteBuyParams) =>
    http.post('main/my/invite-buy', params, { custom: { loading: true } });

/*
 * 更新弹窗次数
 * @parma {*} action  行为（1：注册奖励 2：绑定奖励 3：下单优惠劵奖励 4：下单积分奖励）【必要】
 * @returns
 */
export const updatePopupCount = (action: string) => http.post('main/activity/update-popup-count', { action });
