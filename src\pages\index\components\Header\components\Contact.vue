<template>
    <view class="icon-contact" @click="decryptContact">
        <image class="icon" src="@/static/header/icon_contact.png"></image>
        <customer-service-picker/>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils'
import { BuType } from '@/components/CustomerServicePicker/customer-butype';

@Component
export default class Contact extends Vue {
    constructor() {
        super();
    }

    decryptContact() {
        Utils.decryptContact({}, BuType.BuType_NONE);
    }
}
</script>
<style lang="scss" scoped>
.icon-contact {
    position: relative;

    .icon {
        width: 48rpx;
        height: 48rpx;
    }
}
</style>
