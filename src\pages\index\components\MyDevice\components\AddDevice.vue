<template>
    <view class="add-device" @click="addDevice">
        <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f60747041a4600020679.png" style="width: 22rpx;height:22rpx;"></image>
        <view class="text">添加设备</view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

export default {
    name: 'WashMachine',
    components: {},
    props: {
        currentDevice: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    methods: {
        // 添加设备
        addDevice() {
            Utils.newMessageChannel('PAGE', 'push', { path: '/product_list' });
        },
    },
};
</script>

<style scoped lang="scss">
.add-device {
    width: 320rpx;
    height: 156rpx;
    border:2rpx dashed #E2E2E2;
    display: flex;
    align-items: center;
    justify-content: center;
    background-repeat: no-repeat;
    background-position:left top, right bottom;
    border-radius: 24rpx;
    .icon{
        width:22rpx;
        height: 22rpx;
    }
    .text{
        color:#404040;
        font-size: 28rpx;
        font-family: MiSans;
        font-weight: normal;
        margin-left: 18rpx;
    }
}
</style>
