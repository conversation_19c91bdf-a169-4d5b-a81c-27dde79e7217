<template>
    <view></view>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
@Component({})
export default class order extends Vue {
    onLoad(options) {
        const url = '/pagesC/dtdService/dtdService'
        let params = ''
        if (JSON.stringify(options) != '{}') {
            Object.keys(options).forEach((item, index) => {
                params += index === 0 ? `?${item}=${options[item]}` : `&${item}=${options[item]}`
            })
        }
        uni.redirectTo({ url: url + params })
    }
}
</script>
