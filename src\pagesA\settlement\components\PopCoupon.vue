<template>
    <u-popup :show="isShow" v-prevent-scroll="isShow" mode="bottom" round="32rpx" @close="onClose"
        :closeOnClickOverlay="false" :safeAreaInsetBottom="true">
        <view class="container u-flex u-flex-col u-row-left u-col-center" :class="containerBgColor">
            <view class="title">优惠券</view>
            <block v-if="canUserCouponData && canUserCouponData.length > 0">
                <scroll-view class="main" scroll-y>
                    <view class="main-list" v-for="item in canUserCouponData" :key="item.id">
                        <CouponItem :isSelect="1" :itemData="item" @onTap="onSelect(item)" :selected="selected(item)"
                            :consumeList="consumeList" :couponList="couponList" :selectCouponId="selectCouponId" :isQixiCoupon="getIsQixiCoupon(item)"
                            :selectConsumeId="selectConsumeId" @showRule="handleShowRule" />
                    </view>
                </scroll-view>
                <view class="btn-container" v-if="canUseList && canUseList.length > 0">
                    <CustomButton :disabled="isDisabled" :customStyle="{
        width: '662rpx',
        height: '86rpx',
        margin: 'auto',
        'line-height': '86rpx',
        'border-radius': '192rpx',
        'text-align': 'center',
        'font-size': '32rpx',
        'font-weight': 500
    }" hover-class="btn-hover" text="确定使用" @click="handleComfirm"></CustomButton>
                </view>
            </block>
            <view class="empty-box" style="margin-top: 144rpx" v-else>
                <DefaultPage imgMbottom="0" imgHeight="508" imgWidth="508" :show="true"
                    icon="https://wpm-cdn.dreame.tech/images/202306/178397-1687834429297.png" tip="暂无优惠券" />
            </view>
            <view class="icon-close u-flex u-col-center u-row-center">
                <image class="icon" src="@/static/close.png" @click="onClose" />
            </view>
        </view>
        <CustomModal :show="showRuleModel" width="616rpx" contentStyle="color:#1D1E20;font-weight:500;font-size: 28rpx;"
            title="提示" :content="rule" confirmText="我知道了" :showCancelButton="false"
            confirmStyle="width: 524rpx; background: #E8DEC1;color: #8C6533;" @confirm="showRuleModel = false">
        </CustomModal>
    </u-popup>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import CouponItem from '@/components/CouponItem/CouponItem.vue';
import { OrderModule } from '@/store/modules/order';
import { ICouponInfo } from '@/store/interface/order';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';

const TYPE_COUPON = '1'; // 优惠卷
const EXCHANGE_COUPON = '6'; // 兑换券
const CONSUMER_COUPON = '10'; // 消费券
@Component({
    components: {
        CouponItem,
        DefaultPage,
    },
})
export default class PopCoupon extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isDisabled!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isChooseShopMoney!: Boolean;

    @Prop({ type: Array, default: () => [] })
    public consumeIds!: [];

    @Prop({ type: Array, default: [] })
    public carList: [];

    public selectCouponId: string = '';
    public selectConsumeId: string = '';
    public cardType: string = '';
    public canUserCouponData: Array<ICouponInfo> = [];
    public showRuleModel: Boolean = false;
    public rule: String = '';
    public canUseList: Array<any> = [];
    public consumeList: Array<any> = []; // 消费券列表
    public couponList: Array<any> = []; // 优惠券列表
    public oldItem: any = {};

    @Watch('isShow') // watch，此处是监听isShow的变化
    isShowChange(newVal: Boolean, oldVal: Boolean) {
        if (newVal) {
            this.init();
        }
    }

    @Watch('consumeIds') // watch，此处是监听consumeIds的变化
    consumeIdsChange(newVal: string[], oldVal: string[]) {
        if (!this.arraysEqual(newVal, oldVal)) {
            this.init();
        }
    }

    get containerBgColor(): string {
        return this.canUserCouponData && this.canUserCouponData.length > 0 ? 'bg-color' : '';
    }

    get qixiCouponIdList(): any {
        return process.env.VUE_APP_QIXI_FESTIVAL_COUPON_IDS.split('|');
    }

    getIsQixiCoupon(item: any): Boolean {
        if (item.get_relation && this.qixiCouponIdList.length > 0) {
            return this.qixiCouponIdList.includes(item.get_relation);
        }
        return false;
    }

    arraysEqual(arr1: any[], arr2: any[]): boolean {
        if (arr1.length !== arr2.length) {
            return false;
        }
        const sortedArr1 = [...arr1].sort();
        const sortedArr2 = [...arr2].sort();
        for (let i = 0; i < sortedArr1.length; i++) {
            if (sortedArr1[i] !== sortedArr2[i]) {
                return false;
            }
        }
        return true;
    }

    init() {
        console.log('🚀 ~ PopCoupon ~ init ~ OrderModule.orderCouponInfo:', OrderModule.orderCouponInfo)

        if (uni.$u.test.isEmpty(OrderModule.orderCouponInfo)) {
            this.canUserCouponData = [];
            return;
        }
        const result = OrderModule.orderCouponInfo.list;
            console.log('🚀 ~ PopCoupon ~ init ~ result:', result)
        const selectIdsSet = new Set<string>(OrderModule.orderCouponInfo.select.ids);
        console.log('🚀 ~ PopCoupon ~ init ~ selectIdsSet:', selectIdsSet)
        const consumeIdsSet = new Set<string>(this.consumeIds);
        console.log('🚀 ~ PopCoupon ~ init ~ consumeIdsSet:', consumeIdsSet)

        // 先标记所有优惠券为不可用
        result.forEach(item => { item.canUse = false });

        // 标记可用的优惠券
        result.forEach(item => {
            if (selectIdsSet.has(item.id) || consumeIdsSet.has(item.id)) {
                item.canUse = true;
            }
        });

        this.consumeList = result.filter(item => item.type === CONSUMER_COUPON);
        this.couponList = result.filter(item => item.type === TYPE_COUPON || item.type === EXCHANGE_COUPON);
        this.canUseList = [...Array.from(selectIdsSet), ...Array.from(consumeIdsSet)];
        this.canUserCouponData = result;

        // 如果有购物金且选中购物金，不默认选择优惠券
        this.selectCouponId = this.isChooseShopMoney ? '' : OrderModule.orderCouponInfo.select.id;
        this.selectConsumeId = this.isChooseShopMoney ? '' : OrderModule.orderCouponInfo.consume.id;
    }

    selected(item) {
        return [this.selectCouponId, this.selectConsumeId].includes(item.id);
    }

    handleShowRule(rule) {
        this.rule = rule;
        this.showRuleModel = true;
    }

    onSelect(item) {
        if (!item.canUse) {
            return false;
        }

        // 兑换券和优惠券互相切换
        if (item.type !== CONSUMER_COUPON && this.oldItem && this.oldItem.type && this.oldItem.type !== item.type && !this.selected(item)) {
            this.$emit('onSelectCoupon', { coupon_id: item.id, consume_id: '-1', card_type: item.type });
        }

        // 更新 oldItem 为当前选择的 item
        this.oldItem = item;

        // 初始化 emit 参数
        let coupon_id: string = '-1';
        let consume_id: string = '-1';

        // 如果选中的是当前已选的优惠券，取消选择
        if (this.selectCouponId === item.id) {
            this.selectCouponId = '';
            this.cardType = '';
        } else if (this.selectConsumeId === item.id) { // 如果选中的是当前已选的消费券，取消选择
            this.selectConsumeId = '';
            this.cardType = '';
        } else { // 否则，根据类型选择优惠券或消费券
            if (item.type === TYPE_COUPON || item.type === EXCHANGE_COUPON) {
                this.selectCouponId = item.id;
                coupon_id = item.id;
            } else if (item.type === CONSUMER_COUPON) {
                this.selectConsumeId = item.id;
                consume_id = item.id;
            }
            this.cardType = item.type;
        }

        // 触发自定义事件
        this.$emit('onSelectCoupon', { coupon_id, consume_id, card_type: item.type });
    }

    handleComfirm() {
        // if (OrderModule.orderCouponInfo.select.id === this.selectCouponId) return;
        const coupon_id = this.selectCouponId || this.selectCouponId == '0' ? this.selectCouponId : '-1';
        const consume_id = this.selectConsumeId || this.selectConsumeId == '0' ? this.selectConsumeId : '-1';
        const card_type = this.cardType;
        this.$emit('comfirm', { coupon_id, consume_id, card_type });
        this.onClose();
    }

    onClose() {
        this.$emit('update:isShow', false);
    }
}
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    height: 1116rpx;
    background: $text-color-white;
    border-radius: 32rpx;
    padding-top: 38rpx;

    .title {
        color: $text-color-primary;
        height: 42rpx;
        line-height: 42rpx;
        font-size: 32rpx;
        margin-bottom: 46rpx;
        font-weight: 500;
    }

    .main {
        height: 792rpx;
        overflow: hidden;
        padding: 0 38rpx;
        box-sizing: border-box;

        .main-list {
            width: 100%;
        }
    }

    .btn-container {
        height: 192rpx;
        width: 100%;
        padding: 38rpx 44rpx 70rpx;
        box-sizing: border-box;
    }

    .icon-close {
        position: absolute;
        top: 38rpx;
        right: 38rpx;
        width: 32rpx;
        height: 32rpx;

        .icon {
            width: 100%;
            height: 100%;
        }
    }

    .empty-box {
        width: 508rpx;
        height: 558rpx;
    }
}
</style>
