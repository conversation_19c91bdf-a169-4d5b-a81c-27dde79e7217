<template>
    <view class="share-page-container">
        <!-- 顶部导航栏 -->
        <view class="header">
            <view class="nav-bar">
                <view class="nav-left" @click="goBack">
                    <image
                        class="nav-arrow"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687ceb25bc80f7720012945.png"
                        mode="aspectFit"
                    ></image>
                </view>
                <view class="nav-title">
                    <text class="title-text">分享</text>
                </view>
                <view class="nav-right"></view>
            </view>
        </view>

        <!-- 产品信息卡片 -->
        <view class="product-card">
            <view class="product-content">
                <view class="product-image">
                    <image class="product-img" :src="item.cover_image" mode="aspectFill"></image>
                </view>
                <view class="product-info">
                    <view class="product-name">
                        <!-- <text class="brand-text">品牌</text> -->
                        <view class="name-container">
                            <text class="name-text">{{ item.name }}</text>
                        </view>
                    </view>
                    <view class="product-tags">
                        <view class="discount-tag">
                            <image
                                class="tag-icon"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb3fc482d72960011386.png"
                            ></image>
                            <text class="tag-text"
                                >已降价¥{{
                                    Number(item.subsidy_price) === 0
                                        ? Number(item.mprice) - Number(item.price)
                                        : Number(item.subsidy_price)
                                }}</text
                            >
                        </view>
                        <!-- <view class="points-tag">
                            <image
                                class="tag-icon"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb8e8adf7d7130011943.png"
                            ></image>
                            <text class="tag-text">{{ item.deposit }}积分可抵¥{{ item.coin_price }}</text>
                        </view> -->
                    </view>
                    <view class="group-buy-btn">
                        <image
                            class="btn-icon"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb5a89c12c6390068318.png"
                        ></image>
                    </view>
                    <view class="product-price">
                        <text class="price-text">¥{{ item.price }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 推荐理由 -->
        <view class="recommend-section">
            <view class="section-header">
                <text class="section-title">推荐理由</text>
            </view>
            <view class="reason-categories">
                <view class="reason-category" v-for="category in recommendCategories" :key="category.title">
                    <view class="category-title">
                        <text class="category-text">{{ category.title }}</text>
                    </view>
                    <view class="category-tags">
                        <view
                            class="reason-tag"
                            :class="{ selected: selectedReasons.includes(tag) }"
                            v-for="tag in category.reason_info"
                            :key="tag"
                            @click="toggleReason(tag)"
                        >
                            <text class="reason-text">{{ tag }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 分享按钮 -->
        <view class="share-section">
            <view class="share-btn" @click="handleShareClick">
                <text class="share-text">分享</text>
            </view>
        </view>
        <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'default'" />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
// import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { postReasonList, postReason } from '@/http/wish';
import { getShareContent } from '@/http/goods';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';

interface ProductItem {
    id: string;
    gid: string;
    goods_name: string;
    cover_image: string;
    price: string;
    mprice: string;
    total_point: string;
    deduction_rate: string;
    introduce?: string;
    tids: string[];
    name: string;
    inviter_id: number;
    share_price: number;
    is_group_goods: boolean;
    share_from: string;
}

interface RecommendCategory {
    title: string;
    reason_info: string[];
}

@Component({
    components: {
        ShareDialog,
    },
})
export default class SharePage extends Vue {
    public item: ProductItem = {
        id: '',
        gid: '',
        goods_name: '',
        cover_image: '',
        price: '',
        mprice: '',
        total_point: '',
        deduction_rate: '',
        introduce: '',
        tids: [],
        name: '',
        inviter_id: 0,
        share_price: 0,
        is_group_goods: false,
        share_from: '',
    };

    public shareContentList: Array<any> = [];

    public selectedReasons: string[] = [];
    // 推荐理由分类列表
    public recommendCategories: RecommendCategory[] = [];

    public show: Boolean = false;

    public productInfo: any = {
        name: '',
        desc: '',
        image: '',
        imageBg: '',
        price: '',
    };

    async onLoad(options: any) {
        // 从URL参数中获取商品数据
        if (options.item) {
            try {
                this.item = JSON.parse(decodeURIComponent(options.item));
            } catch (error) {
                console.error('解析商品数据失败:', error);
                (uni as any).showToast({
                    title: '数据加载失败',
                    icon: 'none',
                });
            }
        }
        // 获取推荐理由列表
        await this.getRecommendReasons();
        this.getShareContentData();
    }

    // 获取推荐理由列表
    async getRecommendReasons() {
        try {
            const result = await postReasonList({
                type: 6,
                with_total: 100,
            });
            if (result && result.data && result.data.list) {
                this.recommendCategories = result.data.list.map((item: any) => ({
                    title: item.title,
                    reason_info: item.reason_info || [],
                }));
            }
        } catch (error) {
            console.error('获取推荐理由失败:', error);
        }
    }

    // 返回上一页
    goBack() {
        // 返回到上一个页面
        Utils.goBack();
    }

    // 切换推荐理由选择状态
    toggleReason(reasonText: string) {
        const index = this.selectedReasons.indexOf(reasonText);
        if (index > -1) {
            this.selectedReasons.splice(index, 1);
        } else {
            this.selectedReasons.push(reasonText);
        }
    }

    async getShareContentData() {
        const res = await getShareContent({ tid: this.item.tids.length > 0 ? this.item.tids[0] : -1 });
        console.log('%c this.item: ', 'font-size:16px;background-color: #F5CE50;color:#fff;', this.item);
        this.shareContentList = res;
        console.log(
            '%c this.shareContentList: ',
            'font-size:16px;background-color: #42b983;color:#fff;',
            this.shareContentList,
        );
    }

    // 处理分享
    async handleShareClick() {
        // 获取选定的推荐理由
        const reasonContent =
            this.selectedReasons.length > 0 ? this.selectedReasons.join('，') : '这个商品实在是太香了！'; // 如果没有选择，使用默认值
        await postReason({
            goods_id: Number(this.item.gid),
            reason_content: reasonContent,
        });
        try {
            // const url = `pagesB/goodsDetail/goodsDetail?gid=${this.item.gid}`;
            // const data = {
            //     ...Constants.GEN_SHARE_LINK_TYPE,
            //     jumpLink: url,
            // };
            // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            //     const params = {
            //         target: 'wechat,qq,sina',
            //         type: 'web',
            //         content: {
            //             url: res.data,
            //             share_image: this.item.cover_image + '?x-oss-process=image/resize,w_200',
            //             share_title: '好朋友，一起购！',
            //             share_desc: '和朋友一起购物，在追觅享更多优惠。'
            //         },
            //     };
            //     Utils.messageChannel('share', params);
            // });
            // // Utils.goBack();
            // console.log('分享成功');
            // const sharePageUrl = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.item.gid}`;
            // const data = {
            //     ...Constants.GEN_SHARE_LINK_TYPE,
            //     jumpLink: sharePageUrl,
            // };
            // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            //     const params = {
            //         target: 'wechat,weixin_circle,qq,sina',
            //         type: 'web',
            //         content: {
            //             url:
            //                 `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?link=` +
            //                 res.data +
            //                 '&gid=' +
            //                 this.item.gid,
            //             share_image: this.item.cover_image + '?x-oss-process=image/resize,w_200',
            //             share_title: this.item.name,
            //             share_desc:
            //                 this.item.introduce ||
            //                 this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
            //         },
            //     };
            //     Utils.messageChannel('share', params);
            // });
            this.show = true;
            this.productInfo = {
                name: this.item.name,
                desc: this.item.introduce,
                image: this.item.cover_image,
                price: this.item.share_price,
            };
            if (this.item.is_group_goods) {
                Utils.taskComplete('shareGroupGoods');
            } else if (this.item.share_from === 'shopStore') {
                Utils.taskComplete('shareShopGoodsFriend');
            } else {
                Utils.taskComplete('shareGoodsFriendMoney');
            }
        } catch (e) {
            console.log('-------------------', e);
        }
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.cardShare(type, 'goods')(
            {
                target: 'image_template,download,wechat,weixin_circle,qq,sina',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.item.gid}&inviter_id=${this.item.inviter_id}`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.item.gid}&inviter_id=${this.item.inviter_id}`,
            },
            {
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.item.gid}&inviter_id=${this.item.inviter_id}`,
                    image: this.item.cover_image + '?x-oss-process=image/resize,w_200',
                    title: `【${this.productInfo.price}元】${this.item.name}`,
                    desc:
                        this.item.introduce ||
                        this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
                },
                extras: {
                    type: 'goods',
                    id: this.item.gid,
                    goods: {
                        name: this.item.name,
                        desc:
                            this.item.introduce ||
                            this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
                        image: this.item.cover_image,
                        price: this.item.share_price + '',
                        priceColor: '#FF7D01',
                        descColor: '#C59245',
                    },
                    copyText: `${this.item.share_price}元, ${this.item.name}`,
                },
            },
        );
    }
}
</script>

<style lang="scss">
@import './sharePage.scss';
</style>
