<template>
    <scroll-view class="banner-container"
        v-if="bannerList.length">
        <custom-swiper bgColor="$fill-color-bg-white" height="100%" :radius="radius" :circular="true"
            :autoplay="autoplay" :interval="11000" :duration="400" :fullScreenBtn="false" :isAutoPlayVideo="true"
            :showControls="false" @change="change" :list="bannerList" :indicatorStyle="indicatorStyle"
            @click="jumpDetailPage">
            <view slot="indicator" class="indicator-row u-flex u-col-center u-row-center">
                <view v-for="(item, i) in bannerList" class="indicator__dot" :key="i"
                    :class="{ active: i === currIndex }">
                </view>
            </view>
        </custom-swiper>
    </scroll-view>
    <view class="banner-container" :style="{ 'border-radius': '24px' }"
        v-else-if="isLoading">
        <u-skeleton :animate="true" :title="false" :loading="true" rows="1" :rowsHeight="rowsHeight"
            :rowsWidth="['100%']"></u-skeleton>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Constants from '@/common/Constants';
import CustomSwiper from '@/components/CustomSwiper/CustomSwiper.vue';
import Utils from '@/common/Utils';
import { AppModule } from '@/store/modules/app';

@Component({
    components: {
        CustomSwiper,
    },
})
export default class Notice extends Vue {
    @Prop()
    readonly banners;

    @Prop({ type: Boolean, default: true })
    readonly autoplay;

    @Prop({ type: Boolean, default: true })
    readonly isLoading;

    get rowsHeight(): any {
        let rowsHeight = []
        // #ifdef H5
        rowsHeight = [uni.upx2px(616)]
        // #endif

        // #ifdef MP-WEIXIN
        rowsHeight = [uni.upx2px(1192)]
        // #endif
        return rowsHeight
    }

    get bannerList(): Array<any> {
        if (this.banners && this.banners.length > 0) {
            const newList = [];
            this.banners.forEach((itm) => {
                if (itm.image) {
                    const item = { url: '', title: '', type: 'image' };
                    item.url = itm.image;
                    item.title = itm.title;
                    newList.push(item);
                }
                if (itm.video) {
                    const item = { url: '', title: '', type: 'image', poster: '' };
                    item.url = itm.video;
                    item.title = itm.title;
                    item.poster = `${itm.video}?x-oss-process=video/snapshot,t_1000,m_fast`;
                    item.type = 'video';
                    newList.push(item);
                }
            });
            return newList;
        }
        return [];
    }

    // #ifdef H5 || APP-PLUS
    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }
    // #endif

    public currIndex: number = 0;
    public radius: number = 24;
    public indicatorStyle: Object = {
        bottom: '45rpx',
        right: '40rpx'
    }

    created() {
        // #ifdef MP-WEIXIN
        this.radius = 0;
        this.indicatorStyle = {
            bottom: '114rpx'
        }
        // #endif
    }

    // banner跳转
    jumpDetailPage() {
        const item = this.banners[this.currIndex];

        // 特殊场景---红包banner---当banner配置为红包跳转路径时，跳转路径固定
        if (item.jump_url.includes('pagesC/lucky/lucky')) {
            this.$emit('luckyClick', item.jump_url)
            return;
        }

        const path = item.jump_url.split('?')[0];
        const jump_type = item.jump_type + ''
        Utils.reportEvent('banner_click', {
            banner_name: item.title
        })
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BANNER,
        });
        // #ifdef MP-WEIXIN
        switch (jump_type) {
            case '1':
                uni.navigateTo({
                    url: `/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`,
                });
                break;
            case '2':
                uni.navigateTo({
                    url: `/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`,
                });
                break;
            case '3':
                if (Constants.TAB_BAR_LIST.includes(path)) {
                    uni.reLaunch({
                        url: `${item.jump_url}`,
                    });
                } else {
                    uni.navigateTo({ url: `${item.jump_url}` })
                }
                break;
            case '4':
                uni.navigateToMiniProgram({
                    appId: item.appid,
                    path: item.jump_url,
                });
        }
        // #endif

        // #ifdef H5
        switch (jump_type) {
            case '1':
                Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`)
                break;
            case '2':
                Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`)
                break;
            case '3':
                if (Constants.TAB_BAR_LIST.includes(path)) {
                    if (path === '/pages/index/index') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                    } else if (path === '/pages/vipCenter/vipCenter') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/vip' });
                    } else if (path === '/pages/contents/contents') {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/explore' });
                    } else if (path === '/pages/shop/shop') {
                        Utils.navigateTo(`${item.jump_url}`)
                    } else {
                        Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                    }
                } else {
                    Utils.navigateTo(`${item.jump_url}`)
                }
                break;
            case '4':
                Utils.openMiniProgram({
                    id: item.appid,
                    path: item.jump_url,
                });
                break;
        }
        // #endif
    }

    change(e) {
        this.currIndex = e.current;
        const banner_name = this.bannerList[e.current].title
        Utils.reportEvent('banner_show', {
            banner_name: banner_name
        })
    }
}
</script>

<style lang="scss">
// 背景颜色
$fill-bgcolor-black: rgba(255, 255, 255, 0.5);

.banner-container {
    position: relative;
    width: 688rpx;
    height: 320rpx;
    overflow: hidden;
    margin: auto;
    // #ifdef MP-WEIXIN
    width: 750rpx;
    height: 1192rpx;
    position: fixed;
    top: 0;
    // #endif
}

@media screen and (min-width: 450px) {
    .banner-container {
        width: calc(100% - 60rpx);
        height: auto;
        aspect-ratio: 784/512;
    }
}

.swiper {
    width: 100%;
    height: 100%;

    .banner {
        width: 100%;
        height: 100%;
        position: relative;

        .banner-img {
            width: 100%;
            height: 100%;
        }

        .banner-info {
            width: 100%;
            height: auto;
            padding: 0 50rpx;
            position: absolute;
            left: 0;
            bottom: 90rpx;
        }

        .banner-title {
            width: 100%;
            font-size: 48rpx;
            color: $text-color-white;
            line-height: 66rpx;
            letter-spacing: 1rpx;
            overflow: hidden;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-align: center;
            margin-bottom: 22rpx;
        }

        .banner-jump {
            width: 220rpx;
            height: 56rpx;
            border: 2rpx solid $fill-color-bg-white;
            font-size: 26rpx;
            color: $text-color-white;
            line-height: 52rpx;
            letter-spacing: 1rpx;
            text-align: center;
            margin: 0 auto;
        }
    }
}

.indicator-row {
    width: 100%;
    height: 4rpx;
    z-index: 2;

    .indicator__dot {
        width: 38rpx;
        height: 4rpx;
        background-color: $fill-bgcolor-black;

        &.active {
            height: 4rpx;
            background-color: $fill-color-bg-white;
        }
    }
}
</style>
