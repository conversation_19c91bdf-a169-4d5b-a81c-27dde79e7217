<template>
   <view>
        <Comment :topicId="currActiveId"
            :flowList="flowList"
            :finished="finished"
            @share="handleShare"
            @changeFollowStatus="changeFollowStatus">
        </Comment>
   </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

import Utils from '@/common/Utils'

@Component
export default class Search extends Vue {
    constructor() {
        super();
    }

    public currActiveId = '';
    public flowList: any = [];

    navigateTo() {
        Utils.navigateTo('/pagesA/search/search?search_type=' + 1)
    }

    changeFollowStatus(item, status) {
        this.flowList.forEach(i => {
            if (i.creator === item.creator) {
                i.follow_status = status;
            }
        });
    }
}
</script>
<style lang="scss" scoped>
</style>
