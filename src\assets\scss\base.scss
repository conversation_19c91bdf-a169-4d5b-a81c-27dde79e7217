.u-flex {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
}
.item_desc {
    font-size: 24rpx;
    color: #C59245;
    line-height: 32rpx;
    margin-top: 10rpx;
}   

.u-flex-wrap {
    flex-wrap: wrap;
}

.u-flex-nowrap {
    flex-wrap: nowrap;
}

.u-col-center {
    align-items: center;
}

.u-col-top {
    align-items: flex-start;
}

.u-col-bottom {
    align-items: flex-end;
}

.u-row-center {
    justify-content: center;
}

.u-row-left {
    justify-content: flex-start;
}

.u-row-right {
    justify-content: flex-end;
}

.u-row-between {
    justify-content: space-between;
}

.u-row-around {
    justify-content: space-around;
}

.u-flex-col {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
}

.u-flex-1 {
    flex: 1
}

.u-flex-shrink0 {
    flex-shrink: 0;
}

// 定义flex等分
@for $i from 0 through 12 {
    .u-flex-#{$i} {
        flex: $i;
    }
}

// 文字样式
$medium: medium, 500;
$regular: regular, 400;
$light: light, 300;
$x-large-title: x-large-title, 56rpx;
$large-title: large-title, 48rpx;
$title: title, 40rpx;
$s-title: s-title, 36rpx;
$main-text: main-text, 32rpx;
$content: content, 28rpx;
$sub-content: sub-content, 24rpx;

@each $weight in $medium, $regular, $light {
    @each $size in $x-large-title, $large-title, $title, $s-title, $main-text, $content, $sub-content {
        .#{nth($size, 1)}-#{nth($weight, 1)} {
            font-weight: nth($weight, 2);
            font-size: nth($size, 2);
        }
    }
}

// 生成通用类
$mt-values: 12, 16, 32, 38, 78;
@each $top in $mt-values {
    .mt-#{$top} {
        margin-top: $top + rpx;
    }
}

// 全局状态样式
	// 通用标签
	.uni-genaral-tag {
		display: inline-block;
		padding: 6rpx 12rpx;
		font-family: MiSans, MiSans;
		font-weight: 500;
		font-size: 20rpx;
		line-height: 24rpx;
		text-align: center;
		background: linear-gradient( 222deg, #EE0000 0%, #FF8282 100%) !important;
		color: $text-color-white;
		border-radius: 4rpx !important;
	}
	// 预售标签 以旧换新标签
	.uni-presale-tag, .uni-tradeIn-tag {
		@extend .uni-genaral-tag;
		background: linear-gradient( 222deg, #EE6B00 0%, #F6A253 100%) !important;
	}
// 状态按钮
    .uni-button {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80rpx;
        &--primary {
            font-family: MiSans, MiSans;
            font-weight: 500;
            font-size: 32rpx;
            color: #8C6533;
            background: #F5EDD6;
            border-radius: 192rpx;
        }
        &--disabled {
            background: #f5f5f5 !important;
            color: rgba(34, 34, 34, 0.25) !important;
        }
        &:active{
            opacity: 0.8;
        }
        
    }
.uni-scroll-view-refresh {
    svg {
        color: #8c6533 !important;
        fill: #8c6533 !important;
    }
    circle {
        color: #8c6533 !important;
    }
}
