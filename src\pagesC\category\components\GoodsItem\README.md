# GoodsItem 商品卡片组件

一个用于展示商品信息的卡片组件，支持显示商品图片、标题、规格、价格等信息。

## 功能特性

- 🎨 精美的卡片设计，支持点击交互效果
- 📱 响应式布局，适配不同屏幕尺寸
- 🔗 支持自定义跳转链接或商品详情页
- 📊 内置埋点统计功能
- 🎯 支持显示优惠券信息和原价对比
- ⚡ 图片懒加载优化性能

## 使用方法

### 基础用法

```vue
<template>
    <view class="goods-list">
        <GoodsItem 
            :productInfo="product" 
            @card-click="handleProductClick"
        />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { GoodsItem, GoodsItemInfo, ProductCardClickEvent } from '@/pagesC/category/components/GoodsItem';

@Component({
    components: { GoodsItem },
})
export default class ProductList extends Vue {
    public product: GoodsItemInfo = {
        id: 1,
        title: '系列玩偶kawaii',
        image: 'https://example.com/product.jpg',
        specs: '尺寸：XS、S、M',
        currentPrice: 299,
        originalPrice: 499,
        couponText: '起券后价',
        gid: '1001'
    };

    handleProductClick(event: ProductCardClickEvent) {
        console.log('商品被点击:', event.product);
    }
}
</script>
```

### 批量展示

```vue
<template>
    <view class="goods-grid">
        <GoodsItem 
            v-for="(product, index) in productList"
            :key="product.id"
            :productInfo="product"
            @card-click="handleProductClick"
        />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { GoodsItem, GoodsItemInfo } from '@/pagesC/category/components/GoodsItem';

@Component({
    components: { GoodsItem },
})
export default class ProductGrid extends Vue {
    public productList: GoodsItemInfo[] = [
        {
            id: 1,
            title: '系列玩偶kawaii',
            image: 'https://example.com/product1.jpg',
            specs: '尺寸：XS、S、M',
            currentPrice: 299,
            originalPrice: 499,
            couponText: '起券后价',
            gid: '1001'
        },
        // 更多商品...
    ];

    handleProductClick(event: ProductCardClickEvent) {
        // 处理商品点击事件
    }
}
</script>

<style lang="scss" scoped>
.goods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, 262rpx);
    gap: 16rpx;
    padding: 16rpx;
}
</style>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| productInfo | GoodsItemInfo | - | 商品信息对象（必填） |
| clickable | boolean | true | 是否可点击 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| card-click | ProductCardClickEvent | 卡片被点击时触发 |

## 类型定义

### GoodsItemInfo

```typescript
interface GoodsItemInfo {
    id: string | number;           // 商品ID
    title: string;                 // 商品标题
    image: string;                 // 商品图片URL
    specs?: string;                // 商品规格描述
    currentPrice: number | string; // 当前价格
    originalPrice?: number | string; // 原价
    couponText?: string;           // 优惠券文本
    url?: string;                  // 跳转链接
    gid?: string | number;         // 商品ID（用于跳转详情）
    visible?: boolean;             // 是否显示
    extra?: Record<string, any>;   // 扩展数据
}
```

### ProductCardClickEvent

```typescript
interface ProductCardClickEvent {
    product: GoodsItemInfo;        // 商品信息
    type: 'card' | 'image' | 'price'; // 事件类型
    index?: number;                // 点击位置索引
}
```

## 样式定制

组件使用 SCSS 编写样式，支持通过 CSS 变量进行定制：

```scss
.kawaii-product-card {
    // 可以通过覆盖这些样式来定制外观
    width: 262rpx;
    height: 251rpx;
    background: #ffffff;
    border-radius: 8px;
    // ...
}
```

## 注意事项

1. 确保传入的 `productInfo` 包含必要的字段
2. 图片建议使用适当尺寸以优化加载性能
3. 组件会自动处理文本溢出，超长文本会显示省略号
4. 点击事件会自动进行埋点统计
