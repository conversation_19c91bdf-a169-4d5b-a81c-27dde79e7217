import http from './index';

// 商品对比分类列表
export function cateList() {
    return http.post('main/goods-param/cate-list');
}

export function goodsList(param) {
    return http.post('main/goods-param/goods-list', param);
}

// 商品详情页获取商品参数
export function goodsParamiIndex(param) {
    return http.post('main/goods-param/compare-detail', { ...param });
}

// 商品详情页获取商品参数
export function goodsDetailCompare(param) {
    return http.post('main/goods-param/compare', { ...param });
}

// 商品是否下架
export function saleStatus(param) {
    return http.post('main/goods/sale-status', { ...param });
}
