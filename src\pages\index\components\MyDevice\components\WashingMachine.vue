<template>
    <div class="device-item" @click="gotoDevice">
        <div class="device-info-card">
            <img :src="image" alt="设备图片" class="device-info-image" />
           <view class="info_container">
            <div class="device-info-name">{{ currentDevice.customName || currentDevice.displayName }}</div>
            <div class="device-info-area">
                <div class="device-info-status">
                    <div>{{ status  }}</div>
                </div>
            </div>
           </view>
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage = 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png'

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage
        },
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true
        }
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber()
            const bind_id = bindDomain.split('.')[0]
            const data = { did, id, from: 'mapp', method: 'get_properties', params: [{ did, siid: 2, piid: 1 }, { did, siid: 3, piid: 1 }, { did, siid: 4, piid: 38 }, { did, siid: 4, piid: 83 }, { did, siid: 4, piid: 48 }] }
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data)
            }
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter(r => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter(r => r.siid === 3 && r.piid == 1);
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel('DEVICE', 'getStatusStr', {
                                model: model,
                                latestStatus: onlineStatus[0].value
                            }, (res) => {
                                newStatus.status = res.data;
                                this.currentDeviceStatus = { ...newStatus };
                            });
                        }
                    }
                }
            };
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: body
            }, commandResultCB);
        },
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    }
}

</script>

<style scoped>
.device-item {
    width: 360rpx;
    height: 156rpx;
    box-sizing: border-box;
    padding: 14rpx;
    background: linear-gradient(112deg, #EBF4FF 0%, #F7F9FA 48%, #FFFFFF 100%, #FFFFFF 100%);
    display: flex;
    flex-direction: column;
    border-radius: 24rpx;
    border-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%) 1;
}
.device-item-more {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    width: 52rpx;
    height: 52rpx;
}
.device-info-card {
    display: flex;
    height: 100%;
}

.device-info-name {
    color: #121212;
    font-size: 34rpx;
    font-weight: 500;
    overflow: hidden;
    margin-top: 8rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: 220rpx;
}
.device-info-status {
    color: #A6A6A6;
    font-size: 24rpx;
    display: flex;
    font-weight: regular;
    align-items: center;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.device-info-image{
    width: 88rpx;
    /* width: 94rpx;
    height: 94rpx; */
    object-fit: contain;
}

.other-device {
    color: #3D3D3D;
    font-size: 24rpx;
    line-height: 32rpx;
    margin: 44rpx 0 32rpx 32rpx;
    /* margin-left: 32rpx; */
}
.info_container{
    margin-left: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    max-width: 40%;
}
.device-info-name {
    color: #404040;
    font-size: 34rpx;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 170rpx;
}
</style>
