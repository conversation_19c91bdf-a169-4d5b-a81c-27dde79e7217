<template>
    <view class="item-container">
        <view class="edge-distance" :class="{ is_last_child: is_last_child }">
            <view :class="['item', direction, handleGoodsDisable(good) ? 'disabled' : '']" @click="gotoGdetailPage"
                @touchstart="drawStart" @touchmove="drawMove" @touchend="drawEnd"
                :style="{ transform: `translateX(${-right}rpx)` }">
                <image v-if="isShowSelect" @click.stop="onSelectItem(index)" class="icon" :src="good.selected
            ? 'https://wpm-cdn.dreame.tech/images/202306/514215-1687241028296.png'
            : 'https://wpm-cdn.dreame.tech/images/202306/340856-1687241129889.png'
            " mode="aspectFit"></image>
                <view class="good-cover" :class="bigCover ? 'big' : ''">
                    <image v-if="!good.spec || (good.spec && good.spec.length === 0)"
                        :src="good.market_image || good.images"></image>
                    <image v-else :src="good.spec.image" />
                    <image v-if="isNotSold" class="tag-image"
                        src="https://wpm-cdn.dreame.tech/images/202306/857253-1687774910809.png">
                    </image>
                    <!-- 已下架 -->
                    <image v-else-if="isSoldOut" class="tag-image"
                        src="https://wpm-cdn.dreame.tech/images/202306/118629-1687774792279.png"></image>
                    <!-- 无货 -->
                </view>
                <view class="good-detail">
                    <view class="good-name"
                        :class="{ 'u-line-1': direction === 'vertical', 'u-line-2': direction != 'vertical' }">
                        <block v-if="is_trade_in === '1'">
                            <text class="isTradein uni-tradeIn-tag">以旧换新</text>
                            <view class="text" style="text-indent: 112rpx;">{{ good.name }}</view>
                        </block>
                        <block v-else>
                            <text class="isInShop uni-genaral-tag" v-if="good.is_internal_purchase == 1">内购</text>
                            <text class="isPresale uni-presale-tag" v-if="good.is_presale == 1">预售</text>
                            <view class="text" style="text-indent: 72rpx;" v-if="good.is_internal_purchase == 1 || good.is_presale == 1">{{ good.name }}</view>
                            <view v-else>{{ good.name }}</view>
                        </block>
                    </view>
                    <view v-if="direction !== 'vertical' && ((good.spec && good.spec.attr_cnf) || good.attr_cnf)"
                        class="attr u-flex u-col-center u-row-between" style="margin-top: 32rpx; margin-bottom: 32rpx">
                        <view class="attr">
                            <text class="attr-item u-line-2"
                                v-for="(item, index) in (good.spec && good.spec.attr_cnf) || good.attr_cnf"
                                :key="index">{{ item.at_val }}</text>
                        </view>
                        <view class="num-text" v-if="isShowNum"> x{{ good.num }} </view>
                    </view>

                    <!-- <view class="attr u-flex u-col-center u-row-left">
                            <text class="attr-item" v-for="(item, index) in orderBoxData.attr_cnf" :key="index"> {{
                                item.at_val
                            }}</text>
                        </view>
                        <view class="num">x{{ orderBoxData.num }}</view> -->
                    <!-- <view class="num-text" v-if="isShowNum"> x{{ good.num }} </view> -->
                    <!-- 仅购物车页面展示 -->
                    <view class="endTime"
                        v-if="good.is_ini == 1 && good.gini_etime > 0 && isShowSelf && !handleGoodsDisable(good)">
                        <text class="timeText">距活动结束剩</text>
                        <view class="originTime">
                            <u-count-down :time="good.gini_etime * 1000 - currentTime" format="DD天 HH:mm:ss"
                                @finish="$emit('refreshList')"></u-count-down>
                        </view>
                    </view>
                    <view v-if="is_trade_in === '1'">
                        <view class="one">x 1</view>
                        <view v-if="good.spec && good.spec['price']" class="good-price u-flex" style="justify-content: flex-end; margin-top: 32rpx">
                            <view class="price" style="color: #404040;">
                                <text class="sign" style="margin-right: 10rpx">¥</text>
                                <text class="num">{{ parseFloat(good.spec['price']) }}</text>
                            </view>
                        </view>
                        <view v-else class="good-price u-flex" style="justify-content: flex-end; margin-top: 32rpx">
                            <slot name="inner">
                                <view class="price" style="color: #404040;">
                                    <text class="sign" style="margin-right: 10rpx">¥</text>
                                    <text class="num">{{ good.is_presale ? parseFloat(good.uprice) :
            parseFloat(good.price) }}</text>
                                </view>
                                <view class="mprice" v-if="isShowMprice && good.mprice">
                                    <text class="sign" style="margin-right: 10rpx">¥</text>
                                    <text class="num">{{ parseFloat(good.mprice) }}</text>
                                </view>
                            </slot>
                        </view>
                    </view>
                    <view v-else class="u-flex u-row-between u-col-center" style="margin-top: 32rpx">
                        <view v-if="pointsMall" class="good-price">
                            <view class="price-point" v-if="good.spec && !Array.isArray(good.spec)">
                                <view class="point" v-if="can_coin"><text class="point-number">{{
            parseFloat(good.spec['point']) }}</text><text class="point-unit">积分</text>
                                </view>
                                <view class="plus-sign" v-if="price && can_coin">+</view>
                                <view class="pay-price" v-if="price">￥{{ parseFloat(good.spec['price']) }}</view>
                            </view>
                            <view class="price-point" v-else>
                                <view class="point" v-if="can_coin"><text class="point-number">{{
            parseFloat(good['point']) }}</text><text class="point-unit">积分</text></view>
                                <view class="plus-sign" v-if="price && can_coin">+</view>
                                <view class="pay-price" v-if="price">￥{{ parseFloat(good['price']) }}</view>
                            </view>
                        </view>
                        <view v-else-if="good.spec && good.spec['price']" class="good-price">
                            <view class="price">
                                <text class="sign" style="margin-right: 10rpx">¥</text>
                                <text class="num">{{ parseFloat(good.spec['price']) }}</text>
                            </view>
                        </view>
                        <view v-else class="good-price">
                            <slot name="inner">
                                <view class="price">
                                    <text class="sign" style="margin-right: 10rpx">¥</text>
                                    <text class="num">{{
            good.is_presale ? parseFloat(good.uprice) : parseFloat(good.price)
        }}</text>
                                </view>
                                <view class="mprice" v-if="isShowMprice && good.mprice">
                                    <text class="sign" style="margin-right: 10rpx">¥</text>
                                    <text class="num">{{ parseFloat(good.mprice) }}</text>
                                </view>
                            </slot>
                        </view>
                        <view v-if="isShowNumBox" @click.stop>
                            <number-box v-if="good.limit_num > 0" :value.sync="good.num" :min="1" :max="good.limit_num"
                                bgColor="#fff" buttonWidth="48rpx" fontSize="30rpx"
                                :pointDisabled="pointsMall && good.limit_num == 1" color="#333333"
                                :disabled="disabledInput || buy_goods_number" :disableMinus="disabledInput" :disablePlus="disabledInput"
                                @blur="valChange($event, good)" @change="valChange($event, good)" @overlimit="handleOverLimit">
                            </number-box>
                            <number-box v-else :value.sync="good.num" :min="1"
                                bgColor="#fff" buttonWidth="48rpx" fontSize="30rpx" color="#333333"
                                :disabled="disabledInput || buy_goods_number" :disableMinus="disabledInput" :disablePlus="disabledInput"
                                @blur="valChange($event, good)" @change="valChange($event, good)"
                                @overlimit="handleOverLimit">
                            </number-box>
                        </view>
                        <view class="disabled-modify" v-if="isShowNumBoxNoModify"> x{{ good.num }} </view>
                    </view>
                    <view class="deposit" v-if="good.is_presale == 1 && isShowdeduction">
                        <text class="depositStyle"
                            v-if="good.deposit != 0 && good.expand_price != 0 && good.deposit != good.expand_price">定金{{
                            good.deposit }}抵{{ good.expand_price }}</text>
                    </view>
                    <slot name="footer"></slot>
                </view>
            </view>
            <view v-if="canDelete" class="delete u-flex u-row-center u-col-center" @click.stop="delData(good)">
                <image class="iamge" src="https://wpm-cdn.dreame.tech/images/202306/451555-1687085770564.png" />
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit } from 'vue-property-decorator';
import NumberBox from '@/components/NumberBox/NumberBox.vue';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';

@Component({
    components: {
        NumberBox,
    },
})
export default class GoodItem extends Vue {
    @Prop({
        type: Object,
        default: {},
    })
    readonly good;

    @Prop({
        type: Number,
        default: 0,
    })
    readonly buy_goods_number;

    @Prop({
        type: Object,
        default: {},
    })
    readonly total;

    @Prop({ type: Boolean, default: false })
    readonly pointsMall;

    @Prop({ type: String, default: 'cart' })
    readonly type;

    @Prop({ type: Boolean, default: false })
    readonly is_last_child;

    @Prop({ type: String, default: 'vertical' })
    readonly direction;

    @Prop({ type: Boolean, default: false })
    readonly isShowSelf;

    @Prop({ type: Boolean, default: false })
    readonly isShowMprice;

    @Prop({ type: Boolean, default: false })
    readonly isShowSelect;

    @Prop({ type: Boolean, default: false })
    readonly isShowNumBox;

    @Prop({ type: Boolean, default: false })
    readonly isShowNumBoxNoModify;

    @Prop({ type: Boolean, default: false })
    readonly isShowPresale;

    @Prop({ type: Boolean, default: true })
    readonly isShowdeduction;

    @Prop({ type: Number, default: 0 })
    readonly index;

    @Prop({ type: Boolean, default: false })
    readonly canDelete;

    @Prop({ type: Boolean, default: true })
    readonly isShowDetail;

    @Prop({ type: Boolean, default: false })
    readonly bigCover;

    @Prop({ type: Boolean, default: false })
    readonly isShowNum;

    @Prop({ type: String, default: '' })
    readonly is_trade_in;

    public goodNoneImageUrl: string = ''; // 蒙层图片
    public startX: number = 0;
    public isSoldOut: boolean = false;
    public isNotSold: boolean = false;
    public right: number = 0;
    public delBtnWidth: number = 180;
    public transition: string = 'transition: none 0 ease 0s';
    public currentTime: number = +new Date();

    get can_coin(): any {
        let can_coin = 0;
        if (this.good.spec && this.good.spec.point) {
            can_coin = Number(this.good.spec.point);
        } else {
            can_coin = Number(this.good.tpoint)
        }

        return can_coin;
    }

    get price(): any {
        let price = 0;
        if (this.good.spec && this.good.spec.price) {
            price = Number(this.good.spec.price);
        } else {
            price = Number(this.good.tprice);
        }
        return price;
    }

    get style(): any {
        return { transform: `translateX(${-this.right}rpx)` };
    }

    get disabledInput(): boolean {
        console.log('this.good', this.good)
        // 虚拟商品 && (停车券) 目前限制只能买一个
        return this.good.type === '2' && this.good.subtype === '2'
    }

    async valChange(event, item) {
        try {
            this.$emit('valChange', { value: event.value });
        } catch (e) {
            console.error('valChange', e);
        }
    }

    handleChange(event, item) {
        if (this.type !== 'cart') {
            this.$emit('valChange', { value: event.value });
        }
    }

    // 处理无货、已下架 样式状态
    handleGoodsDisable(item) {
        if (String(item.status) === Constants.STATUS_SOLD_OUT || String(item.is_del) === Constants.STATUS_DEL) {
            this.isNotSold = true;
            return true;
        } else {
            this.isNotSold = false;
        }
        if (String(item.prod_status) === Constants.PROD_STATUS_NONE_GOODS) {
            this.isSoldOut = true;
            return true;
        } else {
            this.isSoldOut = false;
        }
        return false;
    }

    @Emit('onSelectItem')
    onSelectItem(index) { }

    gotoGdetailPage() {
        // Utils.addCountlyEvent({ key: 'gotoGdetailPage', segmentation: this.good })
        if (!this.isShowDetail || this.isNotSold || this.isSoldOut) return;
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${this.good.gid}`);
    }

    // 开始触摸滑动
    drawStart(e) {
        if (!this.canDelete) return;

        const touch = e.touches[0];
        this.startX = touch.clientX;
        this.transition = 'transition: none 0 ease 0s';
    }

    // 触摸滑动
    drawMove(e) {
        if (!this.canDelete) return;
        this.right = 0;
        this.transition = 'transition: none 0 ease 0s';
        const touch = e.touches[0];
        let disX = this.startX - touch.clientX;

        if (disX >= 0) {
            if (disX > this.delBtnWidth) {
                disX = this.delBtnWidth;
            }
            this.right = disX;
        } else {
            this.right = 0;
        }
    }

    // 触摸滑动结束
    drawEnd(e) {
        if (!this.canDelete) return;
        this.transition = 'transition: transform 300ms ease 0s;';
        if (this.right >= this.delBtnWidth / 2) {
            this.right = this.delBtnWidth - 12;
        } else {
            this.right = 0;
        }
    }

    handleOverLimit(type) {
        this.$emit('limitOver', type);
    }

    @Emit('delData')
    delData(good) { }
}
</script>
<style lang="scss" scoped>
.item-container {
    position: relative;
    background-color: #fff;
    padding: 0rpx 32rpx 0rpx 32rpx;

    .is_last_child {
        border-bottom: 2rpx dashed $fill-color-dark;
    }

    .edge-distance {
        // border-bottom: 2rpx dashed red;
        padding: 38rpx 0rpx 44rpx 0rpx;
    }

    .item {
        z-index: 2;
    }

    .disabled-modify {
        font-size: 28rpx;
        font-weight: 400;
        color: $text-color-disable;
        line-height: 38rpx;
    }

    .good-price {
        .price-point {
            display: flex;
            align-items: center;
            height: 40rpx;
            margin-bottom: 24rpx;
            margin-top: 16rpx;

            .point {
                display: flex;
                align-items: center;
                padding: 0px;

                .point-number {
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #ab8c5e;
                }

                .point-unit {
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #ab8c5e;
                    margin-left: 4rpx;
                }
            }

            .plus-sign {
                font-size: 28rpx;
                font-weight: 500;
                color: #ab8c5e;
                width: 26rpx;
                margin: 0rpx 0rpx 0rpx 12rpx;
            }

            .pay-price {
                font-size: 28rpx;
                font-weight: 500;
                color: #ab8c5e;
            }
        }

        .price {
            display: inline-block;
            height: 40rpx;
            font-size: 32rpx;
            font-family: MiSans-Medium, MiSans;
            font-weight: 500;
            color: $fill-color-primary-active;
            line-height: 40rpx;

            .sign {
                font-size: 32rpx;
                font-weight: 500;
                line-height: 40rpx;
            }
        }

        .mprice {
            margin-left: 8rpx;
            display: inline-block;
            height: 40rpx;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #aaaaaa;
            line-height: 40rpx;
            text-decoration: line-through;
        }
    }

    .item.horizontal {
        width: 100%;
        background: $fill-color-bg-white;
        // margin-bottom: 32rpx;
        // border-radius: 16rpx;
        box-sizing: border-box;
        display: flex;
        position: relative;

        .icon {
            min-width: 36rpx;
            width: 36rpx;
            height: 36rpx;
            margin-right: 8rpx;
            z-index: 1;
        }

        .good-cover {
            width: 154rpx;
            min-width: 154rpx;
            height: 154rpx;
            margin-right: 16rpx;
            position: relative;

            image {
                width: 100%;
                height: 100%;
                border-radius: 14rpx;
            }

            &.big {
                width: 154rpx;
                min-width: 154rpx;
                height: 154rpx;
            }

            .tag-image {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 92rpx;
                height: 92rpx;
                z-index: 1;
            }
        }

        .attr {
            display: flex;
            flex-wrap: wrap;
            font-size: 24rpx;
            flex: 1;

            .attr-item {
                color: $text-color-secondary;
                background-color: $fill-color-bg-gray;
                margin-right: 12rpx;
                // height: 40rpx;
                max-height: 80rpx;
                line-height: 40rpx;
                border-radius: 8rpx;
                font-size: 24rpx;
                padding-right: 12rpx;
                padding-left: 12rpx;
                margin-top: 12rpx;
                border-radius: 8rpx;
                word-break: break-all;
            }
        }

        .good-detail {
            min-height: 180rpx;
            flex: 1;

            .good-name {
                line-height: 38rpx;
                font-weight: 500;
                font-size: 28rpx;
                color: $text-color-primary;
                position: relative;

                .isPresale,
                .isInShop,
                .isTradein {
                    position: absolute;
                    left: 0;
                    line-height: 26rpx;
                }

                .text {
                    font-size: 28rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #1d1e20;
                }
            }
        }
    }

    .item.vertical {
        width: 332rpx;
        height: 492rpx;
        background: #ffffff;
        box-sizing: border-box;
        border-radius: 16rpx;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        position: relative;

        .good-cover {
            width: 240rpx;
            height: 240rpx;
            margin: auto;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .good-name {
            line-height: 38rpx;

            .tag {
                display: inline-block;
                width: 62rpx;
                height: 38rpx;
                background: #f44e4e;
                border-radius: 8rpx 8rpx 8rpx 8rpx;
                margin-right: 18rpx;
                color: #ffffff;
                font-size: 20rpx;
                text-align: center;
            }

            .text {
                font-size: 28rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: $text-color-primary;
                line-height: 38rpx;
            }
        }

        .good-price {
            margin-top: 16rpx;

            .price {
                display: inline-block;
                height: 56rpx;
                font-size: 40rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: $theme-color;
                line-height: 56rpx;

                .sign {
                    font-size: 24rpx;
                    font-weight: 400;
                    line-height: 34rpx;
                }
            }

            .mprice {
                margin-left: 8rpx;
                display: inline-block;
                height: 34rpx;
                font-size: 24rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #aaaaaa;
                line-height: 34rpx;
                text-decoration: line-through;
            }
        }
    }

    .delete {
        width: 116rpx;
        height: 100%;
        background-color: $func-color-danger-bg;
        position: absolute;
        top: 0;
        right: 0rpx;
        font-size: 28rpx;
        font-family: MiSans-Medium, MiSans;
        font-weight: 500;
        color: #ffffff;
        border-radius: 0rpx 16rpx 16rpx 0rpx;
        z-index: 1;

        .iamge {
            width: 46rpx;
            height: 46rpx;
        }

        .icon_delete {
            height: 80rpx;
            width: 80rpx;
        }
    }

    .disabled::after {
        display: block;
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 24rpx;
        background-color: rgba($color: #ffffff, $alpha: 0.7);
    }

    .disabled {
        .icon {
            opacity: 0.7;
        }
    }

    .presaleStyle {
        width: 70rpx;
        height: 38rpx;
        background: #f44e4e;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        opacity: 1;
        color: #ffffff;
        margin-right: 18rpx;
        font-size: 19rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 27rpx;
        padding: 8rpx 12rpx;
        text-align: center;
    }

    .postionRelative {
        position: relative;
        top: -6rpx;
    }

    .endTime {
        white-space: normal;
        background-image: url('https://wpm-cdn.dreame.tech/images/202212/527618-1671012081184.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        height: 40rpx;
        width: 100%;
        padding-left: 12rpx;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        margin-top: 16rpx;
        margin-bottom: 16rpx;

        .timeText {
            height: 28rpx;
            font-size: 20rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #f9281a;
            line-height: 28rpx;
            margin-right: 8rpx;
        }

        .originTime {
            height: 28rpx;
            font-size: 20rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #f9281a !important;
            line-height: 28rpx;

            ::v-deep .u-count-down__text {
                height: 28rpx;
                font-size: 20rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #f9281a !important;
                line-height: 28rpx;
            }
        }
    }

    .num-text {
        text-align: right;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #aaaaaa;
    }

    .deposit {
        .depositStyle {
            background: #ffffff;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
            opacity: 1;
            border: 1rpx solid #e72d2d;
            font-size: 19rpx;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #f44e4e;
            padding: 4rpx 12rpx;
        }
    }

    .one {
        font-size: 26rpx;
        color: $text-color-disable;
        line-height: 34rpx;
        margin-top: 12rpx;
    }
}
</style>
