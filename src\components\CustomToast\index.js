import Vue from 'vue';
import CustomToast from './CustomToast.vue';

// 创建全局的Vue实例
const toastVue = new Vue({
    components: {
        CustomToast,
    },
    methods: {
        showToast(message, duration = 2500, icon = null) {
            console.log(CustomToast);
            CustomToast.methods.show({
                message: message,
                duration: duration,
                icon: icon,
            });
        },
    },
});

// 将全局的toast方法挂载到Vue原型上
Vue.prototype.$toast = toastVue.showToast;

export default toastVue;
