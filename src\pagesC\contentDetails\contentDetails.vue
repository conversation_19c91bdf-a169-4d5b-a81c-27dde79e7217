<template>
    <view>
        <APPSHARE :link="link"></APPSHARE>
        <view
            class="contentDetails-container"
            :style="{ paddingBottom: pagePaddingBottom + 'rpx' }"
            v-if="!pageLoading"
        >
            <!-- 他人，被举报审核成功的内容不展示 -->
            <!-- 自定义导航栏 -->
            <custom-bar
                :isFixed="true"
                :customBack="true"
                backIcon="https://wpm-cdn.dreame.tech/images/202302/508756-1676371936507.png"
                @back="toHome"
                :customStyle="{ top: $isInApp() ? '0' : '96rpx' }"
            >
                <block v-if="!(downStatus || is_view === 0 || is_delete === 1) || isContentCreator">
                    <view class="user-header">
                        <view class="user-header-imgs" @click="handleJumpHome(false)">
                            <image class="user-header-imgs--avatar" :src="data.author_avatar" />
                            <image
                                v-if="data.user_avatar_icon"
                                class="user-header-imgs--icon"
                                :src="data.user_avatar_icon"
                            />
                        </view>
                        <view
                            class="user-header-author u-line-1"
                            :style="{ maxWidth: isContentCreator ? '400rpx' : '300rpx' }"
                            @click="handleJumpHome(false)"
                        >
                            {{ data.author }}
                        </view>
                        <img
                            v-if="data.creator == '3587481'"
                            style="margin-left: 10rpx; width: 138rpx; height: 36rpx"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png"
                            alt=""
                        />
                    </view>
                </block>
                <!-- #ifdef H5 -->
                <template v-if="!downStatus || isContentCreator" v-slot:operate>
                    <view v-if="delistStatus || isContentCreator" class="operate-header">
                        <view
                            class="user-header-relation"
                            :class="{ active: relationStatus !== 0 }"
                            v-if="!isContentCreator && user_id != data.creator"
                            @click="handleUserFollow"
                        >
                            <img
                                style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                v-if="relationText === '关注'"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                            />
                            <img
                                style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                v-if="relationText === '已关注'"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png"
                            />
                            {{ relationText }}
                        </view>
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ecd48e252d9270423552.png"
                            @click="handlerShare"
                        />
                    </view>
                </template>
                <!-- #endif -->
            </custom-bar>
            <view class="remove" v-if="(!isContentCreator && (downStatus || !is_view)) || is_delete">
                <image
                    v-if="!is_view || is_delete"
                    :class="{ is_view: !is_view || is_delete }"
                    src="https://wpm-cdn.dreame.tech/images/202501/700717-1736407575627.png"
                ></image>
                <image v-else src="https://wpm-cdn.dreame.tech/images/202501/587347-1736820436449.png"></image>
                <text>{{ !is_view || is_delete ? '当前内容无法展示' : '当前内容已下架' }}</text>
                <view class="jump-home" @click="handleJumpHome(true)">
                    <text>跳转{{ !isContentCreator ? 'Ta' : '我' }}的主页·{{ timeNum }}s</text>
                </view>
            </view>
            <template v-else>
                <!-- 审核状态栏，自己发布的内容，且不为草稿，且状态为发布审核中、举报审核中、举报成功，展示tip -->
                <view
                    v-if="
                        isContentCreator &&
                        data.status !== 1 &&
                        (data.audit_status !== 2 || [1, 2].includes(data.report_status))
                    "
                    class="status-bar"
                    :style="{
                        top: `${pagePaddingTop}rpx`,
                        color: `${currentAuditStatus.color}`,
                        background: `${currentAuditStatus.bgColor}`,
                    }"
                >
                    {{ currentAuditStatus.label }}
                </view>
                <view class="content">
                    <!-- 轮播图 -->
                    <view :style="{ opacity: opacity }" v-if="banners.length" @longpress="handOperatePopupOpen">
                        <custom-swiper
                            height="750rpx"
                            :circular="true"
                            :autoplay="false"
                            :showControls="true"
                            :interval="11000"
                            :duration="400"
                            :xVideo="true"
                            :radius="'0rpx'"
                            imgMode="aspectFit"
                            :fullScreenBtn="false"
                            :isAutoPlayVideo="determine"
                            @change="handleSwiperChange"
                            @click="handlePreviewImage"
                            :list="bannerList"
                            :indicatorStyle="indicatorStyle"
                            :isFullscreenPlay="!determine"
                            :indicator="true"
                        >
                            <view slot="indicator" class="indicator-num">
                                <view
                                    class="indicator-num-slider"
                                    :style="{
                                        transform: `translateX(${transformDistance}rpx)`,
                                        width: `${178 / bannerList.length}rpx`,
                                    }"
                                />
                            </view>
                        </custom-swiper>
                    </view>
                    <!-- 详情描述 -->
                    <view class="detail-desc">
                        <view class="title">{{ data.title }}</view>
                        <view class="message" v-if="data.body">
                            <u-parse :content="data.body.replaceAll(/\n/g, '<br/>')" :tagStyle="tagStyle"></u-parse>
                        </view>
                        <!-- 添加放置商品链接 -->
                        <template v-if="data.goods_ids && data.goods_ids.length">
                            <CardLink :goodsIds="data.goods_ids" @showGoodsMask="showGoodsMask" />
                        </template>
                        <view class="address" v-if="data.address_intro && data.address_id !== 'notShow'">
                            <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                            <text>{{ data.address_intro }}</text>
                        </view>
                        <view class="operate">
                            <text>{{ publishText }}</text>
                            <text class="publish-time">{{ publishAt }}</text>
                            <text class="city" v-if="data.province">
                                <text v-if="permissionType === 1">·</text>
                                {{ data.province }}
                            </text>
                            <text class="permission" v-if="isContentCreator && permissionType !== 1"
                                ><text>·</text>{{ permissionText }}
                            </text>
                            <!-- #ifdef MP-WEIXIN -->
                            <button open-type="share" plain class="footer-item share-btn">
                                <img
                                    class="icon"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6867910c382842300410858.png"
                                />
                            </button>
                            <img
                                src="https://wpm-cdn.dreame.tech/images/2024010/049235-1728612782446.png"
                                @click="handleReport"
                            />
                            <!-- #endif -->
                        </view>
                    </view>
                    <!-- 评论区 -->
                    <view class="comment">
                        <view>
                            <text class="comment-title">{{ commentTypeList[currentCommentIndex].label }}评论</text>
                            <text class="comment-sub-title">{{ `共${data.comment}条评论` }}</text>
                        </view>
                        <!-- 筛选 -->
                        <view class="comment-type" v-if="reviewList.length > 0">
                            <template v-for="(item, index) in commentTypeList">
                                <view
                                    :class="['comment-type--item', { active: index === currentCommentIndex }]"
                                    :key="item.value"
                                    @click="handleCommentTypeClick(index)"
                                >
                                    {{ item.label }}
                                </view>
                            </template>
                        </view>
                        <!-- 空态 -->
                        <view class="comment-empty" v-if="!reviewList || reviewList.length === 0">
                            <view class="comment-empty-title">还没有评论，快来抢沙发吧</view>
                            <view class="comment-empty-review" @click="handleCommentSubmit">抢首评</view>
                        </view>
                        <!-- 评论详情 -->
                        <Comment
                            v-else
                            :dataSource="reviewList"
                            :creator="data.creator"
                            @handleReply="handleReply"
                            @handleLongpress="handleLongpress"
                            @handleContentClick="handleContentClick"
                            @handleReplyLoadMore="handleReplyLoadMore"
                            @handlePickUp="handlePickUp"
                            @handlePraise="handlePraise"
                            @handleUserClick="handleUserClick"
                            ref="myComment"
                        />
                        <!-- 触底 -->
                        <view
                            class="comment-end"
                            v-if="reviewList.length === reviewPagination.total && reviewList.length !== 0"
                        >
                            已显示全部评论
                        </view>
                    </view>
                </view>
                <!-- 底部操作栏 -->
                <view style="height: 154rpx">
                    <view class="bottom">
                        <!-- 发布评论 -->
                        <view class="bottom-publish">
                            <img src="https://wpm-cdn.dreame.tech/images/2024010/305258-1728378600901.png" />
                            <text @click="handleCommentSubmit">说点什么...</text>
                        </view>
                        <!-- 点赞 -->
                        <view class="bottom-operate" @click="handleOper('praise')">
                            <img
                                v-if="data.isPraise"
                                src="https://wpm-cdn.dreame.tech/images/202306/647807cbe8c199533616129.png"
                            />
                            <img v-else src="https://wpm-cdn.dreame.tech/images/202306/64794db2f28209931744857.png" />
                            <text>{{ data.praiseNum || '点赞' }}</text>
                        </view>
                        <!-- 收藏 -->
                        <view class="bottom-operate" @click="handleOper('fav')">
                            <img
                                v-if="data.isFavorite"
                                src="https://wpm-cdn.dreame.tech/images/2024010/904204-1728612711889.png"
                            />
                            <img v-else src="https://wpm-cdn.dreame.tech/images/2024010/836006-1728612662092.png" />
                            <text>{{ data.favoriteNum || '收藏' }}</text>
                        </view>
                        <!-- 评论 -->
                        <view class="bottom-operate" @click="handleScrollTo">
                            <img
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6867aea4180b00980010772.png"
                            />
                            <text>{{ data.comment || '评论' }}</text>
                        </view>
                    </view>
                </view>
            </template>
            <!-- 举报弹窗 -->
            <BottomOperatePopup
                :open="operatePopupOpen"
                :reportParams="reportParams"
                @close="handleOperateClose"
                @handleOpeneReply="handleOpeneReply"
                @handleDelete="beforeHandleDelete"
                cancelText="取消"
            />

            <!-- 回复弹窗 -->
            <ReplyPopup
                v-if="replyPopupOpen"
                @close="handleReplyClose"
                :replyHolder="replyHolder"
                :replyInput.sync="replyInput"
                @handleSend="handleSend"
                @handleInputBlur="handleInputBlur"
            />

            <CustomModal
                :show="deleteModalOpen"
                width="616rpx"
                contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
                content="确认删除此评论"
                confirmText="确认"
                cancelText="取消"
                showCancelButton
                @confirm="handleDelete"
                @cancel="handleDeleteClose"
            >
            </CustomModal>

            <!-- 我的内容操作弹窗 -->
            <MyOperate
                :open="myOperateOpen"
                :reportStatus="data.report_status"
                @close="myOperateOpen = false"
                @myOperateEdit="myOperateEdit"
                @myOperatePermission="myOperatePermission"
                @myOperateDelete="myOperateDelete"
            />

            <!-- 权限弹窗 -->
            <PermissionPopup
                :open="permissionOpen"
                :type="permissionType"
                @close="permissionOpen = false"
                @handlePermissionClick="handlePermissionClick"
            />

            <!-- 删除作品确认弹窗 -->
            <CustomModal
                :show="deleteContentOpen"
                width="616rpx"
                contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
                content="是否确认删除"
                confirmText="确认"
                cancelText="取消"
                showCancelButton
                @confirm="handleContenDelete"
                @cancel="deleteContentOpen = false"
            >
            </CustomModal>

            <!-- 用户取消关注拉黑 -->
            <CustomModal
                :show="operContentOpen"
                width="616rpx"
                contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
                :title="operContent.titie"
                :content="operContent.tip"
                :confirmText="operContent.confirmText"
                :cancelText="operContent.cancelText"
                showCancelButton
                @confirm="handleContenConfirm"
                @cancel="(operContentOpen = false), (operContent = { type: '' })"
            >
            </CustomModal>
        </view>

        <!-- #ifdef MP-WEIXIN -->
        <WxLogin v-if="isWxLogin" @success="initDetailData"></WxLogin>
        <!-- #endif -->
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
        <u-loading-page :loading="pageLoading" loading-text="加载中..."></u-loading-page>

        <GoodsCard ref="GoodsCard" />
        <GoodsList ref="GoodsList" :goods_ids="data.goods_ids" @buyGoods="showGoodsMask" />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from '@/components/CustomSwiper/CustomSwiper.vue';
import { AppModule } from '@/store/modules/app';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
import BottomOperatePopup, { ReportParams } from './components/BottomOperatePopup/BottomOperatePopup.vue';
import ReplyPopup from './components/ReplyPopup/ReplyPopup.vue';
import Comment from './components/Comment/Comment.vue';
import MyOperate from './components/MyOperate/index.vue';
import PermissionPopup from '@/pagesC/components/PermissionPopup/index.vue';
import CardLink from './components/CardLink/CardLink.vue';
import oldIDMap from './oldIDMap.json';
import {
    bannedPermission,
    BannedPermissionRes,
    ContentPermissionType,
    createBrowse,
    createReply,
    createReview,
    createShare,
    delContent,
    deleteReply,
    deleteReview,
    detailContent,
    detailContentNoLogin,
    getContentPer,
    getReplyList,
    getReviewList,
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
    ReplyItem,
    ReviewItem,
    ReviewListRes,
    updatePermission,
    userFollow,
    userFollowDelete,
    userRelation,
} from '@/http/requestGo/community';
import { IResponse } from '@/http/requestGo/apiTypes';
import { AUDIT_STATUS_OPTION, FOLLOW_BTN_OPTION, PERMISSION_OPTION, REPORT_STATUS_OPTION } from './components/contant';
import { PATH } from '../createContent/constant';
import { OperContent } from '../selfCommunity/selfCommunity.vue';
import { CheckAppJump } from '@/common/decorators';
import GoodsCard from './components/CardLink/GoodsCard.vue';
import GoodsList from './components/CardLink/GoodsList.vue';

@Component({
    components: {
        CustomSwiper,
        WxLogin,
        BottomOperatePopup,
        ReplyPopup,
        Comment,
        MyOperate,
        PermissionPopup,
        CardLink,
        GoodsCard,
        GoodsList,
    },
})
export default class Contents extends Vue {
    public cId: any = '';
    public pageLoading: boolean = false;
    public delistStatus: boolean = false;
    public isWxLogin: any = false;
    public paddingLeft: number = 19;
    public typeArray: Array<String> = [];
    public isLoaded: any = false;
    public opacity: number = 1;
    public is_view: number = 1; // 是否有权限查看
    public is_delete: number = 0; // 作品被删除 1 已删除
    public data: any = { goods_ids: [] };
    public showIsImage: any = false;
    public currentNum: number = 0;
    public share: number = 0;
    public indicatorStyle: any = { bottom: '24rpx' };
    public banners: any = [];
    public operDisabled: boolean = false;
    public scrollToComment: number = 0;
    public scrollToPage: number = 0;
    public timeNum: number = 3; // 倒计时
    public timer: any = null;
    public link: string = '';
    public tagStyle: Object = {
        p: 'font-size:28rpx;line-height: normal;color: #777777;font-weight: normal;letter-spacing: 0',
    };

    public editIcon =
        'data:image/png;base64,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';

    public permissionIcon =
        'data:image/png;base64,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';

    public deleteIcon =
        'data:image/png;base64,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';

    public reportIcon = 'data:image/png;base64,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';

    public relationStatus = 0;
    public isContentCreator = true;
    public myOperateOpen = false;
    public permissionOpen = false;
    public deleteContentOpen = false;
    public permissionType: ContentPermissionType = ContentPermissionType.PUBLIC;
    public userBanPermission = {} as BannedPermissionRes;
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: OperContent = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    get user_id(): any {
        let user_id: any = 0;
        // #ifdef MP-WEIXIN
        user_id = UserModule.userInfo.user_id;
        // #endif

        // #ifdef H5
        user_id = UserModule.user_id;
        // #endif
        return user_id;
    }

    get pagePaddingBottom(): number {
        return AppModule.pagePaddingBottom;
    }

    // 下架状态
    get downStatus() {
        return !this.isContentCreator && (this.data.report_status === 2 || this.data.audit_status === 3);
    }

    get relationText() {
        return FOLLOW_BTN_OPTION.find((v) => v.value === this.relationStatus)?.label || '关注';
    }

    //  现在拥有的操作
    get existOperations() {
        return this.isContentCreator
            ? [
                  {
                      key: 'edit',
                      icon: this.editIcon,
                      text: '编辑',
                  },
                  {
                      key: 'permission',
                      icon: this.permissionIcon,
                      text: '权限管理',
                  },
                  {
                      key: 'delete',
                      icon: this.deleteIcon,
                      text: '删除',
                  },
              ]
            : [
                  {
                      key: 'report',
                      icon: this.reportIcon,
                      text: '举报',
                  },
              ];
    }

    onLoad(options) {
        const link = options.link;
        this.link = link;
        this.pageLoading = true;
        this.cId = options.content_id.trim() || oldIDMap[options.did] || '';
        this.is_view = options.is_view === undefined ? 1 : +options.is_view; // 是否有权限 可看
        this.is_delete = options.is_delete === undefined ? 0 : +options.is_delete;
        this.share = +options.share || 0;
        // #ifdef H5
        this.initDetailData();
        // #endif
        // 防止一进来就调用initDetailData获取不到content_id
        this.isWxLogin = true;
        // #ifdef MP-WEIXIN
        if (this.isPhone && this.wxAuth) {
            // #endif
            // 登录了才校验权限
            Utils.isInApp() &&
                bannedPermission().then((res) => {
                    this.userBanPermission = res.data;
                });
            // #ifdef MP-WEIXIN
        }
        // #endif
    }

    onReachBottom() {
        if (this.reviewList && this.reviewList.length >= this.reviewPagination.total) return;
        this.reviewPagination.page = this.reviewPagination.page + 1;
        getReviewList({
            content_id: this.cId,
            page: this.reviewPagination.page,
            page_size: this.reviewPagination.page_size,
            order_type: this.commentTypeList[this.currentCommentIndex].value,
        }).then((res) => {
            const result = res.data.list.map((v) => {
                return {
                    ...v,
                    replyList: v.is_report ? [] : v.reply_list ? [v.reply_list] : [],
                    reply_count: v.is_report ? 0 : v.reply_count,
                    outIds: v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [],
                };
            });
            this.reviewList = this.reviewList.concat(result);
        });
    }

    onPageScroll(res) {
        this.scrollToPage = res.scrollTop;
    }

    async onShareAppMessage() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        await createShare({ content_id: this.data.content_id });
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FORWARD_BUTTON_CLICK,
        });
        Utils.reportEvent('seek_out_share', { title: this.data.title });
        Utils.getDataCallback({ id: this.data.content_id, type: Constants.TYPE_REPORT_CONTENTS_PAGE });
        const path = `/pagesC/contentDetails/contentDetails?content_id=${this.data.content_id}`;
        return {
            title: this.data.title,
            imageUrl: this.data.cover_image,
            path,
        };
    }

    onShow() {
        // #ifdef MP-WEIXIN
        !this.isContentCreator &&
            this.wxAuth &&
            this.isPhone &&
            userRelation({ user_id: Number(UserModule.user_id), to_user_id: Number(this.data.creator) }).then((res) => {
                this.relationStatus = res.data.follow_status;
            });
        // #endif
        const createContentData = uni.getStorageSync('createContent');
        this.permissionType = Number(createContentData.selectPermission) || this.permissionType;
    }

    onUnload() {
        uni.removeStorageSync('createContent');
        clearInterval(this.timer);
    }

    // 举报
    clickHandleReport() {
        this.reportParams = {
            id: this.cId,
            hasReport: true,
            reportType: 'detail',
        };

        uni.navigateTo({
            url: `/pagesC/contentReport/contentReport?id=${this.cId}&reportType=${1}`,
        });
    }

    shareDMCallBack(res) {
        const { key } = res.data;
        switch (key) {
            case 'edit':
                this.myOperateEdit();
                return;
            case 'permission':
                this.myOperatePermission();
                break;
            case 'delete':
                this.myOperateDelete();
                break;
            case 'report':
                this.clickHandleReport();
                break;
        }
    }

    @CheckAppJump()
    async handlerShare() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        await createShare({ content_id: this.data.content_id });
        // #ifdef H5

        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/contentDetails/contentDetails?content_id=${this.data.content_id}`;

        if (UserModule.sdkVersion < 15) {
            const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };

        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            const params = {
                target: 'wechat,weixin_circle,qq,sina',
                type: 'web',
                content: {
                    url: url + '&link=' + res.data,
                    share_image: this.data.cover_image + '?x-oss-process=image/resize,w_200',
                    share_title: this.data.title,
                    share_desc: this.data.body,
                },
            };
            Utils.messageChannel('share', params);
        });
        }

        Utils.cardShare(
            'web',
            undefined,
            this.shareDMCallBack,
        )(
            {
                target: 'wechat,weixin_circle,qq,sina',
                link: url,
                jumpLink: url,
            },
            {
                content: {
                    url: url,
                    image: this.data.cover_image + '?x-oss-process=image/resize,w_200',
                    title: this.data.title,
                    desc: this.data.body,
                },
                extras: {
                    type: 'goods',
                    id: this.data.content_id,
                },
                options: this.existOperations,
            },
        );

        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FORWARD_BUTTON_CLICK,
        });
        // #endif
    }

    get publishText() {
        if ([1, 3].includes(this.data.audit_status) && this.data.edit_at === 0) {
            return '创作于';
        }
    }

    // edit_at为0表示首次发布，audit_status为1/3表示审核中/审核失败，即首次发布且审核中/审核失败才使用create_at，edit_at和publish_at哪个新用哪个
    get publishAt() {
        const time =
            [1, 3].includes(this.data.audit_status) && this.data.edit_at === 0
                ? this.data.create_at
                : this.data.edit_at > this.data.publish_at
                ? this.data.edit_at
                : this.data.publish_at;
        return Utils.timeAgoFormat(time, 'publish');
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        // 有些机型获取的paddingtop不准确，向上去一点保证tips和header之间没有空隙
        return AppModule.pagePaddingTop - 2;
    }

    // #ifdef MP-WEIXIN
    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    get bannerList(): Array<any> {
        if (this.banners && this.banners.length > 0) {
            const newList = [];
            this.banners.forEach((itm) => {
                if (itm.image) {
                    const item = { url: '', title: '', type: 'image' };
                    item.url = itm.image;
                    item.title = itm.title;
                    newList.push(item);
                    if (!this.typeArray.includes('image')) {
                        this.typeArray.push('image');
                    }
                }
                if (itm.video) {
                    const item = { url: '', title: '', type: 'image', poster: '' };
                    item.url = itm.video;
                    item.title = itm.title;
                    item.type = 'video';
                    item.poster = itm.video + '?x-oss-process=video/snapshot,t_1000,m_fast';
                    newList.push(item);
                    if (!this.typeArray.includes('video')) {
                        this.typeArray.push('video');
                    }
                }
            });
            return newList;
        }
        return [];
    }

    get determine(): any {
        return this.typeArray.includes('video') && this.typeArray.length == 1;
    }

    get transformDistance() {
        const len = this.banners.length;
        const silderWidth = 178 / this.banners.length;
        // 轨道178减去滑块70
        const perDistance = ((this.banners.length - 1) * silderWidth) / (len - 1);
        const totalDistance = Math.floor(perDistance * this.currentNum);
        return len - 1 === this.currentNum ? (this.banners.length - 1) * silderWidth : totalDistance;
    }

    get permissionText() {
        return PERMISSION_OPTION.find((v) => v.value === (this.permissionType || this.data?.permission_type))?.label;
    }

    get currentAuditStatus() {
        // 举报下架 > 审核下架 > 举报中 > 审核中
        if (this.data.report_status === 2) {
            return REPORT_STATUS_OPTION.find((v) => v.value === this.data?.report_status);
        }
        if (this.data.audit_status === 3) {
            return AUDIT_STATUS_OPTION.find((v) => v.value === this.data?.audit_status);
        }
        if (this.data.report_status === 1) {
            return REPORT_STATUS_OPTION.find((v) => v.value === this.data?.report_status);
        }
        if (this.data.audit_status === 1) {
            return AUDIT_STATUS_OPTION.find((v) => v.value === this.data?.audit_status);
        }
    }

    get getContentApi() {
        return Utils.isInApp() ? detailContent : detailContentNoLogin;
    }

    // 首次进入调用---h5直接调用，小程序等登录后调用
    async initDetailData() {
        // 详情
        Promise.all([
            getReviewList({
                content_id: this.cId,
                page: 1,
                page_size: this.reviewPagination.page_size,
                order_type: this.commentTypeList[this.currentCommentIndex].value,
            }).catch((err) => {
                console.log('err', err);
            }),
            this.getContentApi({ content_id: this.cId }).catch((err) => {
                console.log('err', err);
            }),
        ])
            .then(async ([list, detail]: [IResponse<ReviewListRes>, any]) => {
                if (
                    detail.data.video_media_list.length > 0 &&
                    detail.data.audit_status !== 3 &&
                    detail.data.report_status !== 2 &&
                    this.is_view
                ) {
                    uni.redirectTo({
                        url: `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${this.cId}`,
                    });
                    return;
                }
                Utils.setPageTitle(detail.data.title);
                this.data = {
                    ...detail.data,
                    isPraise: detail.data.is_praise,
                    praiseNum: detail.data.praise,
                    isFavorite: detail.data.is_favorite,
                    favoriteNum: detail.data.favorite,
                    creator: Number(detail.data.creator),
                    // goods_ids: [1136, 258]
                };
                console.log(this.data);
                this.is_delete = detail.data.is_delete;
                this.isContentCreator = +UserModule.user_id == this.data.creator;
                // this.isContentCreator = true;
                // 无访问权限
                if (!this.isContentCreator || this.is_delete) {
                    if (this.is_view === 0 || this.is_delete) {
                        this.pageLoading = false;
                        this.abnormalView();
                        return;
                    }
                    // #ifdef MP-WEIXIN
                    if (this.isPhone && this.wxAuth) {
                        // #endif
                        if (Utils.isInApp()) {
                            const viewRes = await getContentPer({ content_id: this.cId });
                            this.is_view = viewRes.data.is_view;
                            if (this.is_view === 0) {
                                this.pageLoading = false;
                                this.abnormalView();
                                return;
                            }
                        }
                        // #ifdef MP-WEIXIN
                    }
                    // #endif
                }
                this.permissionType = detail.data.permission_type;
                uni.setStorageSync('createContent', {
                    selectFans: this.permissionType === ContentPermissionType.PARTIALLY ? detail.data.user_list : [],
                    unSelectFans: this.permissionType === ContentPermissionType.NOT_ALLOW ? detail.data.user_list : [],
                });
                if (this.downStatus || !this.is_view) {
                    this.abnormalView();
                    return;
                }
                this.banners = detail.data.image_media_list.map((item) => ({
                    image: item.url,
                }));
                this.delistStatus = !(
                    [0, 1, 2].includes(this.data.status) ||
                    this.data.is_report === 1 ||
                    this.data.is_delete === 1
                );
                this.reviewList = list.data.list || [];
                // 临时根据is_report处理，二期后端不应该给被举报的评论返回回复列表
                this.reviewList.forEach((v) => {
                    v.replyList = v.is_report ? [] : v.reply_list ? [v.reply_list] : [];
                    v.reply_count = v.is_report ? 0 : v.reply_count;
                    v.outIds = v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [];
                    v.page = 1;
                });
                this.reviewPagination.total = list.data.total;

                // #ifdef MP-WEIXIN
                if (this.isPhone && this.wxAuth) {
                    // #endif
                    !this.isContentCreator &&
                        Utils.isInApp() &&
                        userRelation({
                            user_id: Number(UserModule.user_id),
                            to_user_id: Number(detail.data.creator),
                        }).then((res) => {
                            this.relationStatus = res.data.follow_status;
                        });
                    // #ifdef MP-WEIXIN
                }
                // #endif
                // 审核通过加访问量
                this.data.audit_status === 2 && this.data.report_status !== 2 && createBrowse({ content_id: this.cId });
            })
            .finally(() => {
                Utils.reportEvent('article_presentation', { title: this.data.title });
                Utils.reportEvent('article_click', { title: this.data.title });
                this.pageLoading = false;
            });
    }

    @CheckAppJump()
    async handleOper(type) {
        const that = this;
        uni.$u.throttle(async () => {
            if (that.data.report_status === 2) {
                Utils.Toast('作品已下架，暂不可用');
                return;
            }

            if (that.data.audit_status === 3) {
                Utils.Toast('作品审核未通过，暂不可用');
                return;
            }

            if (that.data.audit_status === 1) {
                Utils.Toast('作品审核中，暂不可用');
                return;
            }

            // if (that.operDisabled) return;
            // #ifdef MP-WEIXIN
            try {
                if (that.wxAuth && that.isPhone) {
                    await that.setOper(type);
                } else {
                    const target = 'handleOper';
                    // 进行授权
                    UserModule.authFlow({ target });
                }
            } catch (e) {
                console.error('getDetailData err=', e);
            }
            // #endif
            Utils.reportEvent('give_like', { title: that.data.title });
            // #ifdef H5
            await that.setOper(type);
            // #endif
        }, 500);
    }

    async setOper(type) {
        let flag, res;
        // if (
        //     ([0, 1, 2].includes(this.data.status) || this.data.is_report === 1 || this.data.is_delete === 1) &&
        //     !this.data[type === 'praise' ? 'isPraise' : 'isFavorite']
        // ) {
        //     // this.operDisabled = true;
        //     this.$set(this.data, type === 'praise' ? 'isPraise' : 'isFavorite', true);
        //     setTimeout(() => {
        //         this.$set(this.data, type === 'praise' ? 'isPraise' : 'isFavorite', false);
        //         Utils.Toast('违规内容，已下架');
        //         // this.operDisabled = false;
        //     }, 1000);
        //     return;
        // }
        // const defaultPraise = item.is_praise;
        if (this.data[type === 'praise' ? 'isPraise' : 'isFavorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: this.data[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: this.data.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: this.data.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            this.$set(this.data, type === 'praise' ? 'isPraise' : 'isFavorite', flag);
            this.$set(
                this.data,
                type === 'praise' ? 'praiseNum' : 'favoriteNum',
                this.data[type === 'praise' ? 'praiseNum' : 'favoriteNum'] + (+flag ? 1 : -1),
            );
            this.$set(this.data, type === 'praise' ? 'is_praise' : 'is_favorite', res.data.id);
        }
    }

    handleSwiperChange(e) {
        if (this.bannerList[e.current].type == 'video') {
            this.showIsImage = false;
        } else {
            this.showIsImage = true;
        }

        this.currentNum = e.current;
    }

    handlePreviewImage() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        uni.previewImage({
            current: this.bannerList[this.currentNum].url,
            urls: this.bannerList.map((img) => img.url),
        });
    }

    @CheckAppJump()
    handleScrollTo(duration) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        if (this.scrollToComment > 0) {
            this.scroll();
        } else {
            uni.createSelectorQuery()
                .select('.comment')
                .boundingClientRect((rect) => {
                    this.scrollToComment = rect?.top - Utils.rpxToPx(this.pagePaddingTop) + this.scrollToPage;
                    this.scroll(duration);
                })
                .exec();
        }
        if (this.reviewList.length === 0) {
            this.replyPopupOpen = true;
        }
    }

    scroll(duration = 200) {
        // #ifdef MP-WEIXIN
        uni.pageScrollTo({
            scrollTop: this.scrollToComment,
            duration,
        });
        // #endif

        // #ifdef H5
        scrollTo({
            top: this.scrollToComment,
            behavior: 'smooth',
        });
        // #endif
    }

    async abnormalView() {
        console.log(this.isContentCreator, this.is_delete, 'this.is_delete');
        if (this.isContentCreator && this.is_delete === 0) return;
        this.delListContent(this.data.content_id);
        // 取消点赞收藏
        if (this.data.is_praise) {
            await postCancelPraise({ id: this.data.is_praise });
        }
        if (this.data.is_favorite) {
            await postCancelFavorite({ id: this.data.is_favorite });
        }
        this.timer = setInterval(() => {
            this.timeNum--;
            if (this.timeNum <= 0) {
                clearInterval(this.timer);
                uni.redirectTo({ url: `/pagesC/selfCommunity/selfCommunity?creator=${this.data.creator}` });
            }
        }, 1000);
    }

    // 评论列表和分页
    public reviewPagination = {
        page: 1,
        page_size: 10,
        total: 0,
    };

    public replyPagination = {
        page: 1,
        page_size: 5,
        total: 0,
    };

    public commentTypeList: { label: string; value: number }[] = [
        {
            label: '最新',
            value: 2,
        },
        {
            label: '热门',
            value: 1,
        },
    ];

    public reviewList: ReviewItem[] = [];
    public currentCommentIndex: number = 0;

    handleCommentTypeClick(index) {
        this.reviewPagination.page = 1;
        getReviewList({
            content_id: this.cId,
            page: 1,
            page_size: this.reviewPagination.page_size,
            order_type: this.commentTypeList[index].value,
        })
            .then((res) => {
                this.reviewList = res.data.list;
                this.reviewList.forEach((v) => {
                    v.replyList = v.is_report ? [] : v.reply_list ? [v.reply_list] : [];
                    v.reply_count = v.is_report ? 0 : v.reply_count;
                    v.outIds = v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [];
                    v.page = 1;
                });
                this.reviewPagination.total = res.data.total;
                this.currentCommentIndex = index;
            })
            .catch((err) => console.log('err', err));
    }

    // 举报和举报弹窗
    public reportParams: Partial<ReportParams> = {};
    public operatePopupOpen: boolean = false;
    public deleteModalOpen: boolean = false;

    handleReport(): void {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleReport' });
            return;
        }
        // #endif
        // 自己的作品且未举报下架可以编辑，其他作品是举报
        if (this.isContentCreator) {
            this.myOperateOpen = true;
        } else {
            this.reportParams = {
                id: this.cId,
                hasReport: true,
                reportType: 'detail',
            };
            this.operatePopupOpen = true;
        }
    }

    handleLongpress(item: ReviewItem, secondItem?: ReplyItem) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleLongpress' });
            return;
        }
        // #endif

        const { user_id } = UserModule;
        const isReport = secondItem?.is_report || item.is_report;
        const isOwner = Number(secondItem?.replier_id || item.reviewer_id) === Number(user_id);
        const isCreator = Number(this.data.creator) === Number(user_id);

        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.currentReplyId = secondItem ? secondItem.id : item.id;

        // 如果评论被举报了，只有评论者或者创建者可以操作，且只能删除
        if (isReport) {
            if (isOwner || isCreator) {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasDelete: true,
                };
            } else {
                return;
            }
        } else {
            // 没被举报，评论者只能删除自己的评论，非评论者可以回复或举报该评论，创建者还可以删除
            if (isOwner) {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasDelete: true,
                };
            } else {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasReply: true,
                    hasReport: true,
                    hasDelete: this.data.creator == user_id,
                    reportType: secondItem ? 'secondComment' : 'comment',
                };
            }
        }

        this.reviewItem = item;
        this.replyItem = secondItem;
        this.operatePopupOpen = true;
    }

    handOperatePopupOpen() {
        if (this.isContentCreator) {
            this.myOperateOpen = true;
        } else {
            this.reportParams = {
                id: this.cId,
                hasReport: true,
                reportType: 'detail',
            };
            this.operatePopupOpen = true;
        }
    }

    handleContentClick(item: ReviewItem, secondItem?: ReplyItem) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleContentClick' });
            return;
        }
        // #endif

        if (secondItem?.is_report || item.is_report) {
            return;
        }

        this.currentReplyId = secondItem ? secondItem.id : item.id;
        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.replyInput = this.replyMap.get(this.currentReplyId) || '';

        this.reviewItem = item;
        this.replyItem = secondItem;
        this.replyPopupOpen = true;
    }

    handlePraise(item: ReviewItem, secondItem?: ReplyItem) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handlePraise' });
            return;
        }
        // #endif

        if (secondItem?.is_report || item.is_report) {
            return;
        }

        const index = this.reviewList.findIndex((v) => v.id === item.id);
        if (index === -1) return;

        const updatePraise = (target, isPraise, id, type) => {
            const action = isPraise
                ? postCancelPraise({ id: isPraise })
                : postPraise({ entity_id: String(id), type, content_id: this.cId });
            action
                .then((res) => {
                    target.is_praise = isPraise ? 0 : res.data.id;
                    target.praise_count += isPraise ? -1 : 1;
                    const tempReviewItem = this.reviewList[index];
                    this.$set(this.reviewList, index, tempReviewItem);
                })
                .catch((err) => console.log('err', err));
        };

        if (secondItem) {
            const secondIndex = this.reviewList[index].replyList.findIndex((v) => v.id === secondItem.id);
            if (secondIndex === -1) return;

            const reply = this.reviewList[index].replyList[secondIndex];
            updatePraise(reply, reply.is_praise, reply.id, 3);
        } else {
            const review = this.reviewList[index];
            updatePraise(review, review.is_praise, review.id, 2);
        }
    }

    @CheckAppJump()
    handleUserClick(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleCommentSubmit' });
            return;
        }
        // #endif

        const creator = secondItem?.replier_id || item.reviewer_id;
        const isMe = creator == UserModule.user_id;
        const url = isMe
            ? '/pagesC/selfCommunity/selfCommunity'
            : `/pagesC/selfCommunity/selfCommunity?creator=${creator}`;
        uni.navigateTo({ url });
    }

    handleOperateClose() {
        this.operatePopupOpen = false;
    }

    // 举报弹窗回复
    handleOpeneReply() {
        this.operatePopupOpen = false;
        setTimeout(() => {
            this.replyInput = this.replyMap.get(this.currentReplyId) || '';
            this.replyPopupOpen = true;
        }, 200);
    }

    beforeHandleDelete() {
        this.operatePopupOpen = false;
        this.deleteModalOpen = true;
    }

    handleDelete() {
        this.operatePopupOpen = false;
        const index = this.reviewList.findIndex((v) => v.id === this.reviewItem.id);
        if (index === -1) return;

        if (this.replyType === 'review') {
            deleteReview({ id: this.reviewItem.id }).then(() => {
                this.data.comment = this.data.comment - this.reviewList[index].replyList.length - 1;
                this.reviewList.splice(index, 1);
                this.reviewPagination.total -= 1;
                this.deleteModalOpen = false;
                Utils.Toast('删除成功');
            });
        } else {
            deleteReply({ id: this.replyItem.id }).then(() => {
                // 如果没有展开且删除的是第一条
                if (this.reviewList[index].replyList.length === 1) {
                    // 有更多，加载下一条
                    if (this.reviewList[index].reply_count > 0) {
                        getReplyList({
                            content_id: this.cId,
                            out_id: this.reviewList[index].outIds,
                            page: 1,
                            page_size: 1,
                            review_id: this.reviewItem.id,
                            type: 3,
                        }).then((res) => {
                            if (!res.data.list) {
                                res.data.list = [];
                            }

                            const tempReviewItem = this.reviewList[index];
                            tempReviewItem.replyList = [].concat(res.data.list);
                            tempReviewItem.outIds = [res.data.list[0].id];
                            tempReviewItem.reply_count = res.data.total - 1;
                            this.$set(this.reviewList, index, tempReviewItem);
                        });
                    } else {
                        const tempReviewItem = this.reviewList[index];
                        tempReviewItem.replyList = [];
                        tempReviewItem.outIds = [];
                        this.$set(this.reviewList, index, tempReviewItem);
                    }
                } else {
                    // 删除当前replyList里面的选中的评论
                    const secondIndex = this.reviewList[index].replyList.findIndex((v) => v.id === this.replyItem.id);
                    this.reviewList[index].replyList.splice(secondIndex, 1);
                    // 在新的replyList里面重设replyList和outIds
                    this.reviewList[index].replyList = this.reviewList[index].replyList.slice(0, 1);
                    this.reviewList[index].outIds = this.reviewList[index].replyList.map((v) => v.id);
                    const page = this.reviewList[index].page;
                    if (page === 1) {
                        getReplyList({
                            content_id: this.cId,
                            out_id: this.reviewList[index].outIds,
                            page: 1,
                            page_size: this.replyPagination.page_size,
                            review_id: this.reviewItem.id,
                            type: 3,
                        }).then((res) => {
                            if (!res.data.list) {
                                res.data.list = [];
                            }

                            const tempReviewItem = this.reviewList[index];
                            tempReviewItem.page = res.data.page + 1;
                            tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
                            tempReviewItem.reply_count = res.data.total;
                            this.$set(this.reviewList, index, tempReviewItem);
                        });
                    } else {
                        for (let i = 1; i < page; i++) {
                            getReplyList({
                                content_id: this.cId,
                                out_id: this.reviewList[index].outIds,
                                page: i,
                                page_size: this.replyPagination.page_size,
                                review_id: this.reviewItem.id,
                                type: 3,
                            }).then((res) => {
                                // 后端数组没数据不传[]？
                                if (!res.data.list) {
                                    res.data.list = [];
                                }

                                const tempReviewItem = this.reviewList[index];
                                tempReviewItem.page = res.data.page + 1;
                                tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
                                tempReviewItem.reply_count = res.data.total;
                                this.$set(this.reviewList, index, tempReviewItem);
                            });
                        }
                    }
                }
                this.data.comment -= 1;
                this.deleteModalOpen = false;
                Utils.Toast('删除成功');
            });
        }
    }

    handleDeleteClose() {
        this.deleteModalOpen = false;
    }

    // 回复框
    public replyHolder: string = '说点什么';
    public replyType: 'new' | 'review' | 'reply' = 'new';
    public replyPopupOpen: Boolean = false;
    public reviewItem = {} as ReviewItem;
    public replyItem = {} as ReplyItem;
    public replyMap = new Map();
    public replyInput = '';
    public currentReplyId: any = 'default';

    @CheckAppJump()
    handleCommentSubmit() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleCommentSubmit' });
            return;
        }
        // #endif
        this.currentReplyId = 'default';
        this.replyInput = this.replyMap.get('default') || '';
        this.replyType = 'new';
        this.replyPopupOpen = true;
    }

    // 评论里面的回复
    @CheckAppJump()
    handleReply(item: ReviewItem, secondItem?: ReplyItem) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleReply' });
            return;
        }
        // #endif

        if (secondItem?.is_report || item.is_report) {
            return;
        }

        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.currentReplyId = secondItem ? secondItem.id : item.id;

        const { user_id } = UserModule;
        if ((secondItem?.replier_id || item.reviewer_id) === user_id) {
            this.reportParams = {
                id: secondItem ? secondItem.id : item.id,
                hasDelete: true,
                hasReply: true,
            };
            this.operatePopupOpen = true;
        } else {
            this.replyInput = this.replyMap.get(this.currentReplyId) || '';
            this.replyPopupOpen = true;
        }

        this.reviewItem = item;
        this.replyItem = secondItem;
    }

    // 获取所点击评论下面的回复分页
    @CheckAppJump()
    handleReplyLoadMore(item: ReviewItem) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        getReplyList({
            content_id: this.cId,
            out_id: item.outIds,
            page: item.page,
            page_size: this.replyPagination.page_size,
            review_id: item.id,
            type: 3,
        }).then((res) => {
            if (!res.data.list) return;

            const index = this.reviewList.findIndex((v) => v.id === item.id);
            this.reviewList[index].page = res.data.page + 1;
            const tempReviewItem = this.reviewList[index];
            tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
            this.$set(this.reviewList, index, tempReviewItem);
        });
    }

    @CheckAppJump()
    handlePickUp(item: ReviewItem) {
        const index = this.reviewList.findIndex((v) => v.id === item.id);

        const tempReviewItem = this.reviewList[index];
        tempReviewItem.page = 1;
        tempReviewItem.reply_count += item.outIds.length - 1;
        tempReviewItem.replyList = this.reviewList[index].replyList.slice(0, 1);
        tempReviewItem.outIds = [].concat(this.reviewList[index].replyList[0].id);
        this.$set(this.reviewList, index, tempReviewItem);
    }

    public sendLoading: boolean = false;
    handleSend(content: string, imageUrl: string[]) {
        if (!content && imageUrl.length === 0) {
            Utils.Toast('评论内容不能为空');
            return;
        }

        if (this.sendLoading) return;
        this.sendLoading = true;

        const { user_id, sessid } = UserModule;

        if (this.replyType === 'new') {
            createReview({ content, content_id: this.cId, sessid, user_id, image_url: imageUrl })
                .then((res) => {
                    this.replyPopupOpen = false;
                    this.reviewList.unshift({ ...res.data, replyList: [], outIds: [] });
                    this.reviewPagination.total += 1;
                    this.data.comment += 1;
                    this.handleScrollTo(500);
                    this.replyMap.delete(this.currentReplyId);
                    Utils.Toast('发送成功');
                })
                .finally(() => {
                    this.sendLoading = false;
                });
        } else {
            createReply({
                content,
                content_id: this.cId,
                sessid,
                user_id,
                parent_review_id: this.replyItem?.id,
                review_id: this.reviewItem.id,
                type: this.replyItem?.id ? 3 : 2,
                image_url: imageUrl,
            })
                .then((res) => {
                    this.replyPopupOpen = false;
                    const index = this.reviewList.findIndex((v) => v.id === this.reviewItem.id);
                    this.data.comment += 1;
                    const tempReviewItem = this.reviewList[index];
                    tempReviewItem.replyList = [...this.reviewList[index].replyList, res.data];
                    tempReviewItem.outIds = [...this.reviewList[index].outIds, res.data.id];
                    this.$set(this.reviewList, index, tempReviewItem);
                    this.replyMap.delete(this.currentReplyId);
                    this.$nextTick(() => {
                        const query = uni.createSelectorQuery().in(this.$refs.myComment);
                        query
                            .select(`#reply${res.data.id}`)
                            .boundingClientRect((rect) => {
                                this.scrollToComment =
                                    rect?.top - Utils.rpxToPx(this.pagePaddingTop) + this.scrollToPage;
                                this.scroll(500);
                            })
                            .exec();
                    });

                    Utils.Toast('发送成功');
                })
                .finally(() => {
                    this.sendLoading = false;
                });
        }
    }

    handleInputBlur() {
        console.log('失焦');
    }

    handleReplyClose(content) {
        this.replyMap.set(this.currentReplyId, content);
        this.replyHolder = '说点什么';
        this.replyPopupOpen = false;
    }

    toHome() {
        if (this.share) {
            uni.reLaunch({
                url: '/pages/contents/contents',
            });
        } else {
            Utils.goBack();
        }
    }

    @CheckAppJump()
    handleUserFollow() {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleUserFollow' });
            return;
        }
        // #endif

        uni.$u.throttle(() => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(this.data.creator);
            // const api = this.relationStatus === 0 ? userFollow : userFollowDelete;
            if (this.relationStatus === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        this.relationStatus = res.data.follow_status;
                    });
                });
            } else {
                this.operContentOpen = true;
                this.operContent = {
                    type: 'follow',
                    titie: '',
                    tip: '确定不再关注该用户',
                    confirmText: '不再关注',
                    cancelText: '取消',
                };
            }
        }, 500);
    }

    @CheckAppJump()
    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.data.creator });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            userRelation({ user_id: +UserModule.user_id, to_user_id: +this.data.creator }).then((res) => {
                this.relationStatus = res.data.follow_status;
            });
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    @CheckAppJump()
    myOperateEdit() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.report_status === 1) {
            Utils.Toast('作品被举报，平台正在审核，不可编辑');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.myOperateOpen = false;
        uni.redirectTo({ url: `/pagesC/createContent/index?contentType=img&content_id=${this.cId}&isEdit=1` });
    }

    @CheckAppJump()
    myOperatePermission() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.permissionOpen = true;
    }

    @CheckAppJump()
    myOperateDelete() {
        if (this.data.report_status === 1) {
            Utils.Toast('作品被举报，平台正在审核，不可删除');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.myOperateOpen = false;
        this.deleteContentOpen = true;
    }

    @CheckAppJump()
    handleContenDelete() {
        delContent({ content_id: this.cId }).then(() => {
            this.delListContent(this.cId);
            Utils.Toast('删除成功');
            Utils.goBack();
        });
    }

    delListContent(content_id) {
        // #ifdef H5
        Utils.sharedStorage('set', 'del_content_id', content_id);
        // #endif
        // #ifdef MP-WEIXIN
        const pages = getCurrentPages();
        const prevPage: any = pages[pages.length - 2];
        const _pre_route: any = prevPage && prevPage.route;
        // if (_pre_route === 'pagesC/selfCommunity/selfCommunity') {
        //     prevPage.$vm.listData[prevPage.$vm.swiperCurrent].list = prevPage.$vm.listData[
        //         prevPage.$vm.swiperCurrent
        //     ].list.filter((i) => i.content_id !== content_id);
        // } else
        if (_pre_route === 'pages/contents/contents') {
            if (prevPage.$vm.tabsIndex === 0) {
                prevPage.$vm.flowList = prevPage.$vm.flowList.filter((i) => i.content_id !== content_id);
            } else if (prevPage.$vm.tabsIndex === 1) {
                prevPage.$vm.$refs.exploreRef[0].flowList = prevPage.$vm.$refs.exploreRef[0].flowList.filter(
                    (i) => i.content_id !== content_id,
                );
            } else if (prevPage.$vm.tabsIndex === 2) {
                prevPage.$vm.$refs.CommunityRef[0].flowList = prevPage.$vm.$refs.CommunityRef[0].flowList.filter(
                    (i) => i.content_id !== content_id,
                );
            }
        }
        // #endif
    }

    handlePermissionClick(type) {
        this.permissionOpen = false;
        this.myOperateOpen = false;
        if ([ContentPermissionType.PUBLIC, ContentPermissionType.PRIVATE].includes(type)) {
            this.permissionType = type;
            uni.setStorageSync('createContent', {
                ...uni.getStorageSync('createContent'),
                selectPermission: type,
                selectFans: [],
                unSelectFans: [],
            });
            updatePermission({
                content_id: this.cId,
                permission_type: this.permissionType,
                user_ids: this.data.user_list?.map((v) => v.user_id) || [],
            });
        } else {
            uni.navigateTo({
                url: `${PATH.permission}?type=${type}&modifyType=edit&contentId=${this.cId}`,
            });
        }
    }

    @CheckAppJump()
    handleJumpHome(type) {
        // #ifdef H5
        const win: any = window;
        win.onAppPageShow = () => {
            !this.isContentCreator &&
                userRelation({ user_id: Number(UserModule.user_id), to_user_id: Number(this.data.creator) }).then(
                    (res) => {
                        this.relationStatus = res.data.follow_status;
                    },
                );
        };
        // #endif
        Utils.jumpPersonHome(this.isContentCreator ? -1 : this.data.creator, type);
    }

    showGoodsMask(options) {
        if (options) {
            (this.$refs.GoodsCard as any).showGoodsMask(options.gid, this.cId);
            (this.$refs.GoodsList as any).toggleVisible();
            return;
        }

        const goods_ids = this.data.goods_ids;
        if (goods_ids.length === 1) {
            (this.$refs.GoodsCard as any).showGoodsMask(goods_ids[0], this.cId);
        } else {
            (this.$refs.GoodsList as any).toggleVisible();
        }
    }
}
</script>

<style lang="scss" scoped>
.contentDetails-container {
    height: auto !important;
    position: relative;

    .user-header {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 90rpx;

        &-imgs {
            position: relative;
            margin-right: 16rpx;

            &--avatar {
                width: 72rpx;
                height: 72rpx;
                display: block;
                border-radius: 36px;
            }

            &--icon {
                width: 32rpx;
                height: 32rpx;
                position: absolute;
                left: 46rpx;
                bottom: -4rpx;
                border-radius: 36px;
                z-index: 1;
            }
        }

        &-author {
            font-size: 32rpx;
            color: $text-color-primary;
        }

        &-relation {
            // width: 140rpx;
            padding: 0 20rpx;
            height: 56rpx;
            line-height: 56rpx;
            margin-right: 23rpx;
            border-radius: 192rpx;
            font-weight: 500;
            font-size: 28rpx;
            border: 2rpx solid #dbc49a;
            color: #c2a271;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                border: 2rpx solid #e2e2e2;
                color: #a6a6a6;
            }
        }
    }

    .operate-header {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56rpx;
        line-height: 56rpx;
        top: 50%;
        right: 22rpx;
        transform: translateY(-50%);

        img {
            width: 48rpx;
            height: 48rpx;
        }

        .report {
            margin-left: 46rpx;
        }
    }

    .status-bar {
        width: 750rpx;
        height: 92rpx;
        @include flex(row, center, center);
        position: fixed;
        z-index: 100;
        font-size: 27rpx;
    }

    .remove {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: fixed;
        width: 100%;
        top: 39%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-size: 28rpx;
        color: $text-color-secondary;

        image {
            width: 508rpx;
            height: 508rpx;

            &.is_view {
                width: 365rpx;
                height: 300rpx;
                margin-bottom: 100rpx;
            }
        }

        text {
            font-family: PingFang SC;
            font-size: 28rpx;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            letter-spacing: 0rpx;
            color: #777777;
        }

        .jump-home {
            padding: 8rpx 24rpx;
            box-sizing: border-box;
            border: 1px solid #121212;
            border-radius: 30rpx;
            margin-top: 16rpx;

            text {
                font-size: 28rpx;
                font-weight: 500;
                color: #121212;
            }
        }
    }

    .content {
        width: 100vw;

        .indicator-num {
            width: 178rpx;
            height: 4rpx;
            border-radius: 186rpx;
            background: rgba(38, 38, 38, 0.4);

            &-slider {
                height: 4rpx;
                border-radius: 186rpx;
                background-color: white;
                transition: all 0.5s ease-in-out;
            }
        }
    }

    .detail-desc {
        background: $background-ffffff;

        .title {
            padding: 46rpx 32rpx 30rpx 32rpx;
            font-size: 34rpx;
            word-break: break-all;
            font-weight: 500;
            color: $text-color-primary;
            line-height: 40rpx;
        }

        .message {
            padding: 0rpx 32rpx 24rpx 32rpx;
            font-size: 30rpx;
            word-break: break-all;
            line-height: 48rpx;
            color: #404040;
            letter-spacing: 0;
            text-align: justify;
            height: max-content;

            ::v-deep ._root {
                overflow: visible !important;
            }
        }

        .operate {
            padding: 0 30rpx;
            font-size: 23rpx;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            color: $text-color-secondary;

            .publish-time {
                margin-left: 8rpx;
            }

            .city {
                margin-left: 8rpx;

                text {
                    margin-right: 8rpx;
                }
            }

            .permission {
                text {
                    margin: 0 8rpx;
                }
            }

            img {
                width: 38rpx;
                height: 38rpx;
            }

            .share-btn {
                margin: 0 38rpx 0 auto;
                padding: 0;
                border: none;
                @include flex($justify: center, $align: center);
            }
        }

        .address {
            padding: 0 30rpx;
            margin-bottom: 30rpx;
            @include flex(row, flex-start, center);

            image {
                width: 30rpx;
                height: 30rpx;
            }

            text {
                font-size: 24rpx;
                color: #ab8c5e;
                line-height: 30rpx;
                margin-left: 12rpx;
            }
        }
    }

    .comment {
        padding: 62rpx 30rpx 0 30rpx;

        &-empty {
            padding: 286rpx 0 176rpx 0;

            &-title {
                font-size: 28rpx;
                color: $text-color-disable;
                line-height: 38rpx;
                text-align: center;
            }

            &-review {
                font-size: 28rpx;
                color: $brand-color-btn-text;
                line-height: 38rpx;
                text-align: center;
                margin-top: 54rpx;
            }
        }

        &-title {
            font-weight: 500;
            font-size: 32rpx;
            color: $text-color-regular;
            line-height: 40rpx;
        }

        &-sub-title {
            font-size: 24rpx;
            color: #a6a6a6;
            margin-left: 16rpx;
        }

        &-type {
            @include flex($justify: flex-start, $align: center);
            margin: 30rpx 0;

            &--item {
                width: 142rpx;
                height: 54rpx;
                border-radius: 154rpx 154rpx 154rpx 154rpx;
                border: 2rpx solid $uni-border-color-gray;
                font-size: 28rpx;
                color: $text-color-gray;
                margin-right: 30rpx;
                @include flex;

                &.active {
                    color: $text-color-primary;
                    border: 2rpx solid $text-color-primary;
                }
            }
        }

        &-end {
            padding: 130rpx 0 104rpx 0;
            font-size: 24rpx;
            color: $text-color-disable;
            text-align: center;
        }
    }

    .bottom {
        height: 154rpx;
        padding: 0 30rpx;
        position: fixed;
        bottom: 0;
        border-top: 2rpx solid $uni-bg-color-modal;
        width: 100%;
        background: $background-ffffff;
        @include flex($justify: flex-start, $align: center);

        &-publish {
            width: 246rpx;
            height: 69rpx;
            background: #f4f4f4;
            border-radius: 194rpx;
            @include flex($justify: center, $align: center);
            margin-right: auto;

            img {
                width: 36rpx;
                height: 36rpx;
            }

            text {
                font-size: 28rpx;
                color: $text-color-disable;
                margin-left: 16rpx;
            }
        }

        &-operate {
            @include flex($justify: flex-start, $align: center);
            margin-left: 38rpx;
            font-size: 28rpx;
            color: $text-color-regular;

            img {
                width: 48rpx;
                height: 48rpx;
                margin-right: 8rpx;
                object-fit: contain;
            }

            text {
                text-align: center;
                min-width: 54rpx;
                color: #404040;
                white-space: nowrap;
            }
        }
    }
}
</style>
