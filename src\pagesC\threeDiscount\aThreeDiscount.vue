<template>
    <view v-if="isInit">
        <APPSHARE :link="link" />
        <view v-if="baseInfo.order_no && orderInfo.status >= 300" class="invitedPage">
            <!-- 活动倒计时 -->
            <view class="activity-time-down" :style="{ position: 'absolute', 'top': statusBarHeight + 200 + 'rpx' }">
                <view class="countdown-container">
                    <u-count-down
                        :time="countdownTime"
                        format="HH:mm:ss"
                        millisecond
                        @finish="onFinish"
                        @change="onChange"
                    >
                        <view class="time">
                            <view>本次限时任务将在</view>
                            <view class="time__item">
                                <view class="time__item_text">{{
                                    timeData.hours + timeData.days * 24 >= 10 ? timeData.hours + timeData.days * 24 : '0' + (timeData.hours + timeData.days * 24)
                                }}</view>
                                <view style="margin: 0rpx 4rpx">时</view>
                            </view>
                            <view class="time__item">
                                <view class="time__item_text">{{
                                    timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes
                                }}</view>
                                <view style="margin: 0rpx 4rpx">分</view>
                            </view>
                            <view class="time__item">
                                <view class="time__item_text">{{
                                    timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
                                }}</view>
                                <view style="margin: 0rpx 4rpx">秒</view>
                            </view>
                            <view>后结束</view>
                        </view>
                    </u-count-down>
                </view>
            </view>
          <view class="header" :style="{ position: 'fixed', top: statusBarHeight + 'rpx', left: '0', right: '0', zIndex: '1000' }">
                <view class="left">
                    <image
                        v-show="$isInApp()"
                        class="search_back"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc328c9a8e8260014034.png"
                        @click="goBack"
                    ></image>
                </view>
                <view class="right">
                    <view class="rule" @click="openRulePopup"></view>
                    <view v-show="$isInApp()" class="share" @click="shareActivity"></view>
                </view>
            </view>
            <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ca628003aa0010010538.png" class="success_icon" />
            <view class="invitePage_content">
                <view class="invitePage_content_goods">
                    <view class="left">
                        <image class="invitePage_content_goods_img" :src="orderInfoGoods.cover_image" />
                    </view>
                    <view class="right">
                        <view class="invitePage_content_goods_name">{{ orderInfoGoods.name }}</view>
                        <img class="invitePage_content_goods_sale" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ca3921c2e71150010314.png" />
                        <view class="invitePage_content_goods_price_wrap">
                            <view class="invitePage_content_goods_price">
                                ￥<text>{{ orderInfoGoods.price }}</text>
                            </view>
                            <view class="invitePage_content_goods_price_text">
                                优惠前：{{ orderInfoGoods.oprice }}元
                            </view>
                        </view>
                        <view class="goods_count">
                            X 1
                        </view>
                    </view>
                </view>
            </view>
            <view class="success_btn" @click="goShopPage"></view>
        </view>
        <view
            class="oneYuanFlashSale_container"
            v-else-if="loading && !isEndAcitivity"
            :style="{ 'margin-top': user_id ? '0' : '96rpx' }"
        >
            <view class="oneYuanFlashSale_content" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="header">
                    <view class="left">
                        <image
                            v-show="$isInApp()"
                            class="search_back"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc328c9a8e8260014034.png"
                            @click="goBack"
                        ></image>
                    </view>
                    <view class="right">
                        <view class="rule" @click="openRulePopup"></view>
                        <view v-show="$isInApp()" class="share" @click="shareActivity"></view>
                    </view>
                </view>
                <view class="header_title"> </view>
                <!-- 活动倒计时 -->
                <view class="activity-time-down" :style="{ 'margin-top': (194 - statusBarHeight) + 'rpx' }">
                    <view class="countdown-container">
                        <u-count-down
                            :time="countdownTime"
                            format="HH:mm:ss"
                            millisecond
                            @finish="onFinish"
                            @change="onChange"
                        >
                            <view class="time">
                                <view>本次限时任务将在</view>
                                <view class="time__item">
                                    <view class="time__item_text">{{
                                        timeData.hours + timeData.days * 24 >= 10 ? timeData.hours + timeData.days * 24 : '0' + (timeData.hours + timeData.days * 24)
                                    }}</view>
                                    <view style="margin: 0rpx 4rpx">时</view>
                                </view>
                                <view class="time__item">
                                    <view class="time__item_text">{{
                                        timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes
                                    }}</view>
                                    <view style="margin: 0rpx 4rpx">分</view>
                                </view>
                                <view class="time__item">
                                    <view class="time__item_text">{{
                                        timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
                                    }}</view>
                                    <view style="margin: 0rpx 4rpx">秒</view>
                                </view>
                                <view>后结束</view>
                            </view>
                        </u-count-down>
                    </view>
                </view>
                <!-- 助力人数 -->
                <view class="assist_users_fixed_wrap">
                    <img v-if="inviteCount >= baseInfo.required_invite_number" class="assist_users_fixed_mask" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b5101c7bb08180016057.png" />
                    <view class="assist_users_fixed">
                        <view v-if="assistUsers.length > 5" class="assist_users_wrap">
                            <template v-for="(user, index) in assistUsers">
                                <view v-if="user" :key="index" class="assist_user_item">
                                    <image class="assist_user_avatar_real" :src="user.avatar || 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2e2b9eb4a6500131096.png'" />
                                    <image class="assist_user_avatar_bottom" :src="'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2f1f39f322370135791.png'" />
                                </view>
                                <image v-else :key="index" class="assist_user_avatar" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2e2b9eb4a6500131096.png" />
                            </template>
                        </view>
                        <view v-else-if="assistUsers.length <= 5" :class="['assist_users_wrap', assistUsers.length === 1 &&'assist_users_wrap_one']" :style="{ 'margin-top': '50rpx' , width: baseInfo.required_invite_number * 150 + 'rpx' }">
                            <template v-for="(user, index) in assistUsers">
                                <view v-if="user" :key="index" class="assist_user_item">
                                    <image class="assist_user_avatar_real" :src="user.avatar || 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2e2b9eb4a6500131096.png'" />
                                    <image class="assist_user_avatar_bottom" :src="'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2f1f39f322370135791.png'" />
                                </view>
                                <image v-else :key="index" class="assist_user_avatar" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2e2b9eb4a6500131096.png" />
                            </template>
                        </view>
                    </view>
                </view>
                <view class="assist_users_btn" @click="handleOpenStore">
                    <view class="assist_users_btn_text">
                        邀请好友开店赚钱（{{ inviteCount }}/{{baseInfo.required_invite_number || 10}}）
                    </view>
                </view>
                <!-- 小店消费一单 -->
                 <view class="small_shop_icon_wrap">
                    <img v-if="baseInfo && baseInfo.shop_purchase && baseInfo.shop_purchase.has_order" class="small_shop_icon_mask" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b58efbec107810015925.png" />
                    <view v-if="baseInfo && baseInfo.shop_purchase && baseInfo.shop_purchase.has_order" class="assist_user_item">
                        <image class="assist_user_avatar_real" :src="baseInfo.shop_purchase.order_info.avatar" />
                        <view class="assist_user_avatar_bottom_text">
                            小店消费一单
                        </view>
                    </view>
                    <image v-else class="small_shop_icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b3213b9c1c7610133835.png"></image>
                 </view>
                <view class="assist_users_btn" style="margin-top: 20rpx" @click="handleOpenStoreBuy">
                    <view class="assist_users_btn_text">
                        分享小店
                    </view>
                </view>
                <!-- <view class="activity-time-text">活动每天10点准时开始！数量有限！先到先得！</view> -->
                <view :class="['content', !unlocked && 'lock']" style="margin-top: 68rpx">
                    <view
                        class="content_title"
                        v-for="(item, index) in productList"
                        :key="index"
                        @click="handleAssist(item)"
                    >
                        <view class="content_img_wrap">
                            <image class="content_img" :src="item.goods_image" />
                            <image
                                class="content_img_icon"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879fb73920835980010652.png"
                            />
                            <image
                                class="content_img_icon1"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689afc608f7675880031791.png"
                            />
                            <view
                                class="content_img_icon2"
                                v-if="
                                    item.sales / (item.sales + item.stock) > 0.8 &&
                                    item.sales / (item.sales + item.stock) < 1
                                "
                            >
                                <image
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c53135f18b3900011088.png"
                                />
                            </view>
                        </view>
                        <view class="content_title_text">
                            <view class="content_title_text_title">{{ item.show_name }}</view>
                            <view class="content_title_text_content">
                                <view
                                    style="
                                        display: flex;
                                        align-items: flex-end;
                                        width: 74rpx;
                                        height: 54rpx;
                                        color: #ff0084;
                                        font-weight: 600;
                                        padding-bottom: 10rpx;
                                    "
                                >
                                    <text style="font-size: 24rpx; align-self: flex-end; line-height: 1">¥</text>
                                    <view style="display: flex; align-items: flex-end; height: 54rpx">
                                        <text
                                            v-if="String(item.sale_price).indexOf('.') !== -1"
                                            style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                        >
                                            {{ String(item.sale_price).split('.')[0] }}
                                        </text>
                                        <text v-else style="font-size: 38rpx; line-height: 1; align-self: flex-end">
                                            {{ item.sale_price }}
                                        </text>
                                        <text
                                            v-if="String(item.sale_price).indexOf('.') !== -1"
                                            style="
                                                font-size: 38rpx;
                                                line-height: 1;
                                                align-self: flex-end;
                                                margin-left: 2rpx;
                                            "
                                        >
                                            .
                                        </text>
                                        <text
                                            v-if="String(item.sale_price).indexOf('.') !== -1"
                                            style="font-size: 24rpx; line-height: 1; align-self: flex-end"
                                        >
                                            {{ String(item.sale_price).split('.')[1] }}
                                        </text>
                                    </view>
                                </view>
                                <view class="content_title_text_grab_text">优惠前：<text>{{ Math.floor(item.extra.mprice) }}元</text>
                                </view>
                                <!-- 抢商品 -->
                                <view class="content_title_text_grab" @click.stop="handleMprice(item)">
                                    <view class="content_title_text_grab_bg">
                                        <view class="content_title_text_grab_bg_img">
                                            <!-- 进度条 -->
                                            <view
                                                class="content_title_text_grab_bg_progress_bg"
                                                v-if="item.stock !== 0"
                                            >
                                                <view class="content_title_text_content_progresss">
                                                    <view
                                                        class="progress"
                                                        :style="{
                                                            width: (item.sales / (item.stock + item.sales)) * 100 + '%',
                                                        }"
                                                    >
                                                    </view>
                                                </view>
                                                <view class="content_title_text_grab_text_progress_text">
                                                    已抢
                                                    {{
                                                        ((item.sales / (item.stock + item.sales)) * 100) % 1 === 0
                                                            ? (item.sales / (item.stock + item.sales)) * 100
                                                            : ((item.sales / (item.stock + item.sales)) * 100).toFixed(
                                                                  2,
                                                              )
                                                    }}%
                                                </view>
                                            </view>
                                            <view class="content_title_text_grab_text_assist_bg" v-else></view>
                                            <!-- 抢商品 -->
                                            <view
                                                class="content_title_text_grab_text"
                                                :class="
                                                    item.user_seckill && item.user_seckill.order_status == 300
                                                        ? 'normal_buy_active'
                                                        : ''
                                                "
                                                v-if="item.stock !== 0"
                                            ></view>
                                            <view class="content_title_text_grab_text_assist" v-else> </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 测试助力弹窗按钮 -->
                    <view class="see_more_button" @click="seeMore">
                        <text>查看更多商品</text>
                    </view>
                </view>
            </view>

            <!-- 活动规则弹窗 -->
            <u-popup
                :show="showRulePopup"
                v-prevent-scroll="showRulePopup"
                mode="center"
                :round="18"
                :safe-area-inset-bottom="false"
                catchtouchmove
            >
                <view class="rule-popup-content">
                    <view class="rule-popup-header">
                        <view class="rule-title">
                            <view class="rule-title-decoration left"></view>
                            <text class="rule-title-text">活动规则</text>
                            <view class="rule-title-decoration right"></view>
                        </view>
                        <view class="rule-popup-close" @click="closeRulePopup">
                            <view class="close-icon"></view>
                        </view>
                    </view>
                    <scroll-view scroll-y class="rule-container">
                        <view class="rule-content">
                            <view class="rule-content-text">
                                <!-- <image class="rule-img"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68947388803fc5250011718.png"
                                /> -->
                                <view class="rule-section" v-html="activityRule"> </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </u-popup>

            <!-- 助力弹窗 -->
            <u-popup
                :show="showAssistPopup"
                mode="center"
                :round="10"
                width="600rpx"
                :closeOnClickOverlay="true"
                @close="closeAssistPopup"
            >
                <view class="passist_popup">
                    <!-- 头部标题 -->
                    <view class="assist_popup_header">
                        <image
                            class="assist_popup_header_left"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png"
                        />
                        <text class="assist_popup_title">快帮TA助力</text>
                        <image
                            class="assist_popup_header_right"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png"
                        />
                    </view>

                    <!-- 内容区域 -->
                    <view class="assist_popup_content">
                        <!-- 头像昵称 -->
                        <view class="assist_popup_user">
                            <image class="assist_popup_avatar" :src="inviter_info.avatar" />
                            <text class="assist_popup_nickname">{{ inviter_info.nick }}</text>
                        </view>

                        <!-- 倒计时 -->
                        <view class="assist_popup_countdown">
                            <text class="assist_popup_countdown_time">{{ popupCountdownTime }}</text>
                            <text class="assist_popup_countdown_desc">后未达成，本轮加入将失效</text>
                        </view>

                        <!-- 助力按钮 -->
                        <view class="assist_popup_button_container">
                            <view class="assist_popup_button" @click="handleAssistAction">
                                <text class="assist_popup_button_text">为TA助力</text>
                            </view>
                        </view>
                    </view>
                </view>
            </u-popup>

            <!-- 助力弹窗 -->
            <u-popup
                :show="goToSelectPopup"
                mode="center"
                :round="10"
                width="600rpx"
                :closeOnClickOverlay="true"
                @close="closeAssistPopup"
            >
                <view class="passist_popup">
                    <!-- 头部标题 -->
                    <view class="assist_popup_header">
                        <image
                            class="assist_popup_header_left"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png"
                        />
                        <text class="assist_popup_title">快帮TA助力</text>
                        <image
                            class="assist_popup_header_right"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png"
                        />
                    </view>

                    <!-- 内容区域 -->
                    <view class="assist_popup_content">
                        <!-- 头像昵称 -->
                        <view class="assist_popup_user">
                            <image class="assist_popup_avatar" :src="inviter_info.avatar" />
                            <text class="assist_popup_nickname">{{ inviter_info.nick }}</text>
                        </view>

                        <!-- 倒计时 -->
                        <view style="text-align: center; padding-top: 10rpx">
                            <view style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center">
                                恭喜您！已助力成功！
                            </view>
                            <view style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center">
                                快来活动区挑选您的心仪好物吧！
                            </view>
                        </view>

                        <!-- 助力按钮 -->
                        <view class="assist_popup_button_container">
                            <view class="assist_popup_button" @click="handleSelectAction">
                                <text class="assist_popup_button_text">去挑选</text>
                            </view>
                        </view>
                    </view>
                </view>
            </u-popup>

            <!-- 未付款弹窗 -->
            <u-popup :show="unPayPopup" mode="center" :round="10" width="600rpx">
                <view class="passist_popup-title">
                    <view class="rule-popup-close" @click="closeRulePopupPay">
                        <view class="close-icon"></view>
                    </view>
                    <!-- 头部标题 -->
                    <!-- <view class="assist_popup_header">
                        <image class="assist_popup_header_left"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png" />
                        <text class="assist_popup_title">温馨提示</text>
                        <image class="assist_popup_header_right"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b093834395380024872.png" />
                    </view> -->

                    <!-- 内容区域 -->
                    <view class="assist_popup_content">
                        <view style="text-align: center; height: 300rpx; padding: 0rpx 24rpx 0rpx; margin-top: 90rpx">
                            <view style="height: 100rpx; font-size: 24rpx; color: #fd4a55; font-weight: 600"
                                >订单编号：{{ order_no }}</view
                            >
                            <text style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center"
                                >点击下方按钮完成支付，锁定心仪商品，库存有限先到先得！</text
                            >
                        </view>
                        <!-- 助力按钮 -->
                        <view class="assist_popup_button_container">
                            <view class="assist_popup_button" @click="getOrderPayStatus()">
                                <text class="assist_popup_button_text">去支付</text>
                            </view>
                        </view>
                    </view>
                </view>
            </u-popup>
            <!-- 商品售罄，去其它商品 -->
            <u-popup
                :show="isEmptyPopup"
                mode="center"
                :round="40"
                width="600rpx"
                :closeOnClickOverlay="true"
                @close="isEmptyPopup = false"
            >
                <view class="is_empty_popup">
                    <view class="is_empty_popup_title" @click="closeEmptyPopup"></view>
                </view>
            </u-popup>
            <!-- 活动未开始 -->
            <u-popup
                :show="isActivityNotStartPopup"
                mode="center"
                :round="40"
                width="600rpx"
                :closeOnClickOverlay="true"
                @close="isActivityNotStartPopup = false"
            >
                <view class="is_activity_not_start">
                    <view class="is_empty_popup_title" @click="closeActivityNotStartPopup"></view>
                </view>
            </u-popup>
        </view>
        <view class="oneYuanFlashSale_container_end" v-if="isEndAcitivity && loading">
            <view class="left" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <image
                    class="search_back"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cdc6691f655980010633.png"
                    @click="goBack"
                ></image>
            </view>
            <view class="oneYuanFlashSale_container_end_content"> </view>
            <view class="oneYuanFlashSale_container_end_content_title"> 活动已结束，返回上一页</view>
            <view
                @click="goBack"
                style="
                    margin-bottom: 316rpx;
                    background: linear-gradient(101deg, #ff2c49 -12%, #ff6a76 100%);
                    color: #ffffff;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 612rpx;
                    height: 94rpx;
                    margin: 54rpx auto 0rpx;
                    border-radius: 58rpx;
                    font-size: 30rpx;
                "
            >
                确认</view
            >
        </view>
        <custom-toast ref="customToast" />
        <gold-coin-timeout-tips task-code="oneYuanPurchase" ref="TimeoutTips" />
        <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'">
            <template #active_tag>
                <img
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891d4845695e3550011642.png"
                    style="
                        width: 64rpx;
                        height: 64rpx;
                        position: absolute;
                        left: 20rpx;
                        top: 10rpx;
                        transform: translate(-50%, -50%);
                    "
                />
            </template>
        </share-dialog>

        <!-- 活动规则弹窗 -->
        <u-popup
            :show="showRulePopup"
            v-prevent-scroll="showRulePopup"
            mode="center"
            :round="18"
            :safe-area-inset-bottom="false"
            catchtouchmove
        >
            <view class="rule-popup-content">
                <view class="rule-popup-header">
                    <view class="rule-title">
                        <view class="rule-title-decoration left"></view>
                        <text class="rule-title-text">活动规则</text>
                        <view class="rule-title-decoration right"></view>
                    </view>
                    <view class="rule-popup-close" @click="closeRulePopup">
                        <view class="close-icon"></view>
                    </view>
                </view>
                <scroll-view scroll-y class="rule-container">
                    <view class="rule-content">
                        <view class="rule-content-text">
                            <!-- <image class="rule-img"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68947388803fc5250011718.png"
                            /> -->
                            <view class="rule-section" v-html="activityRule"> </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </u-popup>

        <view class="pop-new" v-if="showPopNew">
            <view class="pop-new-wrap" @click.stop>
                <view class="close-icon" @click="showPopNew = false"></view>
                <image class="pop-new-icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b44356096d3960011785.png"></image>
                <view class="pop-new-content">
                    <view class="pop-new-content-text">
                        <view class="br">
                            {{ popNewInfo.content1 }}
                        </view>
                        <view class="br1">
                            {{ popNewInfo.content2 }}
                        </view>
                    </view>
                </view>
                <view class="pop-new-btn" @click="handlePopNewBtn">
                    <view class="pop-new-btn-text">
                        {{ popNewInfo.btnText }}
                    </view>
                </view>
            </view>
        </view>
        <!-- 商品详情弹窗 -->
        <u-popup :show="showProduct" v-prevent-scroll="showProduct" :safeAreaInsetBottom="false" bg-color="transparent" round="30" mode="bottom"  @close="closeProduct" @open="openProduct">
            <view class="product-popup-close" @click="closeProduct"></view>
            <view class="product-popup-wrap" style="background-color: transparent;">
                <view class="warp" style="background-color: #fff;">
                    <view class="product-popup-content">
                        <view class="product-popup-header">
                            <GoodsGallery />
                        </view>
                        <view class="product-popup-title-new">
                            <view class="product-popup-title-text-new">
                                {{ productInfoPopData.show_name }}
                            </view>
                            <view class="product-popup-title-text-price-new">
                                <view class="product-popup-title-text-price-text-new">
                                    ￥<span style="font-size: 42rpx; color: #404040; font-weight: 600;">{{ productInfoPopData.sale_price }}</span>
                                </view>
                            </view>
                        </view>
                        <view class="feature-wrap" v-if="sellPointList.length">
                            <view class="u-flex feature" v-for="(item, index) in sellPointList" :key="index">
                                <view class="dot"></view>{{ item }}
                            </view>
                        </view>
                        <view class="product-popup-content-new">
                            <view class="product-popup-content-text-new" v-html="goodsData.detail" v-lazy-html></view>
                        </view>
                    </view>
                </view>
            </view>
        </u-popup>
        <!-- 开店弹窗 -->
        <CustomModal
            :show="isOrderShow"
            width="616rpx"
            title="您的订单已存在"
            content="本轮活动仅限购买1件商品"
            confirmText="确定"
            :showCancelButton="true"
            cancelText="去订单页"
            contentStyle="text-align: center;font-size: 32rpx;font-weight: 400;"
            @confirm="isOrderShow = false"
            @cancel="goOrderPage"
        >
        </CustomModal>
        <!-- 开店弹窗 -->
        <CustomModal
            :show="isBackShow"
            width="616rpx"
            title="您已选择购买其他商品"
            content="本轮活动仅限购买1件商品 请取消购买订单后再重新选择"
            confirmText="确定"
            :showCancelButton="true"
            cancelText="去订单页"
            contentStyle="text-align: center;font-size: 32rpx;font-weight: 400;"
            @confirm="isBackShow = false"
            @cancel="goOrderPage"
        >
        </CustomModal>
        <!-- 是否确认购买此商品？ -->
        <CustomModal
            :show="isConfirmPopup"
            width="616rpx"
            title="是否确认购买此商品？"
            content="本轮活动仅限购买1件商品 购买完成后任务将重置"
            confirmText="确定"
            :showCancelButton="true"
            cancelText="取消"
            contentStyle="text-align: center;font-size: 32rpx;font-weight: 400;"
            @confirm="handleConfirmBuy"
            @cancel="isConfirmPopup = false"
        >
        </CustomModal>
        <EarnMoneySpendTimeoutTips
            v-if="fromPage == 'goldCoins'"
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '360rpx' }"
            :task-code="'viewGoodsThreeGold'"
            :fromPage="fromPage"
            :watchTime="15000"
        />
    </view>
</template>

<script lang="ts" src="./aThreeDiscount"> </script>
<style lang="scss" scoped src="./aThreeDiscount.scss"></style>
