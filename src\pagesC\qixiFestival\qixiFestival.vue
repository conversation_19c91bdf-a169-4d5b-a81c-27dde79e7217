<template>
    <view>
        <u-skeleton
            :rows="6"
            rowsWidth="750rpx"
            :rowsHeight="[200, 150, 150, 250, 250, 250]"
            :title="false"
            :loading="!isLoaded"
        ></u-skeleton>
        <view v-if="isLoaded">
            <APPSHARE :link="link" />
            <view class="qixi-festival-page" :style="{ 'margin-top': user_id ? '0' : '108rpx' }">
                <!-- 老头部 -->
                <view
                    class="header"
                    :style="{
                        'padding-top': statusBarHeight + 'rpx',
                        background: customBarBg,
                        position: $isInApp() ? 'sticky' : 'relative',
                    }"
                >
                    <view class="left" v-if="$isInApp()">
                        <image
                            class="search_back"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6883a563688974280011027.png"
                            @click="goBack"
                        ></image>
                    </view>
                    <view class="header_title">七夕活动 </view>
                    <view class="right">
                        <view class="share" @click="shareActivity"></view>
                        <view class="rule" @click="openRulePopup"></view>
                    </view>
                </view>
                <!-- 页面头部导航栏 -->
                <!-- <CustomBar2
                v-show="$isInApp()"
                :isHeaderTransparent="isHeaderTransparent"
                BackIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6883a563688974280011027.png"
                title="七夕活动"
                :titleStyle="{ color: '#fff' }"
                :background="customBarBg"
                :isBack="true"
                class="header"
            >
                <template #moreBtn>
                    <view class="more-btn">
                        <view class="share" @click="(_) => shareActivity()"></view>
                        <view class="rule" @click="openRulePopup"></view>
                    </view>
                </template>
            </CustomBar2> -->
                <!-- 签到活动 -->
                <view class="checkIn_box">
                    <CheckIn :link="link" :info="checkInInfo" :overTime="overTime" @refresh="initData()" />
                    <BoostProgress @invite="shareActivity" :info="progressInfo" />
                </view>
                <!-- 3折优惠券区域 -->
                <view class="coupon-section main-coupon">
                    <!-- <view class="section-header">
                <view class="title">3折神券限时抢</view>
                <view class="limit-tag">
                    <text class="iconfont icon-heart"></text>
                    <text>每日限量100</text>
                </view>
            </view>-->
                    <view class="event-details">8月25日-8月31日 每天上午10点开抢</view>
                    <view class="coupon-card">
                        <!-- 等待活动开始 -->
                        <view class="coupon-content coupon-right" v-if="mainCoupon.coupon_status === 0 && diffTime > 0">
                            <view class="coupon-text">
                                <view class="coupon-title">专场限时优惠券</view>
                                <u-count-down
                                    :time="getCountDownTime(mainCoupon.extra.time[0])"
                                    format="HH:mm:ss"
                                    autoStart
                                    @change="onChange"
                                    @finish="countDownFinish"
                                >
                                    <view class="time countdown">
                                        剩余
                                        <text class="time_item">{{
                                            timeData.hours > 9 ? timeData.hours : '0' + timeData.hours
                                        }}</text
                                        >时
                                        <text class="time_item">{{
                                            timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes
                                        }}</text
                                        >分
                                        <text class="time_item">{{
                                            timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds
                                        }}</text
                                        >秒可领取
                                    </view>
                                </u-count-down>
                            </view>
                            <view class="claim-btn claim-btn-grey" @click="greyClick(0)">待准点领取</view>
                        </view>
                        <!-- 待领取 -->
                        <view
                            class="coupon-content coupon-right"
                            v-else-if="mainCoupon.coupon_status === 0 && diffTime <= 0"
                        >
                            <view class="coupon-text">
                                <view class="coupon-title coupon-title-big">专场限时优惠券</view>
                            </view>
                            <view class="claim-btn" @click="couponClick(mainCoupon, 0)">立即领取</view>
                        </view>
                        <!-- 已领取 -->
                        <view class="coupon-content coupon-right" v-else-if="mainCoupon.coupon_status === 1">
                            <view class="coupon-text">
                                <view class="coupon-title">专场限时优惠券</view>
                                <u-count-down
                                    :time="getCountDownTime(mainCoupon.extra.time[1])"
                                    format="HH:mm:ss"
                                    autoStart
                                    @change="onChange"
                                    @finish="countDownFinish"
                                >
                                    <view class="time countdown">
                                        剩余有效时间：
                                        <text class="time_item2">{{
                                            timeData.hours > 9 ? timeData.hours : '0' + timeData.hours
                                        }}</text
                                        >时
                                        <text class="time_item2">{{
                                            timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes
                                        }}</text
                                        >钟
                                        <text class="time_item2">{{
                                            timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds
                                        }}</text
                                        >秒
                                    </view>
                                </u-count-down>
                            </view>
                            <view class="claim-btn" @click="couponClick(mainCoupon, 0)">去使用</view>
                        </view>
                        <!-- 3已使用 4已领光 -->
                        <view
                            class="coupon-content coupon-right"
                            v-else-if="mainCoupon.coupon_status === 3 || mainCoupon.coupon_status === 4"
                        >
                            <view class="coupon-text">
                                <view class="coupon-title coupon-title-big">专场限时优惠券</view>
                            </view>
                            <view class="claim-btn claim-btn-grey" @click="greyClick(mainCoupon.coupon_status)">{{
                                mainCoupon.coupon_status === 3 ? '今日已用' : '今日已抢光'
                            }}</view>
                        </view>
                    </view>
                </view>

                <!-- 多种优惠券区域 -->
                <view class="coupon-section multiple-coupons">
                    <view class="multiple-coupons-content">
                        <view
                            class="multiple-coupons-item"
                            :class="[
                                `multiple-coupons-item${index + 1}`,
                                item.coupon_status === 3 || item.coupon_status === 4
                                    ? `multiple-coupons-list${index + 1}-grey`
                                    : '',
                            ]"
                            v-for="(item, index) in multipleCoupons.coupon.slice(0, 3)"
                            :key="index"
                            @click="couponClick(item, 1)"
                        >
                            <view class="multiple-coupons-item-content">
                                <text v-if="item.coupon_status === 0">立即领取</text>
                                <text v-else-if="item.coupon_status === 1">去使用</text>
                                <text v-else-if="item.coupon_status === 3">今日已用</text>
                                <text v-else-if="item.coupon_status === 4">已抢光</text>
                            </view>
                        </view>
                    </view>
                    <view class="multiple-coupons-tip" @click="showTipPopup = true">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a33160ddefb9090011777.png"
                            mode="aspectFill"
                        ></image>
                        <text>优惠券仅限部分商品可用</text>
                    </view>
                </view>

                <!-- 产品推荐区域 -->
                <view v-for="(section, sectionIndex) in productSections" :key="sectionIndex" class="product-section">
                    <view class="section-header">
                        <view
                            class="title"
                            :style="{ backgroundImage: `url(${section.title})` }"
                            style="background-size: 100% 100%"
                        ></view>
                    </view>
                    <view class="product-list">
                        <view class="content">
                            <view
                                class="content_title"
                                v-for="(item, index) in section.products"
                                :key="index"
                                @click="handleAssist(item)"
                            >
                                <view class="content_img_wrap">
                                    <image class="content_img" :src="item.goods_image" mode="aspectFit"/>
                                    <image
                                        v-if="item.tids.includes('2')"
                                        class="content_img_icon"
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879fb73920835980010652.png"
                                    />
                                </view>
                                <view class="content_title_text">
                                    <view class="content_title_text_title">
                                        <!-- <image
                                        class="content_title_text_title_icon"
                                        v-if="item.tids.includes('1')"
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a42d190bee30490087980.png"
                                    />
                                    <image
                                        class="content_title_text_title_icon"
                                        v-if="item.tids.includes('2')"
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a42d190caf20520088251.png"
                                    /> -->
                                        <text>{{ item.goods_name }}</text>
                                    </view>
                                    <view class="content_title_text_content">
                                        <view
                                            style="
                                                display: flex;
                                                align-items: flex-end;
                                                width: 74rpx;
                                                height: 54rpx;
                                                color: #ff4462;
                                                font-weight: 600;
                                                padding-bottom: 10rpx;
                                            "
                                        >
                                            <text style="font-size: 24rpx; align-self: flex-end; line-height: 1"
                                                >¥</text
                                            >
                                            <view style="display: flex; align-items: flex-end; height: 54rpx">
                                                <text
                                                    v-if="String(item.sale_price).indexOf('.') !== -1"
                                                    style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                                >
                                                    {{ String(item.sale_price).split('.')[0] }}
                                                </text>
                                                <text
                                                    v-else
                                                    style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                                >
                                                    {{ item.sale_price }}
                                                </text>
                                                <text
                                                    v-if="String(item.sale_price).indexOf('.') !== -1"
                                                    style="
                                                        font-size: 38rpx;
                                                        line-height: 1;
                                                        align-self: flex-end;
                                                        margin-left: 2rpx;
                                                    "
                                                >
                                                    .
                                                </text>
                                                <text
                                                    v-if="String(item.sale_price).indexOf('.') !== -1"
                                                    style="font-size: 24rpx; line-height: 1; align-self: flex-end"
                                                >
                                                    {{ String(item.sale_price).split('.')[1] }}
                                                </text>
                                            </view>
                                        </view>
                                        <view class="content_title_text_grab_text"
                                            >优惠前：<text>{{ Math.floor(item.mprice) }}元</text>
                                        </view>
                                        <!-- 抢商品 -->
                                        <view class="content_title_text_grab">
                                            <view class="content_title_text_grab_bg">
                                                <view class="content_title_text_grab_bg_img">
                                                    <!-- 进度条 -->
                                                    <view
                                                        class="content_title_text_grab_bg_progress_bg"
                                                        v-if="item.stock !== 0"
                                                    >
                                                        <view class="content_title_text_content_progresss">
                                                            <view
                                                                class="progress"
                                                                :style="{
                                                                    width:
                                                                        (item.sales / (item.stock + item.sales)) * 100 +
                                                                        '%',
                                                                }"
                                                            >
                                                            </view>
                                                        </view>
                                                        <view class="content_title_text_grab_text_progress_text">
                                                            已抢
                                                            {{
                                                                ((item.sales / (item.stock + item.sales)) * 100) % 1 ===
                                                                0
                                                                    ? (item.sales / (item.stock + item.sales)) * 100
                                                                    : (
                                                                          (item.sales / (item.stock + item.sales)) *
                                                                          100
                                                                      ).toFixed(2)
                                                            }}%
                                                        </view>
                                                    </view>
                                                    <view class="content_title_text_grab_text_assist_bg" v-else></view>
                                                    <!-- 抢商品 -->
                                                    <view
                                                        class="content_title_text_grab_text"
                                                        :class="
                                                            item.user_seckill && item.user_seckill.order_status == 300
                                                                ? 'normal_buy_active'
                                                                : ''
                                                        "
                                                        v-if="item.stock !== 0"
                                                    ></view>
                                                    <view class="content_title_text_grab_text_assist" v-else> </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="footer">
                    <image
                        @click="goEarnMoney"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a539071dd1b1220018410.png"
                        mode="widthFix"
                    />
                </view>
                <!-- 底部按钮 -->
                <!-- <view class="footer">
            <view class="footer-btn" @click="goEarnMoney"> 去赚钱花省更多 </view>
        </view> -->
                <!-- 活动规则弹窗 -->
                <u-popup
                    :show="showRulePopup"
                    v-prevent-scroll="showRulePopup"
                    mode="center"
                    bgColor="transparent"
                    :round="18"
                    :safe-area-inset-bottom="false"
                    catchtouchmove
                >
                    <view class="rule-popup-content">
                        <view class="rule-popup-header">
                            <view class="rule-title">
                                <view class="rule-title-decoration left"></view>
                                <text class="rule-title-text">活动规则</text>
                                <view class="rule-title-decoration right"></view>
                            </view>
                            <view class="rule-popup-close" @click="closeRulePopup">
                                <view class="close-icon"></view>
                            </view>
                        </view>
                        <scroll-view scroll-y class="rule-container">
                            <view class="rule-content">
                                <view class="rule-content-text">
                                    <view class="rule-section" v-html="baseInfo.rule"> </view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </u-popup>
                <!-- 发布商品弹窗 -->
                <u-popup
                    :show="showPublishPopup"
                    mode="bottom"
                    :round="24"
                    @close="closePublishPopup"
                    :closeOnClickOverlay="true"
                    :safeAreaInsetBottom="false"
                    v-prevent-scroll="showPublishPopup"
                    catchtouchmove
                >
                    <view class="publish-popup">
                        <!-- 弹窗主体 -->
                        <view class="publish-popup-body">
                            <!-- 弹窗头部 -->
                            <view class="publish-popup-header">
                                <text class="publish-popup-title">{{ qixiTitle }}</text>
                                <view class="publish-popup-close" @click="closePublishPopup"> </view>
                            </view>
                            <!-- 商品详情 -->
                            <view class="content">
                                <view
                                    class="content_title"
                                    v-for="(item, index) in canUseProductList"
                                    :key="index"
                                    @click="handleAssist(item)"
                                >
                                    <view class="content_img_wrap">
                                        <image class="content_img" :src="item.goods_image" mode="aspectFit" />
                                        <image
                                            class="content_img_icon"
                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879fb73920835980010652.png"
                                        />
                                    </view>
                                    <view class="content_title_text">
                                        <view class="content_title_text_title">{{ item.goods_name }}</view>
                                        <view class="content_title_text_content">
                                            <view
                                                style="
                                                    display: flex;
                                                    align-items: flex-end;
                                                    width: 74rpx;
                                                    height: 54rpx;
                                                    color: #ff4462;
                                                    font-weight: 600;
                                                    padding-bottom: 10rpx;
                                                "
                                            >
                                                <text style="font-size: 24rpx; align-self: flex-end; line-height: 1"
                                                    >¥</text
                                                >
                                                <view style="display: flex; align-items: flex-end; height: 54rpx">
                                                    <text
                                                        v-if="String(item.sale_price).indexOf('.') !== -1"
                                                        style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                                    >
                                                        {{ String(item.sale_price).split('.')[0] }}
                                                    </text>
                                                    <text
                                                        v-else
                                                        style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                                    >
                                                        {{ item.sale_price }}
                                                    </text>
                                                    <text
                                                        v-if="String(item.sale_price).indexOf('.') !== -1"
                                                        style="
                                                            font-size: 38rpx;
                                                            line-height: 1;
                                                            align-self: flex-end;
                                                            margin-left: 2rpx;
                                                        "
                                                    >
                                                        .
                                                    </text>
                                                    <text
                                                        v-if="String(item.sale_price).indexOf('.') !== -1"
                                                        style="font-size: 24rpx; line-height: 1; align-self: flex-end"
                                                    >
                                                        {{ String(item.sale_price).split('.')[1] }}
                                                    </text>
                                                </view>
                                            </view>
                                            <view class="content_title_text_grab_text"
                                                >优惠前：<text>{{ Math.floor(item.mprice) }}元</text>
                                            </view>
                                            <!-- 抢商品 -->
                                            <view class="content_title_text_grab">
                                                <view class="content_title_text_grab_bg">
                                                    <view class="content_title_text_grab_bg_img">
                                                        <!-- 进度条 -->
                                                        <view
                                                            class="content_title_text_grab_bg_progress_bg"
                                                            v-if="item.stock !== 0"
                                                        >
                                                            <view class="content_title_text_content_progresss">
                                                                <view
                                                                    class="progress"
                                                                    :style="{
                                                                        width:
                                                                            (item.sales / (item.stock + item.sales)) *
                                                                                100 +
                                                                            '%',
                                                                    }"
                                                                >
                                                                </view>
                                                            </view>
                                                            <view class="content_title_text_grab_text_progress_text">
                                                                已抢
                                                                {{
                                                                    ((item.sales / (item.stock + item.sales)) * 100) %
                                                                        1 ===
                                                                    0
                                                                        ? (item.sales / (item.stock + item.sales)) * 100
                                                                        : (
                                                                              (item.sales / (item.stock + item.sales)) *
                                                                              100
                                                                          ).toFixed(2)
                                                                }}%
                                                            </view>
                                                        </view>
                                                        <view
                                                            class="content_title_text_grab_text_assist_bg"
                                                            v-else
                                                        ></view>
                                                        <!-- 抢商品 -->
                                                        <view
                                                            class="content_title_text_grab_text"
                                                            :class="
                                                                item.user_seckill &&
                                                                item.user_seckill.order_status == 300
                                                                    ? 'normal_buy_active'
                                                                    : ''
                                                            "
                                                            v-if="item.stock !== 0"
                                                        ></view>
                                                        <view class="content_title_text_grab_text_assist" v-else>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </u-popup>
                <!-- 分享弹窗 -->
                <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'">
                    <template #active_tag>
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891d4845695e3550011642.png"
                            style="
                                width: 64rpx;
                                height: 64rpx;
                                position: absolute;
                                left: 20rpx;
                                top: 10rpx;
                                transform: translate(-50%, -50%);
                            "
                        />
                    </template>
                </share-dialog>
                <share-success-popup
                    :isShow="showShareSuccess"
                    :count="count"
                    @closePopup="showShareSuccess = false"
                ></share-success-popup>
            </view>
            <prizeResult
                @getPrizeResult="getPrizeResult"
                @claimPrize="claimPrize"
                @close="showPrizeModelEnd = false"
                :prizeList="prizeList"
                :rewardList="rewardList"
                :myprizeList="myprizeList"
                :show="showPrizeModelEnd"
                :isShowIcon="Number(progressInfo.draw_time) * 1000 < new Date().getTime()"
            />
            <!-- 没有到开奖时间 图片显示 -->
            <prizeModel
                v-if="Number(progressInfo.draw_time) * 1000 < new Date().getTime()"
                :show.sync="showPrizeModel"
                :imageUrl="prizeImageUrl"
                @close="closePrizeModel"
            />
            <u-popup :show="showTipPopup" mode="center" :round="18" bgColor="transparent">
                <view class="tipContent">
                    <view class="tipBtn" @click="showTipPopup = false"> </view>
                </view>
            </u-popup>
            <claim-prize-popup
                :isShow="showClaimPrizePopup"
                :couponId="couponId"
                @closePopup="showClaimPrizePopup = false"
            ></claim-prize-popup>
        </view>
    </view>
</template>

<script lang="ts">
import { getQixiFestivalList, getQixiFestivalDetail, getQixiFestivalInvite } from '@/http/qixiFestival';
import { checkNewUser } from '@/http/doubleEleven';
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import { getThemeActivity } from '@/http/memberActivity';
import { reciveCoupon } from '@/http/goods';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import Constants from '@/common/Constants';
import CheckIn from './components/checkIn.vue';
import ShareSuccessPopup from './components/shareSuccessPopup.vue';
import ClaimPrizePopup from './components/claimPrizePopup.vue';
import BoostProgress from './components/boostProgress.vue';
import PrizeResult from './components/prizeResult.vue';
import PrizeModel from './components/prizeModel.vue';
import { CheckAppJump } from '@/common/decorators';
interface Product {
    id: string;
    name: string;
    image: string;
    tag?: string;
    features: string[];
    currentPrice: number;
    originalPrice: number;
}

interface ProductSection {
    title: string;
    products: Product[];
}

@Component({
    components: {
        ShareDialog,
        CheckIn,
        ShareSuccessPopup,
        BoostProgress,
        PrizeResult,
        PrizeModel,
        ClaimPrizePopup,
    },
})
export default class QixiFestival extends Vue {
    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    public count: number = 0;
    public overTime: number = 0;
    public qixiTitle: string = '七夕礼遇';

    public prizeList: any[] = [];
    public rewardList: any[] = [];
    public myprizeList: any[] = [];
    public progressInfo: any[] = [];
    public showPrizeModelEnd: boolean = false;

    public showPrizeModel: boolean = false;
    public prizeImageUrl: string =
        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a96a7b696694320011089.png';

    public isLoaded: boolean = false;
    public showTipPopup: boolean = false;
    // 倒计时时间
    countdownTime = '23:46:32';
    public show: boolean = false;
    public productInfo: any = {
        name: '',
        image: '',
        price: '',
        desc: '',
        priceColor: '',
        descColor: '',
        imageBg: '',
    };

    public showShareSuccess: boolean = false;
    public showClaimPrizePopup: boolean = false;
    public link: string = '';
    public info: any = { checkInCount: 10 };

    public showPublishPopup: boolean = false;
    public canUseProductList: any[] = [];
    public allProducts: any[] = [];

    // 多种优惠券数据
    public multipleCoupons: any = {};

    // 产品区域数据
    productSections: ProductSection[] = [
        {
            title: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a9f4021f76a1290011848.png',
            products: [],
        },
        {
            title: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a9f41665e554170011724.png',
            products: [],
        },
    ];

    // 是否透明头部
    public isHeaderTransparent: boolean = true;
    // 倒计时定时器
    private countdownTimer: number | null = null;
    // 初始化活动数据
    public activityId: number = 0;
    public productList: any[] = [];
    public baseInfo: any = {};
    // 活动规则弹窗
    public showRulePopup: boolean = false;
    // 主优惠券
    public mainCoupon: any = {};
    // 倒计时时间
    public timeData: any = {};
    public diffTime: number = 1;
    public shareTime: any = null;
    public customBarBg = 'transparent';
    // 模拟签到数据
    public checkInInfo: any = {
        extra: {
            list: [],
        },
    };

    public couponId: string = '';

    onShow() {
        this.initData();
    }

    onPageScroll(e) {
        if (e.scrollTop > 0) {
            this.customBarBg = '#C21842';
        } else {
            this.customBarBg = 'transparent';
        }
    }

    get statusBarHeight() {
        console.log(AppModule.statusBarHeight, 3333);
        return AppModule.statusBarHeight;
    }

    async onLoad(options: any) {
        this.link = options.link;
        this.activityId = Number(process.env.VUE_APP_QIXI_FESTIVAL_ACTIVITY_ID);
        await this.initData();
        const params = {
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_EXPOSURE,
            name: options.inviter_id || '',
        };

        Utils.logTrace(params);
        this.isLoaded = true;
        if (options.inviter_id) {
            await getQixiFestivalInvite({ inviter_id: options.inviter_id, activity_id: this.activityId });
            const result = await checkNewUser();
            const isNewUser = result.is_new_user;
            if (isNewUser) {
                this.count = 1;
                if (options.inviter_id) {
                    Utils.logTrace({
                        module: Constants.LOG_TRACE_MODULE_DREAME,
                        event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                            .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_NEW_USER,
                    });
                }
            } else {
                this.count = 1;
            }
            this.showShareSuccess = true;
            this.initData();
        }
    }

    mounted() {
        // this.startCountdown('2024-08-31T10:00:00');
    }

    beforeDestroy() {
        // if (this.countdownTimer) {
        //     clearInterval(this.countdownTimer);
        // }
    }

    claimPrize(id: string) {
        this.showPrizeModel = false;
        this.showPrizeModelEnd = false;
        this.showClaimPrizePopup = true;
        this.couponId = id;
    }

    closePrizeModel() {
        this.showPrizeModel = false;
    }

    async getPrizeResult() {
        // 当前时间小于8月31日12:00
        const now = new Date();
        // 修复类型问题，确保progressInfo存在且有draw_time属性
        const drawTime =
            this.progressInfo && (this.progressInfo as any).draw_time
                ? Number((this.progressInfo as any).draw_time) * 1000
                : 0;
        if (now.getTime() < drawTime) {
            this.showPrizeModel = true;
        } else {
            const res = await getQixiFestivalList({ activity_id: this.activityId });
            this.prizeList = res.prize_signs.filter((item: any) => item.prize_type === 2 || item.prize_type === 3);
            this.rewardList = res.prize_signs.filter((item: any) => item.prize_type === 1);
            this.myprizeList = res.my_signs;
            this.showPrizeModelEnd = true;
        }
    }

    @CheckAppJump()
    handleAssist(item: any) {
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.goods_id}&activityType=qixiFestival`);
    }

    async initData() {
        await this.getPageData();
        getQixiFestivalDetail({
            activity_id: this.activityId,
        }).then((res) => {
            this.progressInfo = res;
        });
    }

    greyClick(status: number) {
        if (status === 0) {
            Utils.Toast('优惠券待准点领取');
        } else if (status === 3) {
            Utils.Toast('优惠券已用完');
        } else if (status === 4) {
            Utils.Toast('优惠券已领光');
        }
    }

    goEarnMoney() {
        Utils.navigateTo(`/pagesC/earnMoneySpend/earnMoneySpend`);
    }

    async getPageData() {
        this.activityId = Number(process.env.VUE_APP_QIXI_FESTIVAL_ACTIVITY_ID);
        const { modules, base_info } = await getThemeActivity({ id: Number(this.activityId) });
        if (modules.length > 0) {
            this.allProducts = [];
            modules.forEach((item, index) => {
                // 主优惠券
                if (index === 0 && item.extra && item.module_code === 'CHECKIN') {
                    this.overTime = item.extra.checkin_end_time; // 打卡结束时间
                    this.checkInInfo = item;
                } else if (index === 1 && item.extra && item.extra.coupon.length > 0) {
                    this.mainCoupon = item.extra.coupon[0];
                    this.mainCoupon.ids = item.extra.coupon_activity_id;
                    // 多优惠券
                } else if (index === 2 && item.extra && item.extra.coupon.length > 0) {
                    this.multipleCoupons = item.extra;
                    // 产品推荐
                } else if (
                    index === 3 &&
                    item.extra &&
                    item.extra.length > 0 &&
                    item.module_code === 'RECOMMEND_GOODS'
                ) {
                    this.productSections[0].products = item.extra.reduce((acc, cur) => acc.concat(cur.arr), []);
                    this.allProducts = this.allProducts.concat(this.productSections[0].products);
                } else if (
                    index === 4 &&
                    item.extra &&
                    item.extra.length > 0 &&
                    item.module_code === 'RECOMMEND_GOODS'
                ) {
                    this.productSections[1].products = item.extra.reduce((acc, cur) => acc.concat(cur.arr), []);
                    this.allProducts = this.allProducts.concat(this.productSections[1].products);
                } else if (
                    index === 5 &&
                    item.extra &&
                    item.extra.length > 0 &&
                    item.module_code === 'RECOMMEND_GOODS'
                ) {
                    this.productSections[2].products = item.extra.reduce((acc, cur) => acc.concat(cur.arr), []);
                    this.allProducts = this.allProducts.concat(this.productSections[2].products);
                }
            });
        }
        console.log(this.allProducts, 399393);
        this.baseInfo = base_info;
    }

    @CheckAppJump()
    shareActivity() {
        uni.$u.throttle(() => {
            if (UserModule.sdkVersion < 13) {
                Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
                return;
            }
            const randomIndex = Math.floor(Math.random() * this.allProducts.length);
            this.productInfo = {
                name: this.allProducts[randomIndex].goods_name,
                image: this.allProducts[randomIndex].goods_image,
                price: this.allProducts[randomIndex].sale_price,
                desc: '',
                priceColor: '#FF1F0E',
                descColor: '#C59245',
                imageBg:
                    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a3d6a8992546270011179.png',
            };
            this.show = true;
        }, 1000);
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_SHARE_FRIEND,
        });
        Utils.cardShare(type)(
            {
                target: 'wechat,weixin_circle,qq,sina,image_template,download',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/qixiFestival/qixiFestival?inviter_id=${this.user_id}`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/qixiFestival/qixiFestival?inviter_id=${this.user_id}`,
            },
            {
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/qixiFestival/qixiFestival?inviter_id=${this.user_id}`,
                    image:
                        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a98b79bb4987670064894.png' +
                        '?x-oss-process=image/resize,w_200',
                    title: '“礼”遇七夕，为爱放“价”',
                    desc: '三折券限时抢,签到抽大奖',
                },
                extras: {
                    type: 'activity',
                    copyText: '礼遇七夕为爱放价，3折券限时抢，集花瓣抽大奖！',
                    // id: this.activityId,
                    goods: {
                        name: this.productInfo.name,
                        // desc: '邀请新用户加入，新款扫地机器人一元拿走！',
                        imageBg:
                            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a98b79bb4987670064894.png',
                        image: this.productInfo.image,
                        price: this.productInfo.price,
                        priceColor: '#FF1F0E',
                        descColor: '#C59245',
                    },
                },
            },
        );
    }

    onChange(e) {
        this.timeData = e;
    }

    closePublishPopup() {
        this.showPublishPopup = false;
    }

    @CheckAppJump()
    couponClick(coupon: any, type: number) {
        if (coupon.coupon_status === 0) {
            this.receiveCoupon(coupon, type);
        } else if (coupon.coupon_status === 1) {
            this.canUseProductList = this.getCanUseProductList(coupon.goods_val);
            if (this.canUseProductList.length > 0) {
                if (type == 0) {
                    this.qixiTitle = '3折专区';
                } else {
                    this.qixiTitle = `${coupon.coupon_image_price}折专区`;
                }
                this.showPublishPopup = true;
            } else {
                Utils.Toast('暂无可用商品');
            }
        } else if (coupon.coupon_status === 3) {
            Utils.Toast('优惠券已用完');
        }
    }

    // 获取可使用商品列表
    getCanUseProductList(goods_val: string) {
        if (!goods_val) {
            return this.allProducts;
        }
        const goods_val_arr = goods_val.split(',');
        const list = this.allProducts.filter((item) => goods_val_arr.includes(item.goods_id.toString()));

        return list.length > 0 || goods_val_arr.length > 0 ? list : this.allProducts;
    }

    // 倒计时完成
    countDownFinish() {
        this.getPageData();
    }

    // 获取倒计时时间
    // time 格式为 HH:mm:ss
    getCountDownTime(time: string): number {
        // 1. 解析时间字符串
        const [hours, minutes, seconds] = time.split(':').map(Number);
        const now = new Date();
        const endTime = new Date();
        endTime.setHours(hours);
        endTime.setMinutes(minutes);
        endTime.setSeconds(seconds);
        const diff = endTime.getTime() - now.getTime();
        this.diffTime = diff > 0 ? diff : 0;
        return diff > 0 ? diff : 0;
    }

    // 返回上一页
    goBack() {
        Utils.goBack();
    }

    // 打开规则弹窗
    openRulePopup() {
        this.showRulePopup = true;
    }

    // 关闭规则弹窗
    closeRulePopup() {
        this.showRulePopup = false;
    }

    async receiveCoupon(coupon: any, type: number) {
        // 三折券
        if (type === 0) {
            await reciveCoupon({ market_ids: coupon.coupon_id, ids: this.mainCoupon.ids, grant_type: 3 });
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_THREE_COUPON,
                id: coupon.coupon_id,
                name: coupon.coupon_name,
            });
            coupon.coupon_status = 1;
            // 随时抢
        } else if (type === 1) {
            await reciveCoupon({
                market_ids: coupon.coupon_id,
                ids: this.multipleCoupons.coupon_activity_id,
                grant_type: 3,
            });
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY_FREE_COUPON,
                id: coupon.coupon_id,
                name: coupon.coupon_name,
            });
            coupon.coupon_status = 1;
        }
        Utils.Toast('领取成功');
        await this.getPageData();
    }

    // 跳转到产品详情
    goToProduct(product: Product) {
        uni.navigateTo({
            url: `/pages/product/detail?id=${product.id}`,
        });
    }
}
</script>

<style lang="scss" scoped>
@import './qixiFestival.scss';
</style>
