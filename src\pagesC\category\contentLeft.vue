<template>
  <view class="content-left">
    <view class="content-left-item" v-for="(item, index) in dataList" :key="index" @click="handleClick(item)">
      <view class="content-left-item-text">{{ item.name }}</view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class ContentLeft extends Vue {
  @Prop()
  readonly dataList: Array<any> = []

  handleClick(item: any) {
    this.$emit('clickItem', item)
  }
}
</script>

<style lang="scss" scoped>
.content-left {
  flex: 0.3;

  .content-left-item {
    padding: 15rpx 24rpx;
    font-size: 27rpx;
    font-weight: bold;
    color: #121212;
  }

  .content-left-item-text {
    padding: 15rpx 24rpx;
    font-size: 27rpx;
    font-weight: bold;
    color: #121212;
  }
}
</style>
