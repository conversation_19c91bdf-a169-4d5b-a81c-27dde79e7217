<template>
    <view class="container">
        <CustomBar title="金币明细" background="transparent"></CustomBar>

        <view class="coinDetails" :style="{ height: `100%` }">
            <view class="tabs-header">
                <view class="tab-item"
                      v-for="(item, index) in tabList"
                      :key="item.id"
                      :class="{ 'active': current === index }"
                      @click="tabClick(index)">
                    <text class="tab-text">{{ item.name }}</text>
                    <view class="tab-line" v-if="current === index"></view>
                </view>
            </view>

            <swiper class="swiper-box" :current="swiperCurrent" @change="swiperChange"
                @animationfinish="animationfinish" :style="{ height: 'calc(100% - 96rpx)' }">
                <!-- 获取明细 -->
                <swiper-item class="swiper-item">
                    <scroll-view @scrolltolower="earnLowerBottom" scroll-y style="height: 100%; width: 100%">
                        <view class="swiper-div" style="height: 100%">
                            <view class="main u-flex u-flex-col u-col-center u-row-left"
                                v-if="earnList && earnList.length > 0">
                                <view class="main-item" v-for="item in earnList" :key="item.id">
                                    <CoinRecord style="width: 100%" :itemData="item" colorStyle="color:#FE7F3C"
                                        @showMask="showMaskclick" />
                                </view>
                            </view>
                            <view v-if="earnList.length === 0" class="empty-box u-flex u-col-center u-row-center"
                                :style="{ height: `100%` }">
                                <view class="empty-text">
                                    暂无记录
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </swiper-item>

                <!-- 消耗明细 -->
                <swiper-item class="swiper-item">
                    <scroll-view @scrolltolower="consumeLowerBottom" scroll-y style="height: 100%; width: 100%">
                        <view class="swiper-div" style="height: 100%">
                            <view class="main u-flex u-flex-col u-col-center u-row-left"
                                v-if="consumeList && consumeList.length > 0">
                                <view class="main-item" v-for="item in consumeList" :key="item.id">
                                    <CoinRecord style="width: 100%; margin-top: 16rpx;" :itemData="item" colorStyle="color:#FF6200"
                                        @showMask="showMaskclick" />
                                </view>
                            </view>
                            <view v-if="consumeList.length === 0" class="empty-box u-flex u-col-center u-row-center"
                                :style="{ height: `calc(100% - 98rpx)` }">
                                <view class="empty-text">
                                    暂无记录
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </swiper-item>
            </swiper>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import CoinRecord from './components/CoinRecord.vue';
import { AppModule } from '@/store/modules/app';
import { getCoinDetail } from '@/http/coin';

@Component({
    components: {
        DefaultPage,
        CoinRecord,
    },
})
export default class CoinDetail extends Vue {
    public tabList: Array<any> = [
        {
            name: '获取明细',
            checked: true,
            id: 1,
        },
        {
            name: '消耗明细',
            checked: false,
            id: 2,
        },
    ];

    public current: Number = 0;
    public swiperCurrent: number = 0;
    public isShowMask: boolean = false; // 遮罩层是否显示
    public earnList: Array<any> = []; // 获取明细列表
    public consumeList: Array<any> = []; // 消耗明细列表
    public earnPage: number = 1; // 获取明细页码
    public consumePage: number = 1; // 消耗明细页码
    public size: number = 15; // 每页条数
    public isEarnMore: Boolean = true; // 判断获取明细是否触底加载
    public isConsumeMore: Boolean = true; // 判断消耗明细是否触底加载
    public numValue: String = '';
    public eventName: String = '';

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    onLoad() {
        this.init();
    }

    async init() {
        this.loadEarnList();
        this.loadConsumeList();
    }

    // 加载获取明细列表
    async loadEarnList() {
        const params = { asset_type: 3, type: 1, page: this.earnPage, size: this.size };
        const result = await getCoinDetail(params);
        if (result && result.records) {
            this.earnList = [...this.earnList, ...result.records];
            if (result.records.length < this.size) {
                this.isEarnMore = false;
            }
        }
    }

    // 加载消耗明细列表
    async loadConsumeList() {
        const params = { asset_type: 3, type: 2, page: this.consumePage, size: this.size };
        const result = await getCoinDetail(params);
        if (result && result.records) {
            this.consumeList = [...this.consumeList, ...result.records];
            if (result.records.length < this.size) {
                this.isConsumeMore = false;
            }
        }
    }

    // 获取明细触底加载
    earnLowerBottom() {
        if (!this.isEarnMore) return;
        this.earnPage += 1;
        this.loadEarnList();
    }

    // 消耗明细触底加载
    consumeLowerBottom() {
        if (!this.isConsumeMore) return;
        this.consumePage += 1;
        this.loadConsumeList();
    }

    // 标签点击切换
    tabClick(index: number) {
        this.current = index;
        this.swiperCurrent = index;
    }

    swiperChange(e) {
        const current = e.detail.current;
        this.swiperCurrent = current;
        this.current = current;
    }

    animationfinish(e) {
        const current: number = e.detail.current;
        this.swiperCurrent = current;
        this.current = current;
    }

    showMaskclick(item) {
        this.isShowMask = true;
        this.eventName = item.eventName;
        this.numValue = (item.numValue > 0 ? '+' : '') + item.numValue;
    }
}
</script>

<style lang="scss" scoped>
.container {
    background: $fill-color-bg-gray;

    .coinDetails {
        border-radius: 24rpx 24rpx 0 0;
        padding: 0 32rpx;
        -webkit-transform: rotate(0deg);
        background: $fill-color-bg-white;
        overflow: hidden;
        margin: 24rpx 32rpx 0;

        .tabs-header {
            display: flex;
            height: 96rpx;
            background: #ffffff;

            .tab-item {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                cursor: pointer;
                .tab-text {
                    font-size: 32rpx;
                    color: #777777;
                    font-weight: 500;
                    transition: color 0.3s;
                }
                .tab-line {
                    position: absolute;
                    bottom: 0rpx;
                    width: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    height: 5.5rpx;
                    background: #121212;
                    border-radius: 3rpx;
                }
                &.active {
                    .tab-text {
                        color: #121212;
                        font-weight: 600;
                    }
                }
            }
        }

        .swiper-box {
            height: 100%;

            .main {
                height: 100%;
                background-color: $fill-color-bg-white;
                border-radius: 24rpx;
                display: -webkit-box;
                padding-top: 16rpx;

                .main-item {
                    width: 100%;
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}
.empty-text {
    font-size: 32rpx;
    color: #777777;
    font-weight: normal;
}
</style>

