<template>
    <div class="device-item" @click="gotoDevice">
        <div class="device-info-card">
            <!-- <img :src="image" alt="设备图片" class="device-info-image" /> -->
            <img
                :src="image"
                class="device-info-image"
                mode="aspectFit"
            />
            <div class="info_contianer">
                <div class="device-info-name">{{ name }}</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png';
export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({}),
        },
    },
    computed: {
        name() {
            return (
                this.currentDevice.customName ||
                this.currentDevice.deviceInfo?.displayName ||
                this.currentDevice.displayName ||
                ''
            );
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage;
        },
    },
    methods: {
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    },
};
</script>

<style scoped>
.device-item {
    width: 360rpx;
    height: 156rpx;
    box-sizing: border-box;
    padding: 14rpx;
    background: linear-gradient(108deg, #e2f0ff 0%, #f7f9fa 48%, #ffffff 100%, #ffffff 100%);
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    border-radius: 24rpx;
    border-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%) 1;
}
.device-item-more {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    width: 52rpx;
    height: 52rpx;
}
.device-info-card {
    display: flex;
}
.device-info-area {
    padding: 32rpx;
}
.device-info-name {
    color: #121212;
    font-size: 34rpx;
    font-weight: 500;
    /* margin-top: 12rpx; */
    margin-top: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 220rpx;
}
.device-info-status {
    color: #777777;
    font-size: 24rpx;
    line-height: 32rpx;
    display: flex;
    align-items: center;
    margin-top: 12rpx;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.col-line {
    width: 1rpx;
    height: 16rpx;
    background-color: #777777;
    margin: 0 16rpx;
}
.device-info-image {
    width: 88rpx;
    /* width: 94rpx;
    height: 94rpx; */
    object-fit: contain;
}

.other-device {
    color: #3d3d3d;
    font-size: 24rpx;
    line-height: 32rpx;
    margin: 44rpx 0 32rpx 32rpx;
    margin-left: 32rpx;
}
.info_contianer {
    display: flex;
    flex-direction: column;
    align-items: center;
}
</style>
