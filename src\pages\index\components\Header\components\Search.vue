<template>
    <view class="search" @click="navigateTo">
        <image style="width: 46rpx; height: 46rpx;display: block;" src="https://wpm-cdn.dreame.tech/images/202306/535513-1686106493238.png"></image>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils'

@Component
export default class Search extends Vue {
    constructor() {
        super();
    }

    navigateTo() {
        Utils.navigateTo('/pagesA/search/search?search_type=' + 1)
    }
}
</script>
<style lang="scss" scoped>
</style>
