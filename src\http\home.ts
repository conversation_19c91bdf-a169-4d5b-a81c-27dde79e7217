import http from './index';
import req from './requestGo/index';
/*
 * 首页banner
 * @returns
 */
// export const bannerList = (params) => http.post('main/home/<USER>', params);
export const bannerList = (params) => req.post('api/v1/banner/list', { ...params });

/*
 * 好物推荐
 * @returns
 */
export const goodRecommend = () => http.post('main/goods/recommend', {});

/*
 * 公众号链接
 * @returns
 */
export const homeOa = () => http.post('main/home/<USER>', {});

/**
 * 精彩直达
 * @returns
 */
export const goodsTag = () => http.post('main/goods-recommend/goods-tag', {});

/**
 * 推荐商品
 * @retuens
 */
export const goodsRecommend = () => http.post('main/goods-recommend/list', {});

/**
 * 获取系统时间戳
 * @retuens
 */
export const getSystemTime = (params) => http.post('main/data/time', params);
