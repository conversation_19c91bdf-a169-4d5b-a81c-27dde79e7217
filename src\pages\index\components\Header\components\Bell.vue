<template>
    <view class="notice-area" @click="goMessages">
        <image
            style="width: 48rpx; height: 48rpx; display: block"
            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/745327-1751428225191.png"
        ></image>
        <view
            v-if="hasNewMessage"
            class='red-dot'
        />
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils';

@Component
export default class Search extends Vue {
    constructor() {
        super();
    }

    public timer: any = null;
    public hasNewMessage: Boolean = false;

    mounted() {
        const cb = (data) => {
            if (data.code === 0) {
                const info = JSON.parse(data.data || '{}');
                const { serviceMsgUnread = 0, shareUnread = 0, systemMsgUnread = 0 } = info.data || {};
                this.hasNewMessage = serviceMsgUnread + shareUnread + systemMsgUnread > 0;
            }
        }
        this.timer = setInterval(() => {
            Utils.newMessageChannel('HTTP', 'request', {
            method: 'get',
            path: `/dreame-message-push/v1/message-record/homestat`,
            body: {}
        }, cb);
        }, 10000); // 每10秒获取一次消息记录
    }

    unmounted() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    // 跳转到消息页面
    goMessages() {
        Utils.newMessageChannel('PAGE', 'push', { path: '/message_main' });
    }
}
</script>
<style lang="scss" scoped>
.notice-area {
    position: relative;
}
.red-dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background-color: #ff0000;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
}
</style>
