<template>
    <view class="activity-bar">
        <swiper
            class="activity-swiper"
            :indicator-dots="true"
            :duration="300"
            :circular="true"
            indicator-color="rgba(255, 255, 255, 0.3)"
            indicator-active-color="#ff6b35"
            @change="onSwiperChange"
            :style="{
                height: swiperHeight + 'rpx',
                transition: 'all 0.3s ease-in-out',
            }"
        >
            <swiper-item v-for="(activity, index) in activityList" :key="index" class="swiper-item">
                <view class="activity-item">
                    <view
                        v-for="(item, index) in activity"
                        :key="index"
                        class="activity-item-content"
                        @click="handleActivityClick(item)"
                    >
                        <image class="activity-icon" :src="item.icon" mode="aspectFit" />
                        <image
                            class="activity-icon-animation"
                            :src="animationIcon"
                            mode="aspectFit"
                            v-if="item.text === '现金红包'"
                        />
                        <text class="activity-text">{{ item.text }}</text>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
@Component
export default class ActivityBar extends Vue {
    constructor() {
        super();
    }

    public activeIndex: number = 0;
    public animationIcon: string =
        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/577484-1751522557065.png';

    get sessid() {
        const { sessid } = UserModule;
        return sessid;
    }

    get PageOneList() {
        return [
            {
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/692829-1751522535838.png',
                text: '现金红包',
                url: '',
                type: 'redEnvelope',
            },
            {
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/307042-1751448729486.png',
                text: '注册福利',
                url: '/pagesC/serve/serve',
            },
            // {
            //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/832411-1751445456934.png',
            //     text: '公益',
            //     url: '/pagesC/contentDetails/contentDetails?content_id=1938527391055876096',
            // },
            // {
            //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68668bc6b4dda7410010460.png',
            //     text: '酒旅',
            //     url: `https://webapp.aitrip123.com?sessid=${this.sessid}`,
            // },

            {
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/951262-1751445624277.png',
                text: '签到',
                url: '/pagesA/daliyCheckIn/daliyCheckIn',
            },

            {
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/418922-1751448807189.png',
                text: '惊喜周边',
                url: '/pagesA/point/new_point',
            },
        ];
    }

    public PageOtherList: Array<any> = [
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/720415-1751448757550.png',
        //     text: '老带新',
        //     url: '/pagesA/activity/newUserDiscount',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/418922-1751448807189.png',
        //     text: '惊喜周边',
        //     url: '/pagesA/activity/newUserDiscount',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/501568-1751448881812.png',
        //     text: '领好券',
        //     url: '/pagesA/activity/accessoryZone',
        // },
        {
            icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/028159-1751448911612.png',
            text: '会员福利',
            url: '/pagesA/memberShip/memberShip',
        },
    ];

    get activityList() {
        return [[...this.PageOneList, ...this.PageOtherList]];
    }

    get swiperHeight() {
        // 获取当前页面的活动数量
        const currentPageActivities = this.activityList[this.activeIndex] || [];
        const activityCount = currentPageActivities.length;

        // 每行显示4个活动，计算需要多少行
        const rowsNeeded = Math.ceil(activityCount / 5);
        const rowHeight = 136 + 12 + 20;
        // 容器padding：上下各20rpx
        const containerPadding = 80;
        // 有多少行就加多少行
        const rowPadding = (rowsNeeded - 1) * 20;

        return rowsNeeded * rowHeight + containerPadding + rowPadding + rowPadding;
    }

    onSwiperChange(event: any) {
        this.activeIndex = event.detail.current;
    }

    handleActivityClick(activity: any) {
        if (activity.type && activity.type === 'redEnvelope') {
            this.$emit('handleLucky');
            return;
        }
        if (activity.url) {
            Utils.navigateTo(activity.url);
        }
    }
}
</script>

<style lang="scss" scoped>
.activity-swiper {
    // 自定义指示器样式
    ::v-deep .uni-swiper-dot {
        width: 16rpx !important;
        height: 8rpx !important;
        border-radius: 4rpx !important;
        margin: 0 6rpx !important;
        background: #e2e2e2 !important;
        // 第一个指示器保持短条样式（默认）
        &:nth-child(1) {
            width: 16rpx !important;
        }

        // 第二个指示器设置为长条样式
        &:nth-child(2) {
            width: 32rpx !important;
        }
    }
    // 激活状态的指示器
    ::v-deep .uni-swiper-dot-active {
        background: #404040 !important;
    }
}

.activity-item {
    display: flex;
    align-items: center;
    justify-content: flex-start; // 改为均匀分布，更适合多个项目
    width: 100%;
    justify-items: flex-start;
    align-items: center;
    height: 180rpx;
    flex-wrap: wrap;
    gap: 16rpx;
    background: #fff;
    padding: 20rpx 0px; // 添加内边距
    &-content {
        display: flex;
        align-items: center;
        width: 136rpx;
        position: relative;
        flex-direction: column;
    }
    .activity-icon-animation {
        width: 36rpx;
        height: 36rpx;
        position: absolute;
        right: 5rpx;
        top: 5rpx;
        z-index: 100;
        transform-origin: left bottom;
        animation: rotate 1.5s linear infinite;
        animation-direction: alternate;
    }
    .activity-icon {
        width: 136rpx;
        height: 136rpx;
    }
    .activity-text {
        font-size: 24rpx;
        font-weight: 500;
        color: #121212;
        letter-spacing: 0px;
        margin-top: 12rpx;
    }
    :nth-child(n + 6) {
        margin-top: 12rpx;
    }
}
@keyframes rotate {
    0% {
        transform: rotate(-15deg);
    }
    100% {
        transform: rotate(15deg);
    }
}
</style>
