<template>
    <view class="content-goods-mask" v-if="isShowMask">
        <view class="content-goods-container" :style="{ top: goodsStyleTop }">
            <GoodsDetail ref="GoodsDetail" :linkGid="linkGid" :isFixedTop.sync="isFixedTop"/>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import GoodsDetail from '@/pagesB/goodsDetail/goodsDetail.vue';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component({
    components: {
        GoodsDetail
    }
})
export default class GoodsCard extends Vue {
    public isShowMask = false;
    public goodsStyleTop = '100vh'
    public linkGid: string
    public isFixedTop = false

    showGoodsMask(gid: string, contentId) {
        console.log(gid)

        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENT_LIST_CLICK,
            id: Number(gid),
            name: contentId,
        });

        this.linkGid = String(gid)

        uni.$on('link_card', () => {
            this.goodsStyleTop = '100vh'
            this.isShowMask = false
        })

        // 监听来自 scrollMixin 的位置更新事件
        uni.$on('update_goods_card_top', (topValue: string | number) => {
            this.goodsStyleTop = String(topValue)
        })

        this.isShowMask = true;
        // 必须使用定时，使用 nextTick 也无法触发 animate
        setTimeout(() => {
            this.goodsStyleTop = '280rpx'
        })
    }

    beforeDestroy() {
        // 清理事件监听器
        uni.$off('link_card')
        uni.$off('update_goods_card_top')
    }
}
</script>

<style lang="scss" scoped>
.content-goods-mask {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10001;

    .content-goods-container {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        transition: all .5s ease-in-out;
        border-radius: 20rpx 20rpx 0 0;
        overflow: hidden;
    }
}
</style>
