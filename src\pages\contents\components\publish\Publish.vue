<template>
    <u-popup :show="openSync" @close="close">
        <view class="publish">
            <view class="publish-item" @click="handlePublish('img')">
                <img src="https://wpm-cdn.dreame.tech/images/202412/939066-1735022159624.png" />
                <text>发动态</text>
            </view>

            <view class="publish-item vedio" @click="handlePublish('vedio')">
                <img src="https://wpm-cdn.dreame.tech/images/202412/755992-1735022241316.png" />
                <text>发视频</text>
            </view>

            <view class="publish-item cancel" @click="close">取消</view>
        </view>
    </u-popup>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { Component, PropSync, Vue } from 'vue-property-decorator';

// const SCOPE_WRITE_PHOTOS_ABLUM = 'scope.writePhotosAlbum';
// const SCOPE_CAMERA = 'scope.camera';

@Component
export default class Publish extends Vue {
    @PropSync('open', { type: Boolean }) openSync: boolean;

    close() {
        this.$emit('close');
    }

    authorizePermission(scope) {
        return new Promise((resolve, reject) => {
            uni.getSetting({
                success(res) {
                    if (!res.authSetting[scope]) {
                        uni.authorize({
                            scope: scope,
                            success(res) {
                                resolve(true);
                            },
                            fail() {
                                reject(false);
                            },
                        });
                    } else {
                        resolve(true);
                    }
                },
                fail() {
                    reject(false);
                },
            });
        });
    }

    handlePublish(type: 'img' | 'vedio') {
        if (type === 'img') {
            uni.chooseImage({
                count: 9,
                sourceType: ['album', 'camera'],
                success: (res) => {
                    this.$emit('close');
                    const tempFilePaths = res.tempFilePaths;
                    Utils.navigateTo(
                        `/pagesC/createContent/index?contentType=${type}&tempFilePaths=${JSON.stringify(
                            tempFilePaths || [],
                        )}`,
                    );
                },
                fail: (err) => {
                    if (!err.errMsg.includes('cancel')) {
                        Utils.Toast('请查看微信相册和相机权限是否打开');
                    }
                },
            });
        } else {
            uni.chooseVideo({
                sourceType: ['album', 'camera'],
                compressed: false,
                success: (res) => {
                    this.$emit('close');
                    const tempFilePath = res.tempFilePath;
                    Utils.navigateTo(`/pagesC/createContent/index?contentType=${type}&tempFilePaths=${tempFilePath}`);
                },
                fail: (err) => {
                    if (!err.errMsg.includes('cancel')) {
                        Utils.Toast('请查看微信相册和相机权限是否打开');
                    }
                },
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.publish {
    z-index: 10001;
    background: #f6f6f6;

    &-item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 750rpx;
        height: 130rpx;
        background-color: white;

        img {
            width: 46rpx;
            height: 46rpx;
        }

        text {
            margin-left: 16rpx;
            font-weight: 500;
            font-size: 36rpx;
            color: #20252b;
        }
    }

    .vedio {
        border-top: 1rpx solid #f7f8fa;
        margin-bottom: 16rpx;
    }

    .cancel {
        color: #20252b;
        font-size: 36rpx;
    }
}
</style>
