import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';

const BASE_URL = process.env.VUE_APP_BASE_URL;

// 全局请求队列和刷新状态
let isRefreshing = false;
let requestsQueue = [];

export default {
    post(url, data, custom = {}) {
        return request(url, 'POST', data, custom);
    },

    get(url, data, custom = {}) {
        return request(url, 'GET', data, custom);
    },
};

function request(url, method, data, custom = {}) {
    return new Promise((resolve, reject) => {
        const BASE_DATA = {
            user_id: UserModule.user_id || 0,
            sessid: UserModule.sessid || '',
            api: AppModule.platform == 'ios' ? 'i_1643178026' : 'a_1643178000',
            version: AppModule.version,
            union: UserModule.union || uni.getStorageSync('union') || '',
            euid: UserModule.euid || uni.getStorageSync('euid') || 0,
            is_internal_purchase: data?.is_internal_purchase || UserModule.isInternalPurchase,
            cps: UserModule.cps,
            referer: uni.getStorageSync('referer') || '',
        };
        if (url.includes('main/login/index')) {
            BASE_DATA.sessid = '';
            BASE_DATA.user_id = 0;
        }
        const security_key = AppModule.platform == 'ios' ? '*&^$%#%!#$&@' : '*&^@!!#$&#@%';
        const data2 = {
            ...data,
            ...BASE_DATA,
        };

        data2.sign = getSign(data2, security_key);
        uni.request({
            url: BASE_URL + url,
            method: method,
            data: {
                ...data2,
            },
            header: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'wx-version': 'v1.0.0',
                'Access-Token': UserModule.sessid || '',
            },
            success: (res) => {
                const response = res.data;
                if (!response.iRet) {
                    resolve(response);
                } else if (response.iRet === Constants.RET_OK) {
                    resolve(response.data);
                } else if (response.iRet === Constants.RET_CUSTOM_JSON) {
                    resolve(response);
                } else if (response.iRet === Constants.RET_ERR) {
                    if (response.sMsg && typeof response.sMsg === 'string' && !custom?.custom?.hideErrorToast) {
                        Utils.Toast(response.sMsg);
                    }
                    reject(response);
                } else if (response.iRet === Constants.RET_SESSION_EXP) {
                    // 会话过期处理
                    UserModule.setSessId('');
                    UserModule.setUserId('');
                    UserModule.setIsLogin(false);
                    // 将当前请求加入队列
                    const requestConfig = { url, method, data, custom, resolve, reject };
                    requestsQueue.push(requestConfig);

                    if (!isRefreshing) {
                        isRefreshing = true;
                        UserModule.asyncWxLogin()
                            .then(() => {
                                // 登录成功，重新发送队列中的请求
                                const queue = [...requestsQueue];
                                requestsQueue = [];
                                queue.forEach((config) => {
                                    request(config.url, config.method, config.data, config.custom)
                                        .then(config.resolve)
                                        .catch(config.reject);
                                });
                            })
                            .catch((error) => {
                                // 登录失败，拒绝所有请求
                                const queue = [...requestsQueue];
                                requestsQueue = [];
                                queue.forEach((config) => {
                                    config.reject(error);
                                });
                            })
                            .finally(() => {
                                isRefreshing = false;
                            });
                    }
                } else {
                    reject(response);
                }
            },
            fail: (err) => {
                const response = err.data;
                if (response.sMsg) {
                    Utils.Toast(response.sMsg);
                }
                reject(response);
            },
            complete: () => {
                if (custom.loading) {
                    uni.hideLoading();
                }
            },
        });

        if (custom.loading) {
            uni.showLoading({ title: custom.title || '正在加载' });
        }
    });
}

function getSign(obj, security_key) {
    obj.sign_time = parseInt(new Date().getTime() / 1000);
    const newObj = {
        ...obj,
    };
    newObj.security_key = security_key;
    let sortArr = [];
    const keysSorted = Object.keys(newObj).sort();
    for (let i = 0; i < keysSorted.length; i++) {
        sortArr.push(keysSorted[i] + '=' + newObj[keysSorted[i]]);
    }
    sortArr = sortArr.join('&');
    return md5Libs.md5(sortArr);
}
