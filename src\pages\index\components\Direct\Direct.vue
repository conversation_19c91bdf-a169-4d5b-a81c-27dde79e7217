<template>
    <view class="direct-container">
        <view class="title">精彩直达</view>
        <view class="direct-content">
            <template v-if="directList.length">
                <view class="direct-list" v-for="item in directList" :key="item.tid" @click="toShop(item.tid)">
                    <view class="img" :style="{ 'background-image': `url(${item.image})` }" @click="handleClick(item.tid)"
                        :src="item.image"></view>
                    <view class="text">{{ item.text }}</view>
                </view>
            </template>
            <template v-else>
                <u-skeleton :animate="true" :title="false" :loading="true" rows="1" :rowsHeight="[124]"
                    :rowsWidth="['100%']"></u-skeleton>
            </template>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';

@Component({
    components: {
    },
})
export default class Direct extends Vue {
    @Prop({ type: Array, default: [] })
    readonly directList;

        // 是否授权
    get sessid(): string | number {
        return UserModule.sessid;
    }

    public tid: string = '-1'

    handleClick(tid) {
        this.tid = tid
    }

    toShop(tid, type) {
        uni.setStorageSync('tid', tid);
        if (type === 'hotel') {
            Utils.navigateTo(`https://webapp.aitrip123.com?sessid=${this.sessid}`);
        } else {
            Utils.navigateTo('/pages/shop/shop?tid=' + tid);
        }
    }
}
</script>

<style lang="scss" scoped>
.direct-container {
    width: 100%;
    padding: 38rpx 30rpx;
    min-height: 400rpx;

    .title {
        font-size: 36rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: $text-color-primary;
        line-height: 48rpx;
        margin-bottom: 24rpx;
    }

    .direct-content {
        // @include flex(row, space-between, center, none);
        @include flex(row, flex-start, center, none);
        overflow-x: scroll;
        overflow-y: hidden;

        .direct-list {
            min-width: 166rpx;
            text-align: center;

            + .direct-list {
                margin-left: 24rpx;
            }

            .img {
                margin-bottom: 16rpx;
                width: 100%;
                height: 192rpx;
                background-size: 100% 100%;
                background-repeat: no-repeat;

                &:active {
                    background-color: #F4F4F4;
                }
            }

            .text {
                font-size: 24rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: $text-color-primary;
                line-height: 34rpx;
            }
        }
    }
}

.active {
    background: $fill-color-bg-gray;
}
</style>
