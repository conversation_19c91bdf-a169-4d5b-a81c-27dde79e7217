/* eslint-disable object-curly-spacing */
import http from './index';

/*
 * 预售提交订单
 * @parma {String} gcombines 购买json数据【必要】
 * @parma {String} aid 收货地址id
 * @returns
 */
export const depositBuy = (params) => http.post('main/deposit/deposit-buy', params);

/*
 * 预售订单结算页详情
 * @parma {String} gcombines 结算json数据【必要】
 * @parma {String} cart_ids 购物车id
 * @parma {String} aid 地址id
 * @returns
 */
export const preOrderBuyInfo = (params, loading: boolean = false) =>
    http.post('main/deposit/deposit-buy-info', params, { custom: { loading } });

/*
 * 支付定金
 * @parma {String} order_no 订单号【必要】
 * @returns
 */
export const depositPay = (params) => http.post('main/deposit/deposit-pay', params);

/*
 * 定金订单待支付详情
 * @returns
 */
export const preOrderInfo = (order_no: String) => http.post('main/deposit/deposit-info', { order_no });

/*
 * 取消订单
 * @parma {String} deposit_order_no 订单号【必要】
 * @parma {String} r_type 取消原因
 * @returns
 */
interface OrderCancelParams {
    deposit_order_no: string;
    r_type: string;
}
export const preOrderCancel = (params: OrderCancelParams) => http.post('main/deposit/deposit-cancel', params);

/*
 * 更新订单
 * @parma {String} deposit_order_no 订单号【必要】
 * @parma {String} r_type 取消原因
 * @returns
 */

export const updateOrderInfo = (params) => http.post('main/order/update-info', params);

/*
 * 获取尾款订单信息
 * @parma {String} order_no 定金订单号【必要】
 * @returns
 */

export const tailOrderDetail = (params) => http.post('main/deposit/tail-order-detail', params);
