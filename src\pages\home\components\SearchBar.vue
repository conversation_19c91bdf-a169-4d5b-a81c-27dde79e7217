<template>
    <div class="search-card">
        <div class="search-bar">
            <img src="https://wpm-cdn.dreame.tech/images/202306/535513-1686106493238.png" class="search-icon" alt="" />
            <input class="search-input" />
            <div class="search-btn" @click="search">搜索</div>
        </div>
        <img src="https://wpm-cdn.dreame.tech/images/202306/120216-1686121914874.png" class="shopping-car-icon" alt="" />
    </div>
</template>

<script lang="ts">
export default {
    name: 'SrarchBar',
    methods: {
        search() {}
    }
}
</script>

<style scoped>
.search-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32rpx;
    margin-top: 12rpx;
    margin-bottom: 28rpx;
}
.search-bar {
    flex: 1;
    display: flex;
    align-items: center;
    height: 80rpx;
    border-radius: 80rpx;
    padding: 12rpx 20rpx;
    background-color: #F6F6F6;
}
.search-icon {
    width: 40rpx;
    height: 40rpx;
}
.search-input {
    flex: 1
}
.search-btn {
    height: 56rpx;
    line-height: 56rpx;
    padding: 0rpx 32rpx;
    background-color: #E5DABC;
    color: #8C6533;
    font-size: 24rpx;
    font-weight: 500;
    border-radius: 56rpx;
}
.shopping-car-icon {
    width: 48rpx;
    height: 48rpx;
    margin-left: 32rpx;
}
</style>
