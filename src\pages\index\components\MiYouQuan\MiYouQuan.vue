<template>
    <view class="MiYou_container" @click="navigateToPage">
        <view class="MiYou_container_left">
            <image
                class="left_icon"
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686b7fba710734630012434.png"
            ></image>
            <view class="left_text">觅友圈</view>
        </view>
        <view class="MiYou_container_right">
            <view class="right_text">查看好友动态</view>
            <image
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6869ec84a223c6640011959.png"
                class="right_icon"
                mode="scaleToFill"
            />
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component
export default class Contact extends Vue {
    constructor() {
        super();
    }

    navigateToPage() {
        console.log('navigateToPage');
        Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' })
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_MIYOUQUAN_CLICK,
        });
    }
}
</script>
<style lang="scss" scoped>
.MiYou_container {
    display: flex;
    justify-content: space-between;
    height: 108rpx;
    background: #ffffff;
    border-top:1rpx solid #EEEEEE;
    padding:0 16rpx 0 23rpx;
    align-items: center;
    &_left {
        display: flex;
        align-items: center;
    }
    .left_icon {
        width: 96rpx;
        height: 96rpx;
    }
    .left_text {
        font-size: 31rpx;
        font-weight: 500;
        margin-left: 10rpx;
    }
    &_right {
        display: flex;
        justify-content: center;
        align-items: center;
     .right_text {
        font-size: 27rpx;
        font-weight: normal;
        color:#A6A6A6;
        }
      .right_icon{
        width: 31rpx;
        height: 31rpx;
      }
    }

}
</style>
