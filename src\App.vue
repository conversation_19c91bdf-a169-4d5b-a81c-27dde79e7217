<script lang="ts">
import Vue from 'vue';
// import VConsole from 'vconsole';
import { AppModule } from '@/store/modules/app';
import { UserModule } from '@/store/modules/user';
import { CartModule } from '@/store/modules/cart';
import Utils from '@/common/Utils';
import Constants from './common/Constants';
import version from '@/version.json';
export default Vue.extend({
    mpType: 'app',
    onLaunch(options) {
        // if (process.env.VUE_APP_SHOW_VCONSOLE === '1') {
        //     this.vConsole = new VConsole();
        // }
        // 获取扫码进来获取的门店信息（服务端接口生成太阳码）
        const scene = options.query.scene ? decodeURIComponent(options.query.scene) : '';
        if (scene) {
            const sceneObj: any = Utils.getRequestParams('?' + scene);
            if (sceneObj.shop_code) {
                AppModule.setShopCode(sceneObj.shop_code);
            }
            if (sceneObj.employee_uid) {
                AppModule.setEmployeeUid(sceneObj.employee_uid);
            }
        }

        // 兼容获取分享进来获取的门店信息（微信后台生成太阳码）
        if (options.query.shop_code) {
            AppModule.setShopCode(options.query.shop_code);
        }

        const persistentKeys = ['searchHistoryList', 'isPayBack', 'searchPointHistoryList', 'vipCenterCache', 'indexCache']
        let persistentObj = {}
        persistentKeys.forEach(key => {
            persistentObj[key] = uni.getStorageSync(key)
        })
        uni.clearStorageSync();
        persistentKeys.forEach(key => {
            persistentObj[key] && uni.setStorageSync(key, persistentObj[key])
        })
        persistentObj = null
        let referer = options.path + '?'
        Object.keys(options.query).map(k => {
            if (k) {
                referer += `${k}=${options.query[k]}&`
            }
        })
        if (referer.endsWith('?') || referer.endsWith('&')) {
            referer = referer.substring(0, referer.length - 1)
        }
        uni.setStorageSync('referer', referer);
        if (options.query.union && options.query.euid) {
            uni.setStorageSync('union', options.query.union);
            uni.setStorageSync('euid', options.query.euid);
        }
        AppModule.setVersion(process.env.VUE_APP_VERSION);
        AppModule.setVersionCode(version.versionCode);
        // #ifdef MP-WEIXIN
        const updateManager = uni.getUpdateManager();
        updateManager.onUpdateReady(function (res) {
            uni.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                showCancel: false,
                success(res) {
                    if (res.confirm) {
                        updateManager.applyUpdate();
                    }
                },
            });
        });
        const customBarHeight = uni.upx2px(Constants.CUSTOM_BAR_HEIGHT)
        const { top, height } = uni.getMenuButtonBoundingClientRect();

        const statusBarHeight = Utils.pxToRpx(top - (customBarHeight - height) / 2)
        AppModule.setSysInfo({ statusBarHeight });
        // #endif

        // #ifdef H5
        // 总是调用getArguments来设置statusBarHeight
        this.getArguments()
        if (this.postInfoAuth()) {
            // 这里可以添加其他需要权限验证的逻辑
        }
        // #endif

        // #ifdef H5
        // 监听网络变化
        uni.onNetworkStatusChange(this.onNetworkChange)

        // 首次获取网络状态
        uni.getNetworkType({
            success: function (res) {
                if (res.networkType == 'none') {
                    AppModule.setNetworkStatus(false)
                } else {
                    AppModule.setNetworkStatus(true)
                }
            }
        })
        if (this.postInfoAuth()) {
            CartModule.loadList()
        }

        // this.checkRoute()
        this.$router.afterEach((to, from) => {
            document.body.style.overflow = '';
            Utils.messageChannel('popstate', '')
        })
        // #endif
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_OPEN_APP,
        }, true);
    },
    onShow() {
        console.log('App Show');
        const href = location.href;
        console.log(href);
        const url = new URL(href);
        const sharerUid = url.searchParams.get('sharerUid');
        console.log(sharerUid, 'sharerUid');
    },
    onHide() {
        console.log('App Hide');
    },
    onError(e) {
        console.log(e);
    },

    methods: {
        checkRoute() {
            const history = this.$router.history
            const currentPath = history.current.path
            const routes = this.$router.options.routes.map(r => r.path)
            if (!routes.includes(currentPath)) {
                uni.redirectTo({ url: '/pagesA/404/404' })
            }
        },
        getArguments() {
            const statusBarHeight = 14
            let params: any = {}
            params = Utils.getRequestParams(location.search)
            params.nick = ''
            params.avatar = ''
            params.phone = ''
            UserModule.setCommit(params)
            const info = uni.getSystemInfoSync()
            info.statusBarHeight = Utils.pxToRpx(info.statusBarHeight || params.statusBarHeight || statusBarHeight)
            info.platform = params.os || '' // 获取app携带的os类型，目前只有鸿蒙会携带
            AppModule.setSysInfo(info);
        },

        onNetworkChange(res) {
            AppModule.setNetworkStatus(res.isConnected)
        },
        postInfoAuth() {
            const whiteList = ['pagesC/sixDiscount/sixDiscount', 'pagesC/sixDiscount/sixDiscountNewUser', 'oneYuanFlashSale/oneYuanFlashSale', 'pagesB/inGroupPurchase/guidPage', '/pagesA/exploreBookingMova/exploreBookingMova', 'pagesC/offPurchase/offPurchaseNewUser', 'pagesA/point/share_undertaking_page', 'pagesC/oneYuanFlashSale/newUser', 'pagesC/halfPrice/sharePage', 'pagesC/billionsOfShoppingGold/sharePage', 'pagesC/earnMoneySpend/sharePage', 'pagesC/purchase/purchase_share']
            return whiteList.some(item => {
                return this.$route.path.includes(item)
            })
        }
    },
});
</script>

<style lang="scss">
@import 'uview-ui/index.scss';

@import './assets/scss/base.scss';

/*  #ifndef  APP-PLUS-NVUE  */

view,
scroll-view {
    box-sizing: border-box;
}

image,
img {
    vertical-align: bottom;
}

page {
    font-family: 'HarmonyOS_Sans_SC';
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background: $uni-bg-color-primary;
    color: #000000;
}

.btn-hover {
    opacity: 0.8;
}

.pop-icon-close {
    position: absolute;
    top: 6rpx;
    right: 0;
    width: 50rpx;
    height: 50rpx;

    .icon {
        width: 36rpx;
        height: 36rpx;
    }
}

.contact {
    margin: 0;
    padding: 0;
    line-height: normal;
    background: #ffffff;
    border: none !important;
}

/*  #endif  */
</style>
