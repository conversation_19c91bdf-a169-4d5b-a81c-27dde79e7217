<template>
    <view class="app-share" v-if="!isDev">
        <view class="app-share-header" v-if="!user_id">
            <view class="app-share-header-left">
                <image
                    class="app-share-header-left-image"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dcc2eb952f7590012268.png"
                    alt=""
                />
                <view class="app-share-header-left-text">打开Dreame APP，有更多优惠</view>
            </view>
            <view class="app-share-header-right" @click="openApp"> 立即打开 </view>
        </view>
        <view class="app-share-bottom" v-if="!user_id">
            <image
                class="app-share-bottom-left-image"
                @click="openApp"
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dd0230dbc60560011067.png"
                alt=""
            />
        </view>
    </view>
</template>

<script lang="ts">
import { Prop, Vue, Component } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';
@Component
export default class APPSHARE extends Vue {
    @Prop({ type: String, default: '' })
    readonly link: string;

    get user_id(): any {
        let user_id: any = 0;
        // #ifdef MP-WEIXIN
        user_id = UserModule.userInfo.user_id;
        // #endif

        // #ifdef H5
        user_id = UserModule.user_id;
        // #endif
        return user_id;
    }

    get isDev() {
        return process.env.NODE_ENV === 'development';
    }

    openApp() {
        window.location.href = this.link;
    }
}
</script>

<style lang="scss" scoped>
.app-share {
    .app-share-header {
        position: fixed;
        top: 0;
        left: 0;
        height: 96rpx;
        width: 100%;
        z-index: 9099999;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .app-share-header-left {
            display: flex;
            align-items: center;

            .app-share-header-left-image {
                width: 64rpx;
                height: 64rpx;
                margin-left: 32rpx;
            }

            .app-share-header-left-text {
                font-size: 28rpx;
                color: #121212;
                margin-left: 20rpx;
                font-family: MiSans;
            }
        }

        .app-share-header-right {
            width: 156rpx;
            height: 64rpx;
            margin-right: 28rpx;
            background: #e8dec1;
            border-radius: 32rpx;
            font-size: 28rpx;
            color: #8c6533;
            font-family: MiSans;
            font-weight: 600;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    // 样式将在这里添加
    .app-share-bottom {
        position: fixed;
        z-index: 9099999;
        background: #000000;
        box-shadow: 0px 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
        border-radius: 100rpx;
        bottom: 220rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 442rpx;
        height: 96rpx;

        .app-share-bottom-left-image {
            width: 442rpx;
            height: 96rpx;
            position: absolute;
            left: 0;
            top: 0;
        }
    }
}
</style>
