<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="u-flex u-row-between" style="width: 100%; padding-right: 30rpx">
                    <view style="width: 128rpx; height: 100%"></view>
                    <view class="u-flex u-row-center u-title"> 分类 </view>
                    <view class="community-search" @click="goSearch">
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a1623a0de76590410838.png"
                            alt=""
                        />
                    </view>
                </view>

                <view class="content-view-container">
                    <view class="content-left">
                        <!-- 侧边栏 -->
                    </view>
                    <view class="content-right">
                        <!-- 内容区 -->
                        <FeaturedCategory />
                    </view>
                </view>
            </view>
        </view>
        <NoNetwork v-else :isNetworkConnect="isNetworkConnect" @onRefresh="onRefresh" />
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <WxLogin></WxLogin>
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { AppModule } from '@/store/modules/app';
import { Vue, Component } from 'vue-property-decorator';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import Utils from '@/common/Utils';
import FeaturedCategory from './components/FeaturedCategory/FeaturedCategory.vue';

@Component({
    components: {
        NoNetwork,
        FeaturedCategory,
    },
})
export default class Category extends Vue {
    // 网络状态
    get isNetworkConnect(): Boolean {
        return AppModule.isNetworkConnect;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    async onRefresh() {
        await this.init();
    }

    async init() {}

    goSearch() {
        Utils.navigateTo(`/pagesC/search/search`);
    }
}
</script>

<style lang="scss" scoped>
@import './category.scss';
</style>
