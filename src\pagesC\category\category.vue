<template>
    <FeaturedCategory :categoryList="categories" @category-click="handleCategoryClick" />
    <!-- <GoodsItem
        :productInfo="{
            id: 1,
            title: '系列玩偶kawaii',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            specs: '尺寸：XS、S、M',
            currentPrice: 299,
            originalPrice: 499,
            couponText: '起券后价',
        }"
        @card-click="handleProductClick"
    /> -->
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import FeaturedCategory from '@/pagesC/category/components/FeaturedCategory/FeaturedCategory.vue';
import { CategoryClickEvent, DEFAULT_CATEGORIES } from '@/pagesC/category/components/FeaturedCategory';
import GoodsItem from '@/pagesC/category/components/GoodsItem/GoodsItem.vue';
import { ProductCardClickEvent } from '@/pagesC/category/components/GoodsItem';

@Component({
    components: { FeaturedCategory, GoodsItem },
})
export default class YourPage extends Vue {
    public categories = DEFAULT_CATEGORIES;

    handleCategoryClick(event: CategoryClickEvent) {
        console.log('点击了分类:', event.item.name);
    }

    handleProductClick(event: ProductCardClickEvent) {
        console.log('点击了商品:', event.product.title);
    }
}
</script>
