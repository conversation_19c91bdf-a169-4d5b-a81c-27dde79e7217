<template>
    <!-- <FeaturedCategory :categoryList="categories" @category-click="handleCategoryClick" /> -->
    <GoodsItem 
    :productInfo="{
        id: 1,
        title: '系列玩偶kawaii',
        image: 'https://example.com/product.jpg',
        specs: '尺寸：XS、S、M',
        currentPrice: 299,
        originalPrice: 499,
        couponText: '起券后价'
    }"
    @card-click="handleProductClick"
/>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import FeaturedCategory from '@/pagesC/category/components/FeaturedCategory/FeaturedCategory.vue';
import { CategoryClickEvent, DEFAULT_CATEGORIES } from '@/pagesC/category/components/FeaturedCategory';
import GoodsItems from '@/pagesC/category/components/GoodsItem/GoodsItem.vue';

@Component({
    components: { FeaturedCategory,GoodsItems },
})
export default class YourPage extends Vue {
    public categories = DEFAULT_CATEGORIES;

    handleCategoryClick(event: CategoryClickEvent) {
        console.log('点击了分类:', event.item.name);
    }
}
</script>
