<template>
    <view
        class="u-toast"
        :style="{
            bottom: bottom,
            opacity: opacity,
            backgroundColor: backgroundColor,
        }"
        v-if="isShow"
    >
        <image
            style="width: 36rpx; height: 36rpx; margin-right: 16rpx"
            v-if="tmpConfig.icon"
            :src="tmpConfig.icon"
            :customStyle="iconStyle"
        >
        </image>
        <text class="content-medium" :style="tmpConfig.style">{{ tmpConfig.message }}</text>
    </view>
</template>

<script>
import { AppModule } from '@/store/modules/app';

/**
 * toast 消息提示
 * @description 此组件表现形式类似uni的uni.showToastAPI，但也有不同的地方。
 * @tutorial https://www.uviewui.com/components/toast.html
 * @property {String | Number}	zIndex		toast展示时的zIndex值 (默认 10090 )
 * @property {String | Number}	message		显示的文字内容
 * @property {String}			icon		图标，或者绝对路径的图片
 * @property {String}			type		主题类型 （默认 default）
 * @property {Boolean}			show		是否显示该组件 （默认 false）
 * @property {Boolean}			overlay		是否显示透明遮罩，防止点击穿透 （默认 false ）
 * @property {String}			position	位置 （默认 'center' ）
 * @property {Object}			params		跳转的参数
 * @property {String | Number}  duration	展示时间，单位ms （默认 2000 ）
 * @property {Boolean}			isTab		是否返回的为tab页面 （默认 false ）
 * @property {String}			url			toast消失后是否跳转页面，有则跳转，优先级高于back参数
 * @property {Function}			complete	执行完后的回调函数
 * @property {Boolean}			back		结束toast是否自动返回上一页 （默认 false ）
 * @property {Object}			customStyle	组件的样式，对象形式
 * @event {Function} show 显示toast，如需一进入页面就显示toast，请在onReady生命周期调用
 * @example <u-toast ref="uToast" />
 */
export default {
    name: 'UToast',
    mixins: [uni.$u.mpMixin, uni.$u.mixin],
    data() {
        return {
            isShow: false,
            timer: null, // 定时器
            config: {
                message: '', // 显示文本
                type: '', // 主题类型，primary，success，error，warning，black
                duration: 2000, // 显示的时间，毫秒
                icon: true, // 显示的图标
                position: 'bottom', // toast出现的位置
                complete: null, // 执行完后的回调函数
                overlay: false, // 是否防止触摸穿透
            },
            opacity: 0,
            bottom: 0,
            timerToastShow: null, // 定时器只出现一次弹框
            tmpConfig: {}, // 将用户配置和内置配置合并后的临时配置变量
            contentStyle: {},
            currentPageRoute: '',
            backgroundColor: '#ffffff',
        };
    },
    computed: {
        iconStyle() {
            const style = {};
            // 图标需要一个右边距，以跟右边的文字有隔开的距离
            style.marginRight = '8px';
            style.fontWeight = 500;
            style.color = '#121212';
            return style;
        },
        toastShow() {
            return AppModule.toastShow;
        },
        toastMessage() {
            return AppModule.toastMessage;
        },
    },
    watch: {
        isShow(val) {
            if (val) {
                setTimeout(() => {
                    this.$nextTick(() => {
                        this.opacity = 1;
                        this.bottom = '230rpx';
                    });
                }, 10);
            } else {
                this.opacity = 0;
                this.bottom = 0;
                AppModule.setToast({ isShow: false, message: '' });
            }
        },
        toastShow(val) {
            if (val) {
                const { message, duration, icon, style } = this.toastMessage;
                this.show(message, duration, icon, style);
            }
        },
    },
    created() {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        this.currentPageRoute = route;
    },
    beforeDestroy() {
        // #ifdef H5
        const el = this.$el;
        el.parentNode && el.parentNode.removeChild(el);
        // #endif

        // #ifdef MP-WEIXIN
        AppModule.setToast({ isShow: false });
        // #endif
    },
    methods: {
        // 显示toast组件，由父组件通过this.$refs.xxx.show(options)形式调用
        show(message, duration, icon, style) {
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const route = currentPage.route;
            if (route !== this.currentPageRoute) {
                return;
            }

            // 不将结果合并到this.config变量，避免多次调用u-toast，前后的配置造成混乱
            duration = duration || 2000;
            icon = icon || false;
            message = typeof message == 'object' ? message.message : message;
            this.tmpConfig = uni.$u.deepMerge(this.config, { message, duration, icon, style });

            // 获取background值，支持对象和字符串两种格式
            let backgroundValue = '#ffffff'; // 默认值
            if (this.tmpConfig.style && typeof this.tmpConfig.style === 'object') {
                // 如果是对象，直接获取background字段
                backgroundValue = this.tmpConfig.style.background || this.tmpConfig.style.backgroundColor || '#ffffff';
            } else if (typeof this.tmpConfig.style === 'string') {
                // 如果是字符串，截取background字段
                backgroundValue = this.extractBackgroundFromString(this.tmpConfig.style) || '#ffffff';
            }

            this.backgroundColor = backgroundValue;
            // 清除定时器
            this.clearTimer();
            this.isShow = true;
            this.timer = setTimeout(() => {
                // 倒计时结束，清除定时器，隐藏toast组件
                this.clearTimer();
                // 判断是否存在callback方法，如果存在就执行
                typeof this.tmpConfig.complete === 'function' && this.tmpConfig.complete();
            }, this.tmpConfig.duration);
        },

        // 从CSS样式字符串中截取background字段
        extractBackgroundFromString(styleString) {
            if (!styleString || typeof styleString !== 'string') {
                return null;
            }

            // 匹配background或background-color属性
            const backgroundRegex = /background(?:-color)?\s*:\s*([^;]+)/i;
            const match = styleString.match(backgroundRegex);
            if (match && match[1]) {
                return match[1].trim();
            }

            return null;
        },
        // 隐藏toast组件，由父组件通过this.$refs.xxx.hide()形式调用
        hide() {
            this.clearTimer();
        },
        clearTimer() {
            this.isShow = false;
            // 清除定时器
            clearTimeout(this.timer);
            // this.timer = null
            this.$emit('hide');
        },
    },
};
</script>

<style lang="scss" scoped>
.u-toast {
    @include flex;
    background-color: #ffffff;
    max-width: 576rpx;
    color: $text-color-primary;
    box-shadow: 0px 12px 31px -4px rgba(0, 0, 0, 0.1);
    border-radius: 192px 192px 192px 192px;
    padding: 32rpx 48rpx;
    line-height: 38rpx;
    z-index: 999999999999;
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    box-sizing: content-box;
    margin: auto;
    opacity: 0;
    transition: all 0.1s ease-out;
}

.content-medium {
    margin-left: 16rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 500;
    color: #121212;
    background-color: transparent !important;
}
</style>
