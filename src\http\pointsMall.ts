import http from './index';

// 获取积分商品枚举内容
export const getWaresPointsEnum = (params) => http.post('main/wares/points-enum', params);

// 商品列表
export const getWaresList = (params) => http.post('main/wares/list', params);

/*
 * 积分商城 商品详情
 * @parma {String} gid 商品ID
 * @returns
 */
export const waresInfo = (params) => http.post('main/wares/info', params);

/*
 * 积分商城 商品属性是否有库存
 * @parma {String} av_ids 所选属性值id
 * @returns
 */
export const waresAttrStock = (params) => http.post('main/wares/attr-stock', params);

/*
 * 积分商城 商品规格查询
 * @parma {String} av_ids 所选属性值id
 * @returns
 */
export const waresSpecsInfo = (params) => http.post('main/wares/specs-info', params);

interface OrderBuyInfoParams {
    gcombines?: string;
    cart_ids?: string;
    aid: string;
    coupon_id?: string | number;
    coin?: string | number;
    coin_type?: string | number;
}

export const waresOrderBuyInfo = (params: OrderBuyInfoParams, loading: boolean = false) =>
    http.post('main/wares-order/buy-info', params, {
        custom: { loading },
    });

// 下单
export const waresOrderBuy = (params) => http.post('main/wares-order/buy', params);

// 拉起/再次拉起支付
export const waresOrderPay = (params) => http.post('main/wares-order/pay', params);

// 获取积分弹窗
export const getInvitePoint = (params) => http.post('main/user-pop/get-unshown', params);

// 获取积分商城活动规则
export const getPointMallRules = () => http.post('main/data/member-mall-rule');

// 进入积分商城，给积分
export const enterPointMall = () => http.post('main/my/browse-add-score');

