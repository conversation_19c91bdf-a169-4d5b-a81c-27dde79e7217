<template>
    <view class="outScrollView">
        <Header :backgroundColor="'#F3EBDD'" :isMemberLevel="true"  :isVisible="isHeaderVisible" :coin="coin" :points="points"></Header>
        <Content
            @viewOnreachBottom="viewOnreachBottom"
            @viewRefresherrefresh="viewRefresherrefresh"
            @viewScroll="onViewScroll"
            @refresh="refreshList"
            @switch="handleSwitch"
            :contentTop="contentTop"
            :isShowTab="isProductVisible"
            :isListProduct="isListProduct"
            :isVisible="isHeaderVisible"
        >
            <template #device>
                <MemberServiceBar>
                    <template>
                        <MyDevice :deviceList="deviceList" @refresh-list="refreshList" v-if="deviceList.length > 0"/>
                    </template>
                </MemberServiceBar>
            </template>
            <template #recommend>
                <view style="background: #f5f5f5;">
                    <ActivityBanner :type="10"></ActivityBanner>
                    <view class="lucky-item" v-if="isPrizeShow">
                        <image
                            class="lucky-item-btn-img"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6869ecf0757634810019070.png"
                        >
                        </image>
                        <view class="lucky-item-btn" @click="handleLucky"> </view>
                    </view>
                    <SwtichBtn :choosed="isListProduct" @update:choosed="handleListsUi"></SwtichBtn>
                    <block v-if="isListProduct">
                         <ProductList ref="productList" />
                    </block>
                    <block v-else>
                         <ProductListOne ref="productList"></ProductListOne>
                    </block>
                    <RulesNote
                        :showCommodityModal="showCommodityModal"
                        :content="content"
                        :isHtml="true"
                        :text="activityName"
                        @close="closeRule"
                        @getRule="getRule"
                        :showIcon="false"
                        closeIcon="https://wpm-cdn.dreame.tech/images/202310/6527ba9e0dcec0562958350.png"
                    />

                    <!-- <view class="version">{{ version }}</view> -->
                    <NoNetwork :isNetworkConnect="isNetworkConnect" @onRefresh="onShowExecute" />
                    <custom-toast ref="customToast" />
                    <!-- #ifdef MP-WEIXIN -->
                    <privacy />
                    <!-- #endif -->
                    <customer-service-picker />
                </view>
            </template>
        </Content>
        <!-- #ifdef H5 -->
        <WxLogin v-if="isLoad" ref="wxLogin" @success="init"></WxLogin>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin ref="wxLogin" @success="init"></WxLogin>
        <!-- #endif -->
        <u-popup
            :show="showExplanation"
            mode="center"
            @close="showExplanation = false"
            :closeOnClickOverlay="true"
            customStyle="background: transparent; box-shadow: none;"
            teleport="body"
        >
            <view class="custom-popup-scroll">
                <view class="popup-title">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874cb5b443242790019251.png"
                        class="popup-title-img"
                    />
                </view>
                <view class="close-btn" @click="showExplanation = false">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874cb4361c714000018261.png"
                        style="width: 46rpx; height: 46rpx"
                    />
                </view>
                <scroll-view scroll-y class="popup-scroll-content">
                    <view class="popup-content">
                        <view class="popup-content-item">
                            <view class="popup-content-item-img">
                                <image
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874c673000750000023322.png"
                                    style="width: 100%; height: 100%; border-radius: 10rpx"
                                />
                            </view>
                            <view class="popup-content-item-btn" @click="signUpClick(25072001)">立即报名</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import { bannerList, goodsTag, goodsRecommend, getSystemTime } from '@/http/home';
import Banner from './components/Banner/Banner.vue';
import Content from './components/Content/Content.vue';
import ActivityBar from './components/ActivityBar/ActivityBar.vue';
import MiYouQuan from './components/MiYouQuan/MiYouQuan.vue';
import MemberServiceBar from './components/MemberServicesBar/MemberServicesBar.vue';
import CeoAction from './components/CeoAction/CeoAction.vue';
// #ifdef H5
import Direct from './components/Direct/Direct.vue';
// #endif
import Header from './components/Header/Header.vue';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';
// import Live from '@/components/Live/Live.vue';
import Constants from '@/common/Constants';
import version_mp from '@/version_mp.json';
import version_h5 from '@/version_h5.json';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import RulesNote from '@/components/RulesNote/RulesNote.vue';
import { checkNewUser, getThemeActivity } from '@/http/doubleEleven';
// import SearchBar from './components/SearchBar/index.vue';
import MyDevice from './components/MyDevice/MyDevice.vue';
// import Purchase from './components/Purchase/Purchase.vue';
import ActivitySwiper from './components/ActivitySwiper/ActivitySwiper.vue';
import { deviceInfo } from '../home/<USER>';
import PointCity from './components/PointCity/PointCity.vue';
import { CartModule } from '@/store/modules/cart';
import { VIPModule } from '@/store/modules/vip';
import ProductList from './components/ProductList/ProductList.vue';
import ActivityBanner from './components/ActivityBanner/ActivityBanner.vue';
import ProductListOne from './components/ProductListOne/ProductList.vue';
import { basicInfo } from '@/http/mine';
// import { goodsList } from '@/http/goods';
import SwtichBtn from './components/Content/components/SwtichBtn.vue';
import ListHorizontal from './components/Content/ListHorizontal/ListHorizontal.vue';
import PurchaseOne from './components/Purchase/puschaseOne.vue';
const DataConst = require('./components/Content/DataConst') as any;
const { productLists } = DataConst;

const IndexConst = require('./Const/const') as any;
const { whiteListUserStr, bossListUserStr } = IndexConst;
// import { getWaresList } from '@/http/pointsMall';

interface IBanner {
    id: string;
    title: string;
    image: string;
    jump_type: string; // 跳转类型
    jump_url: string; // 跳转url
}

@Component({
    components: {
        Header,
        WxLogin,
        Banner,
        ActivityBar,
        NoNetwork,
        Content,
        // #ifdef H5
        Direct,
        RulesNote,
        // #endif
        // Live
        // SearchBar,
        MyDevice,
        MiYouQuan,
        ActivityBanner,
        MemberServiceBar,
        CeoAction,
        ProductList,
        ProductListOne,
        PointCity,
        // Purchase,
        PurchaseOne,
        SwtichBtn,
        ListHorizontal,
        ActivitySwiper,
    },
})
export default class Idnex extends Vue {
    $refs: {
        wxLogin: WxLogin;
        tabs: any;
        // live: Live;
        banner: Banner;
        memberServiceBar: any;
        productList: any;
    };

    // private isJumpLive: Boolean = false; // 是否直接跳转进直播间
    public productListMock: any[] = [...productLists];
    public isLoad: Boolean = true;
    public showCommodityModal: Boolean = false;
    public activityName: any = '活动规则';
    public content: string = '';
    public deviceList: any[] = [];
    public enableRefresher: Boolean = true;

    public bannerLoading: boolean = false;

    public showExplanation: boolean = false;

    public menuList: Array<any> = [
        {
            icon: 'https://wpm-cdn.dreame.tech/images/202306/235895-1686121971893.png',
            text: '我的订单',
            type: 2,
            url: '/pagesA/order/order',
        },
        {
            icon: 'https://wpm-cdn.dreame.tech/images/202306/591132-1686122052506.png',
            text: '退换售后',
            type: 3,
            url: '/pagesA/refund/refund',
        },
        {
            icon: 'https://wpm-cdn.dreame.tech/images/202306/588326-1686122119435.png',
            text: '收货地址',
            type: 4,
            url: '/pagesA/address/address-list',
        },
    ];

    public goodsList: Array<any> = [
        {
            price: '34',
            icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cf07030a5f1990011358.png',
            type: 1,
            url: '/pagesA/memberShip/memberShip?typeCode=new_user_gift',
        },
        {
            price: '56',
            icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cf03e7738f4880010745.png',
            type: 1,
            url: '/pagesA/memberShip/memberShip?typeCode=new_user_gift',
        },
    ];

    public waresList: Array<any> = [];

    public eightDisGoodsList: Array<any> = [];

    public sixHalfGoodsList: Array<any> = [];

    public vipList: Array<any> = [
        {
            text: '新人礼赠',
            icon: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
            type: 1,
            url: '/pagesA/memberShip/memberShip?typeCode=new_user_gift',
        },
        {
            text: '邀请有礼',
            icon: 'https://wpm-cdn.dreame.tech/images/202309/64f17f14e4b439361374396.png',
            type: 2,
            url: '/pagesA/recommend/recommend',
        },
        {
            text: '头号玩家',
            icon: 'https://wpm-cdn.dreame.tech/images/202309/64f17f41183f30991374359.png',
            type: 3,
            url: `/pages/webView/webView?title=头号玩家&web_url=${encodeURIComponent(
                process.env.VUE_APP_BASE_URL + 'front/mp-h5/#/recruit',
            )}`,
        },
        {
            text: '专属管家',
            icon: 'https://wpm-cdn.dreame.tech/images/202309/64f17f5a77bdf4901374377.png',
            type: 4,
            url: '/pagesA/memberShip/memberShip?typeCode=exclusive',
        },
    ];

    public timerHongbao: any = 0;
    public clearTimer: any = null;

    public banners: Array<IBanner> = [];
    public recommendList: Array<any> = [];
    public goodsTagList: Array<any> = [];
    public indexCache: any = {};

    private initDataKey: Boolean = true;
    public bannerAutoplay: boolean = true; // banner 轮播
    public show: Boolean = false; // 是否展示
    public presaleInfo: { order_no: string; user_order_type: string } = { order_no: '', user_order_type: '' };
    public opacity: any = 1; // 毛玻璃效果透明度
    public headerBannerOpacity: number = 1; // 毛玻璃效果透明度
    public animationFrameId: any = null;
    public isExpanded: Boolean = false;
    public height: string = '';
    public windowHeight: number = 0;
    public width: string = '';
    public showTimer: any = null; // 用于商品分类的接口防抖
    public showService: boolean = true; // 控制客服和直播的显示/隐藏状态
    public timer: any = null; // 用于延迟显示客服和直播的定时器(防抖)
    public isLoadCategoryData: Boolean = true;
    public version: string = '';
    // public isPrizeShow: boolean = false;
    public time: any = 0;
    public activity_start_time: any = 0;
    public activity_end_time: any = 0;
    public collect_start_time: any = 0;
    public redBagTimer: any = null;
    // public count_down_timestamp: any = '2025-06-26 23:59:59';
    // public record_show_time: any = '2025-06-20 23:59:59';
    public isShowRecordImage: Boolean = false;
    public redEnvelopeAcId: string = '';
    public isNewUser: Boolean = false;
    /** 用户类型 1--全部用户 2--新用户 */
    public userType: number = 1;
    public currentTab: string = 'tab-0';
    public tid: number = 0;
    public categoryTabOption: any = [];
    public isHeaderVisible: boolean = true;
    public isProductVisible: boolean = true;
    public contentTop: number = 0; // 新增：Content组件的顶部定位
    public coin: number = 0;
    public points: number = 0;
    public showProductUI = true;
    public isListProduct: boolean = false; // 是否列表产品
    public deduction_rate: number = 0;
    public oneProductList: Array<any> = [];

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    // 网络状态
    get isNetworkConnect(): Boolean {
        return AppModule.isNetworkConnect;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    @Watch('isNetworkConnect')
    isNetworkConnectChange(newVal: boolean, oldVal: boolean) {
        if (newVal) {
            this.onShowExecute();
        }
    }

    handleHalfPrice() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_HALF_PRICE_PIRCHASE,
            },
            true,
        );
        Utils.navigateTo('/pagesC/billionsOfShoppingGold/billionsOfShoppingGold');
    }

    handleListsUi() {
        this.isListProduct = !this.isListProduct;
    }

    jumpToYiyuanFlashSale() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_INDEX_BANNER_CLICK,
            },
            true,
        );
        Utils.navigateTo(`/pagesC/oneYuanFlashSale/oneYuanFlashSale`);
    }

    handleLucky() {
        // if ((this.isNewUser !== undefined && !this.isNewUser) || this.userType === 1) return Utils.Toast('本轮红包仅限新用户参与', 1000, 'none', 'white-space: nowrap;');
        if (this.collect_start_time > new Date().getTime()) {
            return Utils.Toast('活动未开始，敬请期待', 1000, 'none', 'white-space: nowrap;');
        }

        // Utils.navigateTo(`${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/lucky/lucky`);
        Utils.navigateTo(
            `${process.env.VUE_APP_BASE_URL}front/AppMall/#pagesC/lucky/lucky?redEnvelopeAcId=${this.redEnvelopeAcId}`,
        );
    }

    closeRule() {
        this.showCommodityModal = false;
    }

    swiperFinish(e) {
        this.enableRefresher = true;
    }

    async getRule() {
        this.activityName = '活动规则';
        this.showCommodityModal = true;
    }

    onShareAppMessage(res) {
        return {
            imageUrl: 'https://wpm-cdn.dreame.tech/images/202309/096352-1695714624837.png',
        };
    }

    get isPrizeShow() {
        return this.activity_start_time < new Date().getTime() && this.activity_end_time > new Date().getTime();
    }

    onShareTimeline() {}

    onFinish() {
        this.timerHongbao = 0;
    }

    pageRedict() {
      // console.log('pageRedict');
        const userId = UserModule.user_id;
        const pagesPath = [
            '/pages/index/index',
            '/pages/index/indexMova',
            '/pages/index/indexBrand',
            '/pages/index/indexBrandB',
            // '/pages/index/indexBrandC',
            // '/pages/index/indexBlack',
        ];
        let targetUrl;
        let pageIndex = 0;
        try {
            // 检查userId是否存在且有效
            if (userId) {
                // 统一转换为字符串处理，兼容数字和字符串类型的userId
                const userIdStr = String(userId).trim();
                // 检查处理后的userId是否为空
                if (userIdStr) {
                    // 提取最后一位非数字字符前的数字部分
                    // 从末尾向前查找第一个数字字符
                    let lastDigit = 0;
                    for (let i = userIdStr.length - 1; i >= 0; i--) {
                        const char = userIdStr[i];
                        if (!isNaN(parseInt(char, 10))) {
                            lastDigit = parseInt(char, 10);
                            break;
                        }
                    }
                    // 计算页面索引，确保在有效范围内
                    pageIndex = lastDigit % pagesPath.length;
                    targetUrl = pagesPath[pageIndex];
                } else {
                    // userId为空字符串的情况，分配第一个页面
                    pageIndex = 0;
                    targetUrl = pagesPath[0];
                }
            } else {
                // userId不存在时，使用默认页面（这里选择第一个页面）
                pageIndex = 0;
                targetUrl = pagesPath[0];
            }
        } catch (error) {
            // 任何异常发生时，使用默认页面
            console.error('页面分配出错:', error);
            pageIndex = 0;
            targetUrl = pagesPath[0];
        }
        // 当匹配索引为0时，不进行跳转，保持当前页面
        if (pageIndex === 0) {
            console.log('匹配索引为0，不进行页面跳转，保持当前页面');
            return;
        }
        // 确保最终一定会跳转到一个有效页面
        // console.log('targetUrl', targetUrl);
        if (targetUrl && pagesPath.includes(targetUrl)) {
            console.log(`跳转到页面: ${targetUrl}, 索引: ${pageIndex}`);
            uni.redirectTo({ url: targetUrl });
        } else {
            // 极端情况下，不进行跳转, 默认老版
            console.log('目标页面无效，不进行跳转，保持当前页面');
            // uni.redirectTo({ url: pagesPath[0] });
        }
    }

    allpageRedict() {
       const userId = UserModule.user_id;
    // 目标页面路径
    const targetPages = {
        default: '/pages/index/index',
        mova: '/pages/index/indexMova',
        brandA: '/pages/index/indexBrand',
    };
    try {
        // 检查userId是否存在且有效
        if (userId) {
            // 统一转换为字符串处理，兼容数字和字符串类型的userId
            const userIdStr = String(userId).trim();
            // 检查处理后的userId是否为空
            if (userIdStr && whiteListUserStr.includes(userIdStr)) {
                // 用户在白名单中，跳转到mova页面
                uni.redirectTo({ url: targetPages.brandA });
                return; // 执行跳转后退出函数
            } else if (userIdStr && bossListUserStr.includes(userIdStr)) {
                uni.redirectTo({ url: targetPages.mova });
                return; // 老板白名单访问mova版本首页
            }
        }
        // 以下情况不跳转：默认老版本
        // 1. userId不存在或无效
        // 2. 用户不在白名单中
        console.log('用户不满足跳转条件，保持当前页面');
    } catch (error) {
        // 异常情况下也不跳转，保持当前页面
        console.error('页面跳转处理出错:', error);
    }
    }

    onLoad(options) {
      // console.log('onLoad', options);
        // 页面重定向
       this.pageRedict()
        // 全量
      //  this.allpageRedict()
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_SHOP_CLICK,
        });
        let version: any = {};
        // #ifdef MP-WEIXIN
        version = version_mp;
        // #endif
        // #ifdef H5
        version = version_h5;
        // #endif
        this.version = `${version.firstVer}.${version.secondVer}.${version.thirdVer}.${version.date}.${version.buildCount}`;

        // if (options.zhibo === '1') {
        //   this.isJumpLive = true
        // }
        if (options.rcode) {
            AppModule.setRecEncrypt(decodeURIComponent(options.rcode) || ''); // 通过推荐有礼页面进入携带的分享加密串参数
            console.log('==========> 通过推荐有礼页面进入携带的分享加密串参数');
        }

        if (options.employee_uid) {
            AppModule.setEmployeeUid(options.employee_uid);
        }

        if (options.q) {
            // 通过小程序进入携带的参数
            const _scene: any = decodeURIComponent(options.q).split('?')[1].split('&');
            console.log(_scene);
            const _o = _scene[0].substring(2);
            AppModule.setRecEncrypt(decodeURIComponent(_o) || '');
            console.log('==========> 通过小程序进入携带的参数');
        }
        if (this.wxAuth && this.isPhone && (options.rcode || options.q)) {
            console.log('------------onLoad----------', this.wxAuth, this.isPhone);

            if (options.rcode) {
                console.log('==========> 已登录，通过推荐有礼页面进入携带的分享加密串参数，走loginAuto');
            } else {
                console.log('==========> 已登录，通过小程序进入携带的参数，走loginAuto');
            }
            UserModule.asyncReLogin({ r_code: AppModule.recEncrypt }).then(() => {
                // 有分享加密串的情况下走完自动登录后重新走弹框逻辑
                this.$refs.wxLogin.showPopRegistered = true;
                this.$refs.wxLogin.showInBindPop();
            });
        }
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BANNER_EXPOSURE,
        });

        // #ifdef H5
        if (AppModule.platform === 'android') {
            const win: any = window;
            win.onShow = () => {
                CartModule.loadList();
                VIPModule.getBasicInfo();
            };
        }
        // #endif
        const win: any = window;
        win.onShow = () => {
            this.fetchCoinPoints();
            // this.getWaresList();
            // this.getSixHalfGoodsList();
            // this.getEightDisGoodsList();
            // this.oneMoneyBuy();
        };
        this.fetchCoinPoints();
    }

    created() {
        // this.getWaresList();
        // this.getSixHalfGoodsList();
        // this.oneMoneyBuy();
        // this.getEightDisGoodsList();
        // #ifdef MP-WEIXIN
        this.opacity = 0;
        // #endif
    }

    // tabbar 切换
    onTabItemTap(item) {
        uni.$emit(Constants.TABBAR_CHANGE, item);
    }

    onShow() {
        // this.getIsStart();
        this.isLoad = true;
        this.bannerAutoplay = true;
        Utils.reportEvent('home_page', {});
        const _this = this;
        uni.getSystemInfo({
            success: function (res) {
                const windowHeight = res.windowHeight; // 容器高度
                _this.windowHeight = Utils.pxToRpx(windowHeight);
            },
        });
        if (this.initDataKey) return;
        this.onShowExecute();
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_BUTTON,
        });
        if (AppModule.platform === 'ios') {
            CartModule.loadList();
        }
        this.fetchCoinPoints();
        // this.getWaresList();
        // this.getSixHalfGoodsList();
        // this.getEightDisGoodsList();
        // this.oneMoneyBuy();
    }

    onUnload() {
        console.log('index page onUnload');
        AppModule.setRecEncrypt('');

        if (this.redBagTimer) {
            clearInterval(this.redBagTimer);
            this.redBagTimer = null;
        }
    }

    onHide() {
        this.bannerAutoplay = false;
        this.isLoad = false; // 页面卸载时清除登录弹框
    }

    async getWaresList() {
        const { modules } = await getThemeActivity({
            id: Number(process.env.VUE_APP_OFF_HALF_PRICE_ACTIVITY_ID),
        });
        if (modules.length > 0) {
            modules.forEach((item) => {
                if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                    this.waresList = item.extra.slice(0, 3);
                }
            });
        }
    }

    async getSixHalfGoodsList() {
        const { modules } = await getThemeActivity({
            id: Number(process.env.VUE_APP_SIX_DISCOUNT_ACTIVITY_ID),
        });
        if (modules.length > 0) {
            modules.forEach((item) => {
                if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                    this.sixHalfGoodsList = item.extra.slice(0, 3);
                }
            });
        }
    }

    async getEightDisGoodsList() {
        const { modules } = await getThemeActivity({
            id: Number(process.env.VUE_APP_EIGHT_DISCOUNT_ACTIVITY_ID),
        });
        if (modules.length > 0) {
            modules.forEach((item) => {
                if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                    this.eightDisGoodsList = item.extra.slice(0, 3);
                }
            });
        }
    }

    async oneMoneyBuy() {
         try {
            // 获取主题活动数据
            const { modules } = await getThemeActivity({ id: Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID) });

            if (Array.isArray(modules) && modules.length > 0) {
                // 查找模块代码为 'SNAP_UP' 的模块
                const snapUpModule = modules.find(
                    item => item.extra && Array.isArray(item.extra) && item.module_code === 'SNAP_UP'
                );
                if (snapUpModule) {
                    // 获取该模块的商品列表并提取前三个
                    this.oneProductList = [snapUpModule.extra[2], snapUpModule.extra[3], snapUpModule.extra[1]]
                    console.log('成功获取秒杀商品列表:', this.oneProductList);
                } else {
                    console.log('未找到 SNAP_UP 模块');
                    this.oneProductList = [];
                }
            } else {
                console.log('活动模块列表为空');
                this.oneProductList = [];
            }
        } catch (error) {
            console.error('获取秒杀活动数据失败:', error);
            this.oneProductList = [];
            // 可以在这里添加错误提示，如 Toast 提示用户
        }
    }

    async onShowExecute() {
        if (uni.getStorageSync('indexCache')) {
            const { banners, goodsTagList, recommendList } = uni.getStorageSync('indexCache');
            this.banners = banners;
            this.bannerLoading = false;
            this.goodsTagList = goodsTagList;
            this.recommendList = recommendList;
        }
        await this.getThemeActivity();
        const result = await checkNewUser();
        this.isNewUser = result.is_new_user;
        this.getBannerList();
        this.getGoodsTag();
        this.getGoodsRecommend();

        // #ifdef MP-WEIXIN
        // await this.$refs.live.getLiveList(this.isJumpLive)
        // this.isJumpLive = false
        // #endif
    }

    handleSwitch(val) {
        this.showProductUI = val;
    }

    onPullDownRefresh() {
        this.init();
    }

    overlayClick() {
        this.isExpanded = false;
    }

    moreShow() {
        // #ifdef H5
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        this.animationFrameId = requestAnimationFrame(() => {
            this.isExpanded = !this.isExpanded;
            this.height = '278rpx';
            this.width = '270rpx';
        });
        // #endif

        // #ifdef MP-WEIXIN
        this.isExpanded = !this.isExpanded;
        this.height = '278rpx';
        this.width = '270rpx';
        // #endif
    }

    navigateTo(type, url) {
        if (type === 5) {
            Utils.decryptContact({}, BuType.BuType_NONE);
        } else {
            Utils.navigateTo(url);
        }
        this.isExpanded = false;
    }

    handleMorePoint() {
        Utils.navigateTo('/pagesA/point/point');
    }

    mounted() {
        // 初始化Content组件的定位
        this.contentTop = this.statusBarHeight + 90;
        // 获取我的设备
        setTimeout(() => {
            this.getDeviceList();
        }, 1000);
        Utils.newMessageChannel('MQTT', 'subscribe', {});
        const win = window as any;
        win.mqttMsgArrived = (dataInfo: any) => {
            const { payload = '' } = dataInfo;
            const payloadObj = JSON.parse(payload);
            const { data = {}} = payloadObj;
            const { params = [], method = '' } = data;
            if (method === 'properties_changed') {
                // 处理设备状态更新
                let onlineStatus = null;
                let batteryStatus = null;
                let currentDid = null;
                params.forEach((item) => {
                    const { siid, piid, did, value } = item;
                    if (siid === 2 && piid === 1) {
                        // 设备在线状态
                        onlineStatus = value;
                        currentDid = did;
                    } else if (siid === 3 && piid === 1) {
                        batteryStatus = value;
                        currentDid = did;
                    }
                });
                if (onlineStatus !== null || batteryStatus !== null) {
                    const newList = this.deviceList.map((item: any) => {
                        if (item.did === currentDid) {
                            // 更新设备状态
                            item.latestStatus = onlineStatus !== null ? onlineStatus : item.latestStatus;
                            item.battery = batteryStatus !== null ? batteryStatus : item.battery;
                        }
                        return item;
                    });
                    // console.log('舍尔比', newList)
                    this.deviceList = [...newList];
                }
            }
        };
        win.onAppPageShow = () => {
            // 页面显示时获取设备状态
            this.refreshList();
        };
    }

    navigateToAddress(item) {
        if (this.wxAuth && this.isPhone) {
            if (item.type == 1) {
                Utils.reportEvent('new_gift', {});
            } else if (item.type == 2) {
                Utils.reportEvent('invite', {
                    entrance: '会员专享区',
                });
            } else if (item.type == 3) {
                Utils.reportEvent('recruit', {
                    entrance: '会员专享区',
                });
            } else if (item.type == 4) {
                Utils.reportEvent('exclusive', {});
            }
            Utils.navigateTo(item.url);
        } else {
            const target = 'navigateToAddress';
            UserModule.authFlow({ target });
        }
    }

    refreshList() {
        this.getDeviceList();
    }

    getDeviceList() {
        const callback = (info: deviceInfo) => {
            const { code = 0, data = '' } = info;
            if (code === 0) {
                const list = JSON.parse(data as string);
                console.log('舍尔比_____________', list)
                this.deviceList = [...list];
            }
        };
        Utils.newMessageChannel('DEVICE', 'list', { size: 10, current: 1 }, callback);
    }

    onPageScroll(e) {
        const _this = this;
        // #ifdef H5
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        this.animationFrameId = requestAnimationFrame(() => {
            const num = e.scrollTop;
            if (num <= 200) {
                this.opacity = 1 - (3 / 2000) * num;
            }
        });
        // #endif

        if (this.isLoadCategoryData) {
            uni.createSelectorQuery()
                .select('.product_category')
                .boundingClientRect((rect) => {
                    const elementTop = rect.top;
                    const elementTopRpx = Utils.pxToRpx(elementTop);
                    if (elementTopRpx < _this.windowHeight) {
                        _this.isLoadCategoryData = false;
                        clearTimeout(this.showTimer);
                        this.showTimer = setTimeout(async () => {
                            await _this.getGoodsRecommend();
                        }, 200);
                    }
                })
                .exec();
        }

        // #ifdef MP-WEIXIN
        const num = Utils.pxToRpx(e.scrollTop);
        if (num <= 1016 - this.statusBarHeight) {
            this.opacity = (num / (1016 - this.statusBarHeight)).toFixed(2);
        } else {
            this.opacity = 1;
        }

        this.showService = false; // 每次滚动即隐藏客服和直播
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
            this.showService = true;
        }, 800); // 在1000毫秒后显示客服和直播
        // #endif
    }

    async init() {
        await this.onShowExecute();
        this.initDataKey = false;
    }

    handleToPartner() {
        Utils.navigateTo('/pagesC/ambassador/login/index');
    }

    async getBannerList() {
        try {
            const _banners = await bannerList({ banner_version: 2 });
            // this.banners =
            //     this.isPrizeShow
            //         ? _banners.data
            //         : _banners.data.filter((v) => !v.jump_url.includes('pagesC/lucky/lucky'));
            this.banners = _banners.data;
            this.bannerLoading = false;
            this.indexCache.banners = this.banners;
            uni.setStorageSync('indexCache', this.indexCache);
        } catch (e) {
            console.error('getBannerList e=', e);
        }
    }

    // 精彩直达数据
    async getGoodsTag() {
        try {
            const res = await goodsTag();
            res.push({
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/869077-1751421536340.png',
                text: '酒旅',
                type: 'hotel',
            });

            this.goodsTagList = res;
            this.tid = this.goodsTagList[0].tid;
            this.indexCache.goodsTagList = this.goodsTagList;
            uni.setStorageSync('indexCache ', this.indexCache);
        } catch (e) {
            console.error('getGoodsTag e=', e);
        }
    }

    // 推荐商品数据
    async getGoodsRecommend() {
        try {
            const res = await goodsRecommend();
            this.recommendList = res;
            this.isLoadCategoryData = false;
            this.indexCache.recommendList = this.recommendList;
            uni.setStorageSync('indexCache', this.indexCache);
            // #ifdef MP-WEIXIN
            res.forEach((g) => {
                g.primary &&
                    Utils.reportEvent('goods_show', {
                        product_id: g.primary.id,
                    });
                g.secondary &&
                    Array.isArray(g.secondary) &&
                    g.secondary.forEach((s) => {
                        Utils.reportEvent('goods_show', {
                            product_id: s.id,
                        });
                    });
            });
            // #endif
        } catch (e) {
            console.error('getGoodsRecommend e=', e);
        }
    }

    handleDevice() {
        if (this.wxAuth && this.isPhone) {
            Utils.reportEvent('device_click', {});
            uni.navigateTo({ url: '/pagesB/deviceList/deviceList' });
        } else {
            const target = 'handleDevice';
            UserModule.authFlow({ target });
        }
    }

    handleContact(e) {
        if (this.wxAuth && this.isPhone) {
            Utils.decryptContact(e, BuType.BuType_NONE);
        } else {
            const target = 'handleContact';
            UserModule.authFlow({ target });
        }
    }

    handleNearShop() {
        Utils.navigateTo('/pagesA/nearShop/nearShop');
    }

    toComparison() {
        Utils.reportEvent('intelligent_shopping', {});
        Utils.navigateTo('/pagesA/product/comparison');
    }

    toShop(tid, title) {
        if (tid == 20) {
            Utils.reportEvent('parts_zone', {});
        }
        if (title) {
            Utils.reportEvent('see_all', {
                good_type: title,
            });
        }
        uni.setStorageSync('tid', tid);
        Utils.navigateTo('/pages/shop/shop?tid=' + tid);
    }

    toServe() {
        if (this.wxAuth && this.isPhone) {
            Utils.reportEvent('product_register', {});

            Utils.navigateTo('/pagesC/serve/serve');
        } else {
            const target = 'toServe';
            UserModule.authFlow({ target });
        }
    }

    toGoodsDetail(gid) {
        Utils.reportEvent('goods_click', {
            product_id: gid,
        });

        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}`);
    }

    toPoint() {
        if (this.wxAuth && this.isPhone) {
            Utils.navigateTo('/pagesA/point/point');
        } else {
            const target = 'toPoint';
            UserModule.authFlow({ target });
        }
    }

    navigateToGroupGoods() {
        if (!this.isPhone) {
            UserModule.authFlow({ target: 'groupGoods' });
        } else {
            Utils.navigateTo('/pagesB/groupGoods/groupGoods');
        }
    }

    // 30s请求一次服务器时间刷新倒计时
    initRedBagTimer() {
        if (this.redBagTimer) {
            clearInterval(this.redBagTimer);
            this.redBagTimer = null;
        }

        this.redBagTimer = setInterval(() => {
            if (this.time === 0) {
                clearInterval(this.redBagTimer);
                this.redBagTimer = null;
                return;
            }

            getSystemTime({ timestamp_type: 'millisecond' }).then((res) => {
                this.time =
                    this.collect_start_time - Number(res.time) < 0 ? 0 : this.collect_start_time - Number(res.time);
            });
        }, 30000);
    }

    async getThemeActivity() {
        const result = await getThemeActivity({ id: Number(process.env.VUE_APP_RED_ENVELOPE_ID) });
        const activeInfo = result.modules?.find((v) => Number(v.module_res_id) === 11);
        this.redEnvelopeAcId = activeInfo.id;
        this.activity_start_time = Number(activeInfo.start_time) * 1000;
        this.activity_end_time = Number(activeInfo.end_time) * 1000;
        this.collect_start_time = Number(activeInfo.extra.collect_start_time) * 1000;
        this.userType = Number(activeInfo.extra.is_check_user);
        this.time =
            Number(activeInfo.extra.collect_start_time) * 1000 - new Date().getTime() < 0
                ? 0
                : Number(activeInfo.extra.collect_start_time) * 1000 - new Date().getTime() + 1000;
        this.timerHongbao = this.time;
        this.isShowRecordImage = new Date().getTime() > Number(activeInfo.extra.collect_end_time) * 1000;
        this.content = result.base_info.rule;
        this.initRedBagTimer();
    }

    luckyClick(url) {
        // if ((this.isNewUser !== undefined && !this.isNewUser) || this.userType === 1) return Utils.Toast('本轮红包仅限新用户参与', 1000, 'none', 'white-space: nowrap;');

        if (this.collect_start_time > new Date().getTime()) {
            return Utils.Toast('活动未开始，敬请期待', 1000, 'none', 'white-space: nowrap;');
        }
        Utils.navigateTo(`${url}?redEnvelopeAcId=${this.redEnvelopeAcId}`);
    }

    async swichMenu(current, item) {
        this.tid = item.tid;
        this.currentTab = 'tab-' + (current - 1);
    }

    handleToCategory() {
        Utils.navigateTo('/pages/shop/shop');
    }

    viewOnreachBottom() {
        console.log('页面到底了jjjjjjjj');
        // 通过ref调用ProductList组件的loadMore方法
        if (this.$refs.productList) {
            this.$refs.productList.loadMore();
        }
    }

    viewRefresherrefresh() {
        // 刷新商品列表
        this.$refs.productList?.refresh?.();
        this.refreshList();
        // 刷新服务列表
        // this.$refs.memberServiceBar?.fetchTagList?.();
    }

    onViewScroll(e) {
        const scrollTop = e.scrollTop;
        const hasDevice = 260;
        const hasNoDevice = 120;
        let top = 0;
        if (this.deviceList.length > 0) {
            if (scrollTop > hasDevice) {
                top = hasDevice;
            } else {
                top = hasNoDevice;
            }
        } else {
            top = hasNoDevice;
        }
        if (scrollTop > top) {
            // 启用ProductList的滚动
            this.isProductVisible = false
            if (this.$refs.productList) {
                this.$refs.productList.setScrollEnabled(true);
            }
            // 改变Content组件的定位，让Content能够滚动到最上边
            this.contentTop = this.statusBarHeight + 10;
        } else {
            // 禁用ProductList的滚动
            if (this.$refs.productList) {
                this.$refs.productList.setScrollEnabled(false);
            }
            this.isProductVisible = true;
            // 恢复Content组件的原始定位
            this.contentTop = this.statusBarHeight + 90;
        }
    }

    async fetchCoinPoints() {
        try {
            const res = await basicInfo();
            console.log('resfetchCoinPoints', res);
            this.coin = 0;
            this.points = res.totalPoints || 0;
            this.deduction_rate = res.deduction_rate || 0;
        } catch (e) {
            this.coin = 0;
            this.points = 0;
        }
    }

    ceoActionClick() {
        this.showExplanation = true;
    }

    signUpClick(id) {
        Utils.navigateTo(`/pagesC/jsfl/jsfl?actionId=${id}`);
        this.showExplanation = false;
    }
}
</script>
<style lang="scss" scoped>
@import './styles/index.scss';

// 回到顶部按钮样式
.back-to-top {
    position: fixed;
    right: 30rpx;
    bottom: 200rpx;
    width: 88rpx;
    height: 88rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 1);
    }
}

.back-to-top-icon {
    width: 40rpx;
    height: 40rpx;
    transform: rotate(-90deg); // 让箭头指向上方
}

.custom-popup-scroll {
    position: fixed;
    left: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    width: calc(100vw - 64rpx);
    height: 600rpx;
    background: #07081c;
    border-radius: 24rpx;
    z-index: 999999;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    overflow: hidden;
    padding-bottom: 20rpx;
}

.popup-scroll-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
}

.popup-title {
    height: 96rpx;
    line-height: 96rpx;
    font-family: MiSans;
    font-size: 32rpx;
    color: #3d3d3d;
    font-weight: 500;
    width: 100%;
    text-align: center;
    margin-bottom: 30rpx;
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-title-img {
    width: 252rpx;
    height: 44rpx;
}

.close-btn {
    position: absolute;
    top: 38rpx;
    right: 24rpx;
    color: #333;
    z-index: 10000;
    width: 48rpx;
    height: 48rpx;
}

.popup-content {
    width: calc(100% - 80rpx);
    margin-left: 40rpx;
}

.popup-content-item {
    margin-bottom: 30rpx;
}

.popup-content-item-img {
    width: 100%;
    height: 300rpx;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
}

.popup-content-item-btn {
    width: 100%;
    height: 96rpx;
    border-radius: 200rpx;
    background: linear-gradient(101deg, #f5e2ba -12%, #b98357 100%);
    font-family: MiSans;
    font-size: 32rpx;
    font-weight: 500;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
