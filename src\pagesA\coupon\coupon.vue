<template>
    <view class="container">
        <view class="header">
            <CustomBar title="优惠券" :isFixed="false" background="#F4F4F4"></CustomBar>
        </view>
        <view style="width: 450rpx; margin: auto">
            <tabs-swiper
                activeColor="none"
                ref="tabs"
                :list="tabList"
                :current="current"
                inactive-color="#404040"
                @change="tabsChange"
                :is-scroll="false"
                font-size="28"
                bg-color="#F4F4F4"
                bar-width="58"
                bar-height="58"
                height="96"
                :activeItemStyle="{ fontSize: '32rpx', color: '#121212' }"
                :bar-style="barStyle"
            ></tabs-swiper>
        </view>
        <swiper
            class="swiper-box"
            :style="{ height: `calc(100vh - 120rpx - ${headerHeight}px)` }"
            :current="current"
            @change="swiperChange"
            @animationfinish="animationfinish"
        >
            <block v-for="(item, index) in tabList" :key="index">
                <swiper-item class="swiper-item">
                    <scroll-view
                        scroll-y
                        class="swiper-item-view"
                        :refresher-enabled="true"
                        :upper-threshold="0"
                        @refresherrefresh="refresherrefresh"
                        :refresher-triggered="isRefreshing"
                    >
                        <block v-for="item in listData[index]" :key="item.id">
                            <view class="coupon-box">
                                <CouponItem
                                    :itemData="item"
                                    :isQixiCoupon="getIsQixiCoupon(item)"
                                    @showRule="handleShowRule"
                                />
                            </view>
                        </block>
                        <view v-if="listData[index].length === 0" style="padding-top: 206rpx">
                            <DefaultPage
                                :show="listData[index].length === 0"
                                icon="https://wpm-cdn.dreame.tech/images/202306/178397-1687834429297.png"
                                imgWidth="508"
                                imgHeight="508"
                                imgMbottom="0"
                                tipStyle="font-size:28rpx;color: rgba(29,30,32,0.4);"
                                tip="暂无数据"
                            />
                        </view>
                    </scroll-view>
                </swiper-item>
            </block>
        </swiper>
        <CustomModal
            :show="showRuleModel"
            width="616rpx"
            contentStyle="color:#1D1E20;font-weight:500;font-size: 28rpx;"
            title="提示"
            :content="rule"
            confirmText="我知道了"
            :showCancelButton="false"
            confirmStyle="width: 524rpx; background: #E8DEC1;color: #8C6533;"
            @confirm="showRuleModel = false"
        >
        </CustomModal>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getCouponList } from '@/http/mine';
import CouponItem from '@/components/CouponItem/CouponItem.vue';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import Utils from '@/common/Utils';

@Component({
    components: {
        TabsSwiper,
        CouponItem,
        DefaultPage,
    },
})
export default class Coupon extends Vue {
    public isRefreshing: Boolean = false;
    public timer: any = null;
    public tabList: Array<any> = [{ name: '待使用' }, { name: '已使用' }, { name: '已过期' }];
    public listData: any = [[], [], []];
    public headerHeight: number = 0;
    public current: number = 0;
    public showRuleModel: Boolean = false;
    public rule: String = '';
    public barStyle: Object = {
        bottom: '10rpx',
        backgroundImage: 'url(https://wpm-cdn.dreame.tech/images/202307/609564-1688537989905.png)',
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
    };

    // 七夕活动优惠券id列表
    get qixiCouponIdList(): any {
        return process.env.VUE_APP_QIXI_FESTIVAL_COUPON_IDS.split('|');
    }

    async onLoad(options) {
        this.current = options.index || 0;
        await this._getList(0);
        await this._getList(1);
        await this._getList(2);
        uni.createSelectorQuery()
            .select('.header')
            .boundingClientRect((rect) => {
                this.headerHeight = rect.height;
            })
            .exec();
    }

    refresherrefresh() {
        this.isRefreshing = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this._getList(0);
            await this._getList(1);
            await this._getList(2);
            this.isRefreshing = false;
        }, 200);
    }

    getIsQixiCoupon(item: any): Boolean {
        if (item.get_relation && this.qixiCouponIdList.length > 0) {
            return this.qixiCouponIdList.includes(item.get_relation);
        }
        return false;
    }

    changeIndexFormMine(index) {
        this.current = index;
    }

    async _getList(index) {
        try {
            const res = await getCouponList({ tab_type: index + 1 });
            this.$set(this.listData, index, res.list);
        } catch (e) {
            console.error('_getList e=', e);
        }
    }

    handleShowRule(rule) {
        this.rule = rule;
        this.showRuleModel = true;
    }

    // tabs通知swiper切换
    tabsChange(index: any) {
        this.current = index;
    }

    swiperChange(e) {
        const current = e.detail.current;
        this.current = current;
    }

    animationfinish(e) {
        const current: number = e.detail.current;
        (this.$refs.tabs as any).setFinishCurrent(current);
        this.current = current;
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }
}
</script>

<style lang="scss" scoped>
@import './coupon.scss';
</style>
