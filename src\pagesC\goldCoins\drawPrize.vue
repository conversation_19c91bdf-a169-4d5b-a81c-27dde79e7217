<template>
    <view class="draw-container">
        <CustomBar2 title="盲盒抽奖" titleStyle="color: #FFF" background="transparent" style="z-index: 1000;"
            BackIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890dd41966066160010810.png">
        </CustomBar2>

        <!-- 中将记录飘屏 -->
        <view v-if="drawRollRecord.length > 0" class="roll-banner">
            <swiper class="swiper" :circular="true" vertical :autoplay="true" :interval="3000">
                <swiper-item v-for="(item, index) in drawRollRecord" :key="index">
                    <view class="text">恭喜 @{{ item.nick }} 获得 {{ item.prize_name }}</view>
                </swiper-item>
            </swiper>
        </view>

        <view class="my-coin u-flex">
            <view class="coin-icon"></view>
            <text>我的消费金</text>
            <text class="coin-amount">{{ totalGold }}</text>
            <text>元</text>
        </view>

        <view class="draw-box">
            <template v-if="!prizeShowList.length">
                <view v-for="(item, index) in 6" class="draw-content" :key="index" :class="{ active: currentIndex === index }" @click="drawBoxPrize(index)">
                    <image class="content-box" :src="
                        currentIndex === index
                        ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890e6d357fd33600011799.png'
                        : 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890e6bfca8d08300011041.png'" />
                    <image class="content-select" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890e6e1a4abd6740011381.png" />
                </view>
            </template>
            <template v-else>
                <view  v-for="(prize, index) in prizeShowList" class="draw-content prize" :key="index" :class="{ active: currentIndex === index }">
                    <image class="content-box" :src="prize.prize_image" />
                </view>
            </template>
        </view>

        <view class="hot-area">
            <view class="rule" @click="openRulePopup"></view>
            <view class="draw" @click="() => startDraw()"></view>
            <view class="record" @click="openRecordPopup"></view>
            <view class="draw-tip">{{ drawCost }}消费金/次</view>
        </view>

        <view class="draw-pool" v-if="prizeList.length > 0">
            <scroll-view scroll-x :scroll-left="scrollLeft" style="white-space: nowrap" @touchstart="pauseScroll" @touchend="resumeScroll" @scroll="onScroll">
                <template v-for="(prize, index) in prizeList">
                    <image class="draw-img" :src="prize.prize_image" :data-key="index" :key="index"/>
                </template>
                <template v-for="(prize, index) in prizeList">
                    <image class="draw-img" :src="prize.prize_image" :data-key="`copy-${index}`" :key="`copy-${index}`"/>
                </template>
            </scroll-view>
        </view>

        <view class="btn-invite" @click="goInvite"></view>
        <custom-toast ref="customToast" />
        <ShareDialog
            :show.sync="show"
            :productInfo="productInfo"
            :shareType="'active'"
            @share="handleShare"
        ></ShareDialog>
        <NoCoinPopup ref="NoCoinPopup"/>
        <PrizePopup ref="PrizePopup" :draw_activity_id="draw_activity_id" @startDraw="startDraw" @openFormPopup="handleAction"/>
        <Goldinformation ref="Goldinformation"/>
        <PrizeRecord ref="PrizeRecord" @handleAction="handleAction"/>
        <ShowRulePopup ref="showRulePopup" />
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
import NoCoinPopup from './popup/NoCoinPopup.vue'
import PrizePopup from './popup/PrizePopup.vue'
import { getThemeActivity } from '@/http/doubleEleven';
import { getDrawRollRecord, getCoinLottery } from '@/http/coin'
import { getInviteInfo } from '@/http/halfPrice'
import Goldinformation from './components/Goldinformation.vue'
import PrizeRecord from './components/PrizeRecord.vue'
import ShowRulePopup from './components/showRulePopup.vue'
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
// import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import { goodsList } from '@/http/goods';

type Prize = {
    prize_id: number
    prize_type: number
    prize_name: string
    prize_image: string
}

@Component({
    components: {
        NoCoinPopup,
        PrizePopup,
        Goldinformation,
        PrizeRecord,
        ShowRulePopup,
        ShareDialog
    }
})
export default class DrawPrize extends Vue {
    $refs: {
        NoCoinPopup: NoCoinPopup,
        PrizePopup: PrizePopup,
        Goldinformation: Goldinformation,
        PrizeRecord: PrizeRecord,
        showRulePopup: ShowRulePopup,
        customToast: any,
        productListRef: any
    }

    public eventId = 30;
    public draw_activity_id = '';

    public isOutTime: boolean = null; // 活动是否结束

    public totalGold = 0; // 总消费金持有数
    public drawCost = 0; // 单次抽奖消费金消耗数

    public drawRollRecord = []; // 消费金抽奖滚动记录

    public prizeList: Array<Prize> = []; // 奖品池
    public prizeShowList: Array<Prize> = [] // 作为展示的奖池
    private scrollLeft = 0; // 滚动左移
    private scrollTimer: any = null; // 定时
    private isUserScrolling = false; // 手动滚动
    private scrollResumeTimer: any = null; // 恢复滚动定时器
    private currentIndex = 0; // 当前选中的位置
    private isDrawing = false; // 是否正在抽奖
    private drawTimer: any = null; // 抽奖定时器
    private show = false;
    private shareId = '';
    private productInfo = {};
    private shareType = 'active';
    private shareProduct: any = {};
    private baseInfo: any = {};
    private total_consume_money = 0;
    private isThrottled = false;
    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    onShow() {
        this.getThemeActivity()
        this.getCoinInfo()
    }

    mounted() {
        this.startScroll()
    }

    beforeDestroy() {
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer)
            this.scrollTimer = null
        }
        if (this.drawTimer) {
            clearTimeout(this.drawTimer)
            this.drawTimer = null
        }
        if (this.scrollResumeTimer) {
            clearTimeout(this.scrollResumeTimer)
            this.scrollResumeTimer = null
        }
    }

    /**
     * @description 活动信息
     * 获取 奖池 单次抽奖消耗消费金
     */
    async getThemeActivity() {
        const response = await getThemeActivity({ id: this.eventId })
        const module = response.modules[0]
        this.baseInfo = response.base_info;
        this.drawCost = module.extra.draw_task[0].consume_gold
        this.prizeList = module.extra.draw_prize
        this.draw_activity_id = module.extra.draw_activity_id

        const end_time = module.end_time * 1000;

        this.isOutTime = +new Date() - end_time >= 0;
        if (this.isOutTime) {
            this.$refs.NoCoinPopup.toggleVisible('OutTime')
        }

        this.getDrawRollRecord()
    }

    /**
     * @description 获取中将飘屏数据
     */
    async getDrawRollRecord() {
        const response = await getDrawRollRecord({ draw_activity_id: this.draw_activity_id })
        this.drawRollRecord = Object.freeze(response)
    }

    /**
     * @description 获取消费金总额
     */
    async getCoinInfo() {
        const response = await getInviteInfo({
            invite_type: 5,
            relate_id: 5
        })
        this.total_consume_money = response.total_consume_money
        this.totalGold = response.pending_amount
    }

    public openRulePopup() {
        console.log('openRulePopup');
        (this.$refs?.showRulePopup as any)?.open(this.baseInfo)
    }

    // 中奖记录
    public openRecordPopup() {
        (this.$refs?.PrizeRecord as any)?.togglePopup(this.draw_activity_id);
    }

    public handleAction(item: any) {
        (this.$refs?.PrizeRecord as any)?.close();
        (this.$refs?.Goldinformation as any)?.open(item);
    }

    /**
     * @description 开始抽奖
     */
    async startDraw() {
        if (this.isDrawing == true) return
        if (this.isOutTime) {
            this.$refs.NoCoinPopup.toggleVisible('OutTime');
            return;
        }

        if (this.isDrawing) return

        // 总消费金持有数小于单次抽奖消耗数，打开 NoCoinPopup
        if (this.totalGold < this.drawCost) {
            this.$refs.NoCoinPopup.toggleVisible('NoCoin');
            return;
        }
        // 播放抽奖跑马灯动画
        await this.playDrawAnimate();
        // 先抽奖
        await this.drawPrize()
        // 刷新消费金总额
        setTimeout(() => {
            this.getCoinInfo()
        }, 500)
    }

    async drawPrize() {
        if (this.isDrawing == true) return
        const response = await getCoinLottery({ ac_id: this.eventId })
        const { prize_id } = response;

        const prize = this.prizeList.find(prize => prize.prize_id === prize_id)
        console.log(prize)
        let prize_level = 0; // 未抽到奖品
        // To 需要区分是否是虚拟物品还是实体物品
        if (prize.prize_type === 8) {
            prize_level = 2;
        } else if (prize.prize_type === 1) {
            prize_level = 0
        } else {
            prize_level = 1
        }
        this.$refs.PrizePopup.toggleVisible({
            ...prize,
            prize_level
        })
        Utils.taskComplete('addBlindPrizeDraw')
    }

    async drawBoxPrize(index) {
        if (this.isThrottled) return;
        this.isThrottled = true;
        setTimeout(() => {
            this.isThrottled = false; // 1秒后解锁
        }, 1500);
        if (this.isOutTime) {
            this.$refs.NoCoinPopup.toggleVisible('OutTime');
            return;
        }
        if (this.isDrawing) return

        // 总消费金持有数小于单次抽奖消耗数，打开 NoCoinPopup
        if (this.totalGold < this.drawCost) {
            this.$refs.NoCoinPopup.toggleVisible('NoCoin');
            return;
        }

        this.currentIndex = index;

        const response = await getCoinLottery({ ac_id: this.eventId })
        const { prize_id } = response;

        // 获取 prize_id 在 prizeList 的序列（索引）
        const prize = this.prizeList.find(prize => prize.prize_id === prize_id)
        const prizeIndex = this.prizeList.findIndex(prize => prize.prize_id === prize_id);
        const length = this.prizeList.length;
        // 获取指定抽到商品的前，后值
        const prev = length + prizeIndex - (this.currentIndex - 0);
        const next = length + prizeIndex + (6 - this.currentIndex);
        // 截取，保障前后数据购截取
        const tempList = [...this.prizeList, ...this.prizeList, ...this.prizeList]
        this.prizeShowList = tempList.slice(prev, next)
        let prize_level = 0; // 未抽到奖品
        // To 需要区分是否是虚拟物品还是实体物品
        if (prize.prize_type === 8) {
            prize_level = 2;
        } else if (prize.prize_type === 1) {
            prize_level = 0
        } else {
            prize_level = 1
        }
        setTimeout(() => {
            this.prizeShowList = []
            this.$refs.PrizePopup.toggleVisible({
                ...prize,
                prize_level
            })
        }, 1000)
        // 刷新消费金总额
        setTimeout(() => {
            this.getCoinInfo()
        }, 500)
        Utils.taskComplete('addBlindPrizeDraw')
    }

    /**
     * @description 播放抽奖动画，速度由慢变快再变慢
     */
    private playDrawAnimate() {
        this.isDrawing = true
        // 如果没有指定目标位置，随机生成一个
        const endIndex = Math.floor(Math.random() * 6)

        // 随机生成总圈数 6-8圈 (增加圈数以延长总时长约3秒)
        const totalRounds = Math.floor(Math.random() * 3) + 6
        const totalSteps = totalRounds * 6 + endIndex + 1 // 总步数

        let currentStep = 0
        let speed = 0

        return new Promise(resolve => {
            const animate = () => {
                if (currentStep >= totalSteps) {
                    // 抽奖结束
                    this.isDrawing = false
                    console.log(`抽奖结束，最终位置: ${endIndex}`)
                    resolve(null);
                    return;
                }

                // 计算当前应该高亮的位置
                this.currentIndex = currentStep % 6

                                // 计算速度变化：由慢变快再变慢
                const progress = currentStep / totalSteps

                if (progress < 0.3) {
                    // 前30%：由慢变快 (200ms -> 70ms) - 启动更快
                    const accelerationProgress = progress / 0.3
                    speed = 200 - Math.floor(accelerationProgress * 130)
                } else if (progress < 0.7) {
                    // 30%-70%：保持最快速度 (70ms) - 提速30%
                    speed = 70
                } else {
                    // 70%-100%：由快变慢 (70ms -> 300ms) - 结束更快
                    const decelerationProgress = (progress - 0.7) / 0.3
                    speed = 70 + Math.floor(decelerationProgress * 230)
                }

                currentStep++
                this.drawTimer = setTimeout(animate, speed)
            }

            animate()
        })
    }

    /**
     * @description 自动滚动
     */
    private startScroll() {
        const scrollSpeed = 1 // 每次滚动的像素数
        const scrollInterval = 25 // 滚动间隔时间(ms)

        const scroll = () => {
            if (this.isUserScrolling) return // 用户滑动时不自动滚动
            this.scrollLeft += scrollSpeed
            // 当滚动到第一组图片结束时，重置到第二组开始位置，实现无缝循环
            const singleGroupWidth = this.prizeList.length * (117 + 14) / 2 // 图片宽度 + 间距
            if (this.scrollLeft >= singleGroupWidth - scrollSpeed) {
                this.scrollLeft = 0
            }
            // 递归调用 setTimeout 实现循环
            this.scrollTimer = setTimeout(scroll, scrollInterval)
        }
        this.scrollTimer = setTimeout(scroll, scrollInterval)
    }

    pauseScroll() {
        this.isUserScrolling = true
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer)
            this.scrollTimer = null
        }
        // 清除之前的恢复定时器
        if (this.scrollResumeTimer) {
            clearTimeout(this.scrollResumeTimer)
            this.scrollResumeTimer = null
        }
    }

    resumeScroll() {
        // 清除之前的恢复定时器
        if (this.scrollResumeTimer) {
            clearTimeout(this.scrollResumeTimer)
        }

        // 添加延迟恢复，避免快速滑动时的卡顿
        this.scrollResumeTimer = setTimeout(() => {
            this.isUserScrolling = false
            this.startScroll()
        }, 150) // 增加延迟时间，确保用户完全停止滑动
    }

    onScroll(e) {
        // 兼容不同平台，scrollLeft 可能在 detail 里
        this.scrollLeft = e.detail?.scrollLeft ?? e.target?.scrollLeft ?? 0
    }

    routeBack() {
        uni.navigateBack({ delta: 1 })
    }

    async goInvite() {
        // const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?sharerUid=${this.user_id}`;
        // const data = {
        //     ...Constants.GEN_SHARE_LINK_TYPE,
        //     jumpLink: url,
        // };
        // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
        //     const params = {
        //         target: 'wechat,weixin_circle,qq,sina',
        //         type: 'web',
        //         content: {
        //             url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/sharePage?link=${res.data}`,
        //             share_image:
        //                 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889be0d0dc8a0560012127.png',
        //             share_title: `天天赚消费金`,
        //             share_desc: `看视频赚消费金，新款扫地机0元带走！`,
        //         },
        //     };
        //     Utils.messageChannel('share', params);
        // });
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        await this.getHomeRandomGoods();
        this.show = true;
        // const shareProductList = this.$refs.productListRef.exposeProductList();
        // const shareProductInfo = shareProductList[Math.floor(Math.random() * shareProductList.length)];
        const shareProductInfo = this.shareProduct;
        this.shareId = shareProductInfo.gid;
        this.productInfo = {
            name: shareProductInfo.name,
            desc: shareProductInfo.introduce,
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#FF3654',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68987f87aaf4a7000012110.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    /**
     * @description 分享
     */
    handleShare(type: 'web' | 'image') {
        this.show = false;
        if (this.shareType === 'active') {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887a231e180f9240019837.png',
                        title: this.total_consume_money
                            ? `我在追觅App赚了${this.total_consume_money}元，邀你一起来赚钱`
                            : '来追觅和我一起赚钱！',
                        desc: this.total_consume_money
                            ? '好友助力就能赚钱，搞钱真的能上瘾！'
                            : '躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: '追觅抽盲盒100%中奖，限时解锁，全球新款扫地机免费抽！',
                    },
                },
            );
        } else {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}`,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887a231e180f9240019837.png',
                        title: this.total_consume_money
                            ? `我在追觅App赚了${this.total_consume_money}元，邀你一起来赚钱`
                            : '来追觅和我一起赚钱！',
                        desc: this.total_consume_money
                            ? '好友助力就能赚钱，搞钱真的能上瘾！'
                            : '躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: '追觅抽盲盒100%中奖，限时解锁，全球新款扫地机免费抽！',
                    },
                },
            );
        }
    }

    /* 获取首页随机商品 */
    async getHomeRandomGoods() {
        const { list = [] } = await goodsList({ tid: -1, type: -2, page: 1, page_size: 10, single: 1 });
        this.shareProduct = list[Math.floor(Math.random() * list.length)];
    }
}
</script>

<style lang="scss" scoped>
.draw-container {
    position: relative;
    height: 1624rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed6eac459d8040011490.jpg');
    background-size: 100% 100%;

    &.out {
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68943c96af0967170011168.png');
        background-size: 100% 100%;
    }
    &.in {
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890dcec4e61c3210016097.png');
        background-size: 100% 100%;
    }

    .btn-back {
        position: absolute;
        width: 682rpx;
        height: 104rpx;
        left: 32rpx;
        bottom: 300rpx;
    }

    .roll-banner {
        width: 523rpx;
        height: 35rpx;
        overflow: hidden;
        position: absolute;
        top: 380rpx;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(270deg, rgba(255, 29, 78, 0) 0%, #ff1d4e 52%, rgba(255, 29, 78, 0) 100%);
        .text {
            color: #fff;
            font-weight: 500;
            text-align: center;
            height: 35rpx;
            line-height: 35rpx;
            font-size: 19rpx;
        }
    }

    .my-coin {
        position: absolute;
        top: 490rpx;
        left: 50%;
        font-size: 24rpx;
        color: #FF5A00;
        transform: translateX(-50%);

        .coin-icon {
            margin-right: 8rpx;
            width: 24rpx;
            height: 24rpx;
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890ddd20411c0170010950.png');
            background-size: 100% 100%;
        }
        .coin-amount {
            margin: 0 6rpx;
            padding: 4rpx 20rpx;
            background: #FFF1C1;
            border-radius: 5rpx;
            box-shadow: inset 0px -2rpx 2rpx 0px #FFFFFF,inset 0px 4rpx 12rpx 0px rgba(209, 88, 88, 0.3);
        }
    }

    .draw-box {
        display: flex;
        flex-wrap: wrap;
        position: absolute;
        padding-left: 41rpx;
        padding-top: 19rpx;
        width: 492rpx;
        height: 312rpx;
        left: 130rpx;
        top: 616rpx;

        .draw-content {
            position: relative;
            width: 113rpx;
            height: 113rpx;
            transition: all 0.1s ease;

            + .draw-content {
                margin-left: 30rpx;
            }

            &:nth-child(3n + 1) {
                margin-left: 0;
            }

            &.active {
                transform: scale(1.1);
                filter: brightness(1.2);
                box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8);
            }

            &.prize {
                filter: brightness(0.6);
                border-radius: 12rpx;
                overflow: hidden;

                &.active {
                    transform: scale(1);
                    filter: brightness(1.2);
                }
            }

            .content-box {
                width: 113rpx;
                height: 113rpx;
            }

            .content-select {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -24rpx;
                width: 100rpx;
                height: 40rpx;
            }
        }
    }

    .hot-area {
        position: absolute;
        width: 750rpx;
        height: 90rpx;
        top: 984rpx;

        .rule {
            position: absolute;
            width: 148rpx;
            height: 90rpx;
            left: 88rpx;
        }
        .draw {
            position: absolute;
            width: 256rpx;
            height: 90rpx;
            left: 248rpx;
        }
        .record {
            position: absolute;
            width: 148rpx;
            height: 90rpx;
            left: 514rpx;
        }
        .draw-tip {
            position: absolute;
            left: 380rpx;
            top: -24rpx;
            font-size: 22rpx;
            color: #333436;
        }
    }

    .draw-pool {
        position: absolute;
        top: 1174rpx;
        left: 160rpx;
        width: 480rpx;
        height: 117rpx;

        .draw-img {
            margin-right: 14rpx;
            border-radius: 12rpx;
            width: 117rpx;
            height: 117rpx;
            flex-shrink: 0;
        }
    }
}
.btn-invite {
    position: absolute;
    top: 1432rpx;
    left: 54rpx;
    width: 660rpx;
    height: 120rpx;
}
</style>
