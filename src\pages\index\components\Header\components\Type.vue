<template>
    <view class="type" @click="navigateTo(2)">
        <image style="width: 46rpx; height: 46rpx;display: block;"
            src="https://wpm-cdn.dreame.tech/images/202306/597393-1686106643063.png"></image>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils';

@Component
export default class Type extends Vue {
    constructor() {
        super();
    }

    navigateTo(tid) {
        uni.setStorageSync('tid', tid);
        Utils.navigateTo('/pages/shop/shop?tid=' + tid);
    }
}
</script>
<style lang="scss" scoped></style>
