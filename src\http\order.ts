/* eslint-disable object-curly-spacing */
import http from './index';

/*
 * 订单列表
 * @parma {String} order_no 订单号
 * @parma {String} status 订单状态
 * @parma {String} page  获取第N页商品
 * @returns
 */
interface OrderListParams {
    order_no?: string;
    status?: string | number;
    tab?: number;
    page: string | number;
}
export const orderList = (params: OrderListParams, loading: boolean = true) =>
    http.post('main/order/list', params, { custom: { loading } });

// 我的店铺商品
export const storeGoodsList = (params: any, loading: boolean = true) =>
    http.post('main/goods-store/store-goods', params, { custom: { loading } });

// 分佣统计
export const salesCommission = () => http.post('main/sales-commission/statistics');
// 小店下的订单
export const salesOrderList = (params: any, loading: boolean = true) =>
    http.post('main/sales-commission/list', params, { custom: { loading } });
// 小店的明细
export const salesStoreDetail = (params) => http.post('main/dreame-store/info', params);

// 店铺可筛选标签列表
export const storeFilterLabelList = (params) => http.post('main/goods-store/tag-list', params);

/*
 * 订单详情
 * @parma {String} params
 * @returns
 */
export const orderInfo = (params) => http.post('main/order/info', params);

/*
 * 订单卡券详情
 * @parma {String} order_no 订单号
 * @returns
 */
export const couponInfo = (order_no) => http.post('main/coupon/info', { order_no });

/*
 * 订单支付状态
 * @parma {String} order_no 订单号
 * @returns
 */
export const orderPayStatus = (order_no: String) => http.post('main/order/query-pay-status', { order_no });

/*
 * 提交订单
 * @parma {String} gcombines 购买json数据【必要】
 * @parma {String} cart_ids 购物车id
 * @parma {String} coupon_id 优惠券id
 * @parma {String} coin 使用积分数
 * @parma {String} aid 收货地址id
 * @returns
 */
export const orderBuy = (params) => http.post('main/order/buy', params);

/*
 * 提交订单
 * @parma {String} gcombines 购买json数据【必要】
 * @parma {String} cart_ids 购物车id
 * @parma {String} coupon_id 优惠券id
 * @parma {String} coin 使用积分数
 * @parma {String} aid 收货地址id
 * @returns
 */
export const commonBuy = (params) => http.post('main/order/common-buy', params);

/*
 * 取消_退款原因列表
 * @returns
 */
export const orderRtList = (type) => http.post('main/order/rt-list', { type });

/*
 * 订单结算页详情
 * @parma {String} gcombines 结算json数据【必要】
 * @parma {String} cart_ids 购物车id
 * @parma {String} aid 地址id
 * @returns
 */
interface OrderBuyInfoParams {
    gcombines?: string;
    cart_ids?: string;
    aid: string;
    coupon_id?: string | number;
    coin?: string | number;
    coin_type?: string | number;
    is_use_shopping_price?: string | number;
}
export const orderBuyInfo = (params: OrderBuyInfoParams, loading: boolean = false) =>
    http.post('main/order/buy-info', params, { custom: { loading } });

// 获取用户剩余购物金额
export const getCard = () => http.post('main/gift-card/get-card');

/*
 * 取消订单
 * @parma {String} order_no 订单号【必要】
 * @parma {String} r_type 取消原因
 * @returns
 */
interface OrderCancelParams {
    order_no: string;
    r_type: string;
}
export const orderCancel = (params: OrderCancelParams) => http.post('main/order/cancel', params);

/**
 * 重新支付
 * @param { String } order_no 订单号【必要】
 * @param { number } pay_type 支付类型 【必要】
 * @param { number } pay_source 支付来源 【】
 * @param { string } payment_plan 分期支付 【可选 京东支付需要】
 * @returns
 */
export const orderPay = (data: { order_no: string; pay_type: number; pay_source: number; payment_plan?: string }) =>
    http.post('main/order/pay', data);

/**
 * 微信小程序-支付 miniprogram mp
 * @param { number } pay_type 支付类型
 * @param { string } payment_plan 分期支付
 * @param { string } order_no 订单号
 */
export const orderPay_mp = (data: { pay_type: number; payment_plan: string; order_no: string }) =>
    http.post('main/order/pay', data);

/*
 * 完成订单
 * @parma {String} order_no 订单号【必要】
 * @returns
 */
export const orderFinish = (order_no: string) => http.post('main/order/finish', { order_no });

/*
 * 申请退款
 * @parma {String} order_no 订单号【必要】
 * @parma {String} og_ids 订单商品-id【必要】
 * @parma {String} m_type 退款类型 1：退款 2：退货退款
 * @parma {String} r_type 退款原因
 * @returns
 */
interface OrderRefunParams {
    order_no: string;
    og_ids: string;
    m_type: string;
    r_type: string | number;
}
export const orderRefund = (params: OrderRefunParams) =>
    http.post('main/order/refund', params, { custom: { loading: true, title: '正在提交' } });

/*
 * 可用优惠券
 * @parma {String} gcombines 查询的商品json字符串
 * @returns
 */
interface UsableCouponsParams {
    gcombines: string;
    coupon_id: string;
}
export const usableCoupons = (params: UsableCouponsParams) => http.post('main/order/usable-coupons', params);

/*
 * 退款列表
 * @parma {String} page 查询的商品json字符串
 * @returns
 */
export const orderRlist = (page: string | number) => http.post('main/order/rlist', { page });

/*
 * 退款详情
 * @parma {String} refund_no 订单号
 * @returns
 */
export const orderRinfo = (refund_no: string) => http.post('main/order/rinfo', { refund_no });

/*
 * 退款物流
 * @parma {String} refund_no 订单号
 * @returns
 */
interface OrderRexpressParams {
    refund_no: string; // 退款单号【必要】
    mail_no: string; // 物流单号【必要】
    express_name: string; // 物流名称【必要】
    express_code: string; // 物流code
}
export const orderRexpress = (params: OrderRexpressParams) => http.post('main/order/rexpress', params);

/*
 * 物流查询
 * @parma {String} order_no 订单号
 * @returns
 */
export const orderExpress = (order_no: string) => http.post('main/order/express', { order_no });

/*
 * 小程序订单免邮价
 * @returns
 */
export const orderFreight = () => http.post('main/order/freight', {});

/**
 * 微信小程序-获取收银台支付列表
 * @param {String} order_no 订单号
 */
export const orderPayList = (order_no: string) => http.post('main/cashier/list', { order_no });

/**
 * 异步 轮询获取订单支付状态 目前支持京东支付
 * @param {String} order_no 订单号
 * @param {number | string} order_type 订单类型
 */
export const getOrderPayStatus = (data: { order_no: string; order_type: number | string }) =>
    http.post('main/order/order-status', data);

/**
 * 删除订单
 * @param {String} order_no 订单号
 */
export const deleteOrder = (params) => http.post('main/goods-store/delete-goods', params);
/**
 * 上下架
 * @param {String} status  0 上架 1 下架
 * @param {String} goods_ids 商品id 多个商品以,分隔
 */
export const setGoodsStatus = (params) => http.post('/main/goods-store/set-goods-status', params);
