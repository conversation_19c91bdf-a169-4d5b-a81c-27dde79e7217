<template>
  <view class="tasks">
    <!-- 标题 -->
    <view class="title-container u-flex u-row-between u-col-center">
      <view class="title">会员任务</view>
      <!-- 根据限制的数量显示 -->
      <view class="more" v-if="isMore" @click="moreTask">
        查看更多任务
        <image class="arrow" src="https://wpm-cdn.dreame.tech/images/202309/64f694de656ac4151723914.png" alt=""></image>
      </view>
    </view>
    <!-- 列表整体盒子 -->
    <view class="task-list">
      <!-- 每块的盒子 -->
      <view class="task-item u-flex u-row-between u-col-center" v-for="(item, index) in taskList" :key="index">
        <view class="task-left">
          <view class="name u-flex u-col-center">
            <text>{{ item.name }}</text>
            <text class="week" v-if="item.eventLimitNum">({{ item.currentPeriodEventNum }}/{{
              item.eventLimitNum
            }})</text>
            <image @click="showDetail(item)" style="height:38rpx;width: 38rpx;margin-left:12rpx ;"
              src="https://wpm-cdn.dreame.tech/images/202307/709391-1689660485571.png" alt=""></image>
          </view>
          <view class="integra">
            <view class="integral" v-if="item.point > 0">
              <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6880785584c495440011529.png" />
              <text>+{{ item.point }}</text>
            </view>
            <!-- <text class="separate" v-if="item.point > 0 && item.grow > 0">|</text> -->
            <view class="integral" v-if="item.grow > 0">
              <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6880e70cab7e47020010462.png" />
              <text>+{{ item.grow }}</text>
            </view>
          </view>
        </view>
        <block v-if="item.code==='app_invite_reg'">
           <view class="task-btn" @click="doTask(item)"
          :style="item.completed ? 'background: #E2E2E2; color: #A6A6A6;' : ''">
          {{ item.completed ? '已完成' : "去邀请" }}</view>
        </block>
        <block v-else>
          <view class="task-btn" @click="doTask(item)"
          :style="item.completed ? 'background: #E2E2E2; color: #A6A6A6;' : ''">
          {{ item.completed ? '已完成' : "去完成" }}</view>
        </block>
      </view>
    </view>
    <CustomModal :show="isShow" width="616rpx"
      contentStyle="color:#404040;font-weight:400;font-size: 32rpx;text-align: center;"
      confirmStyle="width: 524rpx; color: #8C6533; margin-bottom: -20rpx;" :content="taskDetail" confirmText="知道了"
      @confirm="isShow = false">
    </CustomModal>
    <Live :isShow="false" ref="live"></Live>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { VIPModule } from '@/store/modules/vip';
import Live from '@/components/Live/Live.vue';
import Utils from '@/common/Utils';

@Component({
  components: {
    Live
  }
})
export default class MemberTasks extends Vue {
  $refs!: {
    live: Live;
  };

  @Prop({ type: Boolean, default: false })
  readonly isMore!: Boolean;

  @Prop({ type: Number, default: 99999999 })
  readonly limit;

  public isShow: Boolean = false // 规则弹窗
  public taskDetail: String = '' // 规则内容

  get taskList(): Array<any> {
    const list = JSON.parse(JSON.stringify(VIPModule.taskList))
    return list.splice(0, this.limit)
  }

  showDetail(task) {
    console.log('%c task: ', 'font-size:16px;background-color: #2EAFB0;color:#fff;', task)
    this.taskDetail = task.descri
    this.isShow = true
  }

  moreTask() {
    Utils.navigateTo('/pagesA/missionCenter/missionCenter')
  }

  doTask(item) {
    console.log(item);

    if (item.completed) return
    switch (item.code) {
      case 'view_goods':
        this.goToShop()
        break;
      case 'goods_add_cart':
        this.goToShop()
        break;
      case 'buy_new_products':
        this.goToShop()
        break;
      case 'day_activity': // 访问会员商城
         Utils.navigateTo('/pagesA/point/shop_point')
        break;
      case 'buy_appoint_goods':
        this.goToShop()
        break;
      case 'buy_main_machine':
        this.goToShop()
        break;
      case 'buy_parts':
        this.goToShop(20)
        break;
      case 'invite_buy':
        Utils.navigateTo('/pagesA/recommend/recommend')
        break;
      case 'reg_sn':
        Utils.navigateTo('/pagesC/serve/serve')
        break;
      case 'evaluate_main_machine':
        Utils.navigateTo('/pagesB/evaluate/myEvaluate')
        break;
      case 'evaluate_parts':
        Utils.navigateTo('/pagesB/evaluate/myEvaluate')
        break;
      case 'share_goods':
        this.goToShop()
        break;
      case 'invite_reg':
        Utils.navigateTo('/pagesA/recommend/recommend')
        break;
      case 'follow_official_account':
        Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent('https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=**********&idx=1&sn=69a3db2d520c0151d8d6e5a9a82f8ef6&chksm=ce51b09ef92639880531af2392c71e94e559b96dfecf1ee98812a9319654e002224ab6ab2060#rd')}`)
        break;
      case 'add_wechat':
        Utils.navigateTo(`/pagesA/memberShip/memberShip?typeCode=exclusive`)
        break;
      case 'f_pair': // 跳转设备页
        Utils.messageChannel('navigation', { type: 'device', path: 'home/device' })
        break;
      case 'start_clean': // 跳转设备页
        Utils.messageChannel('navigation', { type: 'device', path: 'home/device' })
        break;
      case 'login': // app登录
        break;
      case 'clean_area': // 清扫面积
        Utils.messageChannel('navigation', { type: 'device', path: 'home/device' })
        break;
      case 'f_fill': // 完善个人信息
        // #ifdef H5
        Utils.messageChannel('navigation', { type: 'mine', path: 'mine/accountSetting' })
        // #endif
        // #ifdef MP-WEIXIN
        Utils.navigateTo('/pagesA/userInfo/userInfo')
        // #endif
        break;
      case 'signin': // 签到
        Utils.navigateTo('/pagesA/daliyCheckIn/daliyCheckIn')
        break;
      case 'watch_live': // 直播
        this.$refs.live.getLiveList(true)
        break;
      case 'app_invite_reg': // 邀请好友
        // this.$refs.live.getLiveList(true)
        this.$emit('inviteNewUser')
        break;
      case 'f_video': // 使用视频管家
        Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' })
        break;
      case 'accessory_usage': // 查看耗材计时
        Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' })
        break;
      default:
        break;
    }
  }

  goToShop(tid?: any) {
    // #ifdef MP-WEIXIN
    uni.switchTab({ url: `/pages/shop/shop${tid ? `?tid=${tid}` : ''}` })
    // #endif
    // #ifndef MP-WEIXIN
    uni.navigateTo({ url: `/pages/shop/shop${tid ? `?tid=${tid}` : ''}` })
    // #endif
  }

  created() {
    VIPModule.getTaskList()
  }
}
</script>

<style lang="scss">
$text-color-more: #7D7D7D;

.tasks {
  width: 100%;
  // background-color: #FFFEFF;
  background-color: #F4F4F4;
  // border-radius: 24rpx;
  padding: 46rpx 32rpx 32rpx;

  .title-container {
    width: 100%;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: $text-color-primary;
    }

    .more {
      font-size: 28rpx;
      color: $text-color-more;
      line-height: 38rpx;

      .arrow {
        height: 38rpx;
        width: 38rpx;
      }
    }
  }

  .task-list {
    overflow-y: scroll;
    margin-top: 24rpx;
    max-height: calc(100vh - 596rpx);
    border-radius: 24rpx;
    background-color: $fill-color-bg-white;
    padding-bottom: 38rpx;

    .task-item {
      height: 118rpx;
      padding: 38rpx 32rpx 0;

      .task-left {

        .name {
          font-size: 28rpx;
          font-weight: 500;
          color: $text-color-primary;
          line-height: 38rpx;

          .week {
            margin-left: 10rpx;
          }
        }

        .integra {
          display: flex;
          align-items: center;
          margin-top: 8rpx;
          color: #F3558C;

          .integral {
            @include flex(row, center, center, none);

            &+.integral {
              margin-left: 24rpx;
            }

            // color: $text-color-more;
            font-size: 24rpx;
            color: #F3558C;

            image {
              width: 28rpx;
              height: 28rpx;
              margin-right: 8rpx;
            }
          }

          .separate {
            display: inline-block;
            width: 32rpx;
            color: #D8D8D8;
            font-size: 20rpx;
            text-align: center;
          }
        }
      }

      .task-btn {
        width: 154rpx;
        height: 60rpx;
        line-height: 60rpx;
        color: #ffffff !important;
        background: linear-gradient(90deg, rgba(255, 62, 74, 0) 0%, rgba(255, 118, 216, 0.41) 100%), linear-gradient(180deg, #F33C4B -1%, #F33C4B 18%, #FF4230 103%, #E7322B 129%);
        // background: $brand-color-normal;
        border-radius: 96rpx;

        // color: #6C4314;
        font-weight: 500;
        font-size: 28rpx;
        text-align: center;
      }
    }
  }
}
</style>
