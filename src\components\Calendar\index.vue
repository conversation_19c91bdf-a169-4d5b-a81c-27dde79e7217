<template>
  <!-- 打卡日历页面 -->
  <view class='all' :style="{ 'margin-top': isShowDetail ? '46rpx' : '40rpx' }">
    <view v-if="isShowDetail">
      <view class="bar">
        <!-- 上一个月 -->
        <view class="previous" @click="changeMonth(-1)">

        </view>
        <!-- 显示年月 -->
        <view class="date">{{ nowYear || "--" }}年{{ nowMonth || "--" }}月</view>
        <!-- 下一个月 -->
        <view class="next" @click="changeMonth(1)">

        </view>
      </view>
      <!-- 显示星期 -->
      <view class="week-area">
        <view class="week-txt" v-for="(item, index) in weeksTxt[langType]" :key="index">{{ item }}</view>
      </view>
    </view>
    <view class="myDateTable">
      <view v-for="(item, j) in (isShowDetail ? calendarDays : simpleCalendarDays)" :key="j" class='dateCell'>
        <!-- 已签到日期 -->
        <view v-if="item.isSign == true" class='cell'
          :style="{ opacity: item.isThisMonth && (toMonth == nowMonth) ? '' : '0.5' }">
          <view class="signinactiveStyle">
            <!-- <view class="haveSignStyle">
              <view class="gou"></view>
            </view> -->
            <view class="haveSignStyle">
              <view class="signPointStyle">+{{ item.point }}</view>
              <view class="smile"></view>
            </view>
          </view>
          <view :class="String(item.simpleDate.split('.')[1]) === String(today) ? 'singalDate_default' : 'singalDate'">{{
            item.fullDate == toYear + '-' + toMonth + '-' + today ? '今天' : item.simpleDate }}
          </view>
        </view>
        <!-- 漏签 -->
        <view class="cell" v-else-if="item.isBeforeToday"
          :style="{ opacity: item.isThisMonth && (toMonth == nowMonth) ? '' : '0.5' }">
          <view class="lackinactiveStyle">
            <view class="lackSignStyle">
              <!-- <view class="queue">
                <view class="inActiveAdd"></view>
                <view class="inActivePointNum">10</view>
              </view> -->
              <view class="lackPointStyle">
                +10
              </view>
            </view>
          </view>
          <view class="singalDate">{{ item.simpleDate }}</view>
        </view>
        <!-- 今日未签到 -->

        <!-- <view class="cell" v-else-if="item.fullDate==toYear+'-'+toMonth+'-'+today">
          <view class="inactiveStyle">
            <view class="queue">
              <view class="inActiveAdd"></view>
              <view class="inActivePointNum">10</view>
            </view>
          </view>
          <view class="singalDate">今天</view>
        </view> -->
        <!-- 当前日期之后 -->
        <view class="cell" v-else :style="{ opacity: item.isThisMonth && (toMonth == nowMonth) ? '' : '0.5' }">
          <view class="afterStyle">
            <view class="inactiveStyle">
              <!-- <view class="queue">
                <view class="inActiveAdd"></view>
                <view class="inActivePointNum">{{ item.point }}</view>
              </view> -->
              <view class="pointStyle">
                +{{ item.point }}
              </view>
            </view>
          </view>
          <view class="singalDate">{{ item.simpleDate }}</view>
        </view>

      </view>
    </view>

  </view>
</template>

<script>
import Utils from '@/common/Utils';
export default {
  data() {
    return {
      calendarDays: [],
      simpleCalendarDays: [],
      SignData: [], // 已经签到的数据
      nowYear: 0, // 当前选的年
      nowMonth: 0, // 当前选的月
      today: parseInt(new Date().getDate()), // 系统本日
      toMonth: parseInt(new Date().getMonth() + 1), // 系统本月
      toYear: parseInt(new Date().getFullYear()), // 系统本年
      weeksTxt: {
        ch: ['日', '一', '二', '三', '四', '五', '六'],
        en: ['Sun', 'Mon', 'Tues', 'Wed', 'Thur', 'Fri', 'Sat'],
      },
      langType: 'zh',
      isDetail: false,
      isShowToday: false
    };
  },
  props: {
    isReplenishSign: { // 是否允许过期补签
      type: Boolean,
      default: false
    },
    isFullCalendar: { // 是否需要填满日历，前后空的格子填入上/下个月的日期
      type: Boolean,
      default: true
    },
    isShowDetail: {
      type: Boolean,
      default: false
    },
    signDataTotal: {
      type: Object,
      default: () => ({
        dataSource: [],
        continueDay: 0,
        todaySigned: false,
        addPoint: 10
      })
    },
    yearMonth: { // 2022-01 这种格式，默认当前年月
      type: String,
      default: new Date().getFullYear() + '-' + new Date().getMonth() + 1
    }
  },
  created() {
    if (!(/en|ch/g.test(this.langType))) {
      this.langType = 'ch'; // 非中英，则固定中文
    }
    const ymArr = this.yearMonth.split('-');
    this.isDetail = this.isShowDetail;
    this.isShowToday = this.todaySigned
    this.buildCalendar(ymArr[0], ymArr[1]);
  },
  watch: {
    signDataTotal: {
      deep: true,
      handler() {
        this.onSignDataChange()
      }
    },
    isShowDetail: 'showDetail'
  },
  computed: {
    dataSource() { return this.signDataTotal.dataSource },
    continueDay() { return this.signDataTotal.continueDay },
    todaySigned() { return this.signDataTotal.todaySigned },
    addPoint() { return this.signDataTotal.addPoint }
  },
  methods: {
    // 构建日历数据
    buildCalendar(y, m) {
      this.nowYear = y;
      this.nowMonth = m;
      this.calculateEmptyGrids(y, m);
      this.calculateDays(y, m);
      if (this.isFullCalendar) {
        this.fullCell()
      }
    },
    // 监听到签到数据源改变
    onSignDataChange(newData, oldData = []) {
      this.SignData = newData;
      this.matchSign(newData);
    },
    showDetail(newVal) {
      this.isDetail = newVal;
      if (!this.isDetail) {
        // 非详情的时候重新构建日历
        const ymArr = this.yearMonth.split('-');
        this.buildCalendar(ymArr[0], ymArr[1]);
        this.$emit('dateChange', this.nowYear + '-' + this.nowMonth);
      }
      this.matchSign();
    },
    // 匹配标记已经签到的日子
    matchSign() {
      const signs = this.dataSource
      const daysArr = this.calendarDays;
      this.isShowToday = this.todaySigned
      for (let i = 0; i < signs.length; i++) {
        const current = this.$u.timeFormat(signs[i].eventTime, 'yyyy-mm-dd')
        const dayPoint = signs[i].numValue

        // let current = new Date(this.toIOSDate(signs[i])).getTime(); // ios只认/格式的日期
        for (let j = 0; j < daysArr.length; j++) {
          if (current == this.formatDate(daysArr[j].fullDate)) {
            daysArr[j].isSign = true;
            daysArr[j].point = dayPoint
          }
        }
      }

      // 因为后端返回数据延迟问题 需要立即判断当天是否签到此处不要加0 因为fulldate里面的数据也没加0
      for (let j = 0; j < daysArr.length; j++) {
        if ((this.toYear + '-' + this.toMonth + '-' + this.today) == daysArr[j].fullDate && this.isShowToday) {
          daysArr[j].isSign = true;
          daysArr[j].point = this.addPoint
        }
      }

      // 再次判断日期在当天之前还是之后
      daysArr.map((item, index) => {
        if (new Date(this.toIOSDate(item.fullDate)).getTime() - new Date(this.toYear + '/' + this.toMonth + '/' + this.today).getTime() >= 0) {
          item.isBeforeToday = false
        } else {
          item.isBeforeToday = true
        }
      })

      // 判断当前第一天是不是大于当天日期,并且大于几天
      const currentMonthFirstDay = (new Date(this.toIOSDate(daysArr[0].fullDate)).getTime() - new Date(this.toYear + '/' + this.toMonth + '/' + this.today).getTime()) / 1000 / 24 / 60 / 60
      if (currentMonthFirstDay > 0) {
        if (currentMonthFirstDay >= 6) {
          daysArr.forEach((item, index) => {
            item.point = 20
          })
        } else if (currentMonthFirstDay < 7) {
          daysArr.map((item, index) => {
            if (index >= this.continueDay - currentMonthFirstDay + 1) {
              item.point = 20
            } else {
              item.point = 10
            }
          })
        }
      }
      let currentIndex;
      daysArr.forEach((item, index) => {
        if (item.fullDate === this.toYear + '-' + this.toMonth + '-' + this.today) {
          currentIndex = index
        }
      })

      if (!this.isDetail) {
        // 获取当天是星期几
        const weekNum = new Date().getDay()
        const cloneDaysArr = uni.$u.deepClone(daysArr)

        const sevenDays = cloneDaysArr.slice(currentIndex - weekNum, currentIndex - weekNum + 7);
        sevenDays.forEach((item, index) => {
          if (index > weekNum + Number(this.continueDay)) {
            item.point = 20
          }
        })
        this.simpleCalendarDays = sevenDays
      } else {
        // 计算当天的日期后几天 是20
        daysArr.forEach((item, index) => {
          if (index > currentIndex + Number(this.continueDay)) {
            item.point = 20
          }
        })
        this.calendarDays = daysArr;
      }
    },
    formatDate(str) {
      // 根据 - 符号拆分
      return str
        .split('-')
        .map((item) => {
          // +item 将item字符串转换为数字
          // 小于10的时候就补全一个前缀0
          if (+item < 10) {
            return '0' + +item;
          }

          // 大于10的时候不用补0
          return item;
        })
        .join('-'); // 最后重组回来
    },
    simplifyDate(str) {
      // 简化日期显示
      return str
        .split('-')
        .map((item) => {
          // +item 将item字符串转换为数字
          // 小于10的时候就补全一个前缀0
          if (+item < 10 && item.charAt(0) == 0) {
            return item.slice(1, 2);
          }

          // 大于10的时候不用补0
          return item;
        })
        .join('-'); // 最后重组回来
    },
    // 计算当月1号前空了几个格子，把它填充在calendarDays数组的前面
    calculateEmptyGrids(year, month) {
      // 计算每个月时要清零
      this.calendarDays = [];
      const firstDayOfWeek = this.getFirstDayOfWeek(year, month);
      if (firstDayOfWeek > 0) {
        for (let i = 0; i < firstDayOfWeek; i++) {
          this.calendarDays.push({
            date: null, // 显示的日期
            fullDate: null, // 日期yyyy-mm-dd格式
            simpleDate: null,
            isBeforeToday: true, // 今日之前
            isSign: false, // 是否签到
            isThisMonth: false, // 是本月,
            point: 10
          });
        }
      }
    },
    // 绘制当月天数占的格子，并把它放到days数组中
    calculateDays(year, month) {
      const thisMonthDays = this.getMonthDayLength(year, month);
      const toDate = new Date(this.toYear + '/' + this.toMonth + '/' + this.today);
      for (let i = 1; i <= thisMonthDays; i++) {
        const fullDate = year + '-' + month + '-' + i;
        const simpleDate = month + '.' + i;
        const isBeforeToday = new Date(this.toIOSDate(fullDate)) < toDate;
        this.calendarDays.push({
          date: i,
          fullDate,
          simpleDate,
          isBeforeToday,
          isSign: false,
          isThisMonth: true,
          point: 10
        });
      }
    },
    // 切换控制年月，上一个月，下一个月
    changeMonth(type) {
      const nowYear = parseInt(this.nowYear);
      const nowMonth = parseInt(this.nowMonth);
      const newObj = this.getOperateMonthDate(nowYear, nowMonth, type);
      if (parseInt((newObj.year - this.toYear) * 12) + newObj.month - this.toMonth > 1) {
        Utils.Toast('往后最多查看1月', 1000, false);
        return;
      }

      if (parseInt((this.toYear - newObj.year) * 12) + this.toMonth - newObj.month > 11) {
        Utils.Toast('往前最多查看11月', 1000, false);
        return;
      }
      this.buildCalendar(newObj.year, newObj.month); // 重新构建日历数据
      this.$emit('dateChange', this.nowYear + '-' + this.nowMonth);
    },
    // 获取当月共多少天，也就是获取月的最后一天
    getMonthDayLength(year, month) {
      return new Date(year, month, 0).getDate()
    },
    // 获取当月第一天星期几
    getFirstDayOfWeek(year, month, day = 1) {
      return new Date(Date.UTC(year, month - 1, day)).getDay();
    },
    toIOSDate(strDate) { // iso不认识"-"拼接的日期，所以转/
      return strDate ? strDate.replace(/-/g, '/') : strDate;
    },
    // 需要填满格子，上/下个月的日期拉出来填满格子
    fullCell() {
      const endDay = this.getMonthDayLength(this.nowYear, this.nowMonth);
      const beforeEmptyLength = this.getFirstDayOfWeek(this.nowYear, this.nowMonth);
      const afterEmptyLength = 6 - this.getFirstDayOfWeek(this.nowYear, this.nowMonth, endDay);
      const last = this.getOperateMonthDate(this.nowYear, this.nowMonth, -1);
      const lastMonthEndDay = this.getMonthDayLength(last.year, last.month);
      for (let i = 0; i < beforeEmptyLength; i++) {
        const date = lastMonthEndDay - beforeEmptyLength + i + 1;
        this.calendarDays[i].date = date;
        this.calendarDays[i].fullDate = last.year + '-' + last.month + '-' + date;
        this.calendarDays[i].simpleDate = last.month + '.' + date;
      }
      const next = this.getOperateMonthDate(this.nowYear, this.nowMonth, 1);
      for (let i = 1; i <= afterEmptyLength; i++) {
        this.calendarDays.push({
          date: i, // 显示的日期
          simpleDate: next.month + '.' + i, // 日期mm-dd格式
          fullDate: next.year + '-' + next.month + '-' + i, // 日期yyyy-mm-dd格式
          isBeforeToday: false, // 今日之前
          isSign: false, // 是否签到
          isThisMonth: false, // 是本月
          point: 10
        });
      }
    },
    // 获取加/减一个月的日期
    getOperateMonthDate(yy, mm, num) {
      let month = parseInt(mm) + parseInt(num);
      let year = parseInt(yy);
      if (month > 12) {
        month = 1;
        year++;
      } else if (month < 1) {
        month = 12;
        year--;
      }
      return {
        month,
        year,
      }
    },
  }
}
</script>

<style lang='scss' scoped>
.all {

  .bar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 32rpx 0rpx;
    // padding: 10rpx;
    align-items: center;

    .previous {
      width: 46rpx;
      height: 46rpx;
      background-image: url("https://wpm-cdn.dreame.tech/images/202307/333053-1689574488935.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .date {
      font-size: 32rpx;
      font-weight: 500;
      color: $text-color-regular;
      line-height: 42rpx;
    }

    .next {
      width: 46rpx;
      height: 46rpx;
      background-image: url("https://wpm-cdn.dreame.tech/images/202307/565403-1689574576427.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}

.bar .barbtn {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}

.all .week-area {
  display: flex;
  justify-content: space-between;
  margin: 32rpx auto 16rpx;
  flex-wrap: wrap;
}

.all .week-txt {
  text-align: center;
  font-size: 20rpx;
  font-family: PingFang SC-常规体, PingFang SC;
  font-weight: 400;
  color: $text-color-disable;
  line-height: 27rpx;
  width: 14%;
}

.myDateTable {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  row-gap: 32rpx;

  .dateCell {
    display: flex;
    // justify-content: space-between;
    flex-direction: column;
    align-items: center;
    width: 14%;

    .cell {
      width: 100%;

      .signinactiveStyle {
        height: 99rpx;
        // background: #ffd183;
        border-radius: 4rpx;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .haveSignStyle {
          width: 76rpx;
          height: 99rpx;
          background: rgba(203, 183, 148, 0.2);
          border-radius: 16rpx;
          opacity: 1;
          padding: 15rpx 13rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .signPointStyle {
            font-size: 32rpx;
            font-family: PingFang SC-常规体, PingFang SC;
            font-weight: 500;
            color: $fill-color-primary-active;
            line-height: 42rpx;
            margin-bottom: 28rpx;
          }

          .smile {
            position: absolute;
            background-image: url("https://wpm-cdn.dreame.tech/images/202307/339344-1689586831957.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
          }

          .gou {
            width: 23rpx;
            height: 15rpx;
            background-image: url("@/static/checkIn/duigou.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }
      }

      .lackinactiveStyle {
        height: 99rpx;
        border-radius: 16rpx;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .lackSignStyle {
          width: 76rpx;
          height: 99rpx;
          border-radius: 16rpx;
          opacity: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2rpx solid $fill-color-dark;
          background: rgba(255, 255, 255, 0.06);

          .queue {
            width: 57rpx;
            height: 57rpx;
            background: rgba(200, 200, 200, 0.2);
            opacity: 1;
            border-radius: 100%;
            font-size: 27rpx;
            font-family: MiSans-Semibold, MiSans;
            font-weight: 600;
            color: #c2c2c2;
            display: flex;
            align-items: center;
            justify-content: center;

            .inActiveAdd {
              width: 19rpx;
              height: 19rpx;
              background-image: url("@/static/checkIn/grayAdd.png");
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }

            .inActivePointNum {
              //   line-height: 31rpx;
            }
          }

          .lackPointStyle {
            font-size: 32rpx;
            font-family: PingFang SC-常规体, PingFang SC;
            font-weight: 500;
            color: $text-color-disable;
            line-height: 42rpx;
            margin-bottom: 28rpx;
          }
        }
      }

      .afterStyle {
        height: 99rpx;
        // background: #ffd183;
        border-radius: 16rpx;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .inactiveStyle {
        // width: 100%;
        height: 99rpx;
        background: rgba(255, 255, 255, 0.06);
        border-radius: 16rpx;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 76rpx;
        border: 2rpx solid $fill-color-dark;

        .queue {
          width: 57rpx;
          height: 57rpx;
          background: rgba(255, 209, 131, 0.2);
          opacity: 1;
          border-radius: 100%;
          font-size: 27rpx;
          font-family: MiSans-Semibold, MiSans;
          font-weight: 600;
          color: #e5b562;
          display: flex;
          align-items: center;
          justify-content: center;

          .inActiveAdd {
            width: 19rpx;
            height: 19rpx;
            background-image: url("@/static/checkIn/add1.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }

          .inActivePointNum {
            //   line-height: 31rpx;
          }
        }

        .pointStyle {
          font-size: 32rpx;
          font-family: PingFang SC-常规体, PingFang SC;
          font-weight: 500;
          color: $text-color-disable;
          line-height: 42rpx;
          margin-bottom: 28rpx;
          // width: 50rpx;
        }
      }

      .singalDate {
        height: 27rpx;
        font-size: 20rpx;
        font-family: PingFang SC-常规体, PingFang SC;
        font-weight: 400;
        color: $text-color-disable;
        line-height: 28rpx;
        margin-top: 10rpx;
        text-align: center;
      }

      .singalDate_default {
        height: 27rpx;
        font-size: 20rpx;
        font-family: PingFang SC-常规体, PingFang SC;
        font-weight: 400;
        color: #AB8C5E;
        line-height: 28rpx;
        margin-top: 10rpx;
        text-align: center;
      }
    }
  }
}

.dateCell .whiteColor {
  color: #fff;
}

.greenColor {
  color: #01b90b;
  font-weight: bold;
}

.bgWhite {
  background-color: #fff;
}

.bgGray {
  background-color: rgba(255, 255, 255, 0.42);
}

.bgBlue {
  font-size: 14px;
  background-color: #4b95e6;
}

.redColor {
  color: #ff0000;
}

.outSignStyle {
  border: 1px solid #ffffff;
  color: #ffffff;
}

.redDot {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: red;
}
</style>
