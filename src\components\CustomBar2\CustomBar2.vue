<template>
    <view class="header-container" :style="{
        height: `${pagePaddingTop}rpx`,
        'padding-top': `${statusBarHeight}rpx`,
        background,
        position,
        'z-index': zIndex
    }" :class="isHeaderTransparent ? '' : 'background'">
        <view class="back" :style="backIconStyle" v-if="isBack" @click="goBack"></view>
        <slot name="customTitle"></slot>
        <view class="back_activity" v-if="isBackActivity" @click="goBack"></view>
        <view class="title" :class="!isShowDetail ? 'u-line-1' : ''" :style="titleStyle">
            {{ title }}
            <view class="btns u-flex">
                <view v-if="isShowDetail" class="icon detail" @click="detail"></view>
                <view v-if="isQuestionMark" class="icon is_question_mark" @click="toQuestion"></view>
            </view>
        </view>
        <view class="btns u-flex">
            <view v-if="isShowCart" class="icon cart" @click="gotoCartPage">
                <view class="badge" v-if="cartListLen">{{ cartListLen }}</view>
            </view>
            <!-- <view v-if="isComparison" class="icon comparison" @click="toComparison"></view> -->
            <view v-if="isShowService" class="icon service" @click="handleContact"></view>
            <view v-if="isSearch" class="icon search" @click="toSearch"></view>
            <!-- #ifdef H5 -->
            <view v-if="isShowDelete" class="icon delete" @click="deleteAddress"></view>
            <!-- #endif -->
            <view v-if="isShowGrowthTask" class="icon growthTask" :style="growthTaskStyle" @click="grade">{{ growthTask
                }}
            </view>
            <view v-if="isShowExplanation" class="explanation" @click="showExplanation">
            <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870bb2547a482930012484.png" class="explanation-image"></image>
            <view class="explanation-text">注册延保活动说明</view>
        </view>
            <slot name="moreBtn"></slot>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { CartModule } from '@/store/modules/cart';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';

@Component
export default class Header extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Number, default: 4000000000 })
    readonly zIndex!: number;

    @Prop({ type: Boolean, default: true })
    readonly isBack!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isBackActivity!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isHeaderTransparent!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowCart!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowService!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isComparison!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isSearch!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isQuestionMark!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowDelete!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowDetail!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowGrowthTask!: Boolean;

    @Prop({ type: String, default: '' })
    readonly titleStyle: String;

    @Prop({ type: String, default: '' })
    readonly title: String;

    @Prop({ type: String, default: '' })
    readonly growthTask!: String;

    @Prop({ type: String, default: '' })
    readonly growthTaskStyle!: String;

    @Prop({ type: String, default: '' })
    readonly backTrigger;

    @Prop({ type: String, default: '' })
    readonly background;

    @Prop({ type: String, default: 'fixed' })
    readonly position;

    @Prop({ type: Boolean, default: false })
    readonly customBack!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowExplanation!: boolean;

    @Prop({ type: String, default: '' })
    readonly BackIcon: string;

    get cartListLen(): number {
        return CartModule.list.length || 0;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    get backIconStyle() {
        return {
            backgroundImage: this.BackIcon ? `url(${this.BackIcon})` : 'url(https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68761326b81407540010865.png)',
            backgroundSize: '100% 100%'
        };
    }

    goBack() {
        if (this.customBack) {
            this.$emit('back');
        } else {
            Utils.goBack(this.backTrigger);
        }
    }

    gotoCartPage() {
        if (this.wxAuth && this.isPhone) {
            uni.navigateTo({ url: '/pagesA/cart/cart' });
        } else {
            const target = 'gotoCartPage';
            UserModule.authFlow({ target });
        }
    }

    grade() {
        uni.navigateTo({ url: '/pagesA/grade/grade' });
    }

    handleContact(e) {
        this.$emit('handleContact');
    }

    goHome() {
        uni.reLaunch({
            url: '/pages/index/index',
        });
    }

    deleteAddress() {
        this.$emit('deleteAddress');
    }

    showExplanation() {
        this.$emit('showExplanation');
    }

    detail() {
        this.$emit('detail');
    }

    toComparison() {
        Utils.reportEvent('compare_click', {})
        Utils.navigateTo('/pagesA/product/comparison');
    }

    toSearch() {
        uni.navigateTo({ url: '/pagesA/search/search?search_type=' + 1 });
    }

    toQuestion() {
        Utils.navigateTo('/pagesB/giftCard/searchCodeNote');
    }
}
</script>
<style lang="scss" scoped>
.header-container {
    top: 0rpx;
    left: 0;
    right: 0;
    padding-left: 38rpx;

    display: flex;
    // #ifdef H5
    justify-content: space-between;
    // #endif
    // #ifdef MP-WEIXIN
    justify-content: flex-start;
    // #endif
    align-items: center;

    .title {
        font-size: 36rpx;
        font-weight: 600;
        color: $text-color-primary;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        word-break: break-all;
        // white-space: nowrap;

        .btns {
            margin-left: 12rpx;
        }
    }

    // #ifdef MP-WEIXIN
    .icon {
        margin-right: 32rpx;
    }

    // #endif

    .growthTask {
        font-size: 28rpx;
        font-family: PingFangSC-Regular;
        font-weight: normal;
        color: #3d3d3d;
        margin-right: 32rpx;
    }

    .back {
        width: 62rpx;
        height: 62rpx;
        // background-image: url('@/static/goodDetail/back.png');
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        margin-right: 32rpx;
    }

    .back_activity{
        width: 78rpx;
        height: 78rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202504/67fa2b6e3c0db2460011758.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 32rpx;
    }

    .cart {
        width: 64rpx;
        height: 64rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202306/120216-1686121914874.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 16rpx;
        // #endif
        position: relative;

        .badge {
            position: absolute;
            top: -4rpx;
            right: -8rpx;
            min-width: 28rpx;
            line-height: 28rpx;
            background: #AB8C5E;
            border-radius: 50%;
            text-align: center;
            font-size: 20rpx;
            color: #ffffff;
        }
    }

    .comparison {
        width: 58rpx;
        height: 38rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202306/993504-1686642096911.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 32rpx;
        // #endif
    }

    .search {
        width: 46rpx;
        height: 46rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202306/535513-1686106493238.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 32rpx;
        // #endif
    }

    .service {
        width: 64rpx;
        height: 64rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202307/245433-1690437168233.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 32rpx;
        // #endif
    }

    .delete {
        width: 38rpx;
        height: 38rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202307/472781-1689061684190.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 32rpx;
        // #endif
    }

    .detail {
        width: 46rpx;
        height: 46rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202307/013983-1689818403428.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .is_question_mark {
        width: 38rpx;
        height: 38rpx;
        margin-top: 4rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202312/656efc4a721514672088553.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }
}

.background {
    background-color: #ffffff;

    .back {
        width: 46rpx;
        height: 46rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202308/972703-1691654023213.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 32rpx;
    }

    .cart {
        width: 48rpx;
        height: 48rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202306/120216-1686121914874.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 32rpx;
        // #endif
    }

    .service {
        width: 48rpx;
        height: 48rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202411/6731597f91eec5980901513.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        // #ifdef H5
        margin-right: 48rpx;
        // #endif
    }
}

.explanation{
    width: 250rpx;
    height: 48rpx;
    border-radius: 129rpx;
    background: rgba(32, 32, 32, 0.52);
    font-family: PingFang SC;
    font-size: 22rpx;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 32rpx;
    .explanation-image{
      width: 30rpx;
      height: 30rpx;
      margin-right: 8rpx;
    }
}
</style>
