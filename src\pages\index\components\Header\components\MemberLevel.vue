<template>
    <view class="member-level">
        <view class="coin-points-item" @click="handlePointsClick">
            <image v-if="levlName" style="width: 40rpx; height: 40rpx;" :src="levelIcon" mode=""></image>
            <view class="coin-points-item-text">
                <view class="coin-points-item-text-value" :style="{ color: levelColor }">{{ levlName }}</view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { VIPModule } from '@/store/modules/vip';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component
export default class CoinPoints extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

     get levlName(): String {
        return VIPModule.basicInfo.level.name || '';
    }

    get level(): String {
        return VIPModule.basicInfo.level.level || '';
    }

    get levelIcon(): string {
        const level:any = VIPModule.basicInfo.level.level;
        // 定义等级与图片的映射关系
        const levelIcons: { [key: string]: string } = {
            v1: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687637a03aa022400011297.png',
            v4: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763749bba6d7690013034.png',
            v2: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763765cf8708500011384.png',
            v5: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763782964976160022687.png',
            v3: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763725e57ce9400010630.png',

        };

        // 默认使用v1等级的图片
        return levelIcons[level] || '';
    }

    // 根据等级获取对应的文本颜色
    get levelColor(): string {
        const level:any = VIPModule.basicInfo.level.level;
        const levelColors: { [key: string]: string } = {
            v1: '#CA7538', // 铜牌颜色（原颜色）
            v2: '#9E9E9E', // 银牌颜色（灰色系）
            v3: '#57412B', // 金牌颜色（金色系）
            v4: '#325F83', // 白金颜色（银色系）
            v5: '#DBBB71' // 钻石颜色（浅蓝色系）
        };

        return levelColors[level] || levelColors.v1;
    }

    handlePointsClick() {
        Utils.navigateTo('/pages/vipCenter/vipCenter');
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }

    // 组件创建时调用获取会员信息
    created() {
        this.fetchVIPInfo();
    }

    // 监听组件显示事件
    onShow() {
        this.fetchVIPInfo();
    }

    // 封装获取会员信息的方法
    async fetchVIPInfo() {
        try {
            await VIPModule.getBasicInfo();
        } catch (error) {
            console.error('获取会员信息失败:', error);
            // 可以添加错误提示或重试逻辑
        }
    }
}
</script>
<style lang="scss" scoped>
.member-level {
    display: flex;
    align-items: center;
    gap: 30rpx;
    height: 114rpx;
    margin-right: 20rpx;
}
.coin-points-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: start;
}
.coin-points-item-text{
    margin-left: 8rpx;
    text-align: center;
    transform: translateY(2rpx);
}
.coin-points-item-text-value{
   font-family: MiSans;
   font-size: 28rpx;
   font-weight: 500;
   letter-spacing: normal;
//    color: #CA7538;
   line-height: 1;
}
.coin-points-item-text-title{
   font-family: MiSans;
   font-size: 20rpx;
   letter-spacing: normal;
   color: #777777;;
   line-height: 1;
}
</style>
