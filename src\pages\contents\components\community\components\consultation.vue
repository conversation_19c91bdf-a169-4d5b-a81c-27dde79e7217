<template>
    <view class="comment_container">
        <!-- <custom-toast ref="customToast" /> -->
        <div class="comment_list">
            <view class="comment_item" v-for="(item, index) in c_list" :key="index">
                <view class="comment_item_flex">
                    <view class="comment_item_flex_left">
                        <view class="comment_item_title" @click="viewDetail(item, true)">{{ item.title }}</view>
                        <view class="comment_item_top">
                            <view class="comment_item_top_left" @click="jumpHome(item)">
                                <view style="display: flex; align-items: center; position: relative">
                                    <img
                                        :src="item.author_avatar"
                                        mode="aspectFill"
                                        class="comment_item_top_left_img"
                                    />
                                    <img
                                        v-if="item.user_avatar_icon"
                                        :src="item.user_avatar_icon"
                                        class="comment_item_top_left_tag"
                                    />
                                </view>
                                <view class="comment_item_top_left_name u-line-1">{{ item.author }}</view>
                                <img
                                    v-if="item.creator == '3587481'"
                                    class="custom_imag"
                                    style="
                                        margin-left: 10rpx;
                                        width: 128rpx;
                                        height: 36rpx;
                                        border-radius: 0rpx !important;
                                    "
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png"
                                    alt=""
                                />
                            </view>
                            <view class="comment_item_bottom">
                                <view class="comment_item_bottom_oper">
                                    <view class="oper_item" @click="handleOperFlow(item, 'praise')"
                                        ><img
                                            :src="
                                                !item.isPraise
                                                    ? 'https://wpm-cdn.dreame.tech/images/202411/588996-1731294685088.png'
                                                    : 'https://wpm-cdn.dreame.tech/images/2024010/449434-1730107172922.png'
                                            "
                                        /><view class="oper_num">{{ clicks(item.praiseNum, 0) }}</view></view
                                    >
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="comment_item_imgs" @click="() => item.type === 2 && viewDetail(item)">
                        <block v-if="item.type === 1">
                            <image
                                mode="aspectFill"
                                class="comment_item_imgs_item"
                                :src="item.cover_image"
                                @click="viewDetail(item, true)"
                            />
                        </block>
                        <block v-else-if="item.type === 2">
                            <image class="comment_item_imgs_item" mode="aspectFill" :src="item.cover_image" />
                            <view class="comment_item_video_icon">
                                <img src="https://wpm-cdn.dreame.tech/images/2024010/586856-1730278888318.png" alt="" />
                            </view>
                        </block>
                    </view>
                </view>
            </view>
        </div>
        <view class="no_more" v-if="finished">没有更多了~</view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import {
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
    userFollowDelete,
    userRelation,
    userFollow,
} from '@/http/requestGo/community';
import { UserModule } from '@/store/modules/user';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { compressImageUrl } from '@/utils/imageCompression';
const FOLLOW_BTN_OPTION = [
    { value: 0, label: '关注' },
    { value: 1, label: '已关注' },
    { value: 2, label: '互相关注' },
];
@Component({})
export default class Contents extends Vue {
    @Prop({ type: Array })
    public flowList: Array<any>;

    @Prop({ type: Boolean })
    public finished: Boolean;

    @Prop({ type: Number })
    public relationStatus: number;

    @Prop({})
    public topicId: any;

    @Prop({ type: Boolean, default: false })
    public isJump: boolean;

    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    public index: number = 0;

    public creator: any = -1;

    public c_list: Array<any> = [];
    public did: string = '';
    public page: number = 1;
    public isShow: boolean = true;
    public currentTab: string = 'tab-0'; // tab定位锚点

    // #ifdef MP-WEIXIN
    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    get user_id(): any {
        let user_id: any = 0;
        // #ifdef MP-WEIXIN
        user_id = UserModule.userInfo.user_id;
        // #endif

        // #ifdef H5
        user_id = UserModule.user_id;
        // #endif
        return user_id;
    }

    @Watch('flowList', { deep: true })
    flowListChange(newVal: Array<any>) {
        if (newVal.length) {
            this.c_list = newVal.map((i) => ({
                ...i,
                isPraise: i.is_praise,
                praiseNum: i.praise,
                isFavorite: i.is_favorite,
                favoriteNum: i.favorite,
            }));
        } else {
            this.c_list = [];
        }
    }

    clicks(val, index) {
        if (Number(val) === 0) {
            return ['点赞', '收藏', '评论'][index];
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    relationText(item) {
        return FOLLOW_BTN_OPTION.find((v) => v.value === item.follow_status)?.label || '关注';
    }

    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.creator });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                this.c_list[this.index].follow_status = res.data.follow_status;
            });
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    handleUserFollow(item, index) {
        this.index = index;
        this.creator = item.creator;
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleUserFollow' });
            return;
        }
        // #endif

        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.creator);
            // const api = this.relationStatus === 0 ? userFollow : userFollowDelete;
            if (item.follow_status === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        item.follow_status = res.data.follow_status;
                        this.$emit('changeFollowStatus', item, res.data.follow_status);
                    });
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +this.creator });
                if (res.success) {
                    Utils.Toast('取消关注');
                    userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                        this.c_list[this.index].follow_status = res.data.follow_status;
                        this.$emit('changeFollowStatus', item, res.data.follow_status);
                    });
                }
                this.operContentOpen = false;
                setTimeout(() => {
                    this.operContent = { type: '' };
                }, 500);
            }
        }, 500);
    }

    async handleOperFlow(item, type) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                await this.handleOper(item, type);
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
        Utils.reportEvent('give_like', { title: item.title });
        // #ifdef H5
        await this.handleOper(item, type);
        // #endif
    }

    async handleOper(item, type) {
        let flag, res;
        // const defaultPraise = item.is_praise;
        if (item[type === 'praise' ? 'isPraise' : 'isFavorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            item[type === 'praise' ? 'isPraise' : 'isFavorite'] = flag;
            item[type === 'praise' ? 'praiseNum' : 'favoriteNum'] += +flag ? 1 : -1;
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = res.data.id;
        }
        // this.$emit('praise', id);
    }

    countCharacters(str) {
        let count = 0;
        for (let i = 0; i < str.length; i++) {
            const charCode = str.charCodeAt(i);
            // 中文字符的Unicode范围大致是0x4E00到0x9FA5
            if (charCode >= 0x4e00 && charCode <= 0x9fa5) {
                count += 2;
            } else {
                count += 1;
            }
        }
        return count;
    }

    viewDetail(item, isJump = false) {
        // #ifdef H5
        if (item.jump_url && isJump) {
            Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
            return;
        }
        // #endif
        const { content_id = '', type } = item;
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${this.topicId}`,
        );
    }

    jumpHome(item) {
        Utils.jumpPersonHome(item.creator);
    }

    handlePreviewImage(item, url) {
        uni.previewImage({
            current: url,
            urls: item.map((i) => i.url),
        });
    }

    onLoad() {}

    async handerShare(item) {
        this.$emit('share', item);
    }

    getImgUrl(url) {
        return compressImageUrl(url, {
            quality: 85,
            maxWidth: 600,
            maxHeight: 600,
            format: 'webp',
        });
    }
}
</script>

<style lang="scss">
.comment_container {
    .comment_item {
        background-color: #fff;
        width: calc(100% - 46rpx);
        border-radius: 8rpx;
        margin: 0 auto;
        padding: 32rpx;
        margin-bottom: 24rpx;
        .comment_item_flex {
            flex: 1;
            display: flex;
            justify-content: space-between;
            .comment_item_flex_left {
                width: calc(100% - 300rpx);
                padding-right: 32rpx;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .comment_item_title {
                    font-size: 32rpx;
                    font-weight: 500;
                    color: #404040;
                    line-height: 44rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    width: 100%;
                }
                .comment_item_top {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .user-header-relation {
                        padding: 12rpx 42rpx;
                        border-radius: 192rpx;
                        font-weight: 500;
                        font-size: 28rpx;
                        line-height: 36rpx;
                        margin-left: 16rpx;
                        background: #e8dec1;
                        color: #8c6533;

                        &.active {
                            border: 2rpx solid #121212;
                            background: #ffffff;
                            color: #121212;
                        }
                    }
                    &_left {
                        display: flex;
                        align-items: center;
                        position: relative;
                        img,
                        image {
                            object-fit: cover;
                            width: 40rpx;
                            height: 40rpx;
                            border-radius: 50%;
                        }
                        .comment_item_top_left_tag {
                            position: absolute;
                            width: 24rpx;
                            height: 24rpx;
                            left: 24rpx;
                            bottom: 0;
                        }

                        ::v-deep .custom_imag {
                            object-fit: contain;
                            border-radius: 0rpx !important;
                        }

                        &_name {
                            margin-left: 15rpx;
                            font-family: MiSans, MiSans;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #777777;
                            line-height: 40rpx;
                            text-align: left;
                            font-style: normal;
                            text-transform: none;
                            max-width: 400rpx;
                        }
                    }
                }
            }
            .comment_item_bottom {
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                    width: 31rpx;
                    height: 31rpx;
                }

                &_oper {
                    display: flex;
                    .oper_item {
                        display: flex;
                        padding-right: 20rpx;
                        img,
                        image {
                            margin-right: 8rpx;
                            object-fit: cover;
                        }
                        .oper_num {
                            min-width: 25px;
                            font-family: MiSans, MiSans;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #777777;
                            line-height: 31rpx;
                            text-align: center;
                            font-style: normal;
                            text-transform: none;
                        }
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
            .comment_item_imgs {
                img,
                image {
                    width: 300rpx;
                    height: 192rpx;
                    border-radius: 8rpx;
                }
            }
        }
    }
    .no_more {
        text-align: center;
        font-size: 24rpx;
        font-weight: 400;
        color: rgba(29, 30, 32, 0.4);
        line-height: 33px;
        margin-top: 50rpx;
    }
}
.share-btn {
    padding: 0;
    border: unset !important;
    @include flex($justify: center, $align: center);
}
</style>
