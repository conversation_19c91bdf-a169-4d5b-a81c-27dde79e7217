<template>
    <u-popup mode="center" :zoom="zoom" :show="show" v-prevent-scroll="show"  teleport="body"  :customStyle="{
        borderRadius: '24rpx',
        overflow: 'hidden',
        'box-shadow': boxShadow,
        background: backgroundColor,
        marginTop: `-${$u.addUnit(negativeTop)}`,
    }" :closeOnClickOverlay="closeOnClickOverlay" :safeAreaInsetBottom="false" :closeable="closeable"
        :duration="400" :overlayStyle="{
            backdropFilter: `blur(${backdropFilter})`,
        }" @click="clickHandler" @close="close" overlayOpacity=".6">
        <view :class="{ 'custom-modal': customModalPad, customModalStyle: customModalStyle}" class="u-modal" :style="{
            width: $u.addUnit(width),
            height: $u.addUnit(height),
        }">
            <lazy-image @click="closeIconClick" v-if="showClose" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202505/681c639eb4aeb7400011383.png" customStyle="width: 46rpx;height: 46rpx;position: absolute;top: 24rpx;right: 24rpx;"></lazy-image>
            <text class="u-modal__title s-title-medium" v-if="title" :style="titleStyle">{{ title }}</text>
            <!-- tip -->
            <!-- <view class="tip_arr" v-if="tipArr && tipArr.length">
                <view v-for="item in tipArr" :key="item" class="u-modal__tip__text">{{ item }}</view>
            </view> -->
            <view :class="{ 'u-modal__content': !customModalPad, default_content: customModalPad }" class="s-title-medium">
                <slot>
                    <text class="u-modal__content__text" :style="contentStyle">{{ content }}</text>
                </slot>
            </view>
            <view class="u-modal__button-group--confirm-button" v-if="$slots.confirmButton">
                <slot name="confirmButton"></slot>
            </view>
            <template v-else>
                <view class="u-modal__button-group">
                    <custom-button v-if="showCancelButton" class="btn" :customStyle="cancelStyle" :size="buttonSize"
                        @tap="cancelHandler" type="secondary" shape="circle">{{ cancelText }}
                    </custom-button>
                    <custom-button :openType="openType" v-if="showConfirmButton" :customStyle="confirmStyle" class="btn" :size="buttonSize"
                        @tap="confirmHandler" type="primary" shape="circle">{{ confirmText }}</custom-button>
                </view>
            </template>
        </view>
    </u-popup>
</template>

<script>
import props from './props.js';
export default {
    name: 'CustomModal',
    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],
    data() {
        return {
            loading: false,
        };
    },
    watch: {
        show(n) {
            // 为了避免第一次打开modal，又使用了异步关闭的loading
            // 第二次打开modal时，loading依然存在的情况
            if (n && this.loading) this.loading = false;
        },
    },
    methods: {
        closeIconClick() {
            this.$emit('close');
        },
        // 点击确定按钮
        confirmHandler() {
            // 如果配置了异步关闭，将按钮值为loading状态
            if (this.asyncClose) {
                this.loading = true;
            }
            this.$emit('confirm');
        },
        // 点击取消按钮
        cancelHandler() {
            this.$emit('cancel');
        },
        // 点击遮罩
        // 从原理上来说，modal的遮罩点击，并不是真的点击到了遮罩
        // 因为modal依赖于popup的中部弹窗类型，中部弹窗比较特殊，虽然有遮罩，但是为了让弹窗内容能flex居中
        // 多了一个透明的遮罩，此透明的遮罩会覆盖在灰色的遮罩上，所以实际上是点击不到灰色遮罩的，popup内部在
        // 透明遮罩的子元素做了.stop处理，所以点击内容区，也不会导致误触发
        clickHandler() {
            if (this.closeOnClickOverlay) {
                this.$emit('close');
            }
        },

        close() {
            if (this.closeable) {
                this.$emit('close');
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.default_content {
    width: 100%;
}

.custom-modal {
    padding: 0rpx !important;
}

.u-modal {

    .btn,
    ::v-deep button {
        width: 242rpx;
        height: 92rpx;
        line-height: 92rpx;
        border-radius: 192rpx;
        font-weight: 500;
        font-size: 32rpx;
    }

    @include flex(column, center, center);
    border-radius: 24rpx;
    padding: 56rpx 48rpx;
    overflow: hidden;

    &__title {
        color: $text-color-primary;
        text-align: center;
        margin-bottom: 32rpx;
    }

    &__content {
        @include flex;
        max-height: 500rpx;
        overflow-y: auto;
        margin-bottom: 48rpx;
        width: 100%;

        &__text {
            color: $text-color-regular;
            word-break: break-all;
            height: 100%;
        }
    }

    &__button-group {
        width: 100%;
        @include flex;

        &--confirm-button {
            flex-direction: column;
            line-height: 100rpx;
            text-align: center;
        }

        &__wrapper {
            flex: 1;
            @include flex;
            justify-content: center;
            align-items: center;
            height: 100rpx;

            &--confirm,
            &--only-cancel {
                border-bottom-right-radius: 24rpx;
                text-align: center;
            }

            &--cancel,
            &--only-confirm {
                border-bottom-left-radius: 24rpx;
                text-align: center;
            }

            &--hover {
                background-color: $u-bg-color;
            }

            &__text {
                color: $u-content-color;
                font-size: 34rpx;
                text-align: center;
            }
        }
    }
}
.customModalStyle{
    .u-modal__content{
        align-items: start;
        max-height: 1000rpx;
    }
}

.tip_arr {
    margin-bottom: 15rpx;
    .u-modal__tip__text {
        font-size: 25rpx;
        line-height: 1.2;
        font-weight: 400;
        color: #555;
        margin-top: 5rpx;
    }
}
</style>
