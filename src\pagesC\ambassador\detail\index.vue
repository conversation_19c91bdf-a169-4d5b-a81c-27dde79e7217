<template>
    <view class="container">
        <CustomBar
            hideContent
            title="推广赚佣金"
            :background="customBarBg"
            backIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c4e6b1a8f41090024046.png"
        >
            <template v-slot:operate>
                <!-- <view class="rule-wrap" @click="routeNext"> 活动规则 </view> -->
                <view class="right">
                    <view v-show="$isInApp() && isEmployee === true" class="share" @click="shareActivity"></view>
                </view>
            </template>
        </CustomBar>
        <view class="bg-wrapper">
            <view class="rule" v-show="$isInApp()" @click="routeNext"></view>
        </view>
        <view style="margin-top: 850rpx;background: #f3f3f7; z-index: 10">
            <MemberCard ref="MemberCard" @goToPurchaseRecord="goToPurchaseRecord" @withdraw="withdraw" @openRegisterPopup="openRegisterPopup"/>
            <view class="product-list-wrap" :style="{ height: `calc(${computedHeight} - 200rpx)` }">
                <!-- <view class="bg"></view> -->
                <view class="cover-img">选择商品</view>
                <ProductList ref="productListRef" @gotoGdetailPage="gotoGdetailPage" :style="{ height: `calc(${computedHeight} - 250rpx)` }"/>
            </view>
        </view>
        <view class="btn-operate" v-if="$isInApp()">
            <!-- <view v-if="isEmployee">
                <view class="u-flex">
                    <view class="operate-post" @click="onPost">保存海报</view>
                    <view class="operate-select" @click="$u.debounce(onSelect, 300)">选商品</view>
                </view>
                <view class="operate-share" @click="onShare">去赚钱</view>
            </view>
            <view v-else class="operate-share" @click="onShare">一键邀请</view> -->
            <view class="operate-share" @click="$u.debounce(onSelect, 300)">查看更多商品</view>
        </view>
        <canvas
            id="canvas"
            canvas-id="canvas"
            :width="canvasRealWidth"
            :height="canvasRealHeight"
            :style="{
                position: 'fixed',
                left: '-9999px',
                top: '-9999px',
                width: canvasRealWidth + 'px',
                height: canvasRealHeight + 'px',
            }"
        ></canvas>

        <!-- <MarkPopup ref="MarkPopup" :userId="employee.user_id" :level="employee.level" @goToShare="onShare" /> -->
        <PurchaseRecordPopup :open.sync="purchaseRecordVisble" closeable v-if="purchaseRecordVisble" />
        <gold-coin-timeout-tips task-code="richPlan" ref="TimeoutTips" />
        <ShareDialog :show.sync="show" :productInfo="productInfo" :shareType="'active'" @share="handleShare">
            <!-- https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png -->
            <template #active_tag>
                <img
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png"
                    style="
                        width: 78rpx;
                        height: 56rpx;
                        position: absolute;
                        left: 20rpx;
                        top: 10rpx;
                        transform: translate(-50%, -50%);
                    "
                />
            </template>
        </ShareDialog>

        <RulePopup ref="RulePopup" />
        <RegisterPopup ref="RegisterPopup" />
        <WithDrawPopup ref="WithDrawPopup" @refresh="refreshMemberCard"/>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { Vue, Component } from 'vue-property-decorator';
import * as QRCode from '@/wxcomponents/painter/lib/qrcode';
import { employeeInfo } from '@/http/requestGo/user';
import { mineInfo } from '@/http/mine';
import MemberCard from '../components/MemberCard.vue';
// import MarkPopup from '../components/MarkPopup.vue';
import ProductList from '../login/ProductList/ProductList.vue';
import RulePopup from '../components/RulePopup.vue';
import RegisterPopup from '../components/RegisterPopup.vue';
import WithDrawPopup from '../components/WithDrawPopup.vue';
import GoldCoinTimeoutTips from '@/components/GoldCoinTimeoutTips/GoldCoinTimeoutTips.vue';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import { UserModule } from '@/store/modules/user';
import PurchaseRecordPopup from '../components/PurchaseRecordPopup.vue';
import { AppModule } from '@/store/modules/app';

@Component({
    components: {
        MemberCard,
        // MarkPopup,
        ProductList,
        RulePopup,
        RegisterPopup,
        WithDrawPopup,
        GoldCoinTimeoutTips,
        ShareDialog,
        PurchaseRecordPopup,
    },
})
export default class AmbassadorDetail extends Vue {
    $refs: {
        TimeoutTips,
        productListRef: ProductList,
        MemberCard,
        RulePopup,
        RegisterPopup,
        WithDrawPopup
    };

    public active_id = 0;

    public customBarBg = 'transparent';
    public canvasIcon =
        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68850b99d31698650011176.png';

    public baseUrl = '';
    public ossUrl = '';
    public isLoading = false;
    public timer2: any = null;
    public shareType = 'active';

    // 二维码配置
    public qrcodeConfig = {
        // url: `${process.env.VUE_APP_MALL_EMPLOYEE}?employee=${this.employee.uid}`, // 二维码链接
        url: '', // 二维码链接
        canvasWidth: 390,
        canvasHeight: 844,
        qrSize: 106,
        bgImage:
            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68871e5b9c6516410012585.png',
        bgColor: '#ffffff',
        fgColor: '#2D0C00',
        ecLevel: 2,
    };

    public employee: any = {};
    public isEmployee: boolean = null;
    public show = false;
    public productInfo: any = {};
    public shareId = '';
    public link = '';
    public shareName = '';
    public page = 1;
    public page_size = 10;

    get canvasRealWidth() {
        const systemInfo = uni.getSystemInfoSync();
        const screenWidth = systemInfo.screenWidth || 375;
        const rpxToPx = screenWidth / 750;
        return Math.round(this.qrcodeConfig.canvasWidth * rpxToPx);
    }

    get canvasRealHeight() {
        const systemInfo = uni.getSystemInfoSync();
        const screenWidth = systemInfo.screenWidth || 375;
        const rpxToPx = screenWidth / 750;
        return Math.round(this.qrcodeConfig.canvasHeight * rpxToPx);
    }

    get computedHeight() {
        return `100vh - ${AppModule.pagePaddingTop}rpx`
    }

    onShow() {
        mineInfo().then((response) => {
            const { is_employee, uid, nick_name, user_id } = response;

            this.isEmployee = is_employee === 1;

            if (this.isEmployee) {
                employeeInfo().then((info) => {
                    this.employee = info.data;
                    this.qrcodeConfig.url = `${process.env.VUE_APP_MALL_EMPLOYEE}?employee=${info.data.uid}`;

                    const { is_tip } = this.employee;
                    if (is_tip) {
                        this.$nextTick(() => {
                            // @ts-ignore
                            this.$refs?.MarkPopup?.toggleVisible();
                        });
                    }
                });
            } else {
                this.employee = { uid, nick_name, user_id };
            }
        });
        setTimeout(() => {
            this.$refs.TimeoutTips.getViewGoodsTaskStatus();
        }, 500);
    }

    // 监听页面滚动
    onPageScroll(e) {
        if (e.scrollTop > 0) {
            this.customBarBg = '#FAEEE4';
        } else {
            this.customBarBg = 'transparent';
        }
    }

    withdraw(limitMoney) {
        this.$refs.WithDrawPopup.toggleVisible({
            limitMoney,
            user_id: this.employee.user_id
        })
    }

    openRegisterPopup() {
        console.log(this.$refs.RegisterPopup)
        this.$refs.RegisterPopup.toggleVisible()
    }

    refreshMemberCard() {
        // 刷新待提现金额
        this.$refs.MemberCard.getEmployeeInfo()
    }

    onShare() {
        // #ifdef H5
        const shareProductList = this.$refs.productListRef.exposeProductList();
        const shareProductInfo = shareProductList[Math.floor(Math.random() * shareProductList.length)];
        this.shareId = shareProductInfo.gid;
        console.log(shareProductInfo, 'shareProductInfo');
        this.productInfo = {
            name: shareProductInfo.name,
            desc: shareProductInfo.introduce,
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#2B1E10',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891086bd063b8540019947.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #42b983;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.shareType = 'money';
        this.show = true;
        // #endif
    }

    async onPost() {
        if (this.isLoading) {
            return;
        }

        this.isLoading = true;
        uni.showLoading({ title: '海报生成中' });
        try {
            // 生成带二维码的canvas图片
            await this.generateCanvasWithQRCode();
            const getPolicyRes = await Utils.getPolicy();
            const imageList = await Utils.handleImageUpload([{ url: this.baseUrl, name: 'share' }], getPolicyRes);
            this.ossUrl = imageList[0];
            // const params = {
            //     target: 'wechat',
            //     type: 'image',
            //     content: {
            //         url: this.ossUrl,
            //         share_image: this.ossUrl,
            //     },
            // };
            // Utils.messageChannel('share', params);
            uni.hideLoading();

            Utils.saveImage(this.ossUrl);
            Utils.Toast('海报已保存');
            this.isLoading = false;
        } catch (error) {
            uni.showToast({
                title: '生成图片失败',
                icon: 'none',
            });
            this.isLoading = false;
        }
    }

    onSelect() {
        console.log('onSelect');
        // Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
        Utils.navigateTo('/pages/shop/shop');
    }

    // 生成带二维码的Canvas图片
    generateCanvasWithQRCode() {
        return new Promise((resolve, reject) => {
            // 使用$nextTick确保DOM已更新
            this.$nextTick(() => {
                // 延迟执行确保Canvas已渲染
                setTimeout(async () => {
                    try {
                        const ctx = uni.createCanvasContext('canvas', this);

                        // 获取设备信息，计算rpx到px的转换比例
                        const systemInfo = uni.getSystemInfoSync();
                        const screenWidth = systemInfo.screenWidth || 390;
                        const rpxToPx = screenWidth / 780;

                        const canvasWidth = Math.round(this.qrcodeConfig.canvasWidth * rpxToPx);
                        const canvasHeight = Math.round(this.qrcodeConfig.canvasHeight * rpxToPx);
                        const qrSize = Math.round(this.qrcodeConfig.qrSize * rpxToPx);

                        // 清除画布
                        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

                        // 下载并绘制背景图片
                        await this.drawBackgroundImage(ctx, canvasWidth, canvasHeight);

                        // 计算二维码位置（水平居中，距离底部40px）
                        const qrX = (canvasWidth - qrSize) / 2; // 水平居中

                        const textX = qrX + 68 * rpxToPx; // 文字距离左边
                        const textY = 524 * rpxToPx; // 文字距离底部
                        const qrY = textY + 70 * rpxToPx; // 距底部40px
                        await this.drawIconImage(ctx, textX, textY, rpxToPx);
                        ctx.font = '8px MiSans';
                        let nickName = this.employee.nick_name;
                        if (nickName.length > 5) {
                            nickName = `${nickName.substring(0, 5)}...`;
                        }
                        ctx.fillStyle = '#fff';
                        // @ts-ignore
                        ctx.textBaseline = 'middle';
                        ctx.fillText(nickName, textX, textY);

                        ctx.fill();
                        // 绘制二维码背景（白色圆角矩形）
                        this.drawRoundRect(ctx, qrX - 3, qrY - 3, qrSize + 6, qrSize + 6, 3);
                        ctx.fillStyle = this.qrcodeConfig.bgColor;
                        ctx.fill();

                        // 绘制二维码
                        QRCode.api.draw(
                            this.qrcodeConfig.url,
                            ctx,
                            qrX,
                            qrY,
                            qrSize,
                            qrSize,
                            this.qrcodeConfig.bgColor,
                            this.qrcodeConfig.fgColor,
                            this,
                            this.qrcodeConfig.ecLevel,
                        );

                        // 绘制到Canvas并获取本地路径
                        ctx.draw(false, () => {
                            // 获取Canvas本地路径
                            this.getCanvasLocalPath(canvasWidth, canvasHeight)
                                .then((localPath: string) => {
                                    this.baseUrl = localPath;
                                    resolve(localPath);
                                })
                                .catch(reject);
                        });
                    } catch (error) {
                        reject(error);
                    }
                }, 300);
            });
        });
    }

    drawIconImage(ctx: any, textX: number, iconImageTop: number, rpxToPx: number) {
        return new Promise((resolve, reject) => {
            uni.downloadFile({
                url: this.canvasIcon,
                success: (res) => {
                    if (res.statusCode === 200) {
                        // 绘制背景图片，铺满整个canvas
                        console.log(res.tempFilePath, 'res.tempFilePath');
                        ctx.drawImage(res.tempFilePath, textX, iconImageTop + 5, rpxToPx * 60, rpxToPx * 6);
                        resolve(res.tempFilePath);
                    } else {
                        reject(new Error('背景图片下载失败'));
                    }
                },
                fail: (err) => {
                    console.error('背景图片下载失败:', err);
                    reject(err);
                },
            });
        });
    }

    // 绘制背景图片
    drawBackgroundImage(ctx: any, canvasWidth: number, canvasHeight: number) {
        return new Promise((resolve, reject) => {
            uni.downloadFile({
                url: this.qrcodeConfig.bgImage,
                success: (res) => {
                    if (res.statusCode === 200) {
                        console.log('背景图片下载成功:', res.tempFilePath);

                        // 绘制背景图片，铺满整个canvas
                        ctx.drawImage(res.tempFilePath, 0, 0, canvasWidth, canvasHeight);
                        resolve(true);
                    } else {
                        reject(new Error('背景图片下载失败'));
                    }
                },
                fail: (err) => {
                    console.error('背景图片下载失败:', err);
                    reject(err);
                },
            });
        });
    }

    // 绘制圆角矩形
    drawRoundRect(ctx: any, x: number, y: number, width: number, height: number, radius: number) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }

    // 获取Canvas的本地路径
    getCanvasLocalPath(canvasWidth: number, canvasHeight: number): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            uni.canvasToTempFilePath(
                {
                    canvasId: 'canvas',
                    width: canvasWidth,
                    height: canvasHeight,
                    success: (res) => {
                        console.log('Canvas转临时文件成功:', res.tempFilePath);
                        // 直接返回临时文件路径
                        resolve(res.tempFilePath);
                    },
                    fail: (err) => {
                        console.error('Canvas转临时文件失败:', err);
                        reject(err);
                    },
                },
                this,
            );
        });
    }

    // 跳转规则
    routeNext() {
        // uni.navigateTo({ url: '/pagesC/ambassador/rule/index' });
        this.$refs.RulePopup.toggleVisible()
    }

    showMarkTip() {
        // @ts-ignore
        this.$refs.MarkPopup.showMarkTip();
    }

    shareActivity() {
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #42b983;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.shareType = 'active';
        this.show = true;
        console.log(this.$refs.productListRef, 'this.$refs.productListRef');
        const shareProductList = this.$refs.productListRef.exposeProductList();
        const shareProductInfo = shareProductList[Math.floor(Math.random() * shareProductList.length)];
        this.shareId = shareProductInfo.gid;
        console.log(shareProductInfo, 'shareProductInfo');
        this.productInfo = {
            name: shareProductInfo.name,
            desc: shareProductInfo.introduce,
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#2B1E10',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    gotoGdetailPage(item) {
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #42b983;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.shareType = 'goods';
        this.show = true;
        this.shareId = item.gid;
        this.productInfo = {
            name: item.name,
            desc: item.introduce,
            image: item.cover_image,
            price: item.price,
            priceColor: '#2B1E10',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        if (this.shareType === 'active') {
            const shareUrl = `${process.env.VUE_APP_MALL_EMPLOYEE}?employee=${this.employee.uid}`;
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: shareUrl,
                    jumpLink: shareUrl,
                },
                {
                    content: {
                        url: shareUrl,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
                        title: `推广赚佣金`,
                        desc: '来DREAME APP，邀好友下单，享丰厚佣金。',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `我是追觅合伙人${this.employee.nick_name}和我一起到追觅APP，享受更多购物优惠`,
                    },
                },
            );
        } else if (this.shareType === 'goods') {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.employee.uid}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.employee.uid}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.employee.uid}`,
                        image: this.productInfo.image,
                        title: this.productInfo.name,
                        desc: this.productInfo.desc,
                    },
                    extras: {
                        type: 'goods',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `${this.productInfo.price}元, ${this.productInfo.name}`,
                    },
                },
            );
            Utils.taskComplete('shareRichGoodsFriend');
        } else if (this.shareType === 'money') {
            const shareUrl = `${process.env.VUE_APP_MALL_EMPLOYEE}?employee=${this.employee.uid}`;
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: shareUrl,
                    jumpLink: shareUrl,
                },
                {
                    content: {
                        url: shareUrl,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/524434-1752570696059.png',
                        title: `我是追觅合伙人${this.employee.nick_name}`,
                        desc: '和我一起到追觅App，享受更多购物优惠',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `我是追觅合伙人${this.employee.nick_name}和我一起到追觅APP，享受更多购物优惠`,
                    },
                },
            );
        }
    }

    onLoad(options) {
        this.link = options.link;
        this.active_id = options.active_id
    }

    public purchaseRecordVisble = false;
    goToPurchaseRecord() {
        this.purchaseRecordVisble = true;
    }
}
</script>

<style lang="scss" scoped>
.container {
    padding-bottom: 80rpx;
    background: #f3f2f7;

    .rule-wrap {
        position: absolute;
        right: 16rpx;
        width: 144rpx;
        height: 56rpx;
        background: rgba(0, 0, 0, 0.7);
        border: 3rpx solid rgba(255, 255, 255, 0.17);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 76rpx;
        font-size: 24rpx;
        color: #f6f6f6;
    }

    .bg-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 874rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2a843eec2780012469.jpg');
        background-size: 100% 100%;

        .rule {
            position: absolute;
            width: 140rpx;
            height: 50rpx;
            top: 200rpx;
            right: 0;
        }
    }

    .btn-operate {
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 40rpx;
        z-index: 20;

        .operate-post,
        .operate-share,
        .operate-select {
            height: 96rpx;
            border-radius: 12rpx;
            line-height: 96rpx;
            text-align: center;
        }

        .operate-share,
        .operate-post {
            width: 689rpx;
            font-size: 32rpx;
            color: #FFF1DF;
            background: #1D0F02;
            box-shadow: inset 0px 8px 20px 0px rgba(255, 232, 207, 0.41);
        }

        .operate-select {
            font-size: 32rpx;
            margin-left: 30rpx;
            background: linear-gradient(180deg, #f7c98b -10%, #eaaa3f 70%, #cf9535 100%);
        }

        .operate-post,
        .operate-select {
            margin-bottom: 18rpx;
            width: 310rpx;
        }
    }
}

.product-list-wrap {
    // background-color: #f2f3f7 !important;
    padding: 84rpx 14rpx 20rpx;
    margin: 38rpx 20rpx 160rpx;
    border-radius: 16rpx;
    position: relative;
    // min-height: 1800rpx;
    border: 4px solid;
    border-image: linear-gradient(180deg, #FFFFFF 6%, #FFFFFF 43%, rgba(255, 255, 255, 0) 100%) 4;
    backdrop-filter: blur(20px);
    border-radius: 16rpx;

    .bg {
        border-radius: 16rpx;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        min-height: 1800rpx;
        background: linear-gradient(180deg, #FAEFE6 0%, #FFFFFF 13%), linear-gradient(0deg, #FFFFFF, #FFFFFF), rgba(0, 0, 0, 0.69);
    }
    .cover-img {
        position: absolute;
        top: 0;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        width: 176px;
        height: 37px;
        line-height: 37px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #fed9b1;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689affb56d77a4480463667.png');
        background-size: 100% 100%;
    }
}

.right {
    height: 100%;
    display: flex;
    align-items: center;

    .share {
        position: absolute;
        right: 28rpx;
        height: 46rpx;
        width: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c4e827d1985120024461.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .rule {
        position: absolute;
        right: 28rpx;
        height: 46rpx;
        width: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfca2106690670010343.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
}
</style>
