<template>
    <view class="comment-content">
        <template v-for="item in dataSource">
            <!-- 评论 -->
            <view
                class="comment-content-item"
                :key="item.id"
                :id="`review${item.id}`"
                v-if="(Number(item.reviewer_id) === Number(userInfo.user_id) || !item.is_shield) && !item.is_report"
            >
                <!-- 用户 -->
                <view class="user" @click="handleUserClick(item)">
                    <view style="position: relative;width: 46rpx;height: 46rpx;">
                        <img class="user-avatar" :src="item.reviewer_avatar" />
                        <img v-if="item.user_avatar_icon" class="user-icon" :src="item.user_avatar_icon" />
                    </view>
                    <text class="user-name">{{ item.reviewer_name }}</text>
                    <img v-if="item.reviewer_id == '3587481'"  style="margin-left: 10rpx;width: 138rpx;height: 36rpx" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png" alt=""/>
                    <img
                        class="user-auther"
                        v-if="Number(item.reviewer_id) === creator"
                        src="https://wpm-cdn.dreame.tech/images/2024010/836007-1728696064642.png"
                    />
                    <img
                        class="user-top"
                        v-if="!!item.is_top"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202505/789705-1746598258364.png"
                    />
                </view>

                <!-- 内容 -->
                <view class="text" @longpress="handleLongpress(item)" @click="handleContentClick(item)">
                    {{ item.content }}
                </view>

                <!-- 评论图 -->
                <view
                    class="image"
                    v-if="item.image_url && item.image_url.length > 0 && !item.is_report"
                    @click="handlePreviewImage(item.image_url)"
                    @longpress="handleLongpress(item)"
                >
                    <template v-for="imageItem in item.image_url">
                        <image :src="imageItem" :key="imageItem" mode="aspectFill" />
                    </template>
                </view>

                <!-- <view class="address" v-if="item.address">
                    <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                    <text>{{ item.address }}</text>
                </view> -->

                <!-- 时间/回复/收藏 -->
                <view class="operate">
                    <text class="operate-day">{{ formatDate(item.created_at) }}</text>
                    <text v-if="item.province" class="operate-ip">{{ item.province }}</text>
                    <text class="operate-reply" @click="handleReply(item)" v-if="!item.is_report">回复</text>
                    <img :src="praiseIcon(!!item.is_praise)" @click="handlePraise(item)" v-if="!item.is_report" />
                    <text class="operate-like" v-if="item.praise_count > 0 && !item.is_report">{{
                        item.praise_count
                    }}</text>
                </view>
            </view>

            <template v-for="secondItem in item.replyList">
                <!-- 回复 -->
                <view
                    class="comment-content-item-second"
                    :key="secondItem.id"
                    :id="`reply${secondItem.id}`"
                    v-if="(Number(secondItem.replier_id) === Number(userInfo.user_id) || !secondItem.is_shield) && !secondItem.is_report"
                >
                    <!-- 用户 -->
                    <view class="user" @click="handleUserClick(item, secondItem)">
                        <view style="position: relative;width: 46rpx;height: 46rpx;">
                            <img class="user-avatar" :src="secondItem.replier_avatar" />
                            <img v-if="secondItem.user_avatar_icon" class="user-icon" :src="secondItem.user_avatar_icon" />
                        </view>
                        <text class="user-name">{{ secondItem.replier_name }}</text>
                        <img v-if="secondItem.replier_id == '3587481'"  style="margin-left: 10rpx;width: 138rpx;height: 36rpx" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png" alt=""/>
                        <img
                            class="user-auther"
                            v-if="Number(secondItem.replier_id) === creator"
                            src="https://wpm-cdn.dreame.tech/images/2024010/836007-1728696064642.png"
                        />
                        <img
                            class="user-top"
                            v-if="!!secondItem.is_top"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202505/789705-1746598258364.png"
                        />
                    </view>

                    <!-- 回复内容 -->
                    <view
                        class="text"
                        @longpress="handleLongpress(item, secondItem)"
                        @click="handleContentClick(item, secondItem)"
                    >
                        <text>回复</text>
                        <text>{{ secondItem.reply_to_name }}：</text>
                        {{ secondItem.content }}
                    </view>

                    <!-- 回复图 -->
                    <view
                        class="image"
                        v-if="secondItem.image_url && secondItem.image_url.length > 0 && !secondItem.is_report"
                        @click="handlePreviewImage(secondItem.image_url)"
                        @longpress="handleLongpress(item, secondItem)"
                    >
                        <template v-for="imageSecondItem in secondItem.image_url">
                            <image :src="imageSecondItem" :key="imageSecondItem" mode="aspectFill" />
                        </template>
                    </view>

                    <!-- <view class="address" v-if="item.address">
                        <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                        <text>{{ item.address }}</text>
                    </view> -->

                    <!-- 时间/回复/收藏 -->
                    <view class="operate">
                        <text class="operate-day">{{ formatDate(secondItem.created_at) }}</text>
                        <text v-if="secondItem.province" class="operate-ip">{{ secondItem.province }}</text>
                        <text class="operate-reply" @click="handleReply(item, secondItem)" v-if="!secondItem.is_report">
                            回复
                        </text>
                        <img
                            :src="praiseIcon(!!secondItem.is_praise)"
                            @click="handlePraise(item, secondItem)"
                            v-if="!secondItem.is_report"
                        />
                        <text class="operate-like" v-if="secondItem.praise_count > 0 && !secondItem.is_report">{{
                            secondItem.praise_count
                        }}</text>
                    </view>
                </view>
            </template>

            <!-- 更多加载/收起 -->
            <view :key="`more${item.id}`" class="load" v-if="item.reply_count > 0">
                <view class="load-divider"></view>
                <view
                    v-if="item.replyList && item.outIds && item.replyList.length === item.outIds.length"
                    @click="handleReplyLoadMore(item)"
                >
                    展开
                    <text class="load-count">{{ item.reply_count }}</text>
                    条回复
                </view>
                <view
                    v-else-if="
                        item.replyList && item.outIds && item.reply_count + item.outIds.length === item.replyList.length
                    "
                    @click="handlePickUp(item)"
                    >收起</view
                >
                <view v-else @click="handleReplyLoadMore(item)"> 展开更多回复 </view>
            </view>
        </template>
    </view>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { ReviewItem } from '@/http/requestGo/community';
import Utils from '@/common/Utils';
import { UserModule, UserInfo } from '@/store/modules/user';
import { CheckAppJump } from '@/common/decorators';

@Component
export default class Comment extends Vue {
    @Prop({ type: Array, default: [] }) dataSource!: ReviewItem[];
    @Prop({ type: Number, default: 0 }) creator!: number;

    praiseIcon(isPraise) {
        return isPraise
            ? 'https://wpm-cdn.dreame.tech/images/202306/647807cbe8c199533616129.png'
            : 'https://wpm-cdn.dreame.tech/images/202306/64794db2f28209931744857.png';
    }

    formatDate(date) {
        return Utils.timeAgoFormat(date, 'comment');
    }

    handleLongpress(item, secondItem?) {
        this.$emit('handleLongpress', item, secondItem);
    }

    handleContentClick(item, secondItem?) {
        this.$emit('handleContentClick', item, secondItem);
    }

    @CheckAppJump()
    handleReply(item, secondItem?) {
        this.$emit('handleReply', item, secondItem);
    }

    @CheckAppJump()
    handleReplyLoadMore(item) {
        this.$emit('handleReplyLoadMore', item);
    }

    @CheckAppJump()
    handlePickUp(item) {
        this.$emit('handlePickUp', item);
    }

    handlePreviewImage(urls) {
        uni.previewImage({ urls });
    }

    @CheckAppJump()
    handlePraise(item, secondItem?) {
        this.$emit('handlePraise', item, secondItem);
    }

    @CheckAppJump()
    handleUserClick(item, secondItem?) {
        this.$emit('handleUserClick', item, secondItem);
    }

    get userInfo(): UserInfo {
        return UserModule.userInfo;
    }
}
</script>

<style lang="scss" scoped>
.comment-content {
    &-item,
    &-item-second {
        padding: 20rpx 0;

        .user {
            @include flex($justify: flex-start, $align: center);
            font-size: 28rpx;
            margin-bottom: 12rpx;

            &-avatar {
                width: 46rpx;
                height: 46rpx;
                border-radius: 50%;
            }

            &-icon {
                position: absolute;
                width: 20rpx;
                height: 20rpx;
                right: 0;
                bottom: 0;
            }

            &-name {
                max-width: 500rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: $text-color-regular;
                margin: 0 12rpx 0 16rpx;
            }

            &-auther {
                width: 66rpx;
                height: 38rpx;
            }

            &-top {
                width: 66rpx;
                height: 38rpx;
                margin-left: 6rpx;
            }
        }

        .text {
            font-size: 26rpx;
            color: $text-color-secondary;
            line-height: 38rpx;
            word-break: break-all;
            margin: 0 0 16rpx 62rpx;

            text {
                color: $text-color-regular;
                &:first-child {
                    margin-right: 8rpx;
                }
            }
        }

        .image {
            display: inline-block;
            margin: 0 0 16rpx 62rpx;
            image {
                width: 230rpx;
                height: 230rpx;
            }
        }

        /* .address {
            margin: 0 0 16rpx 62rpx;
            @include flex(row, flex-start, center);

            image {
                flex: 0 0 30rpx;
                height: 30rpx;
            }

            text {
                font-size: 24rpx;
                color: #ab8c5e;
                line-height: 30rpx;
                margin-left: 12rpx;
            }
        } */

        .operate {
            font-size: 24rpx;
            margin-left: 62rpx;
            @include flex($justify: flex-start, $align: center);

            &-day,
            &-like,
            &-ip {
                color: $text-color-secondary;
            }

            &-ip {
                margin-left: 12rpx;
            }

            &-reply {
                color: $text-color-primary;
                margin: 0 auto 0 12rpx;
            }

            img {
                width: 30rpx;
                height: 30rpx;
                margin-right: 8rpx;
            }
        }
    }

    &-item-second {
        padding: 10rpx 0 20rpx 62rpx;
    }

    .load {
        @include flex($justify: flex-start, $align: center);
        color: $fill-color-primary-active;
        font-size: 24rpx;
        margin: 10rpx 0 50rpx 62rpx;

        &-divider {
            width: 50rpx;
            height: 2rpx;
            margin-right: 12rpx;
            background-color: $fill-color-dark;
        }

        &-count {
            margin: 0 8rpx;
        }
    }
}
</style>
