import http from './index';

/**
 * @description 金币抽奖
 * @param {Object} params - 抽奖参数
 * @param {number} params.ac_id - 活动ID
 */
export function getCoinLottery(params = { ac_id: 30 }) {
    return http.post('main/draw/do-gold-draw', params, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
}

/**
 * @description 查询当前剩余金币、历史总金币
 * @returns {Promise} 金币信息
 */
export function getCoinInfo() {
    return http.post('main/member-center/score-get');
}

/**
 * @description 查询滚动抽奖的显示西信息
 */
export function getDrawRollRecord(data) {
    return http.post('main/draw/draw-record-roll', data);
}

/**
 * @description 查询金币配置
 */

export function getCoinConfig(data = { ac_id: 30 }) {
    return http.post('/main/draw/gold-config', data);
}

/**
 * @description 获取抽奖记录
 * @param {Object} params.ac_id - 抽奖活动ID
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 */
export function getCoinDrawRecord(params = { page: 1, page_size: 20, ac_id: 30, type: 'draw_gold' }) {
    return http.post('main/draw/draw-record', params, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
}

/**
 * @description 金币明细
 */
export function getCoinDetail(data = { asset_type: 3, type: 1, page: 1, size: 20 }) {
    return http.post('main/member-center/score-get-points', data);
}

/**
 * @description 金币兑换积分
 * @param {Object} params - 兑换参数
 * @param {number} params.coins_amount - 消耗的金币数量
 * @param {number} params.points_amount - 获得的积分数量
 * @param {number} params.exchange_type - 兑换类型 1:金币兑换积分
 */
export function exchangeCoinToPoints(params: { use_gold: number }) {
    return http.post('main/draw/gold-exchange-point', params);
}

/**
 * @description 领取单类任务的奖励金币
 */
export function getcollectTaskGold(data: { code: string; group_code: string; type_code: string }) {
    return http.post('main/member-center/collect-task-gold', data, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
}

/**
 * @description 获取邀请消费明细
 */
export function getInviteConsumeDetail() {
    return http.post('main/invite/consume-money-detail-roll');
}

/**
 * @description 获取邀请消费排行榜
 */
export function getInviteConsumeRank() {
    return http.post('main/invite/consume-money-rank');
}

/**
 * @description 获取看视频的金币
 */
export function getVideoGold(params) {
    return http.post('main/member-center/watch-video-gold', params);
}

/**
 * @description 查询抽奖记录
 */
export function getDrawRecord(params) {
    return http.post('main/member-center/get-draw-record', params);
}
