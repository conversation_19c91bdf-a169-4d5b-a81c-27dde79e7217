<template>
    <div class="my-device-card">
        <div class="device-header">
            <div class="device-title">我的设备</div>
            <div
                v-if="deviceList.length - 1 !== currentDeviceIndex"
                class="go-device"
                @click="gotoDevice"
            >
                进入设备
                <img src="https://wpm-cdn.dreame.tech/images/202306/243935-1686290915081.png" alt="" class="goto-device-icon" />
            </div>
        </div>
        <div class="device-list">
            <swiper
                class="u-swiper"
                @change="changeDevice"
            >
                <swiper-item class="u-swiper__wrapper__item" v-for="(item, index) in deviceList" :key="index">
                    <div class="swiper-item-content">
                        <NoMachine v-if="item.isNoMachine" />
                        <Vacuum
                            v-else-if="item.deviceInfo.categoryPath === '/lifeapps/vacuum'"
                            :currentDevice="item"
                            @edit-device="editDeviceInfo"
                        />
                        <WashMachine
                            v-else-if="item.deviceInfo.categoryPath === '/lifeapps/hold'"
                            :currentDevice="item"
                        />
                        <OtherMachine
                            v-else
                            :currentDevice="item"
                        />
                    </div>
                </swiper-item>
            </swiper>
            <div
                class="swiper-pagination"
            >
                <div
                    v-for="(item, index) in deviceList"
                    :key="index"
                    class="pagination-item"
                    :class="{ activepoint: index === currentDeviceIndex, pointmargin: index !== 0 }"
                />
            </div>
        </div>
        <EditDeviceDialog
            v-if="editDialogVisible"
            :currentDevice="currentDevice"
            @refresh-list="refreshList"
            @close="editDialogVisible = false"
        />
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

import WashMachine from './WashingMachine.vue';
import Vacuum from './Vacuum.vue';
import OtherMachine from './OtherMachine.vue';
import NoMachine from './NoMachine.vue';
import EditDeviceDialog from './EditDeviceDialog.vue';

export default {
    name: 'MyDevice',
    components: {
        WashMachine,
        Vacuum,
        OtherMachine,
        NoMachine,
        EditDeviceDialog
    },
    props: {
        deviceList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            currentDeviceIndex: 0, // 当前设备索引
            editDialogVisible: false, // 编辑对话框是否可见
            currentDeviceStatus: {},
        };
    },
    computed: {
        currentDevice() {
            return this.deviceList[this.currentDeviceIndex] || {};
        },
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        supportFastCommand() {
            const { device = {}} = this.currentDevice;
            const { deviceInfo = {}} = device;
            const { feature = '' } = deviceInfo;
            // 支持快捷指令的设备型号
            return feature === 'fastCommand';
        },
        supportVideo() {
            const { device = {}} = this.currentDevice;
            const { deviceInfo = {}} = device;
            const { videoDynamicVendor = false } = deviceInfo;
            return videoDynamicVendor
        }
    },
    methods: {
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 设备切换
        changeDevice(e: any) {
            const { detail = {}} = e;
            const { current = 0 } = detail;
            this.currentDeviceIndex = current;
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.editDialogVisible = true;
        },
        // 刷新设备列表
        refreshList() {
            this.$emit('refresh-list');
        }
    }
}
</script>

<style scoped>
.my-device-card {
    margin-top: 20rpx;
}
.device-header {
    padding: 0 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.device-title {
    font-size: 36rpx;
    line-height: 48rpx;
    font-weight: 500;
    color: #3D3D3D;
}
.go-device {
    font-size: 28rpx;
    color: #555555;
}
.goto-device-icon {
    width: 32rpx;
    height: 32rpx;
}
.device-list {
    margin-top: 24rpx;
}
.swiper-item-content {
    margin: 0 32rpx;
}
.swiper-pagination {
    display: flex;
    justify-content: center;
    margin-top: 32rpx;
}
.pagination-item {
    width: 12rpx;
    height: 6rpx;
    border-radius: 6rpx;
    background-color: #E2E2E2;
}
.activepoint {
    background-color: #404040;
}
.pointmargin {
    margin-left: 8rpx;
}
</style>
