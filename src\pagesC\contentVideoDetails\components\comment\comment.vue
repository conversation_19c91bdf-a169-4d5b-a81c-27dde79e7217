<template>
    <view>
        <u-popup
            :show="isShow"
            mode="bottom"
            @close="close"
            :safeAreaInsetBottom="false"
            :round="15"
            :mask="false"
            :closeable="true"
        >
            <view class="header">
                评论
            </view>

            <!-- 筛选 -->
            <view class="comment-type" v-if="reviewList.length > 0">
                <template v-for="(item, index) in commentTypeList">
                    <view
                        :class="['comment-type--item', { active: index === currentCommentIndex }]"
                        :key="item.value"
                        @click="handleCommentTypeClick(index)"
                    >
                        {{ item.label }}
                    </view>
                </template>
            </view>

            <scroll-view
                class="comment"
                :scroll-y="true"
                :scroll-top="scrollTop"
                @scrolltolower="handleScrollToLower"
                @scroll="handleScroll"
                :scroll-with-animation="true"
            >
                <!-- 视频评论简介 -->
                <view class="comment-intro">
                    <view class="user" @click="jumpPersonHome(data.creator)">
                        <view class="user-wrapper">
                            <img class="user-avatar" :src="data.author_avatar" />
                            <img v-if="data.user_avatar_icon" class="user-tag" :src="data.user_avatar_icon" />
                            <!-- <img v-if="data.author_tag" class="user-tag" :src="data.author_tag" /> -->
                        </view>
                        <text class="user-name">{{ data.author_name }}</text>
                        <img
                            class="user-auther"
                            src="https://wpm-cdn.dreame.tech/images/202411/887003-1731307406165.png"
                        />
                    </view>

                    <view class="title" :class="{ ellipsis: !showTitle }">{{ data.title }}</view>

                    <view class="text" v-if="showTitle">
                        <u-parse :content="data.body.replaceAll(/\n/g, '<br/>')"></u-parse>
                    </view>

                    <view class="text ellipsis" v-else> {{ data.body }} </view>

                    <view class="address" v-if="data.address_intro && data.address_id !== 'notShow'">
                        <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                        <text>{{ data.address_intro }}</text>
                    </view>

                    <view class="time">
                        <text>{{ publishText }}</text>
                        <text>{{ publishAt }}</text>
                        <text v-if="data.province">{{ data.province }}</text>
                        <text v-if="isContentCreator && permisionType !== 1">{{ permissionText }}</text>
                        <text class="unfold" @click="handleUnfold" v-if="bodyOverlength">
                            {{ showTitle ? '收起' : '展开' }}
                        </text>
                    </view>
                </view>
                <!-- 评论数 -->
                <view class="comment-count" v-if="data.comment > 0">
                    <text>共{{data.comment}}条评论</text>
                </view>

                <!-- 空态 -->
                <view class="comment-empty" v-if="!reviewList || reviewList.length === 0">
                    <view class="comment-empty-title">还没有评论，快来抢沙发吧</view>
                    <view class="comment-empty-review" @click="handleCommentSubmit">抢首评</view>
                </view>

                <view class="comment-content" v-else>
                    <template v-for="item in reviewList">
                        <!-- 评论 -->
                        <view
                            class="comment-content-item"
                            :key="item.id"
                            v-if="isCommentVisible(item) && !item.is_report"
                        >
                            <!-- 用户 -->
                            <view class="user">
                                <view style="position: relative;width: 46rpx;height: 46rpx;">
                                    <img
                                        class="user-avatar"
                                        :src="item.reviewer_avatar"
                                        @click="jumpPersonHome(item.reviewer_id)"
                                    />
                                    <img v-if="item.user_avatar_icon" class="user-icon" :src="item.user_avatar_icon" />
                                </view>
                                <text class="user-name">{{ item.reviewer_name }}</text>
                                <img v-if="item.reviewer_id == '3587481'"  style="margin-left: 10rpx;width: 138rpx;height: 36rpx" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png" alt=""/>
                                <img
                                    class="user-auther"
                                    v-if="item.reviewer_id === data.creator"
                                    src="https://wpm-cdn.dreame.tech/images/2024010/836007-1728696064642.png"
                                />
                                <img
                                    class="user-top"
                                    v-if="!!item.is_top"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202505/789705-1746598258364.png"
                                />
                            </view>

                            <!-- 内容 -->
                            <view class="text" @longpress="handleLongpress(item)" @click="handleContentClick(item)">
                                {{ item.content }}
                            </view>

                            <!-- 评论图 -->
                            <view
                                class="image"
                                v-if="item.image_url && item.image_url.length > 0 && !item.is_report"
                                @click="handlePreviewImage(item.image_url)"
                                @longpress="handleLongpress(item)"
                            >
                                <template v-for="imageItem in item.image_url">
                                    <image :src="imageItem" :key="imageItem" mode="aspectFill" />
                                </template>
                            </view>

                            <!-- <view class="address" v-if="item.address">
                                <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                                <text>{{ item.address }}</text>
                            </view> -->

                            <!-- 时间/回复/收藏 -->
                            <view class="operate">
                                <text class="operate-day">{{ formatDate(item.created_at) }}</text>
                                <text v-if="item.province" class="operate-ip">{{ item.province }}</text>
                                <text class="operate-reply" @click="handleReply(item)" v-if="!item.is_report"
                                    >回复</text
                                >
                                <img
                                    :src="praiseIcon(!!item.is_praise)"
                                    @click="handlePraise(item)"
                                    v-if="!item.is_report"
                                />
                                <text class="operate-like" v-if="item.praise_count > 0 && !item.is_report">{{
                                    item.praise_count
                                }}</text>
                            </view>
                        </view>

                        <template v-for="secondItem in item.replyList">
                            <!-- 回复 -->
                            <view
                                class="comment-content-item-second"
                                :key="secondItem.id"
                                :id="`reply${secondItem.id}`"
                                v-if="(Number(secondItem.replier_id) === Number(userInfo.user_id) || !secondItem.is_shield) && !secondItem.is_report"
                            >
                                <!-- 用户 -->
                                <view class="user">
                                    <view style="position: relative;width: 46rpx;height: 46rpx;">
                                        <img
                                            class="user-avatar"
                                            :src="secondItem.replier_avatar"
                                            @click="jumpPersonHome(secondItem.replier_id)"
                                        />
                                        <img v-if="secondItem.user_avatar_icon" class="user-icon" :src="secondItem.user_avatar_icon" />
                                    </view>
                                    <text class="user-name">{{ secondItem.replier_name }}</text>
                                    <img v-if="secondItem.replier_id == '3587481'"  style="margin-left: 10rpx;width: 138rpx;height: 36rpx" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png" alt=""/>
                                    <img
                                        class="user-auther"
                                        v-if="secondItem.replier_id === data.creator"
                                        src="https://wpm-cdn.dreame.tech/images/2024010/836007-1728696064642.png"
                                    />
                                    <img
                                        class="user-top"
                                        v-if="!!secondItem.is_top"
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202505/789705-1746598258364.png"
                                    />
                                </view>

                                <!-- 回复内容 -->
                                <view
                                    class="text"
                                    @longpress="handleLongpress(item, secondItem)"
                                    @click="handleContentClick(item, secondItem)"
                                >
                                    <text>回复</text>
                                    <text>{{ secondItem.reply_to_name }}：</text>
                                    {{ secondItem.content }}
                                </view>

                                <!-- 回复图 -->
                                <view
                                    class="image"
                                    v-if="
                                        secondItem.image_url && secondItem.image_url.length > 0 && !secondItem.is_report
                                    "
                                    @click="handlePreviewImage(secondItem.image_url)"
                                    @longpress="handleLongpress(item, secondItem)"
                                >
                                    <template v-for="imageSecondItem in secondItem.image_url">
                                        <image :src="imageSecondItem" :key="imageSecondItem" mode="aspectFill" />
                                    </template>
                                </view>

                                <!-- <view class="address" v-if="item.address">
                                    <image src="https://wpm-cdn.dreame.tech/images/202501/958264-1736152983821.png" />
                                    <text>{{ item.address }}</text>
                                </view> -->

                                <!-- 时间/回复/收藏 -->
                                <view class="operate">
                                    <text class="operate-day">{{ formatDate(secondItem.created_at) }}</text>
                                    <text v-if="secondItem.province" class="operate-ip">{{ secondItem.province }}</text>
                                    <text
                                        class="operate-reply"
                                        @click="handleReply(item, secondItem)"
                                        v-if="!secondItem.is_report"
                                        >回复</text
                                    >
                                    <img
                                        :src="praiseIcon(!!secondItem.is_praise)"
                                        @click="handlePraise(item, secondItem)"
                                        v-if="!secondItem.is_report"
                                    />
                                    <text
                                        class="operate-like"
                                        v-if="secondItem.praise_count > 0 && !secondItem.is_report"
                                    >
                                        {{ secondItem.praise_count }}
                                    </text>
                                </view>
                            </view>
                        </template>

                        <!-- 更多加载/收起 -->
                        <view :key="`more${item.id}`" class="load" v-if="item.reply_count > 0">
                            <view class="load-divider"></view>
                            <view
                                class="load-count"
                                v-if="item.replyList.length === item.outIds.length"
                                @click="handleReplyLoadMore(item)"
                            >
                                展开
                                <text class="load-count">{{ item.reply_count }}</text>
                                条回复
                            </view>
                            <view
                                v-else-if="item.reply_count + item.outIds.length === item.replyList.length"
                                @click="handlePickUp(item)"
                                >收起</view
                            >
                            <view v-else @click="handleReplyLoadMore(item)"> 展开更多回复 </view>
                        </view>
                    </template>
                </view>

                <!-- 触底 -->
                <view
                    class="comment-end"
                    v-if="reviewList.length === reviewPagination.total && reviewList.length !== 0"
                >
                    已显示全部评论
                </view>
            </scroll-view>

            <view class="bottom">
                <view class="bottom-wrapper">
                    <!-- 发布评论 -->
                    <view class="bottom-publish" @click="handleCommentSubmit">
                        <img src="https://wpm-cdn.dreame.tech/images/2024010/305258-1728378600901.png" />
                        <text>说点什么...</text>
                    </view>
                </view>
            </view>

            <!-- 回复弹窗 -->
            <ReplyPopup
                v-if="replyPopupOpen"
                @close="handleReplyPopupClose"
                :replyHolder="replyHolder"
                :replyInput.sync="replyPopupInput"
                @handleSend="handleSend"
                :style="{ bottom: `${keyboardHeightSync}rpx`, transition: 'all 0.2s ease-in-out' }"
            />

            <!-- 举报/删除/回复弹窗 -->
            <u-popup :show="operatePopupOpen" @close="handleOperateClose">
                <view class="bottom-operate-popup-wrapper">
                    <view>
                        <view class="operate-item" v-if="reportParams.hasReply" @click="handleOpeneReply">回复</view>

                        <view class="operate-item" v-if="reportParams.hasReport" @click="handleNavgateReport">
                            {{ `举报该${reportParams.reportType === 'detail' ? '作品' : '评论'}` }}
                        </view>

                        <view class="operate-item" v-if="reportParams.hasDelete" @click="beforeHandleDelete">删除</view>
                    </view>

                    <view class="divider"></view>
                    <!-- 取消 -->
                    <view class="cancel" @click="handleOperateClose">取消</view>
                </view>
            </u-popup>

            <CustomModal
                :show="deleteModalOpen"
                width="616rpx"
                contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
                content="确认删除此评论"
                confirmText="确认"
                cancelText="取消"
                showCancelButton
                @confirm="handleDelete"
                @cancel="handleDeleteClose"
            >
            </CustomModal>
        </u-popup>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import AvatarAndNickPop from '@/components/AvatarAndNickPop/AvatarAndNickPop.vue';
import { UserModule, UserInfo } from '@/store/modules/user';
import ReplyPopup from '@/pagesC/contentDetails/components/ReplyPopup/ReplyPopup.vue';
import {
    createReply,
    createReview,
    deleteReply,
    deleteReview,
    getReplyList,
    getReviewList,
    postCancelPraise,
    postPraise,
    ReplyItem,
    ReviewItem,
} from '@/http/requestGo/community';
import { ReportParams } from '@/pagesC/contentDetails/components/BottomOperatePopup/BottomOperatePopup.vue';
import Utils from '@/common/Utils';
import { PERMISSION_OPTION } from '@/pagesC/contentDetails/components/contant';
import { CheckAppJump } from '@/common/decorators';

const ReportTypeEnum = {
    detail: 1,
    comment: 2,
    secondComment: 3,
};

@Component({
    components: {
        AvatarAndNickPop,
        ReplyPopup,
    },
})
export default class CustomShare extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({ type: Number, default: 1 })
    readonly permisionType!: number;

    @Watch('isShow') // watch，此处是监听isShow的变化
    async isShowChange(newVal: Boolean, oldVal: Boolean) {
        this.$emit('changeHeight', newVal ? 1210 : 0);
    }

    public showAvatarAndNickPop: Boolean = false;
    public data: any = {};
    public showTitle = false;
    public currentCommentIndex = 0;

    public commentTypeList: { label: string; value: number }[] = [
        {
            label: '最新',
            value: 2,
        },
        {
            label: '热门',
            value: 1,
        },
    ];

    get publishText() {
        if ([1, 3].includes(this.data.audit_status) && this.data.edit_at === 0) {
            return '创作于';
        }
        return this.data.edit_at > this.data.publish_at ? '编辑于' : '发布于';
    }

    // edit_at为0表示首次发布，audit_status为1/3表示审核中/审核失败，即首次发布且审核中/审核失败才使用create_at，edit_at和publish_at哪个新用哪个
    get publishAt() {
        const time =
            [1, 3].includes(this.data.audit_status) && this.data.edit_at === 0
                ? this.data.create_at
                : this.data.edit_at > this.data.publish_at
                ? this.data.edit_at
                : this.data.publish_at;
        return Utils.timeAgoFormat(time, 'publish');
    }

    get userInfo(): UserInfo {
        return UserModule.userInfo;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isAvatarAndNick(): boolean {
        return (
            this.userInfo.nick !== '微信用户' &&
            this.userInfo.avatar !==
                'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
        );
    }

    get permissionText() {
        return PERMISSION_OPTION.find((v) => v.value === (this.permisionType || this.data.permission_type))?.label;
    }

    formatDate(date) {
        return Utils.timeAgoFormat(date, 'comment');
    }

    praiseIcon(isPraise) {
        return isPraise
            ? 'https://wpm-cdn.dreame.tech/images/202306/647807cbe8c199533616129.png'
            : 'https://wpm-cdn.dreame.tech/images/202306/64794db2f28209931744857.png';
    }

    close() {
        this.replyMap.clear();
        this.replyPopupInput = '';
        this.operatePopupOpen = false;
        this.$emit('update:isShow', false);
    }

    @CheckAppJump()
    handleUserClick(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleCommentSubmit' });
            return;
        }
        // #endif

        const creator = secondItem?.replier_id || item.reviewer_id;
        const isMe = creator == UserModule.user_id;
        const url = isMe
            ? '/pagesC/selfCommunity/selfCommunity'
            : `/pagesC/selfCommunity/selfCommunity?creator=${creator}`;
        uni.navigateTo({ url });
    }

    async onSuccess() {
        this.$nextTick(() => {
            this.$emit('update:showPoster', true);
        });
    }

    handleOpenPoster() {
        if (this.isAvatarAndNick || !this.isPhone) {
            this.$emit('update:showPoster', true);
        } else {
            this.showAvatarAndNickPop = true;
        }
    }

    // 评论列表相关
    public reviewPagination = {
        page: 1,
        page_size: 10,
        total: 0,
    };

    public replyPagination = {
        page: 1,
        page_size: 5,
        total: 0,
    };

    public reviewList: ReviewItem[] = [];
    public commentLoading: boolean = false;
    public showIntro: boolean = false;
    public bodyOverlength: boolean = false;
    public isContentCreator: boolean = false;

    initReviewList(data, showIntro: boolean) {
        this.data = { ...data };
        this.isContentCreator = UserModule.user_id == this.data.creator;

        const canvas = uni.createCanvasContext('myCanvas');
        const text = this.data.body;
        canvas.setFontSize(13);
        const textWidth = canvas.measureText(text).width;
        const title = this.data.title;
        canvas.setFontSize(14);
        const titleWidth = canvas.measureText(title).width;
        this.bodyOverlength = textWidth > 300 || titleWidth > 300;

        this.showIntro = showIntro;
        uni.onWindowResize(this.windowResizeCallback);

        getReviewList({
            content_id: data.content_id,
            page: 1,
            page_size: this.reviewPagination.page_size,
            order_type: 2,
        })
            .then((res) => {
                this.reviewList = res.data.list || [];
                this.reviewList.forEach((v) => {
                    v.replyList = v.is_report ? [] : v.reply_list ? [v.reply_list] : [];
                    v.outIds = v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [];
                    v.reply_count = v.is_report ? 0 : v.reply_count;
                    v.page = 1;
                });
                this.reviewPagination.total = res.data.total;
            })
            .catch((err) => {
                console.log('err', err);
            });
    }

    handleLongpress(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleLongpress' });
            return;
        }
        // #endif

        const { user_id } = UserModule;
        const isReport = secondItem?.is_report || item.is_report;
        const isOwner = Number(secondItem?.replier_id || item.reviewer_id) === Number(user_id);
        const isCreator = Number(this.data.creator) === Number(user_id);

        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.currentReplyId = secondItem ? secondItem.id : item.id;

        // 如果评论被举报了，只有评论者或者创建者可以操作，且只能删除
        if (isReport) {
            if (isOwner || isCreator) {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasDelete: true,
                };
            } else {
                return;
            }
        } else {
            // 没被举报，评论者只能删除自己的评论，非评论者可以回复或举报该评论，创建者还可以删除
            if (isOwner) {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasDelete: true,
                };
            } else {
                this.reportParams = {
                    id: secondItem ? secondItem.id : item.id,
                    hasReply: true,
                    hasReport: true,
                    hasDelete: this.data.creator == user_id,
                    reportType: secondItem ? 'secondComment' : 'comment',
                };
            }
        }

        this.reviewItem = item;
        this.replyItem = secondItem;
        this.operatePopupOpen = true;
    }

    handleContentClick(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleContentClick' });
            return;
        }
        // #endif

        this.currentReplyId = secondItem ? secondItem.id : item.id;
        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.replyPopupInput = this.replyMap.get(this.currentReplyId) || '';

        this.reviewItem = item;
        this.replyItem = secondItem;
        this.replyPopupOpen = true;
    }

    handlePraise(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handlePraise' });
            return;
        }
        // #endif

        const index = this.reviewList.findIndex((v) => v.id === item.id);
        if (index === -1) return;

        const updatePraise = (target, isPraise, id, type) => {
            const action = isPraise
                ? postCancelPraise({ id: isPraise })
                : postPraise({ entity_id: String(id), type, content_id: this.data.content_id });
            action
                .then((res) => {
                    target.is_praise = isPraise ? 0 : res.data.id;
                    target.praise_count += isPraise ? -1 : 1;
                })
                .then(() => {
                    const tempReviewItem = this.reviewList[index];
                    this.$set(this.reviewList, index, tempReviewItem);
                });
        };

        if (secondItem) {
            const secondIndex = this.reviewList[index].replyList.findIndex((v) => v.id === secondItem.id);
            if (secondIndex === -1) return;

            const reply = this.reviewList[index].replyList[secondIndex];
            updatePraise(reply, reply.is_praise, reply.id, 3);
        } else {
            const review = this.reviewList[index];
            updatePraise(review, review.is_praise, review.id, 2);
        }
    }

    // 获取所点击评论下面的回复分页
    handleReplyLoadMore(item: ReviewItem) {
        getReplyList({
            content_id: this.data.content_id,
            out_id: item.outIds,
            page: item.page,
            page_size: this.replyPagination.page_size,
            review_id: item.id,
            type: 3,
        }).then((res) => {
            if (!res.data.list) return;

            const index = this.reviewList.findIndex((v) => v.id === item.id);
            this.reviewList[index].page = res.data.page + 1;
            const tempReviewItem = this.reviewList[index];
            tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
            this.$set(this.reviewList, index, tempReviewItem);
        });
    }

    handlePickUp(item: ReviewItem) {
        const index = this.reviewList.findIndex((v) => v.id === item.id);

        const tempReviewItem = this.reviewList[index];
        tempReviewItem.page = 1;
        tempReviewItem.reply_count += item.outIds.length - 1;
        tempReviewItem.replyList = this.reviewList[index].replyList.slice(0, 1);
        tempReviewItem.outIds = [].concat(this.reviewList[index].replyList[0].id);
        this.$set(this.reviewList, index, tempReviewItem);
    }

    handlePreviewImage(urls) {
        uni.previewImage({ urls });
    }

    handleScroll(e) {
        this.old.scrollTop = e.detail.scrollTop;
    }

    handleScrollToLower() {
        if (this.reviewList && this.reviewList.length >= this.reviewPagination.total) return;
        getReviewList({
            content_id: this.data.content_id,
            page: this.reviewPagination.page + 1,
            page_size: this.reviewPagination.page_size,
            order_type: this.commentTypeList[this.currentCommentIndex].value,
        }).then((res) => {
            const result = res.data.list.map((v) => {
                return {
                    ...v,
                    replyList: v.is_report ? [] : v.reply_list ? [v.reply_list] : [],
                    reply_count: v.is_report ? 0 : v.reply_count,
                    outIds: v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [],
                };
            });
            this.reviewPagination.page = this.reviewPagination.page + 1;
            this.reviewList = this.reviewList.concat(result);
        });
    }

    // 评论弹窗相关
    public replyPopupOpen = false;
    public replyPopupInput = '';
    public replyHolder = '说点什么';
    public maxSize: string = '3145728';
    public maxCount: number = 1;
    public replyType: 'new' | 'review' | 'reply' = 'new';
    public reviewItem = {} as ReviewItem;
    public replyItem = {} as ReplyItem;
    public replyMap = new Map();
    public currentReplyId: any = 'default';
    public scrollTop = 0;
    public old = {
        scrollTop: 0,
    };

    public keyboardHeight = 0;

    get keyboardHeightSync() {
        return Utils.pxToRpx(this.keyboardHeight);
    }

    keyboardHeightChange(event) {
        this.keyboardHeight = event.detail.height;
    }

    // #ifdef H5
    windowResizeCallback = (res) => {
        this.keyboardHeight = res.size.windowHeight;
    };
    // #endif

    handleCommentSubmit() {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleCommentSubmit' });
            return;
        }
        // #endif

        this.currentReplyId = 'default';
        this.replyPopupInput = this.replyMap.get('default') || '';
        this.replyType = 'new';
        this.replyPopupOpen = true;
    }

    handleReplyPopupClose() {
        this.replyPopupOpen = false;
        // this.replyPopupInput = '';
        this.replyMap.set(this.currentReplyId, this.replyPopupInput);
        this.replyHolder = '说点什么';
    }

    public sendLoading: boolean = false;
    handleSend(content: string, imageUrl: string[]) {
        if (!content && imageUrl.length === 0) {
            Utils.Toast('评论内容不能为空');
            return;
        }

        if (this.sendLoading) return;
        this.sendLoading = true;

        const { user_id, sessid } = UserModule;

        if (this.replyType === 'new') {
            createReview({
                content,
                content_id: this.data.content_id,
                sessid,
                user_id,
                image_url: imageUrl,
            })
                .then((res) => {
                    this.replyPopupOpen = false;
                    this.replyHolder = '说点什么';
                    this.reviewList.unshift({ ...res.data, replyList: [] });
                    this.reviewPagination.total += 1;
                    this.data.comment += 1;
                    this.$emit('commentNum', this.data.comment);
                    this.replyMap.delete(this.currentReplyId);
                    this.scrollTop = this.old.scrollTop;
                    this.$nextTick(function () {
                        this.scrollTop = 0;
                    });
                    Utils.Toast('发送成功');
                })
                .catch((err) => console.log('err', err))
                .finally(() => {
                    this.sendLoading = false;
                });
        } else {
            createReply({
                content: this.replyPopupInput,
                content_id: this.data.content_id,
                sessid,
                user_id,
                parent_review_id: this.replyItem?.id,
                review_id: this.reviewItem.id,
                type: this.replyItem?.id ? 3 : 2,
                image_url: imageUrl,
            })
                .then((res) => {
                    this.replyPopupOpen = false;
                    this.replyHolder = '说点什么';
                    this.data.comment += 1;
                    this.$emit('commentNum', this.data.comment);
                    const index = this.reviewList.findIndex((v) => v.id === this.reviewItem.id);
                    const tempReviewItem = this.reviewList[index];
                    tempReviewItem.replyList = [...this.reviewList[index].replyList, res.data];
                    tempReviewItem.outIds = [...this.reviewList[index].outIds, res.data.id];
                    this.$set(this.reviewList, index, tempReviewItem);
                    this.replyMap.delete(this.currentReplyId);
                    Utils.Toast('发送成功');
                    this.scrollTop = this.old.scrollTop;
                    this.$nextTick(function () {
                        this.scrollTop = this.old.scrollTop + 80;
                    });
                })
                .catch((err) => console.log('err', err))
                .finally(() => {
                    this.sendLoading = false;
                });
        }
    }

    @CheckAppJump()
    handleReply(item: ReviewItem, secondItem?: ReplyItem) {
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleReply' });
            return;
        }
        // #endif

        this.replyType = secondItem ? 'reply' : 'review';
        this.replyHolder = `回复@${secondItem?.replier_name || item.reviewer_name}`;
        this.currentReplyId = secondItem ? secondItem.id : item.id;

        const { user_id } = UserModule;

        if ((secondItem?.replier_id || item.reviewer_id) === user_id) {
            this.reportParams = {
                id: secondItem ? secondItem.id : item.id,
                hasDelete: true,
                hasReply: true,
            };
            this.operatePopupOpen = true;
        } else {
            this.replyPopupInput = this.replyMap.get(this.currentReplyId) || '';
            this.replyPopupOpen = true;
        }

        this.reviewItem = item;
        this.replyItem = secondItem;
    }

    // 举报相关
    public reportParams: Partial<ReportParams> = {};
    public operatePopupOpen: boolean = false;
    public deleteModalOpen: boolean = false;

    handleOperateClose() {
        this.operatePopupOpen = false;
    }

    handleOpeneReply() {
        this.operatePopupOpen = false;
        this.replyPopupInput = this.replyMap.get(this.currentReplyId) || '';
        this.replyPopupOpen = true;
    }

    beforeHandleDelete() {
        this.operatePopupOpen = false;
        this.deleteModalOpen = true;
    }

    handleDelete() {
        this.operatePopupOpen = false;
        const index = this.reviewList.findIndex((v) => v.id === this.reviewItem.id);
        if (index === -1) return;

        if (this.replyType === 'review') {
            deleteReview({ id: this.reviewItem.id }).then(() => {
                this.data.comment = this.data.comment - this.reviewList[index].replyList.length - 1;
                this.reviewList.splice(index, 1);
                this.reviewPagination.total -= 1;
                this.$emit('commentNum', this.data.comment);
                this.deleteModalOpen = false;
                Utils.Toast('删除成功');
            });
        } else {
            deleteReply({ id: this.replyItem.id }).then(() => {
                // 如果没有展开且删除的是第一条
                if (this.reviewList[index].replyList.length === 1) {
                    // 有更多，加载下一条
                    if (this.reviewList[index].reply_count > 0) {
                        getReplyList({
                            content_id: this.data.content_id,
                            out_id: this.reviewList[index].outIds,
                            page: 1,
                            page_size: 1,
                            review_id: this.reviewItem.id,
                            type: 3,
                        }).then((res) => {
                            if (!res.data.list) {
                                res.data.list = [];
                            }

                            const tempReviewItem = this.reviewList[index];
                            tempReviewItem.replyList = [].concat(res.data.list);
                            tempReviewItem.outIds = [res.data.list[0].id];
                            tempReviewItem.reply_count = res.data.total - 1;
                            this.$set(this.reviewList, index, tempReviewItem);
                        });
                    } else {
                        const tempReviewItem = this.reviewList[index];
                        tempReviewItem.replyList = [];
                        tempReviewItem.outIds = [];
                        this.$set(this.reviewList, index, tempReviewItem);
                    }
                } else {
                    // 删除当前replyList里面的选中的评论
                    const secondIndex = this.reviewList[index].replyList.findIndex((v) => v.id === this.replyItem.id);
                    this.reviewList[index].replyList.splice(secondIndex, 1);
                    // 在新的replyList里面重设replyList和outIds
                    this.reviewList[index].replyList = this.reviewList[index].replyList.slice(0, 1);
                    this.reviewList[index].outIds = this.reviewList[index].replyList.map((v) => v.id);
                    const page = this.reviewList[index].page;
                    if (page === 1) {
                        getReplyList({
                            content_id: this.data.content_id,
                            out_id: this.reviewList[index].outIds,
                            page: 1,
                            page_size: this.replyPagination.page_size,
                            review_id: this.reviewItem.id,
                            type: 3,
                        }).then((res) => {
                            if (!res.data.list) {
                                res.data.list = [];
                            }

                            const tempReviewItem = this.reviewList[index];
                            tempReviewItem.page = res.data.page + 1;
                            tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
                            tempReviewItem.reply_count = res.data.total;
                            this.$set(this.reviewList, index, tempReviewItem);
                        });
                    } else {
                        for (let i = 1; i < page; i++) {
                            getReplyList({
                                content_id: this.data.content_id,
                                out_id: this.reviewList[index].outIds,
                                page: i,
                                page_size: this.replyPagination.page_size,
                                review_id: this.reviewItem.id,
                                type: 3,
                            }).then((res) => {
                                if (!res.data.list) {
                                    res.data.list = [];
                                }

                                const tempReviewItem = this.reviewList[index];
                                tempReviewItem.page = res.data.page + 1;
                                tempReviewItem.replyList = [...this.reviewList[index].replyList, ...res.data.list];
                                tempReviewItem.reply_count = res.data.total;
                                this.$set(this.reviewList, index, tempReviewItem);
                            });
                        }
                    }
                }
                this.data.comment -= 1;
                this.$emit('commentNum', this.data.comment);
                this.deleteModalOpen = false;
                Utils.Toast('删除成功');
            });
        }
    }

    handleDeleteClose() {
        this.deleteModalOpen = false;
    }

    handleNavgateReport() {
        this.operatePopupOpen = false;
        uni.navigateTo({
            url: `/pagesC/contentReport/contentReport?id=${this.reportParams.id}&reportType=${
                ReportTypeEnum[this.reportParams.reportType]
            }`,
        });
    }

    handleUnfold() {
        this.showTitle = !this.showTitle;
    }

    handleCommentTypeClick(index) {
        this.reviewPagination.page = 1;
        getReviewList({
            content_id: this.data.content_id,
            page: 1,
            page_size: this.reviewPagination.page_size,
            order_type: this.commentTypeList[index].value,
        })
            .then((res) => {
                this.reviewList = res.data.list;
                this.reviewList.forEach((v) => {
                    v.replyList = v.is_report ? [] : v.reply_list ? [v.reply_list] : [];
                    v.reply_count = v.is_report ? 0 : v.reply_count;
                    v.outIds = v.is_report ? [] : v.reply_list ? [v.reply_list.id] : [];
                    v.page = 1;
                });
                this.reviewPagination.total = res.data.total;
                this.currentCommentIndex = index;
            })
            .catch((err) => console.log('err', err));
    }

    jumpPersonHome(creator) {
        Utils.jumpPersonHome(creator);
    }

    isCommentVisible(item) {
        return Number(item.reviewer_id) === Number(this.userInfo.user_id) || !item.is_shield;
    }
}
</script>

<style lang="scss" scoped>
.comment {
    width: 750rpx;
    height: 1008rpx;
    background: #ffffff;
    border-radius: 15rpx 15rpx 0rpx 0rpx;

    /* padding: 62rpx 30rpx 0 30rpx; */

    &-intro {
        padding: 50rpx 42rpx 32rpx 30rpx;
        // background: #faf8f3;

        .user {
            @include flex($justify: flex-start, $align: center);
            font-size: 28rpx;
            margin-bottom: 12rpx;

            &-wrapper {
                position: relative;
            }

            &-avatar {
                width: 46rpx;
                height: 46rpx;
                border-radius: 50%;
            }

            &-tag {
                width: 20rpx;
                height: 20rpx;
                border-radius: 50%;
                position: absolute;
                right: 0;
                bottom: 0;
            }

            &-name {
                max-width: 500rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: $text-color-regular;
                margin: 0 16rpx;
            }

            &-auther {
                width: 66rpx;
                height: 38rpx;
            }
        }

        .address {
            padding-left: 62rpx;
            margin: 12rpx 0;
            @include flex(row, flex-start, center);

            image {
                flex: 0 0 30rpx;
                height: 30rpx;
            }

            text {
                font-size: 24rpx;
                color: #ab8c5e;
                line-height: 30rpx;
                margin-left: 12rpx;
            }
        }

        .title {
            padding-left: 62rpx;
            margin: 16rpx 0;
            font-weight: 500;
            font-size: 28rpx;
            color: #121212;
            &.ellipsis {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .text,
        .time {
            padding-left: 62rpx;
            font-size: 26rpx;
            color: #121212;
            line-height: 36rpx;
            word-break: break-all;

            &.ellipsis {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .time {
            @include flex($justify: flex-start, $align: center);
            margin-top: 16rpx;
            text {
                margin-right: 12rpx;
                color: #777777;
                font-size: 23rpx;
            }

            .unfold {
                font-size: 24rpx;
                color: #121212;
                margin: 0 0 0 auto;
            }
        }
    }

    &-count {
        padding: 10rpx 30rpx 10rpx 60rpx;
        margin: 0 32rpx 0rpx;
        border-top: 2rpx solid #F6F6F6;
        font-weight: 500;

        text {
            font-size: 24rpx;
            color: #777777;
            line-height: 38rpx;
        }
    }

    &-empty {
        padding: 286rpx 0 176rpx 0;

        &-title {
            font-size: 28rpx;
            color: $text-color-disable;
            line-height: 38rpx;
            text-align: center;
        }

        &-review {
            font-size: 28rpx;
            color: $brand-color-btn-text;
            line-height: 38rpx;
            text-align: center;
            margin-top: 54rpx;
        }
    }

    &-title {
        font-weight: 500;
        font-size: 32rpx;
        color: $text-color-regular;
        line-height: 40rpx;
    }

    &-sub-title {
        font-size: 24rpx;
        color: #a6a6a6;
        margin-left: 16rpx;
    }

    &-type {
        @include flex($justify: flex-start, $align: center);
        margin: 30rpx 0;

        &--item {
            width: 142rpx;
            height: 54rpx;
            border-radius: 154rpx 154rpx 154rpx 154rpx;
            border: 2rpx solid $uni-border-color-gray;
            font-size: 28rpx;
            color: $text-color-gray;
            margin-right: 30rpx;
            @include flex;

            &.active {
                color: $text-color-primary;
                border: 2rpx solid $text-color-primary;
            }
        }
    }

    &-end {
        padding: 130rpx 0 104rpx 0;
        font-size: 24rpx;
        color: $text-color-disable;
        text-align: center;
    }

    .comment-content {
        padding: 0 30rpx;

        &-item,
        &-item-second {
            padding: 20rpx 0;

            .user {
                @include flex($justify: flex-start, $align: center);
                font-size: 28rpx;
                margin-bottom: 12rpx;

                &-avatar {
                    width: 46rpx;
                    height: 46rpx;
                    border-radius: 50%;
                }

                &-icon {
                    position: absolute;
                    width: 20rpx;
                    height: 20rpx;
                    bottom: 0;
                    right: 0;
                }

                &-name {
                    color: $text-color-regular;
                    margin: 0 12rpx 0 16rpx;
                }

                &-auther {
                    width: 66rpx;
                    height: 38rpx;
                }

                &-top {
                    width: 66rpx;
                    height: 38rpx;
                    margin-left: 6rpx;
                }
            }

            .text {
                font-size: 26rpx;
                color: $text-color-secondary;
                line-height: 38rpx;
                word-break: break-all;
                margin: 0 0 16rpx 62rpx;

                text {
                    color: $text-color-regular;
                    &:first-child {
                        margin-right: 8rpx;
                    }
                }
            }

            .image {
                margin: 0 0 16rpx 62rpx;
                display: inline-block;
                image {
                    width: 230rpx;
                    height: 230rpx;
                }
            }

            /* .address {
                margin: 0 0 16rpx 62rpx;
                @include flex(row, flex-start, center);

                image {
                    flex: 0 0 30rpx;
                    height: 30rpx;
                }

                text {
                    font-size: 24rpx;
                    color: #ab8c5e;
                    line-height: 30rpx;
                    margin-left: 12rpx;
                }
            } */

            .operate {
                font-size: 24rpx;
                margin-left: 62rpx;
                @include flex($justify: flex-start, $align: center);

                font-size: 24rpx;
                margin-left: 62rpx;
                @include flex($justify: flex-start, $align: center);

                &-day,
                &-like,
                &-ip {
                    color: $text-color-secondary;
                }

                &-ip {
                    margin-left: 12rpx;
                }

                &-reply {
                    color: $text-color-primary;
                    margin: 0 auto 0 12rpx;
                }

                img {
                    width: 30rpx;
                    height: 30rpx;
                    margin-right: 8rpx;
                }
            }
        }

        &-item-second {
            padding: 10rpx 0 20rpx 62rpx;
        }

        .load {
            @include flex($justify: flex-start, $align: center);
            color: $fill-color-primary-active;
            font-size: 24rpx;
            margin: 10rpx 0 50rpx 62rpx;

            &-divider {
                width: 50rpx;
                height: 2rpx;
                margin-right: 12rpx;
                background-color: $fill-color-dark;
            }

            &-count {
                margin: 0 8rpx;
            }
        }
    }
}

.header {
    width: 750rpx;
    height: 80rpx;
    background: #ffffff;
    position: sticky;
    top: 0;
    left: 0;
    border-radius: 15rpx 15rpx 0rpx 0rpx;
    @include flex($justify: center, $align: center);
    font-size: 28rpx;
    color: #121212;
    line-height: 80rpx;
}

.comment-type {
    @include flex($justify: flex-start, $align: center);
    margin: 24rpx 0 24rpx 30rpx;

    &--item {
        width: 142rpx;
        height: 54rpx;
        border-radius: 154rpx 154rpx 154rpx 154rpx;
        border: 2rpx solid $uni-border-color-gray;
        font-size: 28rpx;
        color: $text-color-gray;
        margin-right: 30rpx;
        @include flex;

        &.active {
            color: $text-color-primary;
            border: 2rpx solid $text-color-primary;
        }
    }
}

.bottom {
    position: relative;
    height: calc(env(safe-area-inset-bottom) + 114rpx);

    &-wrapper {
        padding: 22rpx 30rpx calc(env(safe-area-inset-bottom) + 22rpx) 30rpx;
        position: fixed;
        bottom: 0;
        border-top: 2rpx solid $uni-bg-color-modal;
        width: 100%;
        background: $background-ffffff;
        @include flex($justify: flex-start, $align: center);
    }

    &-publish {
        width: 688rpx;
        height: 70rpx;
        background: #f4f4f4;
        border-radius: 194rpx;
        @include flex($justify: flex-start, $align: center);
        margin-right: auto;

        img {
            width: 36rpx;
            height: 36rpx;
            margin-left: 32rpx;
        }

        text {
            font-size: 28rpx;
            color: $text-color-disable;
            margin-left: 16rpx;
        }
    }
}

.reply-popup {
    &-wrapper {
        width: 100%;
        position: fixed;
        bottom: 0;
        z-index: 10001;
        padding: 20rpx 24rpx 16rpx 24rpx;
        background-color: $uni-bg-color-modal;
    }

    &-inner {
        padding: 10rpx;
        background-color: white;
        border-radius: 38rpx;
        box-sizing: border-box;

        .input {
            width: 620rpx;
            padding: 12rpx 28rpx 28rpx 30rpx;
            font-size: 28rpx;
            color: $text-color-gray;
            max-height: 120rpx;
            caret-color: #d3bd9d;
        }

        .operate {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .upload-icon {
                width: 62rpx;
                height: 62rpx;
            }

            .submit {
                width: 154rpx;
                background: $brand-color-normal;
                border-radius: 192rpx;
                text-align: center;
                line-height: 62rpx;
                margin-left: 16rpx;
                color: $brand-color-btn-text;
                font-size: 28rpx;
            }
        }
    }

    &-upload {
        margin-top: 16rpx;

        .fake-img {
            position: relative;
            width: 108rpx;
            height: 108rpx;

            .show-image {
                width: 108rpx;
                height: 108rpx;
            }

            .tip-image {
                position: absolute;
                width: 108rpx;
                height: 32rpx;
                left: 0;
                bottom: 0;
            }

            .close-image {
                position: absolute;
                width: 25rpx;
                height: 25rpx;
                top: 0;
                right: 0;
            }

            text {
                position: absolute;
                width: 108rpx;
                left: 0;
                bottom: 0;
                font-weight: 400;
                font-size: 24rpx;
                color: #ffffff;
                line-height: 32rpx;
                text-align: center;
            }
        }

        .preview {
            &-wrapper {
                position: fixed;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                background-color: black;
                z-index: 10002;

                .close-icon {
                    position: absolute;
                    width: 118rpx;
                    height: 118rpx;
                    top: env(safe-area-inset-top);
                    left: 0;
                }
            }

            &-image {
                width: 750rpx;
                height: 100%;
                object-fit: contain;
            }
        }
    }
}

.bottom-operate-popup {
    &-wrapper {
        position: fixed;
        bottom: 0;
        z-index: 10001;
        border-radius: 20rpx 20rpx 0 0;
        background: white;
        width: 100%;
        padding-bottom: env(safe-area-inset-bottom);

        .operate-item,
        .cancel {
            height: 116rpx;
            font-size: 32rpx;
            color: #20252b;
            @include flex($justify: center, $align: center);
            background-color: white;
            &:first-child {
                border-radius: 20rpx 20rpx 0 0;
            }
        }

        .operate-item:not(:last-child) {
            border-bottom: 1rpx solid #f7f8fa;
        }

        .divider {
            height: 16rpx;
            background: $uni-bg-color-modal;
        }
    }
}
</style>
