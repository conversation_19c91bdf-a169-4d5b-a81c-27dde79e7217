<template>
    <view class="container">
        <view class="header">
            <custom-bar title="金币明细" :background="BarBackground" position="relative"> </custom-bar>
            <view class="header-box">
                <view class="point-section">
                    <view class="point" @click="gotoCoinDetail">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68765b9c1cf5e1190011740.png"
                            class="point-icon"
                        />
                        <text class="point-text">我的金币</text>
                        <image class="detail-arrow" src="@/static/cart_arrow.png" />
                    </view>
                    <view class="pointContent">
                        <view class="pointAmount">
                            <text class="amountNum">{{ totalPoints }}</text>
                            <text class="amountUnit">= {{ totalGold }}元</text>
                        </view>
                        <!-- <view class="pointNumTwo">
                            <image
                                class="pointNum_image"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a618cb03547220012265.png"
                            />
                            {{ totalPoints }}
                        </view> -->
                    </view>
                </view>
            </view>
        </view>
        <!-- 领金币 -->
        <view class="getCoin" v-if="performList.length > 0">
            <view class="getCoin-content">
                <view class="getCoin-item" v-for="item in performList.slice(0, 4)" :key="item.id">
                    <view class="getCoin-item-bg">
                        <view class="getCoin-item-bg-text">{{ item.numValue }}</view>
                    </view>
                    <text class="getCoin-item-text">{{ item.eventName }}</text>
                </view>
            </view>
            <view class="getCoin-btn" @click="CollectAll"> 一键领取 </view>
        </view>
        <!-- 金币权益 -->
        <view class="coin-rights" :style="{ marginTop: performList.length === 0 ? '0' : '24rpx' }">
            <view class="coin-rights-content">
                <view class="rights-cards">
                    <view
                        class="rights-card"
                        v-for="(item, index) in rightsData"
                        :key="index"
                        @click="handleRightClick(item)"
                    >
                        <view class="card-icon">
                            <image :src="item.unlocked ? item.iconActive : item.icon" class="rights-icon" />
                        </view>
                        <view class="card-title">{{ item.name }}</view>
                    </view>
                </view>
                <view class="rights-progress">
                    <view class="progress-bar">
                        <!-- 胶囊进度条 -->
                        <view class="capsule-progress">
                            <!-- 起点胶囊 -->
                            <view class="capsule-wrapper">
                                <view class="capsule-node start-node" :class="{ active: rightsUnlockStatus.collectCoins }">
                                    <view class="capsule-status" >
                                        {{ rightsUnlockStatus.collectCoins ? '已解锁' : '未解锁' }}
                                    </view>
                                </view>
                                <view v-if="rightsUnlockStatus.lottery" class="capsule-label placeholder">已解锁</view>
                            </view>
                            <!-- 第一段进度条 -->
                            <view class="progress-segment" :class="{ active: rightsUnlockStatus.lottery }">
                                <view class="segment-fill" :style="{ width: getSegmentWidth(1) + '%' }"></view>
                            </view>
                            <!-- 中间胶囊 -->
                            <view class="capsule-wrapper">
                                <view class="capsule-node middle-node" :class="{ active: rightsUnlockStatus.lottery }">
                                    <view class="capsule-status" @click="handleLotteryClick" :style="{ marginLeft: rightsUnlockStatus.collectCoins ? '10rpx' : '0' }">
                                        {{ rightsUnlockStatus.lottery ? '去抽奖' : '未解锁' }}
                                        <image  v-if= "rightsUnlockStatus.collectCoins" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68765e0b8539b5460023115.png" style="height: 20rpx; width: 20rpx; margin-left: 5rpx;" />
                                    </view>
                                </view>
                                <view v-if="rightsUnlockStatus.lottery" class="capsule-label">已解锁</view>
                                <view v-else class="capsule-progress">{{ `${totalGoldSum}/${this.min_draw_gold}` }}</view>
                            </view>
                            <!-- 第二段进度条 -->
                            <view v-if="!rightsUnlockStatus.exchange && rightsUnlockStatus.lottery" class="progress-segmentLock" :class="{ active: rightsUnlockStatus.exchange }">
                                <view class="segment-fill" :style="{ width: getSegmentWidth(2) + '%' }"></view>
                            </view>
                            <view v-else class="progress-segment" :class="{ active: rightsUnlockStatus.exchange }">
                                <view class="segment-fill" :style="{ width: getSegmentWidth(2) + '%' }"></view>
                            </view>
                            <!-- 终点胶囊 -->
                            <view class="capsule-wrapper">
                                <view class="capsule-node end-node" :class="{ active: rightsUnlockStatus.exchange }">
                                    <view class="capsule-status" @click="handleExchangeClick" :style="{ marginLeft: rightsUnlockStatus.collectCoins ? '10rpx' : '0' }">
                                        {{ rightsUnlockStatus.exchange ? '去兑换' : '未解锁' }}
                                        <image  v-if= "rightsUnlockStatus.collectCoins" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68765e0b8539b5460023115.png" style="height: 20rpx; width: 20rpx; margin-left: 5rpx;" />
                                    </view>
                                </view>
                                <view v-if="rightsUnlockStatus.lottery && !rightsUnlockStatus.exchange" class="capsule-label placeholder">已解锁</view>
                                <view v-if="rightsUnlockStatus.exchange" class="capsule-label">已解锁</view>
                                <view v-else class="capsule-progress">{{ `${totalGoldSum}/${this.min_exchange_gold}` }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 金币任务 -->
        <view class="checkInContainer">
            <text class="title">
                <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687657a835a4b2200017842.png" style="height: 40rpx; width:144rpx" class="title-icon" />
            </text>
            <view class="alreadyDays">
                <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687657a97a1525000017739.png" style="height: 34rpx; width:160rpx"></image>
                    <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687657ab405232630017078.png" style="margin-left: 10rpx;height: 26rpx; width:230rpx"></image>
            </view>
            <view class="calendar_container">
                <JCalendar
                    ref="jcalendar"
                    :yearMonth="targetDate"
                    :signDataTotal="signDataTotal"
                    @dateChange="getData"
                    :isShowDetail="isShowDetail"
                >
                </JCalendar>
            </view>
            <MemberTasks :isMore="true" :limit="3" @refresh="init"></MemberTasks>
        </view>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import MemberTasks from './MemberTasks.vue';
import { getMonthCheckDetail, signInDaliy, getSignInStatus, getContinueDays } from '@/http/checkIn';
import { getCoinConfig, getScoreGet, getScoreCollectAll, getCoinUncollect, getScoreCollect } from '@/http/vip';
import Utils from '@/common/Utils';
import JCalendar from './Calendar/index.vue';
import { VIPModule } from '@/store/modules/vip';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';

@Component({
    components: {
        JCalendar,
        MemberTasks,
      },
})
export default class DaliyCheckIn extends Vue {
    $refs!: {
        jcalendar;
    };

    public isShowDetail: Boolean = false;
    public targetDate =
        parseInt(new Date().getFullYear().toString()) + '-' + parseInt((new Date().getMonth() + 1).toString());

    public signData = [];
    public homeSignList = [];
    public startTime = '';
    public endTime = '';
    public totalPoints: number = 0;
    public totalGold: number = 0;
    public totalGoldSum: number = 0;
    public goldConversion: number = 0;
    public min_draw_gold: number = 0;
    public min_exchange_gold: number = 0;
    public continueDay: number = 0;
    public BarBackground: string = 'transparent';

    public signInStatus: any = {
        continueSignDays: 0,
    };

    public addPoint = 0;
    public _ScoreGet: any = {};
    public signDataTotal: any = {
        random: +new Date(),
        dataSource: [],
        continueDay: 0,
        todaySigned: false,
        addPoint: 10,
    };

    public todaySigned: Boolean = false;
    public category: String = 'signin';
    public waresList: Array<any> = [];
    public performList: Array<any> = [];
    public points_show: Boolean = false;
    public rightsData: Array<any> = [];

    // 金币权益数据
    get progressWidth(): number {
        const totalCoins = Number(this.totalGoldSum);
        if (totalCoins >= this.min_exchange_gold) return 100;
        return (totalCoins / this.min_exchange_gold) * 100;
    }

    // 获取权益解锁状态
    get rightsUnlockStatus() {
        const totalCoins = Number(this.totalGoldSum);
        return {
            collectCoins: totalCoins >= 0,
            lottery: totalCoins >= this.min_draw_gold,
            exchange: totalCoins >= this.min_exchange_gold,
        };
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    async onShow() {
      this.init()
    }

    async onLoad() {
        const win: any = window;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_GOLD_CLICK,
        });
        this.init()
        win.onAppPageShow = async () => {
           this.init()
        }
    }

    async init() {
        this.getCoinConfigHandler()
        this.getScoreGet();
        VIPModule.getCoinTaskList();
        this.initRightsData();
        await this.fetchContinueDays();
        await this.getSignInDetail();
        this.getSignInStatus();
        // 进入页面时重新拉取未领取金币任务，确保气泡及时刷新
        this.getCoinUncollect();
    }

    async getCoinConfigHandler() {
        const res = await getCoinConfig({ ac_id: 30 });
        this.goldConversion = Number(res.gold_conversion);
        this.min_draw_gold = Number(res.unlock_path[0].value);
        this.min_exchange_gold = Number(res.unlock_path[1].value);
    }

    async Collect(id) {
        await getScoreCollect({ id });
        this.performList = this.performList.filter((item) => item.id !== id);
        if (this.performList.length === 0) {
            this.points_show = false;
        }
        await this.getScoreGet();
    }

    // 监听页面滚动
    onPageScroll(e) {
        // 当滚动距离大于 0 时，设置背景为白色
        if (e.scrollTop > 0) {
            this.BarBackground = 'white';
        } else {
            this.BarBackground = 'transparent';
        }
    }

    handleLotteryClick() {
        this.handleRightClick(this.rightsData[1]);
    }

    handleExchangeClick() {
        this.handleRightClick(this.rightsData[2]);
    }

    async getScoreGet() {
        this._ScoreGet = await getScoreGet();
        this.totalPoints = Number(this._ScoreGet.totalGold);
        this.totalGold = Number((this.totalPoints / this.goldConversion).toFixed(2));
        this.totalGoldSum = this._ScoreGet.totalGoldSum;
        this.initRightsData();
    }

    async getCoinUncollect() {
        const _CoinUncollect: any = await getCoinUncollect({ type: 3 });
        this.performList = _CoinUncollect;
        if (this.performList.length > 0) {
            this.points_show = true;
        }
    }

    async getSignInStatus() {
        const signInStatus = await getSignInStatus();
        this.signInStatus = signInStatus;
        this.todaySigned = signInStatus.todaySignedIn;

        if (!signInStatus.todaySignedIn) {
            // 今天没签到
            this.clickSign();
        } else {
            this.signDataTotal = {
                random: +new Date(),
                dataSource: [...this.signData],
                continueDay: this.continueDay,
                todaySigned: this.todaySigned,
                addPoint: this.addPoint,
            };
        }
    }

    toIOSDate(strDate) {
        // iso不认识"-"拼接的日期，所以转/
        return strDate ? strDate.replace(/-/g, '/') : strDate;
    }

    async fetchContinueDays() {
        const continueDays = await getContinueDays();
        this.continueDay = continueDays.arriveContinueNeedDays;
        this.addPoint = continueDays.todaySignInPoint;
    }

    async getSignInDetail() {
        const calendarDays = this.$refs.jcalendar.calendarDays.filter((d) => d.isThisMonth);
        this.startTime = (new Date(this.toIOSDate(calendarDays[0].fullDate)).getTime() / 1000).toString();
        this.endTime = (
            new Date(this.toIOSDate(calendarDays[calendarDays.length - 1].fullDate)).getTime() / 1000
        ).toString();
        const params = {
            start_time: this.startTime,
            end_time: this.endTime,
        };
        this.signData = [];
        const signInDetail = await getMonthCheckDetail(params);
        this.signData = signInDetail.reduce(function (a, b) {
            return a.concat(b);
        });
        this.signDataTotal = {
            random: +new Date(),
            dataSource: [...this.signData],
            continueDay: this.continueDay,
            todaySigned: this.todaySigned,
            addPoint: this.addPoint,
        };
    }

    showMoreDetail() {
        this.isShowDetail = !this.isShowDetail;
    }

    getData(currentYearAndMonth: any) {
        this.getSignInDetail();
    }

    async clickSign() {
        const signResult = await signInDaliy();
        if (signResult.lastSignInTime) {
            Utils.Toast('签到成功', 1000);
            // 签到成功手动计算积分
            this.totalPoints = Number(this._ScoreGet.totalPoints) + Number(this.addPoint);
            this.initRightsData(); // 更新权益数据
            await this.fetchContinueDays();
            await this.getSignInDetail();
            await this.getSignInStatus();
        }
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }

    navToPointsPage() {
        Utils.navigateTo('/pagesA/point/new_point');
    }

    gotoCoinDetail() {
        uni.navigateTo({
            url: '/pages/coinDetail/coinDetail',
        });
    }

    async CollectAll() {
        await getScoreCollectAll({ type: 3 });
        this.points_show = false;
        this.performList = [];
        await this.getScoreGet();
    }

    // 初始化权益数据
    initRightsData() {
        const totalGoldSum = Number(this.totalGoldSum);
        this.rightsData = [
            {
                id: 1,
                name: '领金币',
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14a4323040017578.png',
                iconActive:
                    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14a4323040017578.png',
                unlocked: totalGoldSum >= 0,
                unlockCoin: 0,
            },
            {
                id: 2,
                name: '金币抽奖',
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14b07d3070011050.png',
                iconActive:
                    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14e3183200012467.png',
                unlocked: totalGoldSum >= this.min_draw_gold,
                unlockCoin: this.min_draw_gold,
                route: '/pages/luckyDraw/luckyDraw',
            },
            {
                id: 3,
                name: '金币兑换',
                icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14ce2f3150021764.png',
                iconActive:
                    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687236a14c4eb3130024084.png',
                unlocked: totalGoldSum >= this.min_exchange_gold,
                unlockCoin: this.min_exchange_gold,
                route: '/pages/coin/coinExchange',
            },
        ];
    }

    // 处理权益点击
    handleRightClick(item: any) {
        if (item.unlocked) {
            // 已解锁，跳转到对应页面
            uni.navigateTo({
                url: item.route,
            });
        } else {
            // 未解锁，提示用户
            Utils.Toast('您尚未解锁此活动', 2000);
        }
    }

    // 获取进度段宽度
    getSegmentWidth(segment: number): number {
        const totalGoldSum = Number(this.totalGoldSum);
        if (segment === 1) {
            // 第一段
            if (totalGoldSum >= this.min_draw_gold) return 100;
            if (totalGoldSum <= 0) return 0;
            return (totalGoldSum / this.min_draw_gold) * 100;
        } else if (segment === 2) {
            // 第二段
            if (totalGoldSum >= this.min_exchange_gold) return 100;
            if (totalGoldSum <= this.min_draw_gold) return 0;
            return ((totalGoldSum - this.min_draw_gold) / (this.min_exchange_gold - this.min_draw_gold)) * 100;
        }
        return 0;
    }
}
</script>

<style lang="scss" scoped>
@import './coin.scss';
</style>
