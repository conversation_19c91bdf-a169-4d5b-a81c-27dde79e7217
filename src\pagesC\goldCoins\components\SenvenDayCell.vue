<template>
    <view>
        <view :class="{ 'whole-line': true, colorful: isColorful, 'short-length': isShortLength }">
            <view class="left-part">
                <view class="hint-text">
                    {{ item.date }}
                </view>
                <view class="num"> +{{ item.goldValue }} </view>
            </view>
            <view class="right-icon"> </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return { };
    },
    props: {
        item: {
            type: Object,
            default: () => ({}),
        },
        index: {
            type: Number,
            default: 0,
        },
        isShortLength: {
            type: Boolean,
            default: false,
        },
    },
    created() {},
    watch: {},
    computed: {
      isColorful() {
        return this.item.isToday && this.item.signedIn;
      },
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.whole-line {
    width: 580rpx;
    height: 200rpx;
    border-radius: 30rpx;
    background: #ffffff;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;

    &.colorful {
        background: linear-gradient(316deg, #ffb547 13%, #ff8826 43%, #fd5922 72%);

        .hint-text,
        .num {
            color: white;
        }

        .right-icon {
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fdd4fda6bf8950011729.png');
            width: 580rpx;
            height: 200rpx;
            left: 0;
            top: 0;
        }
    }

    &.short-length {
        width: 380rpx;
        padding: 0;
        display: block;

        .left-part {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 16rpx 0 12rpx;
        }

        .hint-text {
            font-size: 24rpx;
            height: 24rpx;
            line-height: 24rpx;
        }

        .num {
            font-size: 44rpx;
            height: 44rpx;
            line-height: 44rpx;
            margin-top: 0;
        }

        .right-icon {
            width: 300rpx;
            height: 116rpx;
            left: 26rpx;
            top: 32rpx;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fdd4fd9dc78920012033.png');
        }
    }
}

.hint-text {
    font-family: MiSans;
    font-size: 24rpx;
    font-weight: normal;
    line-height: 32rpx;
    height: 32rpx;
    color: #777777;
}

.num {
    font-family: MiSans;
    font-size: 56rpx;
    height: 56rpx;
    line-height: 56rpx;
    font-weight: 600;
    color: #ff2300;
    margin-top: 12rpx;
}

.right-icon {
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fdd4fd8c028880070593.png');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    top: 2rpx;
    left: 249rpx;
    width: 325rpx;
    height: 181rpx;
}
</style>
