<template>
    <view class="permission">
        <CustomBar :title="title" :customBack="true" backText="取消" @back="handleCancel"></CustomBar>

        <view class="permission__search">
            <view class="permission__search-inner" :class="{ center: !isFocus }">
                <view @click="isFocus = true" v-if="!isFocus" class="permission__search-inner-container">
                    <image
                        class="permission__search-inner-pre"
                        src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png"
                    />
                    <view>可搜索其他用户</view>
                </view>

                <template v-else>
                    <image
                        class="permission__search-inner-pre"
                        src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png"
                    />
                    <input
                        type="text"
                        v-model="permissionInput"
                        @input="searchDebounce"
                        :focus="isFocus"
                        @blur="handleBlur"
                    />
                </template>

                <image
                    v-if="!!permissionInput"
                    class="permission__search-inner-close"
                    src="https://wpm-cdn.dreame.tech/images/202306/446725-1686362627223.png"
                    @click="handleClear"
                />
            </view>

            <view class="permission__search-cancel" v-if="!!permissionInput" @click="handleCancel">取消</view>
        </view>

        <view class="permission__tip" v-if="!permissionInput">以下全部关注和粉丝</view>

        <view v-if="userList.length > 0" class="permission__content">
            <view
                v-for="(item, index) in userList"
                :key="index"
                class="permission__content-item"
                @click="handleSelect(item)"
            >
                <view class="permission__content-item_container">
                    <image class="permission__content-item_container_avatar" :src="item.avatar" />
                    <image class="permission__content-item_container_icon" :src="item.user_avatar_icon" v-if="item.user_avatar_icon" />
                </view>
                <view class="permission__content-item_name">
                    <rich-text class="u-line-1" :nodes="item.highlight_name"></rich-text>
                </view>
                <image class="permission__content-item_icon" :src="checkImage(item.user_id)" />
            </view>
        </view>

        <view v-else class="permission__empty">
            <image src="https://wpm-cdn.dreame.tech/images/202306/308776-1686360313376.png" />
            <text>{{ permissionInput && userList.length === 0 ? '未找到相关结果' : '当前暂无可选用户' }}</text>
        </view>

        <view class="permission__bottom-wrapper" v-if="currentFans.length > 0">
            <view class="permission__bottom">
                <view class="permission__bottom-avatars">
                    <image v-for="(item, index) in displaySelectFans" :key="index" :src="item.avatar" />
                    <view v-if="currentFans.length > 3">+{{ currentFans.length - 3 }}</view>
                </view>
                <view class="permission__bottom-count" @click="handleFinish">已选（{{ currentFans.length }}）</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import {
    ContentPermissionType,
    FansItem,
    getSearchUser,
    searchFollowList,
    updatePermission,
} from '@/http/requestGo/community';
import { Component, Vue } from 'vue-property-decorator';
import { debounce } from 'lodash';

@Component
export default class UserPermission extends Vue {
    public title: string = '部分可见';
    public permissionInput: string = '';
    public selectFans: FansItem[] = [];
    public unSelectFans: FansItem[] = [];
    public permissionType: ContentPermissionType = ContentPermissionType.PUBLIC;
    public modifyType: 'create' | 'edit' = 'create';
    public contentId = '';
    public isFocus = false;

    public userList: FansItem[] = [];
    public page = 1;
    public page_size = 10;

    get displaySelectFans() {
        return this.currentFans.length >= 3
            ? this.currentFans.slice(this.currentFans.length - 3, this.currentFans.length)
            : this.currentFans;
    }

    get currentFans() {
        return this.permissionType === ContentPermissionType.NOT_ALLOW ? this.unSelectFans : this.selectFans;
    }

    get searchDebounce() {
        return debounce(() => {
            this.page = 1;
            const api = this.permissionInput ? getSearchUser : searchFollowList;
            api({
                keyword: this.permissionInput,
                page: this.page,
                page_size: this.page_size,
            }).then((res) => {
                const regex = new RegExp(`(${this.permissionInput})`, 'i');
                this.userList = (res.data.list || []).map((v) => {
                    return { ...v, highlight_name: v.nick_name.replace(regex, '<div class="highlight">$1</div>') };
                });
                this.page++;
            });
        }, 500);
    }

    onLoad(options) {
        this.title = Number(options.type) === ContentPermissionType.PARTIALLY ? '部分可见' : '不给谁看';
        this.permissionType = Number(options.type);

        const createContentData = uni.getStorageSync('createContent');
        this.selectFans = createContentData.selectFans || [];
        this.unSelectFans = createContentData.unSelectFans || [];
        this.modifyType = options.modifyType;
        this.contentId = options.contentId;

        const regex = new RegExp(`(${this.permissionInput})`, 'i');
        searchFollowList({ keyword: this.permissionInput, page: 1, page_size: this.page_size, type: [1, 2, 3] }).then(
            (res) => {
                this.userList = (res.data.list || []).map((v) => {
                    return { ...v, highlight_name: v.nick_name.replace(regex, '<div class="highlight">$1</div>') };
                });
                this.page++;
            },
        );
    }

    onReachBottom() {
        const params = {
            keyword: this.permissionInput,
            page: this.page,
            page_size: this.page_size,
        };

        const api = this.permissionInput ? getSearchUser : searchFollowList;
        api(params).then((res) => {
            const regex = new RegExp(`(${this.permissionInput})`, 'i');
            const result = (res.data.list || []).map((v) => {
                return { ...v, highlight_name: v.nick_name.replace(regex, '<div class="highlight">$1</div>') };
            });
            this.userList = this.userList.concat(result);
            this.page++;
        });
    }

    handleClear() {
        this.permissionInput = '';
        this.isFocus = false;
        this.page = 1;
        this.userList = [];
        const regex = new RegExp(`(${this.permissionInput})`, 'i');
        searchFollowList({ keyword: this.permissionInput, page: 1, page_size: this.page_size, type: [1, 2, 3] }).then(
            (res) => {
                this.userList = (res.data.list || []).map((v) => {
                    return { ...v, highlight_name: v.nick_name.replace(regex, '<div class="highlight">$1</div>') };
                });
                this.page++;
            },
        );
    }

    handleCancel() {
        uni.setStorageSync('createContent', {
            ...uni.getStorageSync('createContent'),
            permissionClose: 0,
        });
        Utils.goBack();
    }

    checkImage(id) {
        return this.currentFans.findIndex((v) => v.user_id === id) !== -1
            ? 'https://wpm-cdn.dreame.tech/images/202412/787633-1735281937878.png'
            : 'https://wpm-cdn.dreame.tech/images/202412/371797-1735281855021.png';
    }

    handleSelect(item) {
        if (this.permissionType === ContentPermissionType.PARTIALLY) {
            this.unSelectFans = [];
            const index = this.selectFans.findIndex((v) => v.user_id === item.user_id);
            if (index !== -1) {
                this.selectFans.splice(index, 1);
            } else {
                this.selectFans.push(item);
            }
        }

        if (this.permissionType === ContentPermissionType.NOT_ALLOW) {
            this.selectFans = [];
            const index = this.unSelectFans.findIndex((v) => v.user_id === item.user_id);
            if (index !== -1) {
                this.unSelectFans.splice(index, 1);
            } else {
                this.unSelectFans.push(item);
            }
        }
    }

    handleFinish() {
        uni.setStorageSync('createContent', {
            ...uni.getStorageSync('createContent'),
            selectPermission: this.permissionType,
            selectFans: this.selectFans,
            unSelectFans: this.unSelectFans,
            permissionClose: 1,
        });

        if (this.modifyType === 'edit') {
            updatePermission({
                content_id: this.contentId,
                permission_type: this.permissionType,
                user_ids: this.currentFans.map((v) => v.user_id),
            });
        }
        Utils.goBack();
    }

    handleBlur() {
        if (!this.permissionInput) {
            this.isFocus = false;
        }
    }
}
</script>

<style lang="scss" scoped>
.permission {
    &__search {
        margin: 8rpx 38rpx;
        @include flex(row, center, center);

        &-inner {
            height: 84rpx;
            padding-left: 24rpx;
            border-radius: 115rpx;
            background: #f4f4f4;
            flex-grow: 1;
            @include flex(row, flex-start, center);

            &.center {
                @include flex(row, center, center);
            }

            &-container {
                @include flex(row, center, center);
                flex-grow: 1;
                font-size: 28rpx;
                color: #6a6a6a;
                line-height: 84rpx;

                image {
                    margin-right: 8rpx;
                }
            }

            &-pre {
                width: 38rpx;
                height: 38rpx;
            }

            &-close {
                width: 30rpx;
                height: 30rpx;
                padding: 24rpx;
            }

            input {
                flex-grow: 1;
                margin-left: 8rpx;
            }
        }

        &-cancel {
            padding-left: 30rpx;
            font-size: 32rpx;
            color: #404040;
            line-height: 84rpx;
        }
    }

    &__tip {
        padding: 8rpx 38rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #777777;
        line-height: 38rpx;
    }

    &__content {
        &-item {
            padding: 32rpx 38rpx 32rpx 30rpx;
            @include flex(row, flex-start, center);
            &_container {
                position: relative;
                height: 96rpx;
                width: 96rpx;
                margin-right: 30rpx;
                &_avatar {
                    height: 96rpx;
                    width: 96rpx;
                    border-radius: 50%;
                }
                &_icon {
                    position: absolute;
                    right: 0;
                    width: 32rpx;
                    height: 32rpx;
                    bottom: 0;
                }
            }
            &_name {
                font-weight: 700;
                font-size: 32rpx;
                color: #121212;
                flex-grow: 1;
                // max-width: 460rpx !important;
            }

            &_icon {
                flex: 0 0 46rpx;
                height: 46rpx;
                margin-left: 50rpx;
            }
        }
    }

    &__empty {
        padding-top: 210rpx;
        @include flex(column, center, center);

        image {
            width: 508rpx;
            height: 508rpx;
            margin: 0 auto 8rpx auto;
        }

        text {
            font-size: 28rpx;
            color: #777777;
            line-height: 38rpx;
        }
    }

    &__bottom {
        padding: 30rpx 38rpx calc(env(safe-area-inset-bottom) + 30rpx) 38rpx;
        @include flex(row, space-between, center);
        width: 100%;
        position: fixed;
        bottom: 0;
        background-color: white;
        border-top: 2rpx solid #eeeeee;
        &-wrapper {
            position: relative;
            height: calc(env(safe-area-inset-bottom) + 124rpx);
        }

        &-avatars {
            @include flex(row, center, center);
            image {
                width: 84rpx;
                height: 84rpx;
                margin-right: 8rpx;
                border-radius: 50%;
            }

            view {
                width: 84rpx;
                height: 84rpx;
                background: #f4f4f4;
                font-size: 28rpx;
                font-weight: 500;
                color: #9e9e9e;
                border-radius: 50%;
                @include flex(row, center, center);
            }
        }

        &-count {
            @include flex(row, center, center);
            padding: 12rpx 30rpx;
            background: #e8dec1;
            border-radius: 192rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #8c6533;
        }
    }
}

::v-deep .highlight {
    display: inline-block;
    color: #ab8c5e;
}
</style>
