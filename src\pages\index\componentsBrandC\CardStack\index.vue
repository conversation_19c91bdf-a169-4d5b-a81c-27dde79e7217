<template>
  <view class="card-stack-container">
      <view class="card-stack"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseLeave"
          @wheel="handleWheel"
          @scroll="handleScroll"
      >
          <!-- <view
              v-for="(item) in visibleCards" :key="`${item.id}-${item.stackIndex}`"
              class="card" :class="{
                  'card-active': item.isCurrent,
                  'card-prev': item.displayIndex === 0,
                  'card-next': item.displayIndex === 2
              }"
              :style="getCardStyle(item.displayIndex)" @click="handleCardClick(item)"
          >
              <image :src="item.image || item.icon" class="card-image" mode="widthFix" />
          </view> -->
          <transition-group :name="deltaY < 0 ? 'slide' : 'slide-down'" tag="div" class="stack-wrapper">
            <view
              v-for="(item) in visibleCards" :key="`${item.id}-${item.stackIndex}`"
              class="card" :class="{
                  'card-active': item.isCurrent,
                  'card-prev': item.displayIndex === 0,
                  'card-next': item.displayIndex === 2
              }"
              :style="getCardStyle(item.displayIndex)" @click.stop="handleCardClick(item)"
          >
              <image :src="item.image || item.icon" class="card-image" mode="widthFix" />
          </view>
        </transition-group>
      </view>

      <!-- 指示器 -->
      <view class="indicators" v-if="showIndicators">
          <view
              v-for="(item, index) in swiperList"
              :key="index"
              class="indicator"
              :class="{ 'indicator-active': index === currentIndex }" @click="goToCard(index)"
          >
          </view>
      </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import memberLevel from '../ActivityBanner/MemberLevel.vue'

@Component({
    components: {
        memberLevel,
    },
})

export default class CardStack extends Vue {
  @Prop({ default: true }) private autoPlay!: boolean;
  @Prop({ default: 5000 }) private autoPlayInterval!: number;
  @Prop({ default: true }) private showIndicators!: boolean;
  @Prop({ default: 3 }) private visibleCount!: number;
  @Prop({ default: 50 }) private swipeThreshold!: number; // 滑动阈值
  @Prop({ default: true }) private enableSwipe!: boolean; // 是否启用滑动
  @Prop({ default: () => [] }) private swiperList!: any[]; // 卡片数据由父组件传递
  public currentIndex: number = 0;
  public isDragging: boolean = false;
  public startY: number = 0;
  public currentY: number = 0;
  public autoPlayTimer: any = null;
  public isUserInteracting: boolean = false;
  public deltaY: number = 0;

  get visibleCards() {
      const totalCards = this.swiperList.length;
      if (totalCards === 0) return [];

      // 根据卡片数量决定显示策略
      if (totalCards >= 3) {
          // 3张或以上：显示3张，支持无限循环
          const cards = [];

          // 计算要显示的3张卡片的索引
          for (let i = 0; i < 3; i++) {
              let cardIndex = this.currentIndex + i - 1; // -1, 0, 1

              // 处理循环
              if (cardIndex < 0) {
                  cardIndex = totalCards + cardIndex; // 负数时从末尾开始
              } else if (cardIndex >= totalCards) {
                  cardIndex = cardIndex % totalCards; // 超出时取模
              }

              cards.push({
                  ...this.swiperList[cardIndex],
                  stackIndex: cardIndex,
                  displayIndex: i, // 0, 1, 2
                  isCurrent: i === 1 // 中间的是当前卡片
              });
          }

          // 调试信息
          // console.log('🔍 可见卡片数量:', cards.length, '当前索引:', this.currentIndex, '总卡片数:', totalCards);
          // cards.forEach((card, i) => {
          //     const style = this.getCardStyle(card.displayIndex);
          //     const position = card.displayIndex === 0 ? '上一张' : card.displayIndex === 1 ? '当前' : '下一张';
          //     console.log(`  ${position}:`, card.text, `(displayIndex: ${card.displayIndex}, zIndex: ${style.zIndex})`);
          //     console.log(`    位置: translateY(${style.transform.match(/translateY\(([^)]+)\)/)?.[1] || '0px'}), scale(${style.transform.match(/scale\(([^)]+)\)/)?.[1] || '1'})`);
          // });

          return cards;
      } else {
          // 少于3张：显示所有卡片
          return this.swiperList.map((card, index) => ({
              ...card,
              stackIndex: index,
              displayIndex: index,
              isCurrent: index === this.currentIndex
          }));
      }
  }

  get prevIndex() {
      return this.currentIndex > 0 ? this.currentIndex - 1 : -1;
  }

  get nextIndex() {
      return this.currentIndex < this.swiperList.length - 1 ? this.currentIndex + 1 : -1;
  }

  created() {
      // 组件创建时不再需要获取数据，由父组件传递
  }

  mounted() {
      this.startAutoPlay();
      // 添加额外的事件监听器确保滚动隔离
      this.$nextTick(() => {
          this.setupScrollIsolation();
      });
  }

  beforeDestroy() {
      this.stopAutoPlay();
      this.cleanupScrollIsolation();
  }

  @Watch('autoPlay')
  onAutoPlayChange() {
      if (this.autoPlay) {
          this.startAutoPlay();
      } else {
          this.stopAutoPlay();
      }
  }

  // 触摸事件处理
  handleTouchStart(event: TouchEvent) {
      // console.log('🔥 TouchStart triggered', event.touches[0].clientY);
      this.isUserInteracting = true;
      this.stopAutoPlay();
      this.startY = event.touches[0].clientY;
      this.currentY = this.startY;
      // 适度阻止默认行为，保持滑动检测功能
      event.preventDefault();
      event.stopPropagation();
  }

  handleTouchMove(event: TouchEvent) {
      if (!this.isUserInteracting) return;
      this.currentY = event.touches[0].clientY;

      // 计算移动距离，只有超过一定阈值才认为是拖拽
      const deltaY = Math.abs(this.currentY - this.startY);
      if (deltaY > 5) { // 移动超过5px才算拖拽
          this.isDragging = true;
          // console.log('🔥 TouchMove - 拖拽中', { startY: this.startY, currentY: this.currentY, deltaY: this.currentY - this.startY });
      }

      // 关键：强制阻止页面滚动，确保只有卡片切换
      event.preventDefault();
      event.stopPropagation();
      // 安全检查stopImmediatePropagation是否存在
      if (typeof event.stopImmediatePropagation === 'function') {
          event.stopImmediatePropagation();
      }
      return false;
  }

  handleTouchEnd(event: TouchEvent) {
      if (!this.isUserInteracting) return;
      // 适度阻止默认行为，保持滑动检测功能
      event.preventDefault();
      event.stopPropagation();

      this.handleDragEnd();
  }

  // 鼠标事件处理
  handleMouseDown(event: MouseEvent) {
      this.isUserInteracting = true;
      this.stopAutoPlay();
      this.startY = event.clientY;
      this.currentY = this.startY;
      event.preventDefault();
      event.stopPropagation();
  }

  handleMouseMove(event: MouseEvent) {
      if (!this.isUserInteracting) return;
      this.currentY = event.clientY;
      this.isDragging = true;
      // 阻止默认行为，防止页面滑动
      event.preventDefault();
      event.stopPropagation();
  }

  handleMouseUp(event: MouseEvent) {
      if (!this.isUserInteracting) return;
      // 阻止默认行为，防止页面滑动
      event.preventDefault();
      event.stopPropagation();
      this.handleDragEnd();
  }

  handleMouseLeave() {
      if (this.isUserInteracting) {
          this.handleDragEnd();
      }
  }

  // 阻止滚轮事件
  handleWheel(event: WheelEvent) {
      event.preventDefault();
      event.stopPropagation();
  }

  // 阻止滚动事件
  handleScroll(event: Event) {
      event.preventDefault();
      event.stopPropagation();
  }

  // 设置滚动隔离
  setupScrollIsolation() {
      const container = this.$el as HTMLElement;
      if (container) {
          // 使用 passive: false 确保可以阻止默认行为
          container.addEventListener('touchstart', this.isolateTouch, { passive: false });
          container.addEventListener('touchmove', this.isolateTouch, { passive: false });
          container.addEventListener('touchend', this.isolateTouch, { passive: false });
          container.addEventListener('wheel', this.isolateWheel, { passive: false });
          console.log('🛡️ 滚动隔离已设置');
      }
  }

  // 清理滚动隔离
  cleanupScrollIsolation() {
      const container = this.$el as HTMLElement;
      if (container) {
          container.removeEventListener('touchstart', this.isolateTouch);
          container.removeEventListener('touchmove', this.isolateTouch);
          container.removeEventListener('touchend', this.isolateTouch);
          container.removeEventListener('wheel', this.isolateWheel);
          // console.log('🛡️ 滚动隔离已清理');
      }
  }

  // 隔离触摸事件
  isolateTouch(event: TouchEvent) {
      // 只在特定情况下阻止页面滚动，保留点击功能
      if (event.currentTarget === this.$el) {
          // console.log('🛡️ 隔离触摸事件:', event.type);
          // 只阻止默认滚动行为，不阻止事件传播，确保点击事件正常
          if (event.type === 'touchmove') {
              event.preventDefault();
          }
          // touchstart和touchend不阻止，让点击事件正常传递
      }
  }

  // 隔离滚轮事件
  isolateWheel(event: WheelEvent) {
      // console.log('🛡️ 隔离滚轮事件');
      event.preventDefault();
      event.stopPropagation();
  }

  handleDragEnd() {
      // console.log('🔥 TouchEnd - isDragging:', this.isDragging);
      if (!this.isDragging) {
          this.isUserInteracting = false;
          this.startAutoPlay();
          return;
      }

      const deltaY = this.currentY - this.startY;
      // console.log('🔥 DragEnd检测:', {
      //     deltaY,
      //     enableSwipe: this.enableSwipe,
      //     threshold: this.swipeThreshold,
      //     absValue: Math.abs(deltaY)
      // });

      if (!this.enableSwipe) {
          // console.log('🔥 滑动被禁用');
          this.isDragging = false;
          this.isUserInteracting = false;
          this.startAutoPlay();
          return;
      }

      if (Math.abs(deltaY) > this.swipeThreshold) {
          if (deltaY > 0) {
              this.prevCard();
          } else {
              this.nextCard();
          }
      } else {
          console.log('🔥 滑动距离不够，不切换卡片');
      }

      this.isDragging = false;
      this.isUserInteracting = false;
      this.startAutoPlay();
  }

  getCardStyle(index: number) {
      const totalCards = this.swiperList.length;

      if (totalCards === 0) return {};

      if (totalCards >= 3) {
          // 3张或以上：显示3张堆叠效果
          // index: 0=上一张, 1=当前, 2=下一张
          // 第二张向下偏移 14px, 第三张相对于第二张向下偏移7px
          const translateY = index === 0 ? 0 : (index === 1 ? 30 : 56); // 第一张0px，第二张14px，第三张25px(增加一点让第三张更明显)
          // 第二张缩小10%，第三张缩小20%
          const scale = index === 0 ? 1 : (index === 1 ? 0.82 : 0.58); // 第一张100%，第二张90%，第三张80%
          const zIndex = 10 + (2 - index); // 当前卡片最高层级，上一张次之，下一张最低
          const opacity = index === 0 ? 1 : (index === 1 ? 0.95 : 0.88); // 适度调整透明度，确保三张都可见

          return {
              transform: `translateY(${translateY}px) scale(${scale})`,
              zIndex: zIndex,
              opacity: opacity,
          };
      } else {
          // 少于3张：居中显示
          const translateY = index * 20; // 减小偏移
          const scale = 1 - index * 0.05; // 较小的缩放
          const zIndex = 10 + (1 - index);
          const opacity = 1 - index * 0.1;

          return {
              transform: `translateY(${translateY}px) scale(${scale})`,
              zIndex: zIndex,
              opacity: opacity,
          };
      }
  }

  goToCard(index: number) {
      if (index < 0 || index >= this.swiperList.length) return;
      this.currentIndex = index;
      this.$emit('card-change', this.swiperList[index], index);
  }

  nextCard() {
      const totalCards = this.swiperList.length;
      if (totalCards === 0) return;

      // 无限循环
      const nextIndex = (this.currentIndex + 1) % totalCards;
      this.goToCard(nextIndex);
  }

  prevCard() {
      const totalCards = this.swiperList.length;
      if (totalCards === 0) return;

      // 无限循环
      const prevIndex = this.currentIndex === 0 ? totalCards - 1 : this.currentIndex - 1;
      this.goToCard(prevIndex);
  }

  startAutoPlay() {
      if (!this.autoPlay || this.isUserInteracting) return;
      this.stopAutoPlay();
      console.log('🔥 开始自动播放');
      this.autoPlayTimer = setInterval(() => {
          this.nextCard();
      }, this.autoPlayInterval);
  }

  stopAutoPlay() {
      if (this.autoPlayTimer) {
          clearInterval(this.autoPlayTimer);
          this.autoPlayTimer = null;
      }
  }

  handleCardClick(item: any) {
      // 发射点击事件给父组件，让父组件处理跳转逻辑
      this.$emit('card-click', item);
  }
}
</script>

<style lang="scss" scoped>
.card-stack-container {
  position: relative;
  width: 674rpx;
  height: 360rpx; // 增加高度以容纳3张卡片
  overflow: visible; // 改为visible以显示堆叠效果
  // padding: 0 20rpx; // 增加内边距
  // 强力防止所有滚动相关行为
  overscroll-behavior: none;
  -webkit-overflow-scrolling: auto;
  touch-action: none; // 完全阻止默认触摸行为
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  // 确保卡片区域独立
  contain: layout style paint;
}

.card-stack {
  position: relative;
  width: 674rpx;
  height: 334rpx; // 回到最初的高度
  cursor: grab;
  margin: 0 auto;
  // max-width: 400px; // 限制最大宽度
  // 完全隔离卡片区域的触摸事件
  touch-action: none; // 阻止所有默认触摸行为
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior: none; // 阻止过度滚动
  // 防止滚动穿透
  position: relative;
  z-index: 10;

  &:active {
      cursor: grabbing;
  }
}

.card {
  position: absolute;
  top: 0;
  left: 0;
  width: 674rpx;
  height: 294rpx;
  border-radius: 40rpx; // 增加圆角
  overflow: hidden;
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); // 更平滑的动画
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); // 恢复渐变背景
  will-change: transform, opacity; // 优化性能
  pointer-events: auto; // 确保可以点击
  // 防止页面滚动和下拉刷新
  touch-action: none; // 完全阻止触摸默认行为
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior: contain;

  &.card-active {
      // box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25), 0 16px 64px rgba(0, 0, 0, 0.15);
      // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); // 当前卡片渐变
  }

  &.card-prev {
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1), 0 4px 20px rgba(0, 0, 0, 0.08);
      // background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); // 上一张卡片渐变
  }

  &.card-next {
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1), 0 4px 20px rgba(0, 0, 0, 0.08);
      // background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); // 下一张卡片渐变
  }
}

.card-image {
  width: 674rpx;
  height: 294rpx;
  object-fit: cover;
}

.card-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 48rpx;
  background: rgba(255, 255, 255, 0.1); // 简化的半透明背景
  backdrop-filter: blur(5px);
}

.card-title {
  color: white;
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 24rpx;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  line-height: 1.2;
  letter-spacing: 0.5px;
}

.card-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 32rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  line-height: 1.3;
  font-weight: 500;
}

.indicators {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 20;
}

.indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 4rpx solid rgba(255, 255, 255, 0.2);

  &.indicator-active {
      background-color: white;
      transform: scale(1.3);
      border-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  }

  &:hover {
      background-color: rgba(255, 255, 255, 0.7);
      transform: scale(1.1);
  }
}
</style>
