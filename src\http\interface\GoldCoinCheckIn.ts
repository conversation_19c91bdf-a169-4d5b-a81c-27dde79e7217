// https://dreametech.feishu.cn/wiki/SgVpwZW6giAnyKk0SgqcZkq5nge
// 获取签到状态
export interface SigninStatusResponse {
  // 用户最后一次签到时间;零时区ms级时间戳
  // 今天未签到 则是之前的某天
  // 今天已签到 则是今天
  lastSignInTime: number;
  // 是否显示昨日签到
  yesterdayIsShow: number;
   // 用户今天是否已经签到
  todaySignedIn: boolean;
  // 今日未签到时：连续签到天数 此处 0就是之前没签到，1 就是连续一天，昨天已签到，2 就是连续两天
  // 今日已签到：此数值包含今天的
  // 连续签到天数 对 7 取模 得到对应数组位置
  continueSignDays: number;
  // 金币连续签到获取的金币数值
  // 今天未签到 continueSignDays = 0 取集合 0 位置，得 2088，即 今日签到可得 2088，后六天依次下面六个数值
  // 今日已签到：continueSignDays -1 取集合下标位置
  continueSignInGoldValues: number[];
}

export interface TaskInfoResponse {
  // 上次领取时间，
  // 前端用这个时间和当前时间对比，达到 10 分钟以上，则领金币
  // 未达到则，10分钟 - （当前时间 - 上次领取时间 = 时间差） 得到还差多少时间，开始倒计时
  lastEventTime: number;
}
