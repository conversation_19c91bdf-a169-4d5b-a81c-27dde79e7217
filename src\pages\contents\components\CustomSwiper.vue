<template>
    <view
        class="u-swiper"
        :style="{
            position: fullScreenPosition,
            backgroundColor: bgColor,
            height: $u.addUnit(height),
            borderRadius: $u.addUnit(radius),
        }"
    >
        <view class="u-swiper__loading" v-if="loading">
            <u-loading-icon mode="circle"></u-loading-icon>
        </view>
        <swiper
            v-else
            class="u-swiper__wrapper"
            :style="{
                height: $u.addUnit(height),
            }"
            @change="change"
            :circular="circular"
            :interval="interval"
            :duration="duration"
            :autoplay="autoplay"
            :current="current"
            :currentItemId="currentItemId"
            :previousMargin="$u.addUnit(previousMargin)"
            :nextMargin="$u.addUnit(nextMargin)"
            :acceleration="acceleration"
            :displayMultipleItems="displayMultipleItems"
            :easingFunction="easingFunction"
        >
            <swiper-item class="u-swiper__wrapper__item" v-for="(item, index) in list" :key="index">
                <view
                    class="u-swiper__wrapper__item__wrapper"
                    :style="[itemStyle(index)]"
                >
                    <!-- 在nvue中，image图片的宽度默认为屏幕宽度，需要通过flex:1撑开，另外必须设置高度才能显示图片 -->
                    <image
                        class="u-swiper__wrapper__item__wrapper__image"
                        v-if="$u.test.image(item[keyName])"
                        :src="
                            Math.abs(currentIndex - index) < 2 || index === 0 || index === list.length - 1
                                ? item[keyName]
                                : ''
                        "
                        :mode="imgMode"
                        @tap="clickHandler(index)"
                        :style="{
                            height: $u.addUnit(height),
                            borderRadius: $u.addUnit(radius),
                        }"
                    ></image>
                    <video
                        ref="videoRef"
                        v-if="$u.test.video(item[keyName])"
                        :key="index"
                        class="video-poster"
                        :style="{ backgroundImage: `url(${item.poster})` }"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        :enable-progress-gesture="false"
                        :autoplay="true"
                        :controls="false"
                        :loop="true"
                        :muted="true"
                        object-fit="cover"
                        :src="
                            Math.abs(currentIndex - index) < 2 || index === 0 || index === list.length - 1
                                ? item[keyName]
                                : ''
                        "
                        @click="handleVideoClick"
                    >
                        <!-- <image
                            src="https://wpm-cdn.dreame.tech/images/202309/64f2ceafc58938092020542.png"
                            class="play"
                            @click="item.type === 'video' && handleClick(item)"
                        ></image> -->
                    </video>
                    <text
                        v-if="showTitle && $u.test.object(item) && item.title && $u.test.image(item[keyName])"
                        class="u-swiper__wrapper__item__wrapper__title u-line-1"
                        >{{ item.title }}</text
                    >
                </view>
            </swiper-item>
        </swiper>
        <view class="u-swiper__indicator" v-if="list.length > 1" :style="[$u.addStyle(indicatorStyle)]">
            <slot name="indicator">
                <u-swiper-indicator
                    v-if="!loading && indicator && !showTitle"
                    :indicatorActiveColor="indicatorActiveColor"
                    :indicatorInactiveColor="indicatorInactiveColor"
                    :length="list.length"
                    :current="currentIndex"
                    :indicatorMode="indicatorMode"
                ></u-swiper-indicator>
            </slot>
        </view>
        <CustomPlayVideo v-if="isShowVideo" :show="videoShow" :video="video" @hide="videoShow = false" />
    </view>
</template>

<script>
import props from './props.js';
import CustomPlayVideo from '@/components/CustomPlayVideo/CustomPlayVideo.vue';

/**
 * Swiper 轮播图
 * @description 该组件一般用于导航轮播，广告展示等场景,可开箱即用，
 * @tutorial https://www.uviewui.com/components/swiper.html
 * @property {Array} list 轮播图数据
 * @property {Boolean}  indicator    是否显示面板指示器（默认 false ）
 * @property {String}  indicatorActiveColor  指示器非激活颜色（默认 '#FFFFFF' ）
 * @property {String}  indicatorInactiveColor  指示器的激活颜色（默认 'rgba(255, 255, 255, 0.35)' ）
 * @property {String | Object}  indicatorStyle  指示器样式，可通过bottom，left，right进行定位
 * @property {String}  indicatorMode  指示器模式（默认 'line' ）
 * @property {Boolean}  autoplay    是否自动切换（默认 true ）
 * @property {String | Number}  current 当前所在滑块的 index（默认 0 ）
 * @property {String}  currentItemId  当前所在滑块的 item-id ，不能与 current 被同时指定
 * @property {String | Number}  interval    滑块自动切换时间间隔（ms）（默认 3000 ）
 * @property {String | Number}  duration    滑块切换过程所需时间（ms）（默认 300 ）
 * @property {Boolean}  circular    播放到末尾后是否重新回到开头（默认 false ）
 * @property {String | Number}  previousMargin  前边距，可用于露出前一项的一小部分，nvue和支付宝不支持（默认 0 ）
 * @property {String | Number}  nextMargin    后边距，可用于露出后一项的一小部分，nvue和支付宝不支持（默认 0 ）
 * @property {Boolean}  acceleration  当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持（默认 false ）
 * @property {Number}  displayMultipleItems  同时显示的滑块数量，nvue、支付宝小程序不支持（默认 1 ）
 * @property {String}  easingFunction  指定swiper切换缓动动画类型， 只对微信小程序有效（默认 'default' ）
 * @property {String}  keyName list数组中指定对象的目标属性名（默认 'url' ）
 * @property {String}  imgMode 图片的裁剪模式（默认 'aspectFill' ）
 * @property {String | Number}  height 组件高度（默认 130 ）
 * @property {String}  bgColor 背景颜色（默认   '#f3f4f6' ）
 * @property {String | Number}  radius 组件圆角，数值或带单位的字符串（默认 4 ）
 * @property {Boolean}  loading 是否加载中（默认 false ）
 * @property {Boolean}  showTitle    是否显示标题，要求数组对象中有title属性（默认 false ）
 * @property {Boolean}  fullScreenBtn   是否显示全屏按钮（默认 true ）
 * @property {Boolean}  isAutoPlayVideo   是否自动播放视频（默认 false ）
 * @property {Boolean}  isFullscreenPlay   是否全屏播放（默认 false ）
 * @event {Function(index)}  click  点击轮播图时触发  index：点击了第几张图片，从0开始
 * @event {Function(index)}  change  轮播图切换时触发(自动或者手动切换)  index：切换到了第几张图片，从0开始
 * @example  <u-swiper :list="list4" keyName="url" :autoplay="false"></u-swiper>
 */
export default {
    name: 'CustomSwiper',
    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],
    components: {
        CustomPlayVideo,
    },
    data() {
        return {
            currentIndex: 0,
            fullScreenPosition: 'relative',
            videoShow: false,
            video: {},
            videoPlayer: null,
        };
    },
    watch: {
        current(val, preVal) {
            if (val === preVal) return;
            this.currentIndex = val; // 和上游数据关联上
        },
    },
    computed: {
        itemStyle() {
            return (index) => {
                const style = {};
                // #ifndef APP-NVUE || MP-TOUTIAO
                // 左右流出空间的写法不支持nvue和头条
                // 只有配置了此二值，才加上对应的圆角，以及缩放
                if (this.nextMargin && this.previousMargin) {
                    style.borderRadius = uni.$u.addUnit(this.radius);
                    if (index !== this.currentIndex) style.transform = 'scale(0.92)';
                }
                // #endif
                return style;
            };
        },
    },
    methods: {
        handleClick(item) {
            // this.video = item;
            // this.videoShow = true;
            // this.$emit('changeRefresh', {
            //     video: item,
            //     videoShow: true,
            // });
        },

        // 轮播切换事件
        change(e) {
            // 当前的激活索引
            const { current } = e.detail;
            this.currentIndex = current;
            const firstItem = this.list[0];
            if (firstItem.type === 'video' && current !== 0) {
                firstItem.isPause = true;
            } else {
                firstItem.isPause = false;
            }
            this.$emit('change', e.detail);
        },
        // 点击某个item
        clickHandler(index) {
            this.$emit('click', index);
        },
        // 进入、退出全屏操作
        handleFullscreenChange(event) {
            uni.showModal({ title: JSON.stringify(event) });
            const { fullScreen = false } = event.detail;
            this.fullScreenPosition = fullScreen ? 'static' : 'relative';
        },
        handleVideoClick() {
            this.$emit('click');
        }
    },
};
</script>

<style lang="scss" scoped>
view,
scroll-view,
swiper-item {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: auto;
    align-items: stretch;
    align-content: flex-start;
}

.u-swiper {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    &__wrapper {
        flex: 1;

        &__item {
            flex: 1;

            &__wrapper {
                display: flex;
                flex-direction: row;
                align-items: center;
                position: relative;
                overflow: hidden;
                transition: transform 0.3s;
                flex: 1;

                &__image {
                    flex: 1;
                }

                &__video {
                    flex: 1;
                }

                &__title {
                    position: absolute;
                    background-color: rgba(0, 0, 0, 0.3);
                    bottom: 0;
                    left: 0;
                    right: 0;
                    font-size: 28rpx;
                    padding: 12rpx 24rpx;
                    color: #ffffff;
                    flex: 1;
                }
            }
        }
    }

    &__indicator {
        position: absolute;
        bottom: 40rpx;
    }
}

.video-poster {
    height: 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;

    .play {
        width: 120rpx;
        height: 120rpx;
    }
}
</style>
