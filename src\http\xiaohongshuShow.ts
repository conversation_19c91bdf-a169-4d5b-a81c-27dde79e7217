import req from './requestGo';

/**
 * 上传图片OCR结果
 *
 */
export const commentImageUpload = (params) => req.post('api/v1/comment/image/upload', params);

/**
 *
提交评论审核
 *
 */
export const commentAuditSubmit = (params) => req.post('api/v1/comment/audit/submit', params, { errorCodes: [55620013, 55620014] });

/**
 * 获取任务信息
 */
export const getTaskInfo = (params) => req.post('api/v1/comment/task/get', params);
