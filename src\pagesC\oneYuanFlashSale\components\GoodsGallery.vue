<template>
    <view class="gallery">
        <view class="gallery-tab-container" v-if="tabList.length > 1">
            <view class="gallery-tab-bar-wrap">
                <view class="tab" :style="{ width: tab.width + 'rpx' }" v-for="(tab, index) in tabList" :key="index"
                    :class="{ active: currentTab === tab.type }" @click="handleTabClick(tab)">
                    <image class="icon" :src="currentTab === tab.type ? tab.activeIcon : tab.icon"></image>
                    <text>{{ tab.label }}</text>
                </view>
            </view>
        </view>
        <view class="swiper" v-if="currentTab === 'image'">
            <custom-swiper ref="cswiper" bgColor="#ffffff" :height="SwiperHeight" radius="0" :circular="true" :autoplay="false"
                @change="handleSwiperChange" :list="goodsImages" indicatorStyle="right :24rpx; bottom: 56rpx"
                @click="handlePreviewImage" :atmosphereImg="goodsData.atmosphere_img">
                <view slot="indicator">
                    <view class="current-pages">{{ currentNum + 1 }}/{{ goodsImages.length }}</view>
                </view>
            </custom-swiper>
        </view>
        <view class="video" v-if="currentTab === 'video'" :style="{ height: SwiperHeight, backgroundImage: `url(${goodsVideo.poster})` }">
            <image src="https://wpm-cdn.dreame.tech/images/202309/64f2ceafc58938092020542.png" class="play"
                @click="handleClick(goodsVideo)"></image>
        </view>
        <view class="_3d" :style="{ height: SwiperHeight }" v-if="currentTab === '3d'">
            <image mode="aspectFill" @click="handle3DView" class="_3d_cover" :src="_3dCover" />
        </view>
        <!-- <CustomPlayVideo  :show="videoShow" :video="video" @hide="videoShow = false" /> -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from '@/components/CustomSwiper/CustomSwiper.vue';
import { GoodsModule, IGoodsInfo } from '@/store/modules/goods';
// import CustomPlayVideo from '@/components/CustomPlayVideo/CustomPlayVideo.vue';
import Utils from '@/common/Utils';

interface IVideo {
    url: string;
    title: string;
    poster: string;
    type: string;
    isPause: boolean;
}

interface IImage {
    url: string;
    title: string;
    type: string;
}

@Component({
    components: {
        CustomSwiper,
        // CustomPlayVideo,
    },
})
export default class GoodsGallery extends Vue {
    public currentNum: number = 0;
    public currentTab: string = 'image';
    public videoShow: boolean = false;
    public video: any = {};

    public tabActiveBGStyle: string = 'width: 118rpx; transform: translate3d(0rpx, 0rpx, 0rpx);';

    get goodsData(): IGoodsInfo {
        console.log(GoodsModule.goodsInfo);
        return GoodsModule.goodsInfo;
    }

    get goodsImages(): Array<IImage> {
        const _goodsInfo = GoodsModule.goodsInfo;
        if (_goodsInfo && _goodsInfo.images) {
            const newList: Array<IImage> = [];
            const list = _goodsInfo.images.split('|');
            list.forEach((itm: string) => {
                const item: IImage = { url: '', title: '', type: 'image' };
                item.url = itm;
                newList.push(item);
            });
            return newList;
        }
        return [];
    }

    get goodsVideo(): IVideo {
        const _goodsInfo = GoodsModule.goodsInfo;
        return {
            url: _goodsInfo.video,
            title: '',
            poster: `${_goodsInfo.video}?x-oss-process=video/snapshot,t_12000,m_fast`,
            type: 'video',
            isPause: false,
        };
    }

    get isFoldableDevice(): Boolean {
        return Utils.isFoldableDevice();
    }

    get SwiperHeight(): String {
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        return this.isFoldableDevice ? screenWidth * (900 / 824) + 'px' : '828rpx';
    }

    get _3dCover(): String {
        const _goodsInfo = GoodsModule.goodsInfo;
        return _goodsInfo.cover_image_3d;
    }

    get _3dModel(): String {
        const _goodsInfo = GoodsModule.goodsInfo;
        return _goodsInfo.model_3d;
    }

    get tabList(): Array<any> {
        const tabList = [
            {
                icon: 'https://wpm-cdn.dreame.tech/images/202304/644769dd3380d2111928583.png',
                activeIcon: 'https://wpm-cdn.dreame.tech/images/202304/644769dd338212111941817.png',
                label: '图片',
                type: 'image',
                width: 124,
            },
        ];
        if (this.goodsVideo.url) {
            tabList.push({
                icon: 'https://wpm-cdn.dreame.tech/images/202304/644769dcf17889891941848.png',
                activeIcon: 'https://wpm-cdn.dreame.tech/images/202304/644769dcf17889891921220.png',
                label: '视频',
                type: 'video',
                width: 124,
            });
        }
        if (this._3dCover) {
            tabList.push({
                icon: 'https://wpm-cdn.dreame.tech/images/202304/644769dd337672101921263.png',
                activeIcon: 'https://wpm-cdn.dreame.tech/images/202304/644769dcf176b9891928595.png',
                label: '3D',
                type: '3d',
                width: 124,
            });
        }
        return tabList;
    }

    handleTabClick(tab) {
        this.currentTab = tab.type;
        if (this.currentTab === 'video') {
            Utils.reportEvent('good_video_show', {
                product_id: this.goodsData.gid
            })
        }
    }

    handleSwiperChange(e) {
        this.currentNum = e.current;
        Utils.reportEvent('banner_swiper', {
        })
    }

    handle3DView() {
        const web_url = encodeURIComponent(
            `${process.env.VUE_APP_BASE_URL}front/mp-h5/#/3d?model_url=${this._3dModel}`,
        );
        uni.navigateTo({
            url: `/pages/webView/webView?title=${this.goodsData.name}&web_url=${web_url}`,
        });
    }

    // 浏览大图
    handlePreviewImage() {
        uni.previewImage({
            current: this.goodsImages[this.currentNum].url,
            urls: this.goodsImages.map((img) => img.url),
        });
    }

    handleClick(item) {
        Utils.reportEvent('good_video_click', {
            product_id: this.goodsData.gid
        })
        Utils.navigateTo(`/pagesB/videoPlayer/index?src=${encodeURIComponent(item.url)}&poster=${encodeURIComponent(item.poster)}`)
    }
}
</script>

<style lang="scss" scoped>
.gallery {
    width: 100vw;

    position: relative;

    .gallery-tab-container {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 56rpx;
        z-index: 10;
        text-align: center;

        .gallery-tab-bar-wrap {
            display: flex;
            justify-content: center;

            .tab {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                color: $theme-text-color5;
                height: 48rpx;
                border-radius: 23rpx;
                background-color: $fill-color-bg-opcity;
                font-size: 24rpx;
                text-align: center;
                transition: all 0.3s;

                .icon {
                    margin-left: 4rpx;
                    width: 31rpx;
                    height: 31rpx;
                }

                &:nth-of-type(1) {
                    .icon {
                        height: 29rpx;
                    }
                }

                &+.tab {
                    margin-left: 24rpx;
                }
            }

            .active {
                color: $text-color-white;
                background: $fill-color-bg-dark1;
            }
        }
    }

    .swiper {
        .current-pages {
            min-width: 86rpx;
            height: 48rpx;
            line-height: 48rpx;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 38rpx;
            color: #ffffff;
            text-align: center;
            font-size: 28rpx;
            z-index: 2;
        }
    }

    .video,
    ._3d {
        width: 750rpx;
        height: 828rpx;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: black;

        ._3d_cover {
            width: 100%;
            height: 100%;
        }

        .play {
            width: 120rpx;
            height: 120rpx;
        }
    }
}

::v-deep .u-swiper__wrapper__item {
    &:first-child {
        .u-swiper__wrapper__item__wrapper {
            // background-image: url(https://wpm-cdn.dreame.tech/images/202412/6769293019a941050009488.png);
        }
    }
}
</style>

