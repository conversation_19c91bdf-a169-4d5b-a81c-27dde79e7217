<template>
    <view class="no_network u-flex-col u-col-center u-row-center" v-show="!isNetworkConnect">
        <!-- <image class="image" :src="require('@/static/no_network.png')"></image> -->
        <view class="empty_text">网络异常，请重新加载</view>
        <view class="reload" hover-class="btn-hover" @click="refresh">重新加载</view>
    </view>
</template>
<script>
export default {
    props: {
        isNetworkConnect: {
            default: false
        }
    },
    methods: {
        refresh() {
            this.$emit('onRefresh')
        }
    },
}
</script>
<style lang="scss">
.no_network {
    width: 100%;
    height: 100vh;

    .image {
        width: 506rpx;
        height: 506rpx;
    }

    .empty_text {
        font-size: 28rpx;
        color: $text-color-secondary;
        line-height: 38rpx;
    }

    .reload {
        margin-top: 70rpx;
        width: 292rpx;
        height: 92rpx;
        border-radius: 192rpx;
        border: 2rpx solid $text-color-primary;
        text-align: center;
        line-height: 88rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: $text-color-primary;
    }
}
</style>
