<template>
    <view class="login">
        <APPSHARE :link="link"></APPSHARE>
        <CustomBar
            hideContent
            title="推广赚佣金"
            :background="customBarBg"
            backIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c4e6b1a8f41090024046.png"
        >
            <template v-slot:operate>
                <!-- <view class="rule-wrap" @click="routeNext('rule')"> 活动规则 </view> -->
                <view class="right">
                    <view v-show="$isInApp()" class="share" @click="shareActivity"></view>
                    <!-- <view v-show="$isInApp()" class="rule" @click="routeNext('rule')"></view> -->
                </view>
            </template>
        </CustomBar>
        <view class="bg-wrapper" style="padding-bottom: 220rpx;">
            <view class="bg-logo">
                <view v-show="$isInApp()" class="rule" @click="routeNext('rule')"></view>
            </view>
            <RankList />
            <template v-if="$isInApp()">
                <view v-if="isEmployee === false" class="btn-apply is_employee" @click="applyEmployee">一键成为合伙人</view>
                <view v-if="isEmployee === true" class="btn-apply">
                    <view class="btn-select" @click="openGoodsPopup">选品去赚钱</view>
                    <view class="u-flex u-row-between">
                      <view class="btn-owner u-flex-col u-row-center" @click="routeNext('detail')">
                          我的佣金
                      </view>
                      <view class="btn-share" @click="shareActivity">邀请好友赚钱</view>
                    </view>
                </view>
            </template>
        </view>
        <EarnMoneySpendTimeoutTips
            v-if="$isInApp() && from == 'goldCoins'"
            ref="TimeoutTips"
            :position="{ top: '400rpx' }"
            :task-code="'richPlan'"
            :fromPage="from"
            :watchTime="15000"
        />

        <ShareDialog :show.sync="show" :productInfo="productInfo" :shareType="'active'" @share="handleShare">
            <template #active_tag>
                <img
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png"
                    style="
                        width: 78rpx;
                        height: 56rpx;
                        position: absolute;
                        left: 20rpx;
                        top: 10rpx;
                        transform: translate(-50%, -50%);
                    "
                />
            </template>
        </ShareDialog>

        <GoodsPopup ref="GoodsPopup" @gotoGdetailPage="gotoGdetailPage"/>
        <RulePopup ref="RulePopup" />
        <EarnMoneySpendTimeoutTips
            v-if="$isInApp() && from == 'earnMoneySpend'"
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '400rpx' }"
            :task-code="'viewGoodsRichPlanMoney'"
        />
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { Vue, Component } from 'vue-property-decorator';
import RankList from '../components/RankList.vue';
import Partner from '../components/Partner.vue';
import GoodsPopup from '../components/GoodsPopup.vue';
import GoldCoinTimeoutTips from '@/components/GoldCoinTimeoutTips/GoldCoinTimeoutTips.vue';
import RulePopup from '../components/RulePopup.vue';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import { UserModule } from '@/store/modules/user';
import { mineInfo } from '@/http/mine';
import { getPartnerGoodsList } from '@/http/goods';
import { applyEmployee } from '@/http/vip';
import Constants from '@/common/Constants';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

@Component({
    components: {
        RankList,
        Partner,
        GoodsPopup,
        RulePopup,
        GoldCoinTimeoutTips,
        ShareDialog,
        EarnMoneySpendTimeoutTips,
    },
})
export default class AmbassadorLogin extends Vue {
    $refs!: {
        TimeoutTips,
        GoodsPopup,
        RulePopup,
        EarnMoneySpendTimeoutTips,
    };

    public active_id = 0;
    public shareType = '';
    public customBarBg = 'transparent';
    public isEmployee: boolean = null;
    public commissionMoney = 0
    public show = false;
    public productInfo: any = {};
    public shareProduct: any = {};
    public shareId = '';
    public link = '';
    public shareName = '';
    public uid = 0;
    public from = '';

    onPageScroll(e) {
        if (e.scrollTop > 0) {
            this.customBarBg = '#FAEEE4';
        } else {
            this.customBarBg = 'transparent';
        }
    }

    onShow() {
        this.getIsEmployee();
        setTimeout(() => {
            this.$refs.TimeoutTips.getViewGoodsTaskStatus();
        }, 500);
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
    }

    async applyEmployee() {
        await applyEmployee()
        this.getIsEmployee();
    }

    async getIsEmployee() {
        const { is_employee, nick_name, uid } = await mineInfo()
        this.isEmployee = is_employee === 1;
        this.shareName = nick_name;
        this.uid = uid
    }

    routeNext(route: 'rule' | 'detail') {
        switch (route) {
            case 'rule':
                // uni.navigateTo({ url: '/pagesC/ambassador/rule/index' })
                this.$refs.RulePopup.toggleVisible()
                break;
            case 'detail':
                uni.navigateTo({ url: '/pagesC/ambassador/detail/index?active_id=' + this.active_id });
                break;
        }
    }

    openGoodsPopup() {
        this.$refs.GoodsPopup.toggleVisibe()
    }

    shareActivity() {
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #42b983;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.shareType = 'active';
        this.show = true;
        this.shareId = this.shareProduct.gid;
        this.productInfo = {
            name: this.shareProduct.name,
            desc: this.shareProduct.introduce,
            image: this.shareProduct.cover_image,
            price: this.shareProduct.price,
            priceColor: '#2B1E10',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    gotoGdetailPage(item) {
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #42b983;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.shareType = 'goods';
        this.show = true;
        this.shareId = item.gid;
        this.productInfo = {
            name: item.name,
            desc: item.introduce,
            image: item.cover_image,
            price: item.price,
            priceColor: '#2B1E10',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;

        if (this.shareType === 'active') {
            const shareUrl = `${process.env.VUE_APP_MALL_EMPLOYEE}?employee=${this.uid}`;
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: shareUrl,
                    jumpLink: shareUrl,
                },
                {
                    content: {
                        url: shareUrl,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2f4e83fe9510087957.png',
                        title: `推广赚佣金`,
                        desc: '来DREAME APP，邀好友下单，享丰厚佣金。',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `我是追觅合伙人${this.shareName}和我一起到追觅APP，享受更多购物优惠`,
                    },
                },
            );
        }

        if (this.shareType === 'goods') {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.uid}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.uid}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}&employee=${this.uid}`,
                        image: this.productInfo.image,
                        title: this.productInfo.name,
                        desc: this.productInfo.desc,
                    },
                    extras: {
                        type: 'goods',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `${this.productInfo.price}元, ${this.productInfo.name}`,
                    },
                },
            );
            Utils.taskComplete('shareRichGoodsFriend');
        }
    }

    /* 获取首页随机商品 */
    async getHomeRandomGoods() {
        const { list = [] } = await getPartnerGoodsList({ page_size: 10, single: 1 });
        this.shareProduct = list[Math.floor(Math.random() * list.length)];
        console.log(this.shareProduct, '==============');
    }

    onLoad(options) {
        this.from = options.from;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_AMBASSADOR_INDEX_EXPOSURE,
        });

        this.link = options.link;
        this.active_id = options.active_id
        this.getHomeRandomGoods();
    }

    onUnload() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }
}
</script>

<style lang="scss" scoped>
.bg-wrapper {
    .bg-logo {
        position: relative;
        width: 100%;
        height: 874rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2d2a843eec2780012469.jpg');
        background-size: 100%;
        background-repeat: no-repeat;

        .rule {
            position: absolute;
            width: 140rpx;
            height: 50rpx;
            top: 200rpx;
            right: 0;
        }
    }

    .btn-apply {
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        bottom: 57rpx;
        width: 690rpx;
        line-height: 96rpx;
        font-size: 32rpx;
        text-align: center;
        color: #FFF1DF;

        &.is_employee {
            font-weight: 600;
            background: #1D0F02;
            border-radius: 12rpx;
            box-shadow: inset 0px 8px 20px 0px rgba(255, 232, 207, 0.41);
        }

        .btn-select {
            margin-bottom: 25rpx;
            height: 96rpx;
            background: #1D0F02;
            border-radius: 12rpx;
            box-shadow: inset 0px 8px 20px 0px rgba(255, 232, 207, 0.41);
        }

        .btn-owner,
        .btn-share {
            width: 321rpx;
            height: 96rpx;
            background: #D9431F;
            border-radius: 12rpx;
            box-shadow: inset 0px 8px 20px 0px rgba(255, 232, 207, 0.41);
        }

        .btn-owner {
            line-height: normal;
        }

        .btn-share {
            background: #1D0F02;
        }
    }
}
.login-logo-top {
    width: 100%;
    height: 1180rpx;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68877d138a0ec5650016391.png');
    background-size: 100%;
    background-repeat: no-repeat;
}
.login-submit-btn {
    z-index: 2;
    width: 236px;
    height: 48px;
    border-radius: 30px;
    opacity: 1;
    background: linear-gradient(110deg, rgba(245, 226, 186, 0.3) -13%, rgba(185, 131, 87, 0.3) 29%),
        linear-gradient(180deg, #ffebca -21%, #83634c 3%, #71533f 48%, #503627 98%);
    box-sizing: border-box;
    border-width: 0px 0px 1px 0px;
    border-style: solid;
    border-image: linear-gradient(270deg, rgba(238, 189, 107, 0) 15%, #ffffff 51%, rgba(229, 185, 116, 0) 94%) 0 0 1 0;
    box-shadow: inset 0px 4px 10px 0px rgba(255, 232, 207, 0.41);
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 50rpx);
    left: 50%;
    transform: translateX(-50%);
    font-size: 32rpx;
    color: #fff1df;
    text-align: center;
    line-height: 48px;
}
.product-list-wrap {
    background-color: #ffe3b5 !important;
    padding: 84rpx 20rpx 20rpx;
    margin-bottom: 160rpx;
    margin-top: 72rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
    border-radius: 16rpx;
    position: relative;
    min-height: 1800rpx;
    .bg {
        border-radius: 16rpx;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        min-height: 1800rpx;
        background-color: #ffe3b5 !important;
    }
    .cover-img {
        position: absolute;
        top: -38rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 308rpx;
        height: 76rpx;
        color: #fed9b1;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #fed9b1;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688ca2973efc22580012121.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.login {
    padding-bottom: 80rpx;
    background: #f3f2f7;
    .imageWrap {
        position: absolute;
        right: 16rpx;
        width: 144rpx;
        height: 56rpx;
        background: rgba(0, 0, 0, 0.7);
        border: 3rpx solid rgba(255, 255, 255, 0.17);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 76rpx;
        font-size: 24rpx;
        color: #f6f6f6;
    }
    &-back {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 48rpx;
        height: 48rpx;
        padding: 24rpx 24rpx 24rpx 32rpx;

        &-wrapper {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 204rpx;
            z-index: 10;
        }
    }

    &-logo {
        width: 780rpx;
        height: 204rpx;
    }

    &-submit {
        position: fixed;
        width: 642rpx;
        height: 92rpx;
        background: #f1c99e;
        box-shadow: 0rpx 8rpx 38rpx -4rpx rgba(241, 201, 158, 0.28);
        border-radius: 192rpx 192rpx 192rpx 192rpx;
        font-weight: 500;
        font-size: 31rpx;
        color: #6b411b;
        line-height: 42rpx;
        text-align: center;
        line-height: 92rpx;
        bottom: calc(env(safe-area-inset-bottom) + 50rpx);
        left: 50%;
        transform: translateX(-50%);
    }
}
.right {
    height: 100%;
    display: flex;
    align-items: center;

    .share {
        position: absolute;
        right: 28rpx;
        height: 46rpx;
        width: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c4e827d1985120024461.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .rule {
        position: absolute;
        right: 28rpx;
        height: 46rpx;
        width: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfca2106690670010343.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
}
</style>
