<template>
    <view>
        <view v-if="target === 'serve'" class="subtitle"> 服务大厅 </view>
        <view class="serve-box" :class="{ 'serve-box-b': target === 'mine' }">
            <view v-if="target === 'mine'" class="subtitle-b"> 服务大厅 </view>
            <view class="serve-list u-flex u-col-center u-row-between"
                :class="target === 'mine' ? 'u-row-left' : 'u-row-around'">
                <view class="serve-item u-flex u-flex-col u-col-center u-row-center" v-for="(item, index) in serveList"
                    :key="index" @tap="handleTapService(item)">
                    <image class="serve-item-icon" :src="item.icon" mode="" />
                    <text class="serve-item-name">{{ item.name }}</text>
                </view>
                <view class="serve-item"></view>
                <view class="serve-item"></view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';
import Constants from '@/common/Constants';
enum JumpType {
    Page = 1, // 小程序内页面
    MiniProgram, // 第三方小程序
}
interface Service {
    name: string;
    icon: string;
    url: string;
    jump: JumpType; // 跳转类型
    isPhone: boolean; // 是否需要已注册会员
}
@Component
export default class ServiceHall extends Vue {
    @Prop({ type: String, default: 'serve' })
    readonly target!: string;

    public serveList: Service[] = [
        {
            name: '上下水服务',
            icon: 'https://wpm-cdn.dreame.tech/images/202207/804093-1656903607636.png',
            url: '/pagesC/dtdService/dtdService',
            jump: JumpType.Page,
            isPhone: true,
        },
        {
            name: '寄修服务',
            icon: 'https://wpm-cdn.dreame.tech/images/202206/057303-1655952410955.png',
            url: Constants.MINIPROGRAM.AFTER_SALE,
            jump: JumpType.MiniProgram,
            isPhone: true,
        },
        {
            name: '进度查询',
            icon: 'https://wpm-cdn.dreame.tech/images/202206/262803-1655952435409.png',
            url: Constants.MINIPROGRAM.AFTER_SALE,
            jump: JumpType.MiniProgram,
            isPhone: true,
        },
        // {
        //     name: '延保卡',
        //     icon: 'https://wpm-cdn.dreame.tech/images/202203/621df2242c8f51822018028.png',
        //     url: '/pages/guarantee/guarantee',
        //     jump: JumpType.Page,
        //     isPhone: true,
        // },
        // {
        //     name: '附近门店',
        //     icon: 'https://wpm-cdn.dreame.tech/images/202203/621df231ac6617061393043.png',
        //     url: '/pagesA/nearShop/nearShop',
        //     jump: JumpType.Page,
        //     isPhone: true,
        // },
    ];

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    beforeDestroy() {
        uni.$off('phoneAuth');
    }

    // 服务列表点击事件
    handleTapService(service: Service) {
        // 判断是否需要授权
        if (service.isPhone && !this.isPhone) {
            const target = 'service';
            // 进行授权
            UserModule.authFlow({ target });
            // 监听授权回调, 授权成功时进行跳转动作
            uni.$once('phoneAuth', (val) => {
                if (target === val) {
                    setTimeout(() => {
                        this.handleJump(service);
                    }, 1500);
                }
            });
        } else {
            // 直接跳转
            this.handleJump(service);
        }
    }

    handleJump(service) {
        switch (service.jump) {
            case JumpType.Page:
                uni.navigateTo({
                    url: service.url,
                });
                break;
            case JumpType.MiniProgram:
                uni.navigateToMiniProgram({
                    appId: service.url,
                });
                break;
        }
    }
}
</script>
<style lang="scss" scope>
.service-hall {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
}

.subtitle {
    height: 42rpx;
    font-size: 30rpx;
    color: #222222;
    line-height: 42rpx;
    margin: 38rpx 0 24rpx;
}

.serve-box {
    width: 100%;
    min-height: 180rpx;
    padding-top: 38rpx;
    background-color: #fff;
    border-radius: 8px;

    .serve-list {
        width: 100%;
        height: 100rpx;
        overflow: hidden;

        .serve-item {
            height: 100rpx;

            .serve-item-icon {
                width: 40rpx;
                height: 40rpx;
                margin-bottom: 17rpx;
            }

            .serve-item-name {
                font-size: 24rpx;
                color: $theme-text-color;
                line-height: 36rpx;
            }
        }
    }

    &.serve-box-b {
        height: 247rpx;
        padding: 40rpx 0 38rpx;
        border-radius: 0;

        .subtitle-b {
            width: 100%;
            font-size: 28rpx;
            color: #000000;
            line-height: 38rpx;
            padding-left: 26rpx;
            margin-bottom: 38rpx;
        }

        .serve-list {
            height: auto;
            padding: 0 26rpx;

            .serve-item {
                min-width: 120rpx;
                height: auto;
            }
        }
    }
}
</style>
