<template>
    <view>
        <u-popup :show="isShow" v-prevent-scroll="isShow" mode="bottom" :round="32" @close="onClose">
            <view class="container">
                <view class="goods-info u-flex u-row-left u-col-top">
                    <view class="goods-image">
                        <u--image :src="specsData.image" width="154rpx" height="154rpx" radius="12rpx"></u--image>
                    </view>

                    <view v-if="!pointsMall" class="info-right u-flex u-flex-col u-row-left u-col-top">
                        <view v-if="specsData.presale_info && specsData.presale_info.is_presale == 1">
                            <view class="presaleLeft">
                                <view class="presaleNumber">
                                    <text class="presaleTextStyle">定金</text>
                                    <text class="cnyStyle">¥</text><text class="depositStyle">{{
                                        specsData.presale_info ? specsData.presale_info.deposit : 0
                                    }}</text>
                                    <text v-if="specsData.presale_info &&
                                        specsData.presale_info.deposit != specsData.presale_info.expand_price
                                        ">
                                        <text class="discount">抵</text>
                                        <text class="cnyStyle">¥</text>
                                        <text class="expandPrice">{{ specsData.presale_info.expand_price || 0 }}</text>
                                    </text>
                                </view>
                                <view class="presalePrice">
                                    <text class="presaleTextStyle">预售价:</text>
                                    <text class="cnyStyle">¥</text><text class="presalePriceStyle">{{ specsData.price
                                    }}</text>
                                </view>
                            </view>
                        </view>
                        <view v-else-if="isGroupPurchase">
                            <view class="is_group_price">
                                <view>
                                  <text class="unit">¥</text>
                                  <text class="digit">{{ couponPrice }}</text>
                                </view>
                                <view class="group_price_text">拼团价</view>
                            </view>
                            <view class="mprice_is_group">
                                <view class="mprice_text">原价: </view>
                                <view class="mprice_digit">¥{{ group_price }}</view>
                            </view>
                        </view>
                        <view v-else>
                            <view class="price">
                                <text class="unit">¥</text>
                                <text class="digit">{{ couponPrice }}</text>
                            </view>
                             <block v-if="isShowPointDiscount">
                                <view class="point" v-html="getPointText()">
                                </view>
                             </block>
                             <!-- <block v-else>
                                 <view class="point" v-if="Number(discount)">积分可抵{{ discount }}元</view>
                             </block> -->
                        </view>
                    </view>
                    <view v-else class="info-right u-flex u-flex-col u-row-left u-col-top">
                        <view class="price-point">
                            <view class="point-shop"><text class="point-number">{{ specsData.point }}</text><text
                                    class="point-unit">积分</text></view>
                            <view class="plus-sign" v-if="Number(specsData.point) && Number(specsData.price)">+</view>
                            <CustomAmount v-if="Number(specsData.price)" :totalPrice="specsData.price"
                                :unitRight="unitRight" :totalStyle="totalStyle" :unitSize="unitSize" unitWeight="400" />
                        </view>
                        <view style="display: flex" v-if="goodsData.goods_limit_num != 0">
                            <custom-button :plain="true" color="#AB8C5E"
                                customStyle="border-radius: 38rpx;width:auto;height:40rpx;font-size: 24rpx;fontWeight:500;margin:0rpx 16rpx 20rpx 0rpx">限购{{
                                    goodsData.goods_limit_num }}件</custom-button>
                        </view>
                    </view>
                </view>

                <view class="shop_sort" v-if="goodsData.attr_cnf && goodsData.attr_cnf.length > 0">
                    <view class="goods-sku" v-for="(attr, index) in goodsData.attr_cnf" :key="attr.id">
                        <view class="sort">{{ `${attr.at_name}` }}</view>
                        <view class="sku-tag">
                            <view class="sku-tag-item u-flex u-flex-wrap">
                                <!-- u-flex-col u-col-center u-row-center -->
                                <view class="tag" v-for="item in attr.val" :key="item.id" @tap="selectAttr(item, index)"
                                    :class="{
                                        'tag-isImage': !item.at_image,
                                        'tag-active': Number(item.in_stock) && selectAvIds[index] === item.id,
                                        'tag-disabled': !Number(item.in_stock),
                                    }">
                                    <image v-if="item.at_image" alt="无图片" style="width: 206rpx; height: 206rpx"
                                        :src="item.at_image"></image>
                                   <view :class="{at_val_image: item.at_image, at_val: !item.at_image}">
                                        <view :class="{ at_val_name: item.at_image }">{{ `${item.at_val}` }}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else class="goods-sku">
                    <view class="sku-tag">
                        <view class="sku-tag-item u-flex u-flex-wrap">
                            <view class="tag u-flex-col u-col-center u-row-center tag-isImage" :class="{
                                'tag-active': true,
                                'tag-active-presale':
                                    specsData.presale_info && specsData.presale_info.is_presale == 1,
                                'tag-disabled': !Number(goodsData.status),
                            }">
                                <view>{{ `${goodsData.name}` }}</view>
                            </view>
                        </view>
                    </view>
                </view>

                <view v-if="!isSoldOut && !isNotSold" class="goods-num u-flex u-row-between u-col-center">
                    <view class="title">
                        数量<text class="limit" v-if="!isGroupPurchase">{{
                            Number(goodsData.goods_limit_num + '') > 0 ? ` (限购${goodsData.goods_limit_num || 1}件)` : ''
                        }}</text>
                    </view>
                    <view class="num-right">
                        <view class="number-box">
                            <number-box v-if="Number(goodsData.limit_num) > 0" v-model="specsData.num" :min="1"
                                :disabled="disabledInput" :disableMinus="disabledInput" :disablePlus="disabledInput"
                                :max="goodsData.limit_num" bgColor="#fff" buttonWidth="52rpx" buttonSize="52rpx"
                                color="#333333" @change="valChange" @blur="valChange" @overlimit="handleOverLimit">
                            </number-box>
                            <number-box v-else v-model="specsData.num" :min="1"
                                :disabled="disabledInput" :disableMinus="disabledInput" :disablePlus="disabledInput"
                                bgColor="#fff" buttonWidth="52rpx"
                                buttonSize="54rpx" color="#333333" @change="valChange" @blur="valChange"
                                @overlimit="handleOverLimit">
                            </number-box>
                        </view>
                    </view>
                </view>

                <block v-if="type === 1 && !isSoldOut && !isNotSold">
                    <view v-if="buyType === 1" class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(0)">
                        <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }"> 加入购物车</view>
                    </view>
                    <view v-else-if="buyType === 2" class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(3)">
                        <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }"> 选定并发起拼团</view>
                    </view>
                    <view v-else-if="buyType === 3" class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(4)">
                        <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }"> 立即购买</view>
                    </view>
                    <view v-else-if="buyType === 4" class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(4)">
                        <view class="comfirm-pay" style="background: #FF2968;color: #fff;" :class="{ 'btn-disabled': !isHasGoNext }"> 立即拼团</view>
                    </view>
                    <view v-else-if="pointsMall" class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(1)">
                        <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }">立即兑换</view>
                    </view>

                    <view v-else class="goods-button u-flex u-col-top u-row-center" hover-class="hover-btn"
                        @click="handleComfirm(1)">
                        <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }">
                            {{
                                specsData.presale_info && specsData.presale_info.is_presale == 1
                                ? '立即支付定金'
                                : '立即购买'
                            }}</view>
                    </view>
                </block>

                <view v-if="type === 2 && !isSoldOut && !isNotSold" class="goods-button u-flex u-col-top u-row-center"
                    hover-class="hover-btn" @click="handleComfirm(2)">
                    <view class="comfirm-pay" :class="{ 'btn-disabled': !isHasGoNext }">确定</view>
                </view>

                <view class="icon-close u-flex u-col-center u-row-center">
                    <image class="icon" src="@/static/close.png" @click="onClose" />
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import CustomAmount from '../CustomAmount/CustomAmount.vue';
import { GoodsModule, IGoodsAttrCnf, IGoodsInfo, ISpecsInfo } from '@/store/modules/goods';
import { initiateGroupPurchase } from '@/http/goods';
import { CartModule } from '@/store/modules/cart';
import NumberBox from './NumberBox/NumberBox.vue';
import Utils from '@/common/Utils';
import { batchAddShopCart } from '@/http/cart';
import { IUnionSource } from '@/store/interface/order';

@Component({
    components: {
        CustomAmount,
        NumberBox,
    },
})
export default class CustomPay extends Vue {
    vPreventScroll(vPreventScroll: any) {
        throw new Error('Method not implemented.');
    }

    // 是否是拼团商品
    @Prop({ type: Boolean, default: false })
    readonly isGroupGoods: Boolean;

     @Prop({ type: String, default: false })
    readonly orderType!: String; // 订单活动类型

    @Prop({ type: String, default: '' })
    readonly activityType!: String; // 活动类型

    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({ type: Number, default: 0 })
    readonly type!: Number;

    @Prop({ type: Number, default: 0 })
    readonly buyType!: Number; // 0: 购买 1: 加入购物车 2: 直接发起拼团 3: 下单并发起拼团 4: 立即购买-拼团商品

    @Prop({ type: Object, default: null })
    readonly cartAid;

    @Prop({ type: Boolean, default: false })
    readonly isSoldOut;

    @Prop({ type: Boolean, default: false })
    readonly pointsMall: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly initSelectAvIds: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isNotSold;

    @Prop({ type: Number, default: 0 })
    readonly point;

    @Prop({ type: Boolean, default: true })
    readonly isInit;

    @Prop({ type: Array, default: null })
    readonly tiedSaleList;

    @Prop({ type: Object, default: () => { return { group_activity_id: '', group_purchase_id: '', group_purchase_id_select: '', group_purchase_info: {}} } })
    readonly activityInfo;

    @Prop({ type: String, default: '' })
    readonly storekeeper;

    @Prop({ type: String, default: '' })
    readonly unionSource: IUnionSource;

    private addCartType: number = 1;
    private cartType: number = 2;

    public totalStyle: Object = {
        color: '#AB8C5E',
        'font-size': '38rpx',
        'line-height': '56rpx',
        'font-weight': 600,
    }; // 金额样式

    public unitSize: String = '38'; // 金额单位字号
    public unitRight: String = '8';
    public selectAvIds: Array<string> = []; // 选中模块
    public isType: String = ''; // 判断是主机或配件
    public specsLoading: Boolean = false; // 规格加载中

    get goodsData(): IGoodsInfo {
        // console.log('GoodsModule.goodsInfo', GoodsModule.goodsInfo);
        return GoodsModule.goodsInfo;
    }

    get useCouponId(): string {
        return GoodsModule.useCouponId;
    }

    get specsData(): ISpecsInfo {
        // console.log('GoodsModule.specsInfo', GoodsModule.specsInfo);
        if (GoodsModule.specsInfo && GoodsModule.specsInfo.id === '') {
            const _data: ISpecsInfo = {
                id: this.pointsMall ? GoodsModule.specsInfo.sid : this.goodsData.id,
                gid: this.goodsData.gid,
                sku: this.goodsData.sku,
                price: this.goodsData.price,
                av_ids: '',
                image: this.goodsData.market_image,
                num: 1,
                gini_id: this.goodsData.gini_id,
                gini_etime: 0,
                is_ini: 0,
                gini_tag: '',
                presale_info: false,
                label_tag: '',
                online_time_tag: '',
                sid: '',
                point: this.goodsData.point,
                discount: this.goodsData.discount,
            };
            return _data;
        }
        return GoodsModule.specsInfo;
    }

    get isSingleAType(): Boolean {
        // 是否是统一规格
        return !!(this.goodsData && this.goodsData.atype === '0');
    }

    get isHasGoNext(): Boolean {
        // 是否可以进入下一页
        if (this.isSingleAType && this.specsLoading === false) return true;
        if (this.selectAvIds && this.selectAvIds.length === this.goodsData.attr_cnf.length && this.specsLoading === false) {
            return true;
        }
        return false;
    }

    /** 有团购id或者团购活动id且是团购的立即拼团 */
    get isGroupPurchase(): Boolean {
        return (Number(this.activityInfo.group_activity_id) > 0 || Number(this.activityInfo.group_purchase_id) > 0) && this.buyType == 4;
    }

    get couponPrice(): number | string {
        // 预售(上面大逻辑已判断) 团购 自定义 不展示券后价
        if (this.isGroupPurchase) {
            return (Number(this.specsData.price) * this.activityInfo.group_purchase_info.group_rate).toFixed(2);
        } else if (this.specsData.is_ini == 1) {
            return this.specsData.price as any;
        } else {
            return this.specsData.discount
                        ? this.specsData.discount.price
                        : this.specsData.price;
        }
    }

    get group_price():number | string {
        let couponPrice:any = 0;
        if (this.isGroupPurchase) {
            couponPrice = (Number(this.specsData.price) * this.activityInfo.group_purchase_info.group_rate);
        } else if (this.specsData.is_ini == 1) {
            couponPrice = this.specsData.price as any;
        } else {
            couponPrice = this.specsData.discount
                        ? this.specsData.discount.price
                        : this.specsData.price;
        }
        return Math.ceil(Number(couponPrice) / this.activityInfo.group_purchase_info.group_rate).toFixed(2);
    }

    get isShowPointDiscount(): boolean {
        return !!(this.specsData.discount && parseFloat(this.specsData.discount.coin.can_coin) && !this.isGroupPurchase)
    }

    get discount(): String {
        return (this.point / 100).toFixed(2);
    }

    get disabledInput(): boolean {
        // 虚拟商品 && (停车券) 目前限制只能买一个
        return this.goodsData.type === '2' && this.goodsData.subtype === '2'
    }

    @Watch('isShow') // watch，此处是监听isShow的变化
    async isShowChange(newVal: Boolean, oldVal: Boolean) {
        if (newVal) {
            await this.getAttrStock();
            // 统一规格直接获取规格信息
            if (this.isSingleAType) {
                await this.getSpecsData();
            }
            if (this.type === this.cartType) {
                // 购物车场景进入携带当前规格值进入
                if (this.cartAid) {
                    this.selectCartAttr();
                }
            }
        } else {
            // this.selectAvIds = [];
            // const specsDefalutData: ISpecsInfo = {
            //     id: '',
            //     gid: '',
            //     sku: '',
            //     price: '',
            //     av_ids: '',
            //     image: '',
            //     num: 0,
            // };
            // GoodsModule.setSpecsInfo(specsDefalutData);
            // this.handleDefalutAttrCnf();
        }
    }

    @Watch('initSelectAvIds')
    async initSelectAvIdsChange(newVal: Boolean, oldVal: Boolean) {
        this.selectAvIds = []
    }

    created() { }

    getPointText() {
        if (this.specsData.discount && this.specsData.discount.coin.can_coin) {
            return ''
            // return `${this.specsData.discount.coin.can_coin}积分抵&nbsp;¥${this.specsData.discount.coin.coin_price}`;
        }
            return '';
        }

    // 获取商品属性是否有库存
    getAttrStock() {
        const gid: string = this.goodsData.gid;
        const av_ids: string = this.selectAvIds && this.selectAvIds.length > 0 ? JSON.stringify(this.selectAvIds) : '';
        const params = { gid, av_ids };
        GoodsModule.asyncCheckAttrStock({ params, pointsMall: this.pointsMall });
    }

    // 获取选择商品规格信息
    async getSpecsData(n: number = 1) {
        const gid: string = this.goodsData.gid;
        const av_ids: string = this.selectAvIds && this.selectAvIds.length > 0 ? JSON.stringify(this.selectAvIds) : '';
        const num: number = n;
        const params = { gid, av_ids, num, is_internal_purchase: GoodsModule.isInshop ? 1 : 0 };
        this.specsLoading = true;
        await GoodsModule.asyncGetSpeceInfo({ params, pointsMall: this.pointsMall });
        this.specsLoading = false
    }

    async selectAttr(item, index) {
        const { in_stock = 0, id = '' } = item;
        if (in_stock === 0) return;
        if (this.selectAvIds[index] === id) return;
        this.$set(this.selectAvIds, index, id);
        await this.getAttrStock();
        // 选完属性才查规格信息
        if (this.selectAvIds.length !== this.goodsData.attr_cnf.length) return;
        await this.getSpecsData();
    }

    // 购物车进入查询规格
    async selectCartAttr() {
        const _avIds = JSON.parse(this.cartAid.id) || [];
        _avIds.forEach(async (id, index) => {
            this.$set(this.selectAvIds, index, id);
            await this.getAttrStock();
            // 选完属性才查规格信息
            if (_avIds.length === index + 1) {
                const n = this.cartAid ? this.cartAid.num : 1;
                await this.getSpecsData(n);
            }
        });
    }

    // // 从已选进入查询规格
    // async selectYXAttr() {
    //     const _avIds = this.defaultSelectAvIds || [];
    //     _avIds.forEach(async (id, index) => {
    //         this.$set(this.selectAvIds, index, id);
    //         await this.getAttrStock();
    //         // 选完属性才查规格信息
    //         if (_avIds.length === index + 1) {
    //             // const n = this.cartAid ? this.cartAid.num : 1;
    //             await this.getSpecsData();
    //         }
    //     });
    // }

    onClose() {
        if (!this.isSingleAType && this.selectAvIds.length === this.goodsData.attr_cnf.length) {
            this.$emit('updateSpecs', {
                selectAvIds: this.selectAvIds,
                specsData: this.specsData,
            });
        }
        this.$emit('update:isShow', false);
    }

    async valChange(e) {
        try {
            this.specsData.num = e.value === '0' ? '1' : e.value;
            await this.getAttrStock();
            if (this.selectAvIds.length === this.goodsData.attr_cnf.length) {
                await this.getSpecsData(Number(this.specsData.num) || 1);
            }
        } catch (e) {
            console.error('ValChange err=', e);
        }
    }

    handleOverLimit(optType) {
        if (optType === 'minus') {
            Utils.Toast('最少购买一件');
        } else {
            Utils.Toast('超出此产品限购数量');
        }
    }

    async handleComfirm(type) {
        if (!this.isHasGoNext) return;
        if (this.type === this.cartType) { // 购物车页面(2: 购物车)
            this.$emit('modifyCart', this.specsData);
            this.onClose();
        } else { // 订单页面(0: 加入购物车 1: 立即购买/兑换 3: 直接发起拼团 4: 下单并发起拼团/立即购买-拼团商品)
            if (type === 0) {
                console.log('加入购物车');
                await this.getBatchAddShopCart();
            } else if (type === 3) {
                initiateGroupPurchase({ gid: this.goodsData.gid, activity_id: this.activityInfo.group_activity_id }).then((res) => {
                    if (!res.id) {
                        Utils.Toast('拼团id不存在');
                        return;
                    }
                    uni.redirectTo({
                        url: `/pagesB/inGroupPurchase/indexNew?group_purchase_id=${res.id}`,
                    });
                });
            } else {
                this.gotoSettlementPage(type);
                console.log('进入下一页');
            }
            this.onClose();
        }
    }

    // 立即购买
    async gotoSettlementPage(type) {
        const gcombines: Array<any> = [];
        const gid: string = this.specsData.gid;
        const sid: string = this.specsData.id || '0';
        const num: number = this.specsData.num;
        const gini_id: number = this.specsData.gini_id || 0;
        gcombines.push({ gid, sid, num, gini_id });
        // 兼容组合购买
        if (this.goodsData.tids && Array.isArray(this.goodsData.tids) && !this.goodsData.tids.includes('20')) {
            this.tiedSaleList.forEach((item) => {
                const gid: string = item.gid;
                const sid: string = '0'; // 暂不考虑多规格
                const num: number = item.num;
                const gini_id: number = item.gini_id || 0;
                if (num != 0) {
                    gcombines.push({ gid, sid, num, gini_id });
                }
            });
        }
        let url = '';
        if (this.pointsMall) {
            url = `/pagesA/settlement/settlement?type=pointsMall&orderType=${this.orderType}&pointGoodType=${this.goodsData.type}&pointGoodSubtype=${this.goodsData.subtype}&sParams=${JSON.stringify(gcombines)}${this.useCouponId ? '&useCid=' + this.useCouponId : ''

                }${this.goodsData.is_presale ? '&is_presale=1' : ''}&activityType=${this.activityType}`;
        } else {
            // 是为团购订单且非立即购买流程，跳转时需要传团购id和团购活动id和是否使用折扣
            const groupParams = this.isGroupPurchase
                ? `&group_activity_id=${this.activityInfo.group_activity_id}&group_purchase_id=${
                      this.activityInfo.group_purchase_id_select || this.activityInfo.group_purchase_id
                  }&disable_all_discount=1`
                : '';

            // 订单来源参数，类型见ts定义
            const unionParams = this.isGroupPurchase
                ? `&unionSource=${IUnionSource.GROUP_PURCHASE}`
                : `&unionSource=${this.unionSource}`;

            url = `/pagesA/settlement/settlement?storekeeper=${this.storekeeper}&sParams=${JSON.stringify(gcombines)}${
                this.useCouponId ? '&useCid=' + this.useCouponId : ''
            }${this.goodsData.is_presale ? '&is_presale=1' : ''}&orderType=${
                this.orderType
            }${groupParams}${unionParams}&activityType=${this.activityType}`;
        }
        if (type === 4) { // 下单并发起拼团 支付成功后自定义返回
            // uni.$on('startOrderGroupBack', () => {
            //     uni.reLaunch({
            //         url: '/pagesB/doubleEleven/doubleEleven',
            //     });
            //     uni.navigateTo({
            //         url: '/pagesB/groupGoods/groupGoods',
            //     });
            // });
        }
        this.selectAvIds = []
        uni.navigateTo({
            url: url,
        });
    }

    // 组合购买加入购物车
    async getBatchAddShopCart() {
        try {
            const gcombines: Array<any> = [];
            const gid: string = this.specsData.gid;
            const sid: string = this.specsData.id || '0';
            const num: number = this.specsData.num;
            const gini_id: number = this.specsData.gini_id || 0;
            if (this.goodsData.tids && Array.isArray(this.goodsData.tids) && !this.goodsData.tids.includes('20')) {
                this.isType = 'main';
            } else {
                this.isType = 'part';
            }
            gcombines.push({ gid, sid, num, gini_id, type: this.isType });
            // 兼容组合购买
            if (this.goodsData.tids && Array.isArray(this.goodsData.tids) && !this.goodsData.tids.includes('20')) {
                this.tiedSaleList.forEach((item) => {
                    const gid: string = item.gid;
                    const sid: string = '0'; // 暂不考虑多规格
                    const num: number = item.num;
                    const gini_id: number = item.gini_id || 0;
                    if (item.tids && Array.isArray(item.tids) && item.tids.includes('20')) {
                        this.isType = 'part';
                    }
                    if (num != 0) {
                        gcombines.push({ gid, sid, num, gini_id, type: this.isType });
                    }
                });
            }
            if (gcombines.length > 0) {
                const isShowToast = await Utils.getTaskInfo('mall/dreame/goods_add_cart');
                await batchAddShopCart({ gcombines: JSON.stringify(gcombines) });
                await CartModule.loadList();
                isShowToast && this.$emit('change');
            }
            this.onClose();
        } catch (e) {
            console.error('handleAddCart e=', e);
        }
    }

    // 初始化规格属性
    handleDefalutAttrCnf() {
        this.goodsData.attr_cnf.forEach((attr) => {
            const _attr: IGoodsAttrCnf = uni.$u.deepClone(attr);
            if (_attr.val && _attr.val.length > 0) {
                _attr.val.forEach((v) => {
                    v.in_stock = 0;
                });
            }
            GoodsModule.setGoodsAttrCnf(_attr);
        });
    }
}
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    height: max-content;
    min-height: 700rpx;
    background: #ffffff;
    border-radius: 38rpx 38rpx 0 0;
    padding: 38rpx 38rpx 350rpx;

    .title {
        font-size: 28rpx;
        line-height: 44rpx;
        font-weight: 400;
        color: $text-color-primary;

        .limit {
            font-size: 24rpx;
            line-height: 44rpx;
            font-weight: 400;
            color: $text-color-secondary;
        }
    }

    .shop_sort {
        max-height: 600rpx;
        overflow: auto;
    }

    .goods-info {
        margin-bottom: 40rpx;

        .goods-image {
            margin-right: 24rpx;
        }

        .info-right {
            flex: 1;
            padding-top: 4rpx;

            .price {
                margin-bottom: 18rpx;
                display: flex;
                align-items: baseline;
                .unit{
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #404040;
                    line-height: 32rpx;
                }
                .digit{
                    margin-left: 4rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 42rpx;
                    color: #404040;
                    line-height: 56rpx;
                }
            }

            .is_group_price{
                margin-bottom: 18rpx;
                display: flex;
                align-items: center;
                .unit{
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #FF2968;
                    line-height: 32rpx;
                }
                .digit{
                    margin-left: 4rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 42rpx;
                    color: #FF2968;
                    line-height: 56rpx;
                }
                .group_price_text{
                    font-size: 20rpx;
                    color: #FFFFFF;
                    height: 26rpx;
                    width: 72rpx;
                    background: #FF2968;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4rpx;
                    margin-left: 8rpx;
                }
            }

            .mprice_is_group{
                display: flex;
                align-items: center;
                background: linear-gradient(90deg, rgba(244, 78, 78, 0.08) 2%, rgba(244, 78, 78, 0) 100%);
                border-radius: 24rpx 0rpx 0rpx 24rpx;
                opacity: 1;
                font-size: 20rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: $func-color-danger-text;
                padding: 6rpx 38rpx 6rpx 18rpx;
                .mprice_digit{
                    margin-left: 8rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: $func-color-danger-text;
                }
            }

            .price-point {
                display: flex;
                align-items: center;
                height: 40rpx;
                margin-bottom: 24rpx;
                margin-top: 16rpx;

                .point-shop {
                    display: flex;
                    align-items: center;
                    padding: 0px;

                    .point-number {
                        font-size: 38rpx;
                        font-weight: 600;
                        color: #ab8c5e;
                    }

                    .point-unit {
                        font-size: 32rpx;
                        font-weight: 500;
                        color: #ab8c5e;
                        margin-left: 4rpx;
                    }
                }

                .plus-sign {
                    font-size: 38rpx;
                    font-weight: 600;
                    color: #ab8c5e;
                    width: 26rpx;
                    margin: 0rpx 0rpx 0rpx 12rpx;
                }
            }

            .presaleLeft {
                display: flex;
                flex-direction: column;

                .presaleNumber {
                    .presaleTextStyle {
                        font-size: 30rpx;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 600;
                        color: $fill-color-primary-active;
                        margin-right: 23rpx;
                    }

                    .cnyStyle {
                        font-size: 30rpx;
                        font-weight: 600;
                        color: $fill-color-primary-active;
                        margin-right: 10rpx;
                    }

                    .discount {
                        font-size: 30rpx;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 600;
                        color: $fill-color-primary-active;
                        margin: 0 20rpx;
                    }

                    .depositStyle {
                        font-size: 30rpx;
                        font-weight: 600;
                        color: $fill-color-primary-active;
                    }

                    .expandPrice {
                        font-size: 30rpx;
                        font-weight: 600;
                        color: $fill-color-primary-active;
                    }
                }

                .presalePrice {
                    width: fit-content;
                    background: linear-gradient(270deg, rgba(244, 78, 78, 0) 0%, rgba(244, 78, 78, 0.08) 100%);
                    border-radius: 24rpx 0px 0px 24rpx;
                    opacity: 1;
                    font-size: 20rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: $func-color-danger-text;
                    padding: 6rpx 38rpx 6rpx 18rpx;
                    margin-top: 10rpx;
                    line-height: 38rpx;

                    .presaleTextStyle {
                        font-size: 20rpx;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 500;
                        color: $func-color-danger-text;
                        margin-right: 10rpx;
                    }

                    .cnyStyle {
                        font-size: 20rpx;
                        font-weight: 400;
                        color: $func-color-danger-text;
                        margin-right: 10rpx;
                    }

                    .presalePriceStyle {
                        height: 53rpx;
                        font-size: 20rpx;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 500;
                        color: $func-color-danger-text;
                    }
                }
            }

            .name {
                flex: 1;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                font-size: 26rpx;
                color: #222;
                line-height: 36rpx;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .point {
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 24rpx;
                color: #F6B654;
                line-height: 32rpx;
            }
        }
    }

    .goods-sku {
        margin-bottom: 48rpx;

        .sort {
            height: 38rpx;
            font-size: 28rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: $text-color-primary;
            line-height: 38rpx;
        }

        .sku-tag {

            .sku-tag-item {
                .tag {
                    margin-top: 16rpx;
                    margin-right: 16rpx;
                    background: $fill-color-bg-gray;
                    border-radius: 14rpx;
                    overflow: hidden;
                    text-align: left;
                    font-size: 26rpx;
                    line-height: 40rpx;
                    font-weight: 400;
                    border: 2rpx solid transparent;
                }

                .tag-isImage {
                    padding: 8rpx 16rpx;
                }
                .at_val_image{
                    display: flex;
                    height: 120rpx;
                    align-items: center;
                    justify-content: center;
                    .at_val_name {
                      max-width: 206rpx;
                      word-break: break-all;
                      text-align: center;
                      padding: 0rpx 16rpx;
                      display: -webkit-box; /* 使用 -webkit-box 模型 */
                      -webkit-line-clamp: 2; /* 限制在 2 行 */
                      -webkit-box-orient: vertical; /* 设置为垂直布局 */
                      overflow: hidden; /* 隐藏超出的文本 */
                    }
                }
                .at_val{
                    .at_val_name {
                      max-width: 206rpx;
                      overflow: hidden;
                      text-align: center;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      padding: 0rpx 16rpx;
                      align-items: center;
                      height: 80rpx;
                      line-height: 80rpx
                    }
                }

                .tag-disabled {
                    color: rgba(34, 34, 34, 0.25);
                    background: #f5f5f5;
                }

                .tag-active {
                    background: none;
                    border: 2rpx solid $fill-color-primary-active;
                    color: #ab8c5e;
                }

                .tag-active-presale {
                    background: none;
                    border: 2rpx solid $fill-color-primary-active;
                    color: $fill-color-primary-active;
                }
            }
        }
    }

    .goods-num {
        width: 100%;
        position: absolute;
        bottom: 260rpx;
        left: 0;
        right: 0;
        box-sizing: border-box;
        padding: 0 32rpx;
    }

    .goods-buttons {
        position: absolute;
        bottom: 96rpx;
        left: 0;
        right: 0;

        .right-item {
            width: 296rpx;
            height: 96rpx;
            font-size: 28rpx;
            font-weight: 500;
            line-height: 94rpx;
            text-align: center;
        }

        .btn-cart {
            background: #ffffff;
            border-radius: 200rpx 0px 0px 200rpx;
            border: 2rpx solid #3c7cfb;
            color: $theme-color;
        }

        .btn-cart-presale {
            background: #ffffff;
            border-radius: 200rpx 0px 0px 200rpx;
            border: 2rpx solid #f44e4e;
            color: #f44e4e;
        }

        .btn-buy {
            background: linear-gradient(135deg, #2e67f8 0%, #226df2 100%, #215aee 100%);
            border-radius: 0rpx 48rpx 48rpx 0rpx;
            color: #ffffff;
        }

        .btn-buy-presale {
            background: #f44e4e;
            border-radius: 0rpx 48rpx 48rpx 0rpx;
            color: #ffffff;
        }

        .btn-cart.btn-disabled {
            background: #f5f5f5;
            color: rgba(34, 34, 34, 0.25);
            border-radius: 200rpx 0px 0px 200rpx;
            border: 2rpx solid #f5f5f5;
        }

        .btn-buy.btn-disabled {
            background: #f5f5f5;
            color: rgba(34, 34, 34, 0.25);
            border-radius: 0px 200rpx 200rpx 0px;
            border: 2rpx solid #f5f5f5;
        }
    }

    .goods-button {
        width: 100%;
        position: absolute;
        bottom: 96rpx;
        left: 0;
        right: 0;
        box-sizing: border-box;

        .comfirm-pay {
            width: calc(100% - 124rpx);
            height: 86rpx;
            line-height: 86rpx;
            background: $brand-color-normal;
            border-radius: 15rpx;
            font-size: 32rpx;
            color: $brand-color-btn-text;
            text-align: center;
            font-weight: 500;
        }

        .comfirm-pay-presale {
            width: calc(100% - 88rpx);
            height: 86rpx;
            line-height: 86rpx;
            background: $brand-color-normal;
            border-radius: 48rpx;
            font-size: 28rpx;
            color: $brand-color-btn-text;
            text-align: center;
            font-weight: 500;
        }

        .btn-disabled {
            background: #f5f5f5;
            color: rgba(34, 34, 34, 0.25);
        }
    }

    .icon-close {
        position: absolute;
        top: 32rpx;
        right: 32rpx;
        width: 32rpx;
        height: 32rpx;

        .icon {
            width: 26rpx;
            height: 26rpx;
        }
    }
}
</style>
