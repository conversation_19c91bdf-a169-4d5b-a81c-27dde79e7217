import http from './index';

/*
 * 获取积分列表
 * @parma {number} type  1：获取； 2：消费
 * @parma {number} page  页数
 * @returns
 */
interface PointsListParams {
    page: number;
}
export const pointsList = (params: PointsListParams) => http.post('main/my/crm-point-log', params);

/*
 * 用户优惠劵列表
 * @returns
 */
export const getCouponList = (params) => http.post('main/my/card', params);

/**
 * 获取用户信息
 */
export const mineInfo = () => http.post('main/my/info');

/**
 * 分享二维码
 * @parma {number} r_code  推荐人加密串
 */
export const shareRcode = (params) => http.post('main/my/share-code', params);

/**
 * 会员基本信息接口
 */
export const basicInfo = () => http.post('main/member-center/basic-info');
