import { IResponse } from './apiTypes';
import req from './index.js';
import reqS from '../index.js';
import { UserModule } from '@/store/modules/user';
/**
 * 创建浏览记录
 * @param {String} content_id
 */
export const createBrowse = (params) => req.post('api/v1/browse/create', { ...params });

/**
 * 获取搜索历史列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getSearchHistory = (params) => req.post('api/v1/user/search-history', { ...params });

/**
 * 获取搜索用户
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getSearchUser = (params): Promise<any> => req.post('api/v1/user/search', { ...params });

/**
 * 创建搜索历史列表
 * @param {String} keyword
 */

export const createSearchHistory = (params) => req.post('api/v1/user/create-search-history', { ...params });

/**
 * 清空搜索历史列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const clearSearchHistory = () => req.post('api/v1/user/empty-search-history', {});

/**
 * 话题列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getTopicList = (params) => req.post('api/v1/topic/list', { ...params });

/**
 * 话题列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getNewTopicList = (params) => req.post('api/v1/topic/list-by-user', { ...params });

/**
 * 获取内容列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getContentList = (params, loading: boolean = true) =>
    req.post('api/v1/content/list', { ...params }, { custom: { loading }});

/**
 * 获取内容列表新 统一keyword
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getContentListNew = (params, loading: boolean = true): Promise<any> =>
    req.post('api/v1/content/waterfall-flow', { ...params }, { custom: { loading }});

/**
 * 获取视频圈子内容列表
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const getExContentList = (params, loading: boolean = true) =>
    req.post('api/v1/content/list-and-detail', { ...params }, { custom: { loading }});

/**
 * 获取视频列表
 */
export const getExVideoList = (params, loading: boolean = true) =>
    req.post('api/v1/content/waterfall-flow', { ...params }, { custom: { loading }});

/**
 * 获取随机视频列表
 */
export const getrandomVideoList = (params, loading: boolean = true) =>
    req.post('api/v1/content/random-list', { ...params }, { custom: { loading }});

/**
 * 内容详情
 * @param {String} keyword
 * @param {String} page
 * @param {String} page_size
 */
export const detailContent = (params) => req.post('api/v1/content/detail', { ...params });

/**
 * 未登录获取详情
 */
export const detailContentNoLogin = (params) => req.post('api/v1/content/get', { ...params });

/**
 * 点赞
 * @param {Number} entity_id
 * @param {Number} type 类型 1文章 2评论 3评论回复
 */
export const postPraise = (params) => req.post('api/v1/praise/create', { ...params });

/**
 * 取消点赞
 * @param {Number} id
 */
export const postCancelPraise = (params) => req.post('api/v1/praise/delete', { ...params });

/**
 * 收藏
 * @param {Number} entity_id
 * @param {Number} type
 */
export const postFavorite = (params) => req.post('api/v1/favorite/create', { ...params });

/**
 * 取消收藏
 * @param {Number} id
 */
export const postCancelFavorite = (params) => req.post('api/v1/favorite/delete', { ...params });

/**
 * 推荐话题
 * @param {String} keyword
 * @param {Number} page
 * @param {Number} page_size
 */
export const getHotList = (params) => req.post('api/v1/content/hot-list', { ...params });

/**
 * 获取数据统计
 */
// export const getStatistics = () => req.post('api/v1/content/statistics', {});

/**
 * 获取数据统计
 */
export const getMyStatistics = () => req.post('api/v1/user/dynamic-statistics', {});

/**
 * 获取他人数据统计
 */
export const getOtherStatistics = (params) => req.post('api/v1/user/other/dynamic-statistics', { ...params });

/**
 * 用户关系
 */
export const getUserRela = (params) => req.post('api/v1/user/relation', { ...params });

/**
 * 用户查看作品权限
 */
export const getContentPer = (params) => req.post('api/v1/content/permission-check', { ...params });

/**
 * 删除内容
 */
export const delContent = (params) => req.post('api/v1/content/delete', { ...params });

/**
 * 点赞列表
 */
export const getPraiseList = (params, loading: boolean = true) =>
    req.post('api/v1/content/praise-list', { ...params }, { custom: { loading }});

/**
 * 点赞列表
 */
export const getOtherPraiseList = (params, loading: boolean = true) =>
    req.post('api/v1/content/other/praise-list', { ...params }, { custom: { loading }});

/**
 * 收藏列表
 */
export const getOhterFavList = (params, loading: boolean = true) =>
    req.post('api/v1/content/other/favorite-list', { ...params }, { custom: { loading }});

/**
 * 收藏列表
 */
export const getFavList = (params, loading: boolean = true) =>
    req.post('api/v1/content/favorite-list', { ...params }, { custom: { loading }});

/**
 * 评论列表
 */
export const postReviewList = (params, loading: boolean = true) =>
    req.post('api/v1/content/review-list', { ...params }, { custom: { loading }});

/**
 * 发布列表
 */
export const getPublishList = (params, loading: boolean = true) =>
    req.post('api/v1/content/my-publish-list', { ...params }, { custom: { loading }});

/**
 * 他人发布列表
 */
export const getOtherPublishList = (params, loading: boolean = true) =>
    req.post('api/v1/content/waterfall-flow', { ...params }, { custom: { loading }});

// 详情评论列表
export const createShare = (params) => req.post('api/v1/share/create', { ...params });

// 用户权限验证
export const userPermission = () => req.post('api/v1/user/publish-permission');

// app分享
export const appShare = (params) => reqS.post('/main/wiki/app-share', { ...params });

/**
 * 草稿箱数量
 */
export const postDraftNum = () => req.post('api/v1/content/my-draft-num');

/**
 * 草稿箱列表
 */
export const postDraftList = (params, loading: boolean = true) =>
    req.post('api/v1/content/my-draft-list', { ...params }, { custom: { loading }});

/**
 * 拉黑
 */
export const postBlock = (params, loading: boolean = true) =>
    req.post('api/v1/block/create', { ...params }, { custom: { loading }});

/**
 * 取消拉黑
 */
export const postCancelBlock = (params, loading: boolean = true) =>
    req.post('api/v1/block/delete', { ...params }, { custom: { loading }});

// 获取直播列表
export const getLiveList = (params, loading: boolean = true) =>
    req.post('api/v1/live/room-list', { ...params }, { custom: { loading }});

// 获取朋友中动态列表
export const getActivetyList = (params, loading: boolean = true) =>
    req.post('api/v1/content/list-follow', { ...params }, { custom: { loading }});

// 获取朋友中消息列表
export const getMessageList = (params, loading: boolean = true) =>
    req.post('api/v1/content/list-interrelated-follow', { ...params }, { custom: { loading }});

// 获取推荐好友列表
export const getRecommentList = (params, loading: boolean = true) =>
    req.post('api/v1/user/recommend-list', { ...params }, { custom: { loading }});

interface ReviewListParams {
    content_id: string;
    keyword?: string;
    page: number;
    page_size: number;
    /** 1热门 2最新 */
    order_type: number;
}

// 回复
export interface ReplyItem {
    content: string;
    created_at: number;
    id: number;
    image_url?: string[];
    /** 点赞id，为0时没有点赞，不为0有点赞且为点赞id */
    is_praise: number;
    is_report: number;
    praise_count: number;
    /** 回复人头像 */
    replier_avatar: string;
    /** 回复人ID */
    replier_id: string;
    /** 回复人名称 */
    replier_name: string;
    /** 被回复人名称 */
    reply_to_name: string;
    /** 评价ID */
    review_id: number;
    updated_at: number;
    video_url: string;
    address: string;
    province: string;
    city: string;
    ip: string;
    /** 是否包含敏感词 */
    is_sensitive: number;
    /** 是否被屏蔽 */
    is_shield: number;
    /** 是否置顶 */
    is_top: number;
    is_employee: boolean;
    user_avatar_icon: string;
}

// 评论
export interface ReviewItem {
    content: string;
    content_id: string;
    created_at: string;
    id: number;
    image_url?: string[];
    is_anonymous: number;
    /** 点赞id，为0时没有点赞，不为0有点赞且为点赞id */
    is_praise: number;
    is_report: number;
    praise_count: number;
    reply_count: number;
    /** 每条评论列表带出来的一条回复记录 */
    reply_list: ReplyItem;
    /** 前端处理后的reply_list */
    replyList: ReplyItem[];
    reviewer_id: string;
    reviewer_name: string;
    reviewer_avatar: string;
    updated_at: number;
    /** 当前的回复列表请求到第几页了 */
    page?: number;
    outIds: number[];
    address: string;
    province: string;
    city: string;
    ip: string;
    /** 是否包含敏感词 */
    is_sensitive: number;
    /** 是否被屏蔽 */
    is_shield: number;
    /** 是否置顶 */
    is_top: number;
    is_employee: boolean;
    user_avatar_icon: string;
}

export interface ReviewListRes {
    list: ReviewItem[];
    page: number;
    page_size: number;
    total: number;
}

// 详情评论列表
export const getReviewList = (params: ReviewListParams): Promise<IResponse<ReviewListRes>> =>
    req.post('api/v1/review/list', { ...params });

interface CreateReviewParams {
    content?: string;
    content_id: string;
    image_url?: string[];
    sessid: string | number;
    user_id: string | number;
}

// 创建评论
export const createReview = (params: CreateReviewParams): Promise<IResponse<ReviewItem>> =>
    req.post('api/v1/review/create', { ...params });

// 删除评论
export const deleteReview = (params: { id: number }): Promise<IResponse<string>> =>
    req.post('api/v1/review/delete', { ...params });

interface ReplyListParams {
    content_id: string;
    keyword?: string;
    /** 需要去除的回复的ids */
    out_id: number[];
    page: number;
    page_size: number;
    review_id: number;
    type: number;
}

interface ReplyListRes {
    list: ReplyItem[];
    page: number;
    page_size: number;
    total: number;
}

// 回复列表
export const getReplyList = (params: ReplyListParams): Promise<IResponse<ReplyListRes>> =>
    req.post('api/v1/reply/list', { ...params });

interface CreateReplyParams extends CreateReviewParams {
    /** 上级ID，即回复的id */
    parent_review_id: number;
    /** 根评论id */
    review_id: number;
    /** 2评论的回复 3回复的回复 */
    type: number;
}

// 创建回复
export const createReply = (params: CreateReplyParams): Promise<IResponse<ReplyItem>> =>
    req.post('api/v1/reply/create', { ...params });

// 删除回复
export const deleteReply = (params: { id: number }): Promise<IResponse<string>> =>
    req.post('api/v1/reply/delete', { ...params });

interface ReportTypeListRes {
    list: {
        description: string;
        level: number;
        reason_id: number;
    }[];
    page: number;
    page_size: number;
    total: number;
}

// 举报类型
export const getReportTypeList = (): Promise<IResponse<ReportTypeListRes>> => req.post('api/v1/report/type-list');

// 举报类型
export const getUserReportTypeList = (): Promise<IResponse<ReportTypeListRes>> =>
    req.post('api/v1/user-report/type-list');

// 举报用户
export const postUserReport = (params): Promise<IResponse<ReportTypeListRes>> =>
    req.post('api/v1/user-report/create', { ...params });

// 其他用户详情
export const userOtherDetail = (params: any): Promise<IResponse<string>> =>
    req.post('api/v1/user/detail', { ...params });

// 用户IP
export const getUserIP = (params: any): Promise<IResponse<string>> =>
    req.post('api/v1/user/ip-location', { ...params });

interface CreateReportParams {
    content: string;
    entity_id: string;
    reason_id: number;
    /** 举报类型 1：文章 2：评论 */
    report_type: number;
    sessid: string | number;
    user_id: string | number;
    image_url?: string[];
}

// 创建举报
export const createReport = (params: CreateReportParams): Promise<IResponse<string>> =>
    req.post('api/v1/report/create', { ...params });

interface MediaItem {
    description?: string;
    id?: number;
    url: string;
}

export enum ContentPermissionType {
    /** 公开 */
    PUBLIC = 1,
    /** 私密 */
    PRIVATE = 2,
    /** 部分可见 */
    PARTIALLY = 3,
    /** 不给谁看 */
    NOT_ALLOW = 4,
}

export enum ContentStatus {
    /** 草稿 */
    DRAFT = 1,
    /** 未发布 */
    UNPUBLISH = 2,
    /** 已发布 */
    PUBLISHED = 3,
}
export interface CreateContentParams {
    goods_ids: number[],
    address: string;
    address_id: string;
    address_intro: string;
    author?: string;
    author_avatar?: string;
    author_tag?: string;
    body: string;
    cover_image?: string;
    image_media_list?: MediaItem[];
    video_media_list?: MediaItem[];
    ip?: string;
    is_recommend?: number;
    permission_type: ContentPermissionType;
    /** 1：管理员 2：前台用户 */
    source: number;
    status: ContentStatus;
    title: string;
    topic1_ids: number[];
    topic2_ids: number[];
    user_ids: number[];
    content_id?: string;
}

// 创建内容
export const createContent = (params: CreateContentParams): Promise<IResponse<{ content_id: string }>> =>
    req.post('api/v1/content/create', { ...params });

// 更新内容
export const updateContent = (params: CreateContentParams): Promise<IResponse<any>> =>
    req.post('api/v1/content/update', { ...params });

interface FansListParams {
    keyword: string;
    page: number;
    page_size: number;
    type: number[];
}

export interface FansItem {
    avatar: string;
    create_at?: number;
    follow_status?: number;
    id?: number;
    nick_name: string;
    update_at?: number;
    user_id: number;
    user_name?: string;
    highlight_name?: string;
    user_avatar_icon?: string;
}

interface FansListRes {
    list: FansItem[];
    page: number;
    page_size: number;
    total: number;
}

// 搜索关注列表
export const searchFollowList = (params: FansListParams): Promise<IResponse<FansListRes>> =>
    req.post('api/v1/follow/search-list', { ...params });

// 我的粉丝列表
export const myFansList = (params: FansListParams): Promise<IResponse<FansListRes>> =>
    req.post('api/v1/follow/my-fans-list', { ...params });

// 我的关注列表
export const myFollowList = (params: FansListParams): Promise<IResponse<FansListRes>> =>
    req.post('api/v1/follow/my-list', { ...params });

// 我的黑名单列表
export const myBlockList = (params: FansListParams): Promise<IResponse<FansListRes>> =>
    req.post('api/v1/block/list', { ...params });

interface AddressSearchParams {
    keyword?: string;
    page_index: number;
    page_size: number;
    lat: number;
    lng: number;
}

export interface AddressItem {
    id: string;
    title: string;
    address: string;
    province?: string;
    city?: string;
    ad_info?: {
        city: string;
        province: string;
    };
}

export interface AddressSearchRes {
    data: AddressItem[];
}

// 地图搜索
export const mapSearch = (params: AddressSearchParams): Promise<IResponse<AddressSearchRes>> =>
    req.post('api/v1/area/lbs-search', { ...params });

interface AddressSuggestionParams {
    keyword?: string;
    page_index: number;
    page_size: number;
    lat: number;
    lng: number;
}

// 地图搜索词联想
export const mapSuggestion = (params: AddressSuggestionParams): Promise<IResponse<AddressSearchRes>> =>
    req.post('api/v1/area/lbs-suggestion', { ...params });

interface UserRelationParams {
    to_user_id: number;
    user_id: number;
}

interface UserRelationRes {
    /** 屏蔽状态,0-未屏蔽,1-已屏蔽,2-被屏蔽 */
    block_status: number;
    /** 关注状态,0-未关注,1-已关注,2-互相关注 */
    follow_status: number;
    to_user_id: number;
    user_id: number;
}

// 当前用户和对象用户之间的关系
export const userRelation = (params: UserRelationParams): Promise<IResponse<UserRelationRes>> =>
    req.post('api/v1/user/relation', { ...params });

interface UserFollowParams {
    followed_user_id: number;
    user_id?: number;
}
// 批量关注
export const userAllFollow = (params) =>
    req.post('api/v1/follow/create-by-ids', { ...params });
// 关注用户
export const userFollow = (params: UserFollowParams): Promise<IResponse<string>> =>
    req.post('api/v1/follow/create', { ...params });

// 取关用户
export const userFollowDelete = (params: UserFollowParams): Promise<IResponse<string>> =>
    req.post('api/v1/follow/delete', { ...params });

interface UpdatePermissionParams {
    content_id: string;
    permission_type?: number;
    user_ids?: number[];
}

// 更新用户权限
export const updatePermission = (params: UpdatePermissionParams): Promise<IResponse<string>> =>
    req.post('api/v1/content/update-permission', { ...params });

export interface BannedPermissionRes {
    /** 是否封禁收藏权限 默认0否 */
    favorite: number;
    /** 是否封禁点赞权限 默认0否 */
    praise: number;
    /** 是否封禁评论权限 默认0否 */
    review: number;
    /** 是否封禁分享权限 默认0否 */
    share: number;
    /** 是否封禁发布作品权限 默认0否 */
    works: number;
    /** 用户权限 1-不封禁 2-部分封禁 3-全部封禁 */
    ban_type: number;
}

// 更新用户权限
export const bannedPermission = (): Promise<IResponse<BannedPermissionRes>> => {
    const { user_id, sessid } = UserModule;
    return req.post('api/v1/user/banned-permission', { user_id, sessid });
};

interface ContentTagParam {
    type: number;
}

export interface TagItem {
    category: string;
    content_num: number;
    create_at: number;
    icon: string;
    id: number;
    name: string;
    type: number;
    update_at: number;
}
interface TagList {
    id: number;
    name: string;
    children: TagItem[];
}

// 获取内容筛选tag列表
export const getContentTagList = (params: ContentTagParam): Promise<IResponse<{ list: TagList[] }>> => {
    return req.post('api/v1/tag-category/list', params);
};

// 获取标签列表
export const getTagList = (params): Promise<IResponse<{ list: TagList[] }>> => {
    return req.post('api/v1/tag/list', params);
};

// 我浏览的礼列表
export const getOfficialAccountList = () => {
    return req.post('api/v1/content/browse-creator-list');
};

/* 观看视频15秒 */
export const viewVideo15s = (params) => {
    return req.post('api/v1/share/update-gold', params);
};

// 通讯录好友列表根据手机号查询用户
export const getPhoneUser = (params, loading: boolean = true) => {
   return req.post('api/v1/user/search-by-phone', { ...params }, { custom: { loading }});
}

// 获取未注册的手机号消息列表
export const getNoRegisterPhoneList = (params, loading: boolean = true) => {
   return req.post('api/v1/user/not-register/list-by-phone', { ...params }, { custom: { loading }});
}

// 发送短信
export const sendSms = (params, loading: boolean = true) => {
  return req.post('/api/v1/user/not-register/send-sms', { ...params }, { custom: { loading }});
}
