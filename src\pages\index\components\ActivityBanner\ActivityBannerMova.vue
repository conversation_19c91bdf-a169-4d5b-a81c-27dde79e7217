<template>
    <view class="activity-banner">
        <view class="top-banner" @click="jumpHandler(topBanner)">
            <image :src="topBanner.image" class="top-banner-image"/>
        </view>
        <view class="swiper">
            <view class="swiper-item" v-for="item in swiperList" :key="item.id">
                <view class="swiper-item-child" @click="jumpHandler(item)">
                    <image :src="item.image" class="swiper-item-image"/>
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { getTagList } from '@/http/requestGo/community';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';

@Component({
    components: {
    },
})
export default class ActivitySwiper extends Vue {
    @Prop({ type: Array, default: () => [] })

    readonly oneGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly fiveHalfGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly eightDisGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly points!: Array<any>;

    @Prop()
    readonly deduction_rate!: number;

    public name: string = ''

    get fullName() {
        return this.name
    }

    public topBanner: any = {}

    public swiperList: any[] = [
        // {
        //     id: 1,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689afc6a5a6203700433472.png',
        //     alt: '赚钱花',
        //     jumpUrl: '/pagesC/earnMoneySpend/earnMoneySpend'
        // },
        // {
        //     id: 2,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689afc6cef4259800472330.png',
        //     alt: '赚金币',
        //     jumpUrl: '/pagesC/goldCoins/goldCoins'
        // },
        // {
        //     id: 3,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d2656deb74500030980.png',
        //     alt: '百亿购物金',
        //     jumpUrl: '/pagesC/billionsOfShoppingGold/billionsOfShoppingGold'
        // },
        // {
        //     id: 4,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c57f92ba2b1790010662.png',
        //     alt: '拼团',
        //     jumpUrl: '/pagesB/groupGoods/groupGoods'
        // },
        // {
        //     id: 5,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688ad6beafea77210012072.png',
        //     alt: '金币',
        //     jumpUrl: '/pagesC/goldCoins/goldCoins'
        // },
        // {
        //     id: 6,
        //     image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688cde88186ed1000011325.png',
        //     alt: '八折内购',
        //     jumpUrl: '/pagesC/purchase/purchase'
        // },
    ];

    async created() {
       await this.fetchTagBannerList();
       await this.fetchTagUniBannerList();
    }

    /**
     * 获取服务列表
     */
    public async fetchTagBannerList() {
        try {
            const version = AppModule.VersionCode;
            const res: any = await getTagList({ type: 19, page_size: 100, is_show: 1, tag_version: `${version}` });
            // 根据接口返回的数据结构取 list
            const tagList = (res?.data?.list || res?.data || []) as any[];
            // const version = AppModule.VersionCode;
            // 仅取 item.version 不超过当前版本的条目
            const validList = tagList.filter((item) => {
                const v = Number(item.version || 0);
                return v !== 0 && v <= Number(version);
            });

            // 找到 <= version 的最大版本号
            const maxVersion = validList.reduce((max, item) => {
                const v = Number(item.version);
                return v > max ? v : max;
            }, -Infinity);

            const extraList = validList
                .filter((item) => Number(item.version) === maxVersion)
                .map((item) => ({
                    ...item,
                    image: item.icon || '',
                    jumpUrl: item.jump_url || '',
                    type: item.jump_type || '',
                    version: item.version || '',
                    // 接口返回 is_show 值为 "1" 或 "2"，此处统一转成字符串便于后续比较
                    isShow: String(item.is_show || ''),
                }));
            // 仅保留 isShow 为 "1" 的数据
            const visibleExtraList = extraList.filter((extra) => extra.isShow === '1');
            // 过滤掉名称(text)已存在的服务项，避免重复追加
            // const uniqueExtraList = visibleExtraList.filter(
            //     (extra) => !this.swiperList.some((service) => service.text === extra.text),
            // );
            this.swiperList = [...this.swiperList, ...visibleExtraList];
        } catch (error) {
            /* eslint-disable no-console */
            console.error('获取服务列表失败', error);
        }
    }

    public async fetchTagUniBannerList() {
        try {
            const version = AppModule.VersionCode;
            const res: any = await getTagList({ type: 25, page_size: 1, is_show: 1, tag_version: `${version}` });
            // 根据接口返回的数据结构取 list
            const tagList = (res?.data?.list || res?.data || []) as any[];
            // const version = AppModule.VersionCode;
            // 仅取 item.version 不超过当前版本的条目
            const validList = tagList.filter((item) => {
                const v = Number(item.version || 0);
                return v !== 0 && v <= Number(version);
            });

            // 找到 <= version 的最大版本号
            const maxVersion = validList.reduce((max, item) => {
                const v = Number(item.version);
                return v > max ? v : max;
            }, -Infinity);

            const extraList = validList
                .filter((item) => Number(item.version) === maxVersion)
                .map((item) => ({
                    ...item,
                    image: item.icon || '',
                    jumpUrl: item.jump_url || '',
                    type: item.jump_type || '',
                    version: item.version || '',
                    // 接口返回 is_show 值为 "1" 或 "2"，此处统一转成字符串便于后续比较
                    isShow: String(item.is_show || ''),
                }));
            // 仅保留 isShow 为 "1" 的数据
            const visibleExtraList = extraList.filter((extra) => extra.isShow === '1');
            // 过滤掉名称(text)已存在的服务项，避免重复追加
            // const uniqueExtraList = visibleExtraList.filter(
            //     (extra) => !this.swiperList.some((service) => service.text === extra.text),
            // );
            console.log('visibleExtraList', visibleExtraList);
            this.topBanner = visibleExtraList[0];
        } catch (error) {
            /* eslint-disable no-console */
            console.error('获取服务列表失败', error);
        }
    }

    jumpHandler(item) {
        console.log('item', item);
        Utils.logTrace(
          {
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .HOME_BANNER_CLICK,
              id: Number(item.id),
              name: item.jumpUrl || ''
          },
          true,
      );
      if (item.jumpUrl) {
        Utils.navigateTo(item.jumpUrl);
      }
    }
}
</script>
<style lang="scss" scoped>
.activity-banner{
    margin: 16rpx;
    margin-top: 28rpx;
    .top-banner{
        // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689afcdd237ef1450434922.png');
        // background-size: cover;
        // background-position: 100% 100%;
        height: 160rpx;
        width: 100%;
        margin-bottom: 16rpx;
        // margin-top: 10rpx;
        .top-banner-image{
            width: 100%;
            height: 100%;
        }

    }
    .swiper{
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 16rpx;
        width: 100%;
        .swiper-item{
            width: 100%;
            .swiper-item-child{
                .swiper-item-image{
                    height: 168rpx;
                    width: 100%;
                }

            }
        }
    }

}

</style>
