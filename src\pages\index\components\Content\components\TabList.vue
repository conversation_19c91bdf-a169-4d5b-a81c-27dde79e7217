<template>
    <view class="Lists_container">
        <!-- <view class="MemberServices_container_title">
            <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687766cd57d623600010688.png" class="member_Icon"></image>
        </view>
         <view class="MemberServices_container_title">
           产品服务
        </view> -->
         <u-scroll-list
                :indicator="false"
                :indicatorActiveColor="'#DBC49A'"
                :indicatorInactiveColor="'#EEEEEE'"
                :indicatorWidth="46.15"
                :indicatorHeight="7.69"
                :indicatorBarRadius="98.08"
                :indicatorBarStyle="{margin: '16rpx auto 0'}"
                :showScrollbar="false"
            >
                <view
                @tap.stop="navigateToPage(index, item, 'image')"
                @click.stop="navigateToPage(index, item, 'image')"
                class="service_item" v-for="(item, index) in BrandTypeLists" :key="item.tid">
                    <image class="service_item_icon" :src="item.tag_img"></image>
                    <view class="service_item_name">{{ item.name }}</view>
                    <image v-if="item.hover_icon" class="service_item_hover_icon" :src="item.hover_icon"></image>
                </view>
            </u-scroll-list>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
    }
})
export default class tabListBar extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Array, default: 0 })
    readonly BrandTypeLists!: Array<any>;

    async created() {
       // await this.fetchTagList();
    }

    navigateToPage(index: number, item: any, falg?: string) {
        this.$emit('switchMenu', index, item, falg);
    }
}
</script>

<style lang="scss" scoped>
.Lists_container {
    height: auto;
    // min-height: 252rpx;
    padding: 16rpx;
    background: linear-gradient(180deg, #FFFFFF 0%, #f6f6f6 100%);
    &_title {
         font-size: 24rpx;
        font-weight: 500;
        color: #777777;
    }
    .services_grid {
        display: flex;
        flex-wrap: nowrap;
        gap: 20rpx;
        margin-top: 20rpx;
    }
    .service_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        position: relative;
         width: calc((100vw - 86rpx) / 5); /* 一行5个，减去4个间隔的宽度 */
        min-width: 130rpx;
        overflow: hidden;
        // &:first-child {
        //     margin-left: 16rpx;
        // }
        &_icon {
            height: 88rpx;
            width: 88rpx;
        }
        &_name {
            font-size: 22rpx;
            // margin-top: 12rpx;
            font-weight: regular;
            color: #404040;
            /* 禁止换行 */
            white-space: nowrap;
            text-align: center;
        }
        &_hover_icon {
            height: 36rpx;
            width: 36rpx;
            right: -20rpx;
            position: absolute;
            top: 0rpx;
            transform-origin: left bottom;
            animation: rotateIcon 1.5s infinite;
        }

        @keyframes rotateIcon {
            0% {
                transform: rotate(0deg);
            }
            50% {
                transform: rotate(30deg);
            }
            100% {
                transform: rotate(0deg);
            }
        }
    }
}
.MemberServices_container_title{
    height: 44rpx;
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
     font-family: MiSans;
     font-size: 28rpx;
    line-height: 48rpx;
    font-weight: 800;
    color: #3d3d3d;
    .member_Icon{
        width: 148rpx;
        height: 44rpx;
    }
}

</style>
