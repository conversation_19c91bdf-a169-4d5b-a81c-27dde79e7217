import http from './index';
/**
 * 获取收货地址列表
 * @parma {String} opt 0:获取列表，1：获取默认地址
 */
export const getAddressList = (params) => http.post('main/my/address', { ...params });

/**
 * 添加收货地址
 */
export const saveAddress = (param) => http.post('main/my/opt-address', { ...param });

/**
 * 设置默认收货地址
 */
export const saveDefaultAddress = (id) => http.post('main/my/save', { id });
/**
 * 删除收货地址
 */
export const delAddress = (id: string | number) => http.post('main/my/del-address-v2', { id });
/**
 * 获取附近门店
 * "pos_l"    :"113.63",                           //地理位置经度
 * "pos_b"    :"36",                               //地理位置纬度
 */
export const nearlist = (param) => http.post('main/retailers/near-list', param);

/**
 * 切换默认地址
 */
export const setDefaultAddress = (add_id: string | number) => http.post('main/my/set-default-address', { add_id });

/**
 * 获取
 */
export const getAdminDivision = (params) => http.post('main/plumbing/service-address', params);
