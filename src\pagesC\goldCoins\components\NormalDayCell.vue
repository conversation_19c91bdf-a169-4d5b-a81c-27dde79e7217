<template>
    <view class="dateCell" :class="{ 'not-bg': !item.signedIn }">
        <!-- 已签到日期 -->
        <view v-if="item.signedIn" class="cell">
            <view class="signinactiveStyle">
                <view class="haveSignStyle">
                    <view class="signPointStyle-daytext">{{ item.date }}</view>
                    <view class="signPointStyle-today">已领取</view>
                    <view class="signPointStyle">+{{ item.goldValue }}</view>
                </view>
            </view>
        </view>
        <!-- 漏签 -->
        <view class="cell" v-else-if="!item.signedIn && index === 0 && !item.isToday">
            <view class="lackinactiveStyle">
                <!-- 日期 -->
                <view class="lack_data">未打卡</view>
                <view class="grey-icon"></view>
                <view class="lack_text">连签失败</view>
            </view>
        </view>
        <!-- 当前日期之后 -->
        <view class="cell" v-else>
            <view class="afterStyle">
                <view class="data_text">
                    {{ item.date }}
                </view>
                <view class="center-gold-icon"></view>
                <view class="data_text_unit"> +{{ item.goldValue }} </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    props: {
        item: {
            type: Object,
            default: () => ({}),
        },
        index: {
            type: Number,
            default: 0,
        },
    },
    created() {},
    watch: {},
    computed: {},
    methods: {},
};
</script>

<style lang="scss" scoped>
.dateCell {
    display: flex;
    // justify-content: space-between;
    flex-direction: column;
    align-items: center;
    width: 14%;
    width: 180rpx;
    height: 200rpx;
    border-radius: 28rpx;
    background: linear-gradient(343deg, #ffb547 -1%, #ff8826 49%, #fd5922 97%);
    overflow: hidden;

    &.not-bg {
        background: none;
    }

    // 九宫格布局中普通日期的宽度
    .grid-container &:not(.dateCell-today) {
        width: 30%;
    }
}
.cell {
    width: 100%;
    height: 100%;

    .signinactiveStyle {
        height: 100%;
        // background: #ffd183;
        border-radius: 4rpx;
        opacity: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        font-weight: 600;
        line-height: 100%;
        text-align: center;
        letter-spacing: 0px;

        .haveSignStyle {
            width: 100%;
            height: 100%;
            background: rgba(203, 183, 148, 0.2);
            border-radius: 16rpx;
            opacity: 1;
            padding: 16rpx 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #fff;
            justify-content: space-between;
            position: relative;

            .signPointStyle-daytext {
                font-family: MiSans;
                font-size: 24rpx;
                font-weight: normal;
                height: 24rpx;
                line-height: 24rpx;
                text-align: center;
                letter-spacing: 0px;
            }

            .smile {
                position: absolute;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a6cc139f9b2370010575.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                z-index: 1;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
            }

            .gou {
                width: 23rpx;
                height: 15rpx;
                background-image: url('@/static/checkIn/duigou.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
        }
    }

    .lackinactiveStyle {
        height: 100%;
        justify-content: center;
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5)), #ffb547;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx 0;
        justify-content: space-between;
        position: relative;

        .grey-icon {
            background-size: contain;
            background-repeat: no-repeat;
            position: absolute;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689f16a3625044030011010.png');
            position: absolute;
            top: 68rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 163rpx;
            height: 81rpx;
        }

        .lack_data {
            font-size: 24rpx;
            font-weight: normal;
            line-height: 24rpx;
            color: #777777;
        }

        .lack_text {
            font-size: 32rpx;
            height: 32rpx;
            line-height: 32rpx;
            font-weight: 600;
            color: #777777;
        }
    }

    .afterStyle {
        height: 100%;
        background-color: #fff !important;
        justify-content: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 16rpx 0;

        .center-gold-icon {
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689edad6c06bd7880035313.png');
            background-repeat: no-repeat;
            background-size: contain;
            width: 163rpx;
            height: 90rpx;
            position: absolute;
            top: 61rpx;
            left: 50%;
            transform: translateX(-50%);
        }

        .data_text {
            font-size: 24rpx;
            color: #777777;
            font-family: PingFang SC-常规体, PingFang SC;
            height: 24rpx;
            line-height: 24rpx;

        }

        .data_text_unit {
            font-size: 36rpx;
            font-weight: 600;
            color: #ff2300;
            height: 36rpx;
            line-height: 36rpx;

        }
    }

    .inactiveStyle {
        // width: 100%;
        height: 100%;
        /* 设置未拆红包背景 */
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689edad6c06bd7880035313.png');
        background-repeat: no-repeat;
        background-size: 163rpx 90rpx;
        background-position: 50% 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .queue {
            width: 57rpx;
            height: 57rpx;
            background: rgba(255, 209, 131, 0.2);
            opacity: 1;
            border-radius: 100%;
            font-size: 27rpx;
            font-family: MiSans-Semibold, MiSans;
            font-weight: 600;
            color: #e5b562;
            display: flex;
            align-items: center;
            justify-content: center;

            .inActiveAdd {
                width: 19rpx;
                height: 19rpx;
                background-image: url('@/static/checkIn/add1.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }

            .inActivePointNum {
                //   line-height: 31rpx;
            }
        }

        .pointStyle {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            font-family: MiSans;
            font-size: 24rpx;
            font-weight: normal;
            text-align: center;
            /* NeutralColor中性色/Gray 4 */
            color: #777777;
        }
    }

    .singalDate {
        height: 48rpx;
        font-size: 20rpx;
        font-family: PingFang SC-常规体, PingFang SC;
        font-weight: 600;
        color: $text-color-disable;
        line-height: 48rpx;
        margin-top: 10rpx;
        text-align: center;
    }

    .singalDate_default {
        width: 79rpx;
        background: linear-gradient(122deg, #ff8d50 7%, #f9580d 52%, #ff2e20 96%);
        height: 47rpx;
        border-radius: 100rpx;
        display: flex;
        text-align: center;
        align-items: center;
        margin: 0 auto;
        jusitify-content: center;
        justify-content: center;
        font-size: 20rpx;
        font-family: PingFang SC-常规体, PingFang SC;
        font-weight: 600;
        color: #fff;
        line-height: 28rpx;
        margin-top: 10rpx;
        text-align: center;
    }
}
</style>
