// 七夕活动背景图
$qixi-festival-bg-url: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a91c6e580c43610011523.png';
$qixi-festival-bg-url-2: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a3dc258b0dc5700012143.png';
$qixi-festival-bg-url-3: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a9110232def2080011182.png';
// 优惠券背景图
$coupon-bg-url: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2de97b3a8c7360013095.png';
$coupon-bg-url-list1: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c701d8150020458.png';
$coupon-bg-url-list1-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c17677920010585.png';
$coupon-bg-url-list2: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c72018160015076.png';
$coupon-bg-url-list2-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c72bf8160011415.png';
$coupon-bg-url-list3: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c0d827900011131.png';
$coupon-bg-url-list3-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c1f427940010854.png';
$coupon-bg-url-list4: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c1f947950010435.png';
$coupon-bg-url-list4-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c236a7950011549.png';
$coupon-bg-url-list5: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c20e57950018136.png';
$coupon-bg-url-list5-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c18997930011883.png';
$coupon-bg-url-list6: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c2b447980013328.png';
$coupon-bg-url-list6-grey: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a58de7c739a8160011971.png';

// 固定定位的返回按钮
.fixed-back-btn {
    position: fixed;
    left: 38rpx;
    z-index: 9999;
    padding: 20rpx;

    .search_back {
        width: 46rpx;
        height: 46rpx;
    }
}

.qixi-festival-page {
    min-height: 100vh;
    width: 100%;
    height: 100%;
    background-image: url($qixi-festival-bg-url),
        linear-gradient(to bottom, #bd183b 100rpx, transparent 1020rpx, #fce6e8 1020rpx, #fce6e8 100%);
    background-size: 100% 1020rpx, 100% 100%;
    background-repeat: no-repeat, no-repeat;
    background-position: 0% -12rpx, 0% 0%;

    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
        height: 177rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        left: 0;
        z-index: 100;
        background: transparent;

        .left {
            height: 108rpx;
            padding-left: 38rpx;
            color: #ffffff;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            z-index: 1000;

            // position: fixed;
            .search_back {
                width: 46rpx;
                height: 46rpx;
                margin-right: 26rpx;

                &.hidden {
                    display: none;
                }
            }
        }

        .header_title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            color: #ffffff;
            font-size: 36rpx;
            font-weight: bold;
            z-index: 1000;
        }

        .header_title_img {
            width: 162rpx;
            height: 60rpx;
            position: absolute;
            left: 92rpx;
        }

        .right {
            height: 100%;
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: flex-end;

            .share {
                height: 46rpx;
                width: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfd593d30b2510010640.png')
                    no-repeat center center;
                background-size: 100% 100%;
                margin-right: 32rpx;
            }

            .rule {
                height: 46rpx;
                width: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfca2106690670010343.png')
                    no-repeat center center;
                background-size: 100% 100%;
                margin-right: 32rpx;
            }
        }
    }

    // .header {
    //     .more-btn {
    //         height: 100%;
    //         display: flex;
    //         flex: 1;
    //         align-items: center;
    //         justify-content: flex-end;

    //         .share {
    //             height: 46rpx;
    //             width: 46rpx;
    //             background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfd593d30b2510010640.png')
    //                 no-repeat center center;
    //             background-size: 100% 100%;
    //             margin-right: 32rpx;
    //         }

    //         .rule {
    //             height: 46rpx;
    //             width: 46rpx;
    //             background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfca2106690670010343.png')
    //                 no-repeat center center;
    //             background-size: 100% 100%;
    //             margin-right: 32rpx;
    //         }
    //     }
    // }
}

.checkIn_box {
    width: 100%;
    height: 1298rpx;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7d64ddb50d8980024739.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    padding-top: 118rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 316rpx;
}

.main-coupon {
    background: url($qixi-festival-bg-url-2) no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    height: 496rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.multiple-coupons {
    background: url($qixi-festival-bg-url-3) no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    height: 390rpx;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    justify-content: flex-end;
    align-items: center;

    .multiple-coupons-content {
        margin-top: 30rpx;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        /* 3列等宽 */
        grid-template-rows: auto auto;
        /* 2行自适应高度 */
        gap: 10px;

        .multiple-coupons-item1 {
            background: url($coupon-bg-url-list1) no-repeat center center;
        }

        .multiple-coupons-item2 {
            background: url($coupon-bg-url-list2) no-repeat center center;
        }

        .multiple-coupons-item3 {
            background: url($coupon-bg-url-list3) no-repeat center center;
        }

        .multiple-coupons-item4 {
            background: url($coupon-bg-url-list4) no-repeat center center;
        }

        .multiple-coupons-item5 {
            background: url($coupon-bg-url-list5) no-repeat center center;
        }

        .multiple-coupons-item6 {
            background: url($coupon-bg-url-list6) no-repeat center center;
        }

        .multiple-coupons-list1-grey {
            background: url($coupon-bg-url-list1-grey) no-repeat center center;
        }

        .multiple-coupons-list2-grey {
            background: url($coupon-bg-url-list2-grey) no-repeat center center;
        }

        .multiple-coupons-list3-grey {
            background: url($coupon-bg-url-list3-grey) no-repeat center center;
        }

        .multiple-coupons-list4-grey {
            background: url($coupon-bg-url-list4-grey) no-repeat center center;
        }

        .multiple-coupons-list5-grey {
            background: url($coupon-bg-url-list5-grey) no-repeat center center;
        }

        .multiple-coupons-list6-grey {
            background: url($coupon-bg-url-list6-grey) no-repeat center center;
        }

        .multiple-coupons-item {
            background-size: 100% 100%;
            width: 196rpx;
            height: 128rpx;

            position: relative;

            .multiple-coupons-item-content {
                position: absolute;
                right: 0rpx;
                top: 50%;
                width: 62rpx;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                transform: translateY(-50%);
                z-index: 2;

                text {
                    color: #fff;
                    writing-mode: vertical-rl;
                    text-align: center;
                    font-size: 18rpx;
                    letter-spacing: 0.2em;
                    display: inline-block;
                }
            }
        }
    }

    .multiple-coupons-tip {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4rpx;
        padding-bottom: 10rpx;

        image {
            width: 24rpx;
            height: 24rpx;
        }

        text {
            font-size: 20rpx;
            color: #3d3d3d;
        }
    }
}

// 优惠券区域样式
.coupon-section {
    border-radius: 16px;
    padding: 60rpx 0rpx;

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .limit-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;

            .iconfont {
                font-size: 12px;
            }
        }
    }

    .event-details {
        color: #666;
        font-size: 24rpx;
        width: 638rpx;
        color: #32160f;
    }
}

// 主优惠券卡片样式
.main-coupon {
    display: flex;
    justify-content: center;
    align-items: center;

    .coupon-card {
        display: flex;
        background: url($coupon-bg-url) no-repeat center center;
        background-size: 100% 100%;
        border-radius: 12px;
        padding: 20px;
        color: #fff;
        width: 638rpx;
        height: 234rpx;
        margin-top: 16rpx;

        .coupon-right {
            flex: 1;
            margin-left: 210rpx;
            padding: 0rpx 0rpx 0rpx 24rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .coupon-text {
                .coupon-title {
                    font-size: 25rpx;
                    font-weight: 600;
                    margin-bottom: 8rpx;
                }

                .coupon-title-big {
                    font-size: 36rpx;
                }

                .countdown {
                    font-size: 21rpx;
                    opacity: 0.9;
                    margin-bottom: 16rpx;

                    .time_item {
                        width: 30rpx;
                        height: 28rpx;
                        line-height: 28rpx;
                        display: inline-block;
                        border-radius: 6rpx;
                        background: #fff6f4;
                        color: #e23255;
                        text-align: center;
                        margin: 0rpx 1rpx;
                    }

                    .time_item2 {
                        width: 25rpx;
                        height: 28rpx;
                        line-height: 28rpx;
                        display: inline-block;
                    }
                }
            }

            .claim-btn {
                padding: 16rpx 30rpx;
                font-size: 24rpx;
                font-weight: 600;
                line-height: 32rpx;
                letter-spacing: 0em;
                color: #e23255;
                background: #fff6f4;
                border-radius: 32rpx;
                width: fit-content;
            }

            .claim-btn-grey {
                opacity: 0.6;
                color: #e23255;
                background: #fff6f4;
            }
        }
    }
}

// 多种优惠券网格样式
.multiple-coupons {
    .coupon-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;

        .coupon-item {
            background: #fff5f5;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 2px solid #ffe4e6;

            .coupon-content {
                margin-bottom: 12px;

                .coupon-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #ff6b9d;
                    margin-bottom: 4px;
                }

                .coupon-condition {
                    font-size: 10px;
                    color: #999;
                    line-height: 1.2;
                }
            }

            .claim-btn-small {
                background: #ff6b9d;
                color: #fff;
                padding: 6px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
            }
        }
    }
}

// 产品区域样式
.product-section {
    margin: 0 16rpx;
    width: 100%;

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
        margin-top: 40rpx;

        .title {
            width: 142rpx;
            height: 46rpx;
            margin-left: 16rpx;
            background-size: 100% 100%;
        }

        .view-switch {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 14px;

            .iconfont {
                font-size: 16px;
            }
        }
    }

    .product-list {
        width: calc(100% - 32rpx);
        margin: 0 auto;

        .content {
            .content_title {
                background: #ffffff;
                border-radius: 40rpx;
                height: 300rpx;
                padding: 24rpx 24rpx 24rpx 24rpx;
                display: flex;

                .content_img_wrap {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    position: relative;

                    .content_img {
                        width: 282rpx;
                        height: 252rpx;
                        border-radius: 20rpx;
                    }

                    .content_img_icon {
                        position: absolute;
                        top: -20rpx;
                        right: -32rpx;
                        width: 94rpx;
                        height: 68rpx;
                        animation: scale 0.8s infinite alternate;
                        transform-origin: center;

                        @keyframes scale {
                            0% {
                                transform: scale(1);
                            }

                            100% {
                                transform: scale(0.8);
                            }
                        }
                    }

                    .content_img_icon1 {
                        position: absolute;
                        top: 0rpx;
                        left: 0rpx;
                        width: 78rpx;
                        height: 30rpx;
                    }

                    .content_img_icon2 {
                        position: absolute;
                        bottom: 0rpx;
                        left: 0rpx;
                        width: 134rpx;
                        height: 30rpx;

                        image {
                            width: 100%;
                            height: 100%;
                            vertical-align: top;
                        }
                    }
                }

                &:not(:last-child) {
                    margin-bottom: 16rpx;
                }

                .content_img {
                    min-width: 282rpx;
                    width: 282rpx;
                    height: 252rpx;
                    border-radius: 20rpx;
                    overflow: hidden;
                }

                .content_title_text {
                    margin-left: 26rpx;
                    width: calc(100% - 282rpx - 24rpx);
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .content_title_text_title {
                        margin-top: 28rpx;
                        font-size: 28rpx;
                        color: #000000;
                        font-weight: 500;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        white-space: normal;

                        .content_title_text_title_icon {
                            width: 56rpx;
                            height: 28rpx;
                        }

                        // width: calc(100% - 48rpx);
                    }

                    .content_title_text_content {
                        position: relative;

                        .content_title_text_content_assist_number {
                            /* NeutralColor中性色/Gray 4 */
                            color: #777777;
                            font-size: 14rpx;
                            font-weight: 500;
                            margin-top: 12rpx;
                            display: flex;
                            align-items: center;
                            gap: 8rpx;

                            .assist-avatars {
                                position: relative;
                                width: 50rpx;
                                height: 30rpx;
                                display: flex;
                                align-items: center;

                                .assist-avatar {
                                    position: absolute;
                                    width: 30rpx;
                                    height: 30rpx;
                                    border-radius: 50%;
                                    overflow: hidden;

                                    .avatar-img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                    }
                                }
                            }
                        }

                        .content_title_text_grab_text {
                            position: absolute;
                            color: #999999;
                            font-size: 20rpx;
                            margin-right: 16rpx;
                            bottom: 54rpx;
                        }

                        .content_title_text_grab {
                            display: flex;
                            justify-content: space-between;
                            height: 66rpx;
                            margin-top: 12rpx;
                            position: relative;

                            .content_title_text_grab_bg_img {
                                width: 380rpx;
                                height: 44rpx;
                                margin-top: 22rpx;
                                position: relative;

                                .content_title_text_grab_bg_progress_bg {
                                    width: 340rpx;
                                    height: 100%;
                                    // background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png') no-repeat center center;
                                    background-color: #fbf0ef;
                                    border-radius: 32rpx;
                                    background-size: 100% 100%;
                                    display: flex;
                                    align-items: center;

                                    .content_title_text_content_progresss {
                                        overflow: hidden;
                                        width: 140rpx;
                                        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c5846e25b29270011050.png')
                                            no-repeat center center;
                                        background-size: 100% 100%;
                                        border-radius: 200rpx;
                                        margin-left: 18rpx;
                                        height: 24rpx;
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;

                                        .progress {
                                            height: 24rpx;
                                            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png')
                                                no-repeat center center;
                                            background-size: 100% 100%;
                                            border-radius: 200rpx;
                                        }

                                        .progress-text {
                                            font-size: 20rpx;
                                            color: #ff3c3c;
                                            font-weight: 500;
                                        }
                                    }

                                    .content_title_text_grab_text_progress_text {
                                        position: absolute;
                                        bottom: 0rpx;
                                        font-size: 15rpx;
                                        left: 160rpx;
                                        color: #ff0084;
                                        height: 100%;
                                        display: flex;
                                        align-items: center;
                                    }
                                }

                                .content_title_text_grab_text_assist_bg {
                                    width: 340rpx;
                                    height: 100%;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc71d825e65340010549.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                    display: flex;
                                    align-items: center;
                                }

                                .content_title_text_grab_text {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 260rpx;
                                    width: 110rpx;
                                    height: 56rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                }

                                .normal_buy_active {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 260rpx;
                                    width: 110rpx;
                                    height: 56rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e06fbbe9327810010375.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                }

                                .content_title_text_grab_text_assist {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 260rpx;
                                    width: 110rpx;
                                    height: 56rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c8cfd68c794290012295.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                    z-index: 50;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 发布商品弹窗样式

.publish-popup {
    position: relative;

    .publish-popup-body {
        background: linear-gradient(180deg, #ba1b37 0%, rgba(186, 27, 55, 0) 100%);
        border-radius: 24rpx 24rpx 0 0;
        padding: 40rpx 16rpx 48rpx;
        position: relative;

        .publish-popup-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32rpx;

            .publish-popup-title {
                font-size: 36rpx;
                padding-left: 40rpx;
                font-weight: 600;
                color: #ffffff;
                flex: 1;
                text-align: center;
            }

            .publish-popup-close {
                width: 40rpx;
                height: 40rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 40rpx;
                color: #999;
                cursor: pointer;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a34c4b91d7e5970015439.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }

        .content {
            max-height: 950rpx;
            overflow: auto;

            .content_title {
                background: #ffffff;
                border-radius: 40rpx;
                height: 300rpx;
                padding: 24rpx 24rpx 24rpx 24rpx;
                display: flex;

                &:not(:last-child) {
                    margin-bottom: 16rpx;
                }

                .content_img_wrap {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    position: relative;

                    .content_img {
                        width: 282rpx;
                        height: 252rpx;
                        border-radius: 20rpx;
                    }

                    .content_img_icon {
                        position: absolute;
                        top: -20rpx;
                        right: -32rpx;
                        width: 94rpx;
                        height: 68rpx;
                        animation: scale 0.8s infinite alternate;
                        transform-origin: center;

                        @keyframes scale {
                            0% {
                                transform: scale(1);
                            }

                            100% {
                                transform: scale(0.8);
                            }
                        }
                    }

                    .content_img_icon1 {
                        position: absolute;
                        top: 0rpx;
                        left: 0rpx;
                        width: 78rpx;
                        height: 30rpx;
                    }

                    .content_img_icon2 {
                        position: absolute;
                        bottom: 0rpx;
                        left: 0rpx;
                        width: 134rpx;
                        height: 30rpx;

                        image {
                            width: 100%;
                            height: 100%;
                            vertical-align: top;
                        }
                    }
                }

                &:not(:last-child) {
                    margin-bottom: 16rpx;
                }

                .content_img {
                    min-width: 282rpx;
                    width: 282rpx;
                    height: 252rpx;
                    border-radius: 20rpx;
                    overflow: hidden;
                }

                .content_title_text {
                    margin-left: 26rpx;
                    width: calc(100% - 282rpx - 24rpx);
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .content_title_text_title {
                        margin-top: 28rpx;
                        font-size: 28rpx;
                        color: #000000;
                        font-weight: 500;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        white-space: normal;
                        // width: calc(100% - 48rpx);
                    }

                    .content_title_text_content {
                        position: relative;

                        .content_title_text_content_assist_number {
                            /* NeutralColor中性色/Gray 4 */
                            color: #777777;
                            font-size: 14rpx;
                            font-weight: 500;
                            margin-top: 12rpx;
                            display: flex;
                            align-items: center;
                            gap: 8rpx;

                            .assist-avatars {
                                position: relative;
                                width: 50rpx;
                                height: 30rpx;
                                display: flex;
                                align-items: center;

                                .assist-avatar {
                                    position: absolute;
                                    width: 30rpx;
                                    height: 30rpx;
                                    border-radius: 50%;
                                    overflow: hidden;

                                    .avatar-img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                    }
                                }
                            }
                        }

                        .content_title_text_grab_text {
                            position: absolute;
                            color: #999999;
                            font-size: 20rpx;
                            margin-right: 16rpx;
                            bottom: 54rpx;
                        }

                        .content_title_text_grab {
                            display: flex;
                            justify-content: space-between;
                            height: 66rpx;
                            margin-top: 12rpx;
                            position: relative;

                            .content_title_text_grab_bg_img {
                                width: 380rpx;
                                height: 44rpx;
                                margin-top: 22rpx;
                                position: relative;

                                .content_title_text_grab_bg_progress_bg {
                                    width: 340rpx;
                                    height: 100%;
                                    // background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png') no-repeat center center;
                                    background-color: #fbf0ef;
                                    border-radius: 32rpx;
                                    background-size: 100% 100%;
                                    display: flex;
                                    align-items: center;

                                    .content_title_text_content_progresss {
                                        overflow: hidden;
                                        width: 140rpx;
                                        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c5846e25b29270011050.png')
                                            no-repeat center center;
                                        background-size: 100% 100%;
                                        border-radius: 200rpx;
                                        margin-left: 18rpx;
                                        height: 24rpx;
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;

                                        .progress {
                                            height: 24rpx;
                                            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png')
                                                no-repeat center center;
                                            background-size: 100% 100%;
                                            border-radius: 200rpx;
                                        }

                                        .progress-text {
                                            font-size: 20rpx;
                                            color: #ff3c3c;
                                            font-weight: 500;
                                        }
                                    }

                                    .content_title_text_grab_text_progress_text {
                                        position: absolute;
                                        bottom: 0rpx;
                                        font-size: 15rpx;
                                        left: 160rpx;
                                        color: #ff0084;
                                        height: 100%;
                                        display: flex;
                                        align-items: center;
                                    }
                                }

                                .content_title_text_grab_text_assist_bg {
                                    width: 340rpx;
                                    height: 100%;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc71d825e65340010549.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                    display: flex;
                                    align-items: center;
                                }

                                .content_title_text_grab_text {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 254rpx;
                                    width: 130rpx;
                                    height: 66rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                }

                                .normal_buy_active {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 250rpx;
                                    width: 130rpx;
                                    height: 66rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e06fbbe9327810010375.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                }

                                .content_title_text_grab_text_assist {
                                    position: absolute;
                                    bottom: 0rpx;
                                    left: 250rpx;
                                    width: 130rpx;
                                    height: 66rpx;
                                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c8cfd68c794290012295.png')
                                        no-repeat center center;
                                    background-size: 100% 100%;
                                    z-index: 1000;
                                }
                            }
                        }
                    }
                }
            }
        }

        .goods-count {
            font-size: 28rpx;
            color: #121212;
            margin-bottom: 24rpx;
            font-weight: 600;
        }

        .publish-popup-goods-container {
            max-height: 900rpx;
            overflow-y: auto;
        }

        .publish-popup-goods {
            display: flex;
            align-items: flex-start;
            background: #f6f6f6;
            margin-bottom: 48rpx;
            padding: 20rpx 30rpx 20rpx 20rpx;
            border-radius: 24rpx;

            .goods-image {
                width: 144rpx;
                height: 144rpx;
                margin-right: 20rpx;
                flex-shrink: 0;

                image {
                    width: 100%;
                    height: 100%;
                    border-radius: 12rpx;
                }
            }

            .goods-info {
                flex: 1;
                min-width: 0;

                .goods-name {
                    font-size: 24rpx;
                    color: #121212;
                    line-height: 1.4;
                    margin-bottom: 16rpx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }

                .goods-commission {
                    font-size: 24rpx;
                    color: #a6a6a6;
                    font-weight: normal;
                    margin-bottom: 8rpx;
                }

                .goods-count {
                    font-size: 24rpx;
                    color: #999;
                }
            }
        }

        .publish-popup-button {
            width: 100%;
            height: 88rpx;
            background: #e8dec1;
            color: #8c6533;
            font-size: 32rpx;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 16rpx;
            cursor: pointer;

            &:active {
                opacity: 0.8;
            }
        }
    }
}

// 底部按钮样式
.footer {
    margin: 40rpx auto 120rpx;
    display: flex;
    justify-content: center;
    width: 90%;
    max-width: 600rpx;

    .footer-btn {
        background: linear-gradient(135deg, #ff6b9d 0%, #ff4757 100%);
        color: #fff;
        padding: 16px;
        border-radius: 16px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        box-shadow: 0 8px 24px rgba(255, 107, 157, 0.3);
    }
}

// 图标字体样式（需要引入对应的图标字体文件）
.iconfont {
    font-family: 'iconfont';
}

.icon-arrow-left::before {
    content: '←';
}

.icon-share::before {
    content: '↗';
}

.icon-info::before {
    content: 'ℹ';
}

.icon-heart::before {
    content: '♥';
}

.icon-view::before {
    content: '👁';
}

// 活动规则弹窗样式
.rule-popup-content {
    width: 664rpx;
    height: 824rpx;
    max-height: 824rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb86eabbf07030010744.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx;
    overflow: hidden;

    .rule-popup-header {
        padding: 94rpx 36rpx 30rpx;
        position: relative;
        text-align: center;

        .rule-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            .rule-title-decoration {
                width: 20rpx;
                height: 20rpx;

                &.left {
                    margin-right: 16rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png')
                        no-repeat center center;
                    background-size: 100% 100%;
                    width: 92rpx;
                    height: 20rpx;
                }

                &.right {
                    margin-left: 16rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png')
                        no-repeat center center;
                    background-size: 100% 100%;
                    width: 92rpx;
                    height: 20rpx;
                }
            }

            .rule-title-text {
                font-size: 36rpx;
                font-weight: 600;
                color: #ff0084;
                position: relative;
            }
        }

        .rule-popup-close {
            position: absolute;
            right: 36rpx;
            top: 24rpx;
            width: 46rpx;
            height: 46rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;

            .close-icon {
                width: 46rpx;
                height: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }
    }

    .rule-container {
        padding: 0 36rpx 32rpx;

        .rule-content {
            background: #ffffff;
            border-radius: 32rpx;
            padding: 24rpx 14rpx 24rpx 0rpx;

            .rule-content-text {
                padding: 0rpx 24rpx;
                max-height: 572rpx;
                height: 620rpx;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    display: block;
                    width: 8rpx !important;
                    height: 0rpx !important;
                    opacity: 0; // 不生效
                    transition: height 2s;
                }

                &::-webkit-scrollbar-track {
                    border-radius: 10rpx !important;
                }

                &::-webkit-scrollbar-thumb {
                    background: #d8d8d8 !important;
                    border-radius: 10rpx !important;
                }
            }

            .rule-section {
                margin-bottom: 32rpx;
                font-size: 22rpx;
                color: #3d3d3d;
                line-height: 42rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .rule-section-title {
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #333333;
                    margin-bottom: 16rpx;
                    line-height: 1.4;
                }

                .rule-section-content {
                    .rule-text {
                        font-size: 28rpx;
                        color: #666666;
                        line-height: 1.6;
                        margin-bottom: 12rpx;
                        text-align: justify;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}

.tipContent {
    width: 640rpx;
    height: 372rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a86358548783460016071.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    position: relative;

    .tipBtn {
        width: 550rpx;
        height: 94rpx;
        position: absolute;
        left: 50%;
        bottom: 42rpx;
        transform: translateX(-50%);
    }
}
