<template>
    <view>
        <view class="waterfall-box" v-for="(item, index) in list" :key="index" @click="viewDeatail(item)">

            <lazy-image :src="item.cover_image" customStyle="width:344rpx;height: 352rpx;border-radius: 16rpx;" :fade="true"
                :lazyLoad="true" radius="16rpx" duration="450">
                <view slot="error" style="font-size: 24rpx">加载失败</view>
            </lazy-image>
            <view class="box-item-title">
                {{ item.title || '--' }}
            </view>
            <view class="box-comment">
                <view class="person">
                    <view class="author">{{ item.author }}</view>
                    <image v-if="item.icon" :src="item.icon" style="width:28rpx;height:28rpx" :fade="true" :lazyLoad="true"
                        duration="450">
                    </image>
                </view>
                <view class="sum"><img class="view-num"
                        src="https://wpm-cdn.dreame.tech/images/202306/647809cf9e3156484414817.png" />{{ item.clicks |
                            clicks }}</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'MyGoodsDemo',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
    filters: {
        clicks(val) {
            if (Number(val) < 1000) {
                return val;
            } else {
                const item = (Number(val) / 1000).toFixed(1);
                return item + 'k';
            }
        }
    },
    data() {
        return {};
    },
    methods: {
        viewDeatail(item) {
            this.$emit('change', item);
        },
    },
};
</script>

<style lang="scss">
.waterfall-box {
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    background-color: #ffffff;
    position: relative;

    .box-item-title {
        padding-right: 20rpx;
        padding-left: 20rpx;
        font-size: 28rpx;
        margin-top: 20rpx;
        color: #1d1e20;
        font-weight: 500;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .box-comment {
        padding-right: 20rpx;
        padding-left: 20rpx;
        font-size: 20rpx;
        font-weight: 400;
        color: rgba(29, 30, 32, 0.4);
        margin-top: 20rpx;
        padding-bottom: 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .person {
            display: flex;
            align-items: center;

            .author {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: keep-all;
                margin-right: 4rpx;
                max-width: 180rpx;
            }
        }

        .sum {
            display: flex;
            align-items: center;

            .view-num {
                width: 28rpx;
                height: 28rpx;
                margin-right: 8rpx;
            }
        }
    }
}
</style>
