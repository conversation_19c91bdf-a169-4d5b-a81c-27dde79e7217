/* eslint-disable object-curly-spacing */
import http from './index';

/*
 * 产品百科
 * @returns
 */
export const productWikiList = () => http.post('main/wiki/product-wiki', {});

/*
 * 用户调研
 * @returns
 */
export const survey = () => http.post('main/wiki/survey', {});

/*
 * 话题列表
 * @param pid
 * @returns
 */
export const tlist = (pid: string) => http.post('main/wiki/tlist', { pid });

/*
 * 内容列表
 * @param page 页码
 * @param t2 所选二级话题id
 * @param sort 0时间排序 1：热度排序
 * @returns
 */
interface wlistParams {
    page: number;
    t2: string;
    sort: number;
}
// 探觅随机数据
export const wnewList = (params) => http.post('main/wiki/wnew-list', params);

export const wlist = (params: wlistParams, loading: boolean = true) =>
    http.post('main/wiki/wlist', params, { custom: { loading } });

// 内容管理-详情
export const wikiWinfo = (params) => http.post('main/wiki/winfo', params);

/*

/*
 * 内容详情
 * @param did 数据id
 * @returns
 */
export const winfo = (did: string) => http.post('main/wiki/winfo', { did });

/*
 * 点赞操作
 * @param did 数据id
 * @param is_del 0：点赞 1：取消点赞
 * @returns
 */
interface praiseParams {
    did: string;
    is_del: number | string;
}
export const praise = (params: praiseParams) => http.post('main/wiki/praise', params);

/*
 * 获取文档列表
 * @param doc_code 唯一标识
 * @returns
 */
export const docList = (doc_code: string) => http.post('main/wiki/doc-list', { doc_code });
