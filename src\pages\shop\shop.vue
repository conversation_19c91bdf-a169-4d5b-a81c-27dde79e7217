<template>
    <view>
        <view class="container" :style="{ 'padding-top': pagePaddingTop + 'rpx' }" v-if="isLoaded">
            <view class="header" style="background-color: #fff">
                <!-- #ifdef H5 -->
                <custom-bar2 :title="pageTitle" :isComparison="true" :isSearch="true"></custom-bar2>
                <!-- #endif -->
                <!-- #ifdef MP-WEIXIN -->
                <custom-bar2 :isBack="false" :title="pageTitle" :isComparison="true" :isShowCart="true"></custom-bar2>
                <!-- #endif -->
                <!-- #ifdef MP-WEIXIN -->
                <view class="search">
                    <CustomSearch
                        ref="search"
                        type="text"
                        searchIcon="search"
                        placeholder="请输入要搜索的关键词"
                        bgColor="#F4F4F4"
                        :showAction="false"
                        :disabled="true"
                        :actionStyle="{
                            display: 'none',
                            color: '#7473BF',
                            paddingLeft: '38rpx',
                            marginRight: '32rpx',
                            borderLeft: '2rpx solid rgba(29,30,32,0.1)',
                        }"
                        @click="_navigateTo"
                        @custom="_navigateTo"
                    >
                    </CustomSearch>
                </view>
                <!-- #endif -->
            </view>
            <view class="tab">
                <scroll-view
                    scroll-x
                    enable-flex
                    :scroll-into-view="currentTab"
                    scroll-with-animation
                    class="tab-view u-flex"
                >
                    <view
                        v-for="(item, index) in tabsList"
                        :key="index"
                        :id="'tab-' + index"
                        class="tab-item u-flex u-col-center u-row-center"
                        :class="[tid == item.tid ? 'tab-item-active' : '']"
                        :data-current="index"
                        @tap.stop="swichMenu(index, item)"
                    >
                        <text class="name">{{ item.name }}</text>
                    </view>
                </scroll-view>
            </view>
            <view class="main u-flex u-col-top u-row-left">
                <view class="menu-wrap u-flex">
                    <swiper
                        class="swiper-container"
                        :current="swiperCurrent"
                        @transition="swiperTransition"
                        @change="swiperChange"
                        @animationfinish="swiperFinish"
                        style="width: 100%; height: 100%"
                    >
                        <block v-for="(item, index) in tabsList" :key="index">
                            <swiper-item class="swiper-slide" v-if="item.tid != '20'">
                                <scroll-view
                                    scroll-y
                                    class="right-box"
                                    :refresher-enabled="enableRefresher"
                                    :upper-threshold="0"
                                    :lower-threshold="300"
                                    :refresher-triggered="isRefreshing"
                                    @refresherrefresh="refresherrefresh"
                                    @scrolltolower="onreachBottom"
                                >
                                    <view v-if="!isPartZone" class="page-view">
                                        <block v-for="itm in listData[index].list" :key="itm.gid">
                                            <view class="class-item" @click="gotoGdetailPage(itm.gid)">
                                                <view class="class-item-top" :class="{ extend: !!itm.atmosphere_img }">
                                                    <!-- <view class="service-content">
                                                        <view class="service1" :class="{ 'service1-active': itm.is_wish }" @click.stop="handleWishList(itm.is_wish, itm.gid)">
                                                        </view>
                                                    </view> -->
                                                    <view
                                                        v-if="itm.atmosphere_img"
                                                        class="atmosphereImg"
                                                        :style="{
                                                            backgroundImage: `url(${itm.atmosphere_img})`,
                                                        }"
                                                    ></view>
                                                    <!-- #ifdef H5 -->
                                                    <LazyImage
                                                        :src="itm.market_image + '?x-oss-process=image/format,webp'"
                                                    >
                                                    </LazyImage>
                                                    <!-- #endif -->
                                                    <!-- #ifdef MP-WEIXIN -->
                                                    <LazyImage
                                                        :src="itm.market_image + '?x-oss-process=image/format,webp'"
                                                        mode="scaleToFill"
                                                    >
                                                    </LazyImage>
                                                    <!-- #endif -->
                                                    <template v-if="isChooseSet">
                                                        <image
                                                            class="class-item-radio"
                                                            v-if="itm.checked"
                                                            @click.stop="(_) => toggleSelected(itm, false)"
                                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d6c69ff386550011456.png"
                                                        />
                                                        <image
                                                            class="class-item-radio"
                                                            v-else
                                                            @click.stop="(_) => toggleSelected(itm, true)"
                                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d6b9c94f38250012232.png"
                                                        />
                                                    </template>
                                                </view>
                                                <view class="class-item-bottom">
                                                    <view class="name u-line-2">
                                                        <text
                                                            class="uni-presale-tag tag"
                                                            style="line-height: 1"
                                                            v-if="itm.is_presale === 1"
                                                            >预售</text
                                                        >
                                                        <view
                                                            class="text"
                                                            :style="{
                                                                textIndent: itm.is_presale === 1 ? '72rpx' : '0rpx',
                                                            }"
                                                            >{{ itm.name }}</view
                                                        >
                                                    </view>
                                                    <view class="price">
                                                        <view
                                                            style="
                                                                color: #a6a6a6;
                                                                white-space: nowrap;
                                                                overflow: hidden;
                                                                text-overflow: ellipsis;
                                                                width: 100%;
                                                                height: 66rpx;
                                                            "
                                                        >
                                                            <!-- 券后价是否加载完成 -->
                                                            <!-- <block v-if="!itm.coupon_finish">
                                                                <view class="price_skeleton">
                                                                    <u-skeleton
                                                                        :animate="true"
                                                                        :title="false"
                                                                        :rowsHeight="[rowHeight_skeleton_coupon]"
                                                                        :loading="true"
                                                                        rows="1"
                                                                        :rowsWidth="[`${rowWidths_skeleton_coupon[0]}`]"
                                                                    ></u-skeleton>
                                                                    <u-skeleton
                                                                        :animate="true"
                                                                        :title="false"
                                                                        :rowsHeight="[rowHeight_skeleton_coupon]"
                                                                        :loading="true"
                                                                        rows="1"
                                                                        :rowsWidth="[`${rowWidths_skeleton_coupon[1]}`]"
                                                                    ></u-skeleton>
                                                                </view>
                                                            </block>
                                                            <block v-else> -->
                                                            <!-- 是否有券后价 -->
                                                            <!-- <block
                                                                    v-if="
                                                                        showCouponPrice(itm) &&
                                                                        itm.coupon_price &&
                                                                        itm.coupon_price !== itm.price
                                                                    "
                                                                >
                                                                    <text class="unit" style="color: #ee3c2f">￥</text>
                                                                    <text
                                                                        class="optimizePrice"
                                                                        style="color: #ee3c2f"
                                                                        >{{
                                                                            parseFloat(itm.coupon_price.split('.')[0])
                                                                        }}</text
                                                                    >
                                                                    <text
                                                                        class="dot"
                                                                        style="color: #ee3c2f"
                                                                        v-if="
                                                                            parseFloat(
                                                                                itm.coupon_price.split('.')[1],
                                                                            ) !== 0
                                                                        "
                                                                        >.{{ itm.coupon_price.split('.')[1] }}</text
                                                                    >
                                                                    <text class="coupon-text">券后价</text>
                                                                    <text
                                                                        class="primaryPrice"
                                                                        style="margin-left: 16rpx"
                                                                        >￥{{ parseFloat(itm.mprice) }}</text
                                                                    >
                                                                </block> -->
                                                            <block>
                                                                <text class="unit">￥</text>
                                                                <text class="optimizePrice">{{
                                                                    (
                                                                        Number(itm.price || 0) -
                                                                        Number(itm.subsidy_price || 0)
                                                                    ).toFixed(2)
                                                                }}</text>
                                                                <!-- <text
                                                                        class="dot"
                                                                        v-if="parseFloat(itm.price.split('.')[1]) !== 0"
                                                                        >.{{ itm.price.split('.')[1] }}</text
                                                                    >
                                                                    <text
                                                                        class="primaryPrice"
                                                                        style="margin-left: 16rpx"
                                                                        v-if="itm.price !== itm.mprice"
                                                                        >￥{{ parseFloat(itm.mprice) }}</text
                                                                    > -->
                                                            </block>
                                                            <!-- </block> -->
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </block>
                                        <block v-if="!listData[index].list.length && !listData[index].finished">
                                            <view class="flex-container" style="width: 100%">
                                                <view class="column right-column" v-for="items in 2" :key="items">
                                                    <view class="skeleton-container" v-for="item in 5" :key="item">
                                                        <u-skeleton
                                                            :animate="true"
                                                            :title="false"
                                                            :rowsHeight="[120]"
                                                            :loading="true"
                                                            rows="1"
                                                            :rowsWidth="['100%']"
                                                        ></u-skeleton>
                                                    </view>
                                                </view>
                                            </view>
                                        </block>
                                        <block v-if="!listData[index].list.length && listData[index].finished">
                                            <view class="epmty">
                                                <LazyImage
                                                    src="https://wpm-cdn.dreame.tech/images/202412/674ef25ad58d98751106040.png"
                                                >
                                                </LazyImage>
                                                <text class="text">暂无商品</text>
                                            </view>
                                        </block>
                                    </view>
                                    <!-- <view
                                        class="no-more"
                                        v-if="
                                            !isPartZone &&
                                            listData[index].finished &&
                                            listData[index] &&
                                            listData[index].list.length > 0
                                        "
                                        >——没有更多了——
                                    </view> -->
                                    <view
                                        class="recommend-title"
                                        v-if="
                                            listData[index].list.length > 0 &&
                                            listData[index].finished &&
                                            recommendedList.length > 0
                                        "
                                    >
                                        <text>精选好物</text>
                                    </view>
                                    <view>
                                        <view class="page-view">
                                            <block v-for="itm in recommendedList" :key="itm.gid">
                                                <view class="class-item" @click="gotoGdetailPage(itm.gid)">
                                                    <view
                                                        class="class-item-top"
                                                        :class="{ extend: !!itm.atmosphere_img }"
                                                    >
                                                        <!-- <view class="service-content">
                                                        <view class="service1" :class="{ 'service1-active': itm.is_wish }" @click.stop="handleWishList(itm.is_wish, itm.gid)">
                                                        </view>
                                                    </view> -->
                                                        <view
                                                            v-if="itm.atmosphere_img"
                                                            class="atmosphereImg"
                                                            :style="{
                                                                backgroundImage: `url(${itm.atmosphere_img})`,
                                                            }"
                                                        ></view>
                                                        <!-- #ifdef H5 -->
                                                        <LazyImage
                                                            :src="itm.market_image + '?x-oss-process=image/format,webp'"
                                                        >
                                                        </LazyImage>
                                                        <!-- #endif -->
                                                        <!-- #ifdef MP-WEIXIN -->
                                                        <LazyImage
                                                            :src="itm.market_image + '?x-oss-process=image/format,webp'"
                                                            mode="scaleToFill"
                                                        >
                                                        </LazyImage>
                                                        <!-- #endif -->
                                                        <template v-if="isChooseSet">
                                                            <image
                                                                class="class-item-radio"
                                                                v-if="itm.checked"
                                                                @click.stop="(_) => toggleSelected(itm, false)"
                                                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d6c69ff386550011456.png"
                                                            />
                                                            <image
                                                                class="class-item-radio"
                                                                v-else
                                                                @click.stop="(_) => toggleSelected(itm, true)"
                                                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d6b9c94f38250012232.png"
                                                            />
                                                        </template>
                                                    </view>
                                                    <view class="class-item-bottom">
                                                        <view class="name u-line-2">
                                                            <text
                                                                class="uni-presale-tag tag"
                                                                style="line-height: 1"
                                                                v-if="itm.is_presale === 1"
                                                                >预售</text
                                                            >
                                                            <view
                                                                class="text"
                                                                :style="{
                                                                    textIndent: itm.is_presale === 1 ? '72rpx' : '0rpx',
                                                                }"
                                                                >{{ itm.name }}</view
                                                            >
                                                        </view>
                                                        <view class="price">
                                                            <view
                                                                style="
                                                                    color: #a6a6a6;
                                                                    white-space: nowrap;
                                                                    overflow: hidden;
                                                                    text-overflow: ellipsis;
                                                                    width: 100%;
                                                                    height: 66rpx;
                                                                "
                                                            >
                                                                <!-- 券后价是否加载完成 -->
                                                                <!-- <block v-if="!itm.coupon_finish">
                                                                <view class="price_skeleton">
                                                                    <u-skeleton
                                                                        :animate="true"
                                                                        :title="false"
                                                                        :rowsHeight="[rowHeight_skeleton_coupon]"
                                                                        :loading="true"
                                                                        rows="1"
                                                                        :rowsWidth="[`${rowWidths_skeleton_coupon[0]}`]"
                                                                    ></u-skeleton>
                                                                    <u-skeleton
                                                                        :animate="true"
                                                                        :title="false"
                                                                        :rowsHeight="[rowHeight_skeleton_coupon]"
                                                                        :loading="true"
                                                                        rows="1"
                                                                        :rowsWidth="[`${rowWidths_skeleton_coupon[1]}`]"
                                                                    ></u-skeleton>
                                                                </view>
                                                            </block>
                                                            <block v-else> -->
                                                                <!-- 是否有券后价 -->
                                                                <!-- <block
                                                                    v-if="
                                                                        showCouponPrice(itm) &&
                                                                        itm.coupon_price &&
                                                                        itm.coupon_price !== itm.price
                                                                    "
                                                                >
                                                                    <text class="unit" style="color: #ee3c2f">￥</text>
                                                                    <text
                                                                        class="optimizePrice"
                                                                        style="color: #ee3c2f"
                                                                        >{{
                                                                            parseFloat(itm.coupon_price.split('.')[0])
                                                                        }}</text
                                                                    >
                                                                    <text
                                                                        class="dot"
                                                                        style="color: #ee3c2f"
                                                                        v-if="
                                                                            parseFloat(
                                                                                itm.coupon_price.split('.')[1],
                                                                            ) !== 0
                                                                        "
                                                                        >.{{ itm.coupon_price.split('.')[1] }}</text
                                                                    >
                                                                    <text class="coupon-text">券后价</text>
                                                                    <text
                                                                        class="primaryPrice"
                                                                        style="margin-left: 16rpx"
                                                                        >￥{{ parseFloat(itm.mprice) }}</text
                                                                    >
                                                                </block> -->
                                                                <block>
                                                                    <text class="unit">￥</text>
                                                                    <text class="optimizePrice">{{
                                                                        (
                                                                            Number(itm.price || 0) -
                                                                            Number(itm.subsidy_price || 0)
                                                                        ).toFixed(2)
                                                                    }}</text>
                                                                    <!-- <text
                                                                        class="dot"
                                                                        v-if="parseFloat(itm.price.split('.')[1]) !== 0"
                                                                        >.{{ itm.price.split('.')[1] }}</text
                                                                    >
                                                                    <text
                                                                        class="primaryPrice"
                                                                        style="margin-left: 16rpx"
                                                                        v-if="itm.price !== itm.mprice"
                                                                        >￥{{ parseFloat(itm.mprice) }}</text
                                                                    > -->
                                                                </block>
                                                                <!-- </block> -->
                                                            </view>
                                                        </view>
                                                    </view>
                                                </view>
                                            </block>
                                        </view>
                                    </view>
                                </scroll-view>
                            </swiper-item>
                        </block>
                        <swiper-item class="swiper-slide" :key="-1">
                            <view class="right-box">
                                <view v-if="isPartZone" class="page-view" style="height: 100%">
                                    <view class="list-title" v-if="!isRefreshing">
                                        <view class="title-img" @click="flagClick">
                                            <text class="">品类选择</text>
                                            <LazyImage
                                                :class="flag ? 'arrowTransformReturn' : 'arrowTransform'"
                                                customStyle="width: 38rpx; height: 38rpx;margin-left: 12rpx;"
                                                src="https://wpm-cdn.dreame.tech/images/202308/64e30ac46cb514451315896.png"
                                            >
                                            </LazyImage>
                                        </view>
                                        <view
                                            class="drop-down"
                                            :style="{ height: flag ? `${cateList2.length * 88 + 90}rpx` : 0 }"
                                        >
                                            <view style="width: 100%; height: 90rpx"></view>
                                            <block v-for="(_item, index) in cateList" :key="index">
                                                <view
                                                    class="down-box"
                                                    :class="{ active: activePartZoneTab === index }"
                                                    v-if="_item.children"
                                                    @click="jump(index)"
                                                >
                                                    {{ _item.name }}
                                                </view>
                                            </block>
                                        </view>
                                    </view>
                                    <scroll-view
                                        scroll-y
                                        class="group-list"
                                        :scroll-into-view="scrollIntoView"
                                        scroll-with-animation
                                    >
                                        <block v-for="(_item, i) in cateList" :key="i">
                                            <view class="item d_jump" :id="'d_jump-' + i" v-if="!!_item.children">
                                                <view class="item-title">{{ _item.name }}</view>
                                                <view class="item-content">
                                                    <view
                                                        class="item-content-block"
                                                        v-for="(item2, j) in _item.children"
                                                        :key="j"
                                                        v-if="_item.isShow ? true : isFoldableDevice || j < 6"
                                                        @click="toPartsCate(item2.id, _item.name, item2.name)"
                                                    >
                                                        <view class="class-content-top">
                                                            <LazyImage :src="item2.icon" />
                                                        </view>
                                                        <view class="class-content-bottom">
                                                            <text class="name u-line-2">{{ item2.name }}</text>
                                                        </view>
                                                    </view>
                                                </view>
                                                <view
                                                    class="more"
                                                    v-if="!isFoldableDevice && _item.children.length > 6"
                                                    @click="moreShow(_item.isShow, i)"
                                                >
                                                    <text class="text">{{ _item.isShow ? '收起' : '更多' }}</text>
                                                    <view :class="_item.isShow ? 'upArrow' : 'downArrow'"> </view>
                                                </view>
                                            </view>
                                        </block>
                                    </scroll-view>
                                </view>
                                <block v-if="!cateList.length && isPartZone">
                                    <view style="position: absolute; top: 90rpx; right: 0; bottom: 0; left: 0">
                                        <block v-for="items in 3" :key="items">
                                            <view
                                                class="flex-container"
                                                style="width: calc(100%); margin: 10rpx 0; padding: 0 16rpx"
                                            >
                                                <u-skeleton
                                                    :animate="true"
                                                    :title="false"
                                                    :loading="true"
                                                    rows="1"
                                                    :rowsHeight="[40]"
                                                    :rowsWidth="['100%']"
                                                ></u-skeleton>
                                            </view>
                                            <view class="flex-container" style="width: 100%">
                                                <view class="column right-column" v-for="items in 2" :key="items">
                                                    <view class="skeleton-container" v-for="item in 1" :key="item">
                                                        <u-skeleton
                                                            :animate="true"
                                                            :title="false"
                                                            :rowsHeight="[200]"
                                                            :loading="true"
                                                            rows="1"
                                                            :rowsWidth="['100%']"
                                                        ></u-skeleton>
                                                    </view>
                                                </view>
                                            </view>
                                        </block>
                                    </view>
                                </block>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>

                <view v-if="isPartZone" class="reg" @click="toProductRegister">
                    <view class="regLeft"
                        >{{ `${products.length === 0 ? '注册您的产品' : '您已注册产品'}` }}，配件无需查找一键购买</view
                    >
                    <CustomButton
                        :text="`${products.length === 0 ? '立即注册' : '查看配件'}`"
                        size="12"
                        :customStyle="{
                            width: '132rpx',
                            height: '54rpx',
                            backgroundColor: '#121212',
                            borderRadius: '235rpx',
                            marginRight: '32rpx',
                            fontweight: '500',
                            color: '#ffffff',
                        }"
                    ></CustomButton>
                </view>
            </view>
            <custom-toast ref="customToast" />
            <!-- #ifdef MP-WEIXIN -->
            <privacy />
            <!-- #endif -->
        </view>
        <view v-else>
            <u-skeleton
                :animate="true"
                :title="false"
                :loading="true"
                rows="1"
                :rowsHeight="[70]"
                :rowsWidth="['100%']"
            ></u-skeleton>
            <view style="margin-top: 5px">
                <u-skeleton
                    :animate="true"
                    :title="false"
                    :loading="true"
                    rows="1"
                    :rowsHeight="[45]"
                    :rowsWidth="['100%']"
                ></u-skeleton>
            </view>
            <view class="flex-container">
                <view class="column right-column" v-for="items in 2" :key="items">
                    <view class="skeleton-container" v-for="item in 5" :key="item">
                        <u-skeleton
                            :animate="true"
                            :title="false"
                            :rowsHeight="[120]"
                            :loading="true"
                            rows="1"
                            :rowsWidth="['100%']"
                        ></u-skeleton>
                    </view>
                </view>
            </view>
        </view>
        <LinkGoods
            v-if="isChooseSet && goodsList.length"
            ref="linkGoods"
            :goodsList="goodsList"
            @removeGoods="removeGoodsSelected"
        />
        <GoldCoinTimeoutTips task-code="viewGoodsMoney" ref="TimeoutTips" />
        <!-- <gold-coin-timeout-tips task-code="pointsShopping" ref="TimeoutTips" /> -->
        <EarnMoneySpendTimeoutTips
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '360rpx' }"
            :task-code="taskCode"
            :fromPage="fromPage"
            :watchTime="60000"
        />
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin v-if="!isLoading" @success="onShowExecute"></WxLogin>
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { goodsList, partCate, wishGoods } from '@/http/goods';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import CustomSearch from '@/components/CustomSearch/CustomSearch.vue';
import LinkGoods from './components/LinkGoods.vue';
import GoldCoinTimeoutTips from '@/components/GoldCoinTimeoutTips/GoldCoinTimeoutTips.vue';
import { GoodsModule, IGoods, ITag } from '@/store/modules/goods';
import { AppModule } from '@/store/modules/app';
import { ServeModule } from '@/store/modules/serve';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

const TID_ZONE = '20'; // 配件专区

interface IListData {
    list: Array<
        IGoods & {
            // 券后价是否加载完成
            coupon_finish?: boolean;
            // 用于显示是否已勾选
            checked?: boolean;
        }
    >;
    pages: number;
    finished?: boolean;
    page?: number;
    isLoading?: boolean;
}

@Component({
    components: {
        CustomSearch,
        WxLogin,
        LinkGoods,
        GoldCoinTimeoutTips,
        EarnMoneySpendTimeoutTips,
    },
})
export default class Shop extends Vue {
    $refs!: {
        linkGoods: LinkGoods;
        TimeoutTips;
        EarnMoneySpendTimeoutTips;
    };

    public pageTitle: string;
    public finished: Boolean = false; // 是否加载完成
    public comparisonList: any = [];
    public timer: any = null;
    public showAction: any = false;
    public scrollIntoView: String = 'd_jump-0';
    public tabsList: Array<ITag> = [
        {
            tid: '1',
            name: '新品',
        },
        {
            tid: '2',
            name: '热销',
        },
        {
            tid: '10',
            name: '洗地机',
        },
        {
            tid: '11',
            name: '扫地机器人',
        },
        {
            tid: '12',
            name: '吸尘器',
        },
        {
            tid: '13',
            name: '吹风机',
        },
    ];

    /** hardcode-点击首页产品tab下的官方信息跳转商城对应产品的配件id的映射 */
    public productIdToPartIdMap = {
        11: 21,
        10: 20,
        12: 30,
        13: 31,
        14: 109,
        16: 69,
        17: 121,
    };

    public isLoaded: boolean = false;
    public isLoading: boolean = true;
    public isRefreshing: Boolean = false;
    public enableRefresher: Boolean = true;
    public page: number = 1;
    public current: number = 0; // 当前数据索引
    public currentTab: string = 'tab-0'; // 当前tab的id 用于定位
    public swiperCurrent: number = 0; // 当前swiper的index
    private menuHeight: 0; // 左边菜单的高度
    private menuItemHeight: number = 0; // 左边菜单item的高度
    public listData: Array<IListData> = [];
    public cateList: Array<any> = [];
    public cateList2: Array<any> = []; // 配件列表中有children的列表
    public result: any = {};
    public TID_ZONE: string = TID_ZONE;
    public flag: Boolean = false; // 箭头翻转
    public activePartZoneTab: number = 0; // 配件专区当前tab
    public tid: string = '';
    public partId: string = '';
    public isChooseSet = false;
    public goodsList: Array<IGoods> = [];
    public c_goods_val: string = '';
    public recommendedList: Array<any> = [];
    public recommendedPage: number = 1; // 推荐商品当前页码
    public recommendedFinished: boolean = false; // 推荐商品是否加载完成
    public recommendedLoading: boolean = false; // 推荐商品是否正在加载
    public fromPage: string = '';

    // 商品是否已加入心愿单
    get isGoodsWish(): boolean {
        return true;
    }

    get isPartZone(): boolean {
        const TID_ZONE = '20'; // 配件专区
        return this.tid == TID_ZONE;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get rowWidths_skeleton_coupon() {
        return [uni.upx2px(100), uni.upx2px(56)];
    }

    get rowHeight_skeleton_coupon() {
        return uni.upx2px(50);
    }

    // 已注册产品列表
    get products() {
        return ServeModule.Products;
    }

    // 增加折疊屏 - 每頁請求數
    get isFoldableDevice() {
        return Utils.isFoldableDevice();
    }

    // tabbar 切换
    onTabItemTap(item) {
        uni.$emit(Constants.TABBAR_CHANGE, item);
    }

    public taskCode: string = '';

    onLoad(options) {
        this.fromPage = options.from || '';
        this.taskCode = this.fromPage == 'goldCoins' ? 'viewGoodsGold' : 'viewGoodsMoney';
        // #ifdef MP-WEIXIN
        console.log('options', options);
        this.tid = options.tid || uni.getStorageSync('tid');
        this.c_goods_val = options.c_goods_val || uni.getStorageSync('c_goods_val');
        this.isLoading = false;
        // #endif
        // #ifdef H5
        this.tid = options.tid;
        this.c_goods_val = options.c_goods_val || '';
        this.partId = this.productIdToPartIdMap[Number(options.partId)];
        this.onShowExecute();
        // #endif
        // 增加选择相关逻辑
        const is_selected = !!Number(options.is_selected || 0);
        this.pageTitle = is_selected ? '选商品' : '商品分类';
        this.isChooseSet = is_selected;

        this.goodsList = [...GoodsModule.linkGoodsList];
    }

    onShow() {
        if (this.isLoaded) {
            // #ifdef MP-WEIXIN
            const tid = uni.getStorageSync('tid');
            if (tid && this.tid != tid) {
                this.tid = tid;
                this.init();
            }
            // #endif
        }
        setTimeout(() => {
            uni.removeStorageSync('tid');
        }, 200);
        // setTimeout(() => {
        //     this.$refs.TimeoutTips.getViewGoodsTaskStatus();
        // }, 500);
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
        Utils.reportEvent('shop_click', {});
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SHOP_BUTTON,
        });
    }

    async onShowExecute() {
        ServeModule.asyncProducts({ is_act: 0 });
        await this.getTagList();
        await this.init();
    }

    async init(reLoad = true) {
        if (this.isPartZone) {
            await this.getPartCate();
        } else {
            if (reLoad) {
                this.listData[this.current].finished = false;
                this.listData[this.current].page = 1;
                await this._getListFactory();
            }
            // 对所有数据重置选中状态
            this.resetGoodsSelected();
        }
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        await this.getGoodsList();
        this.resetGoodsSelected();
    }

    refresherrefresh() {
        if (this.isPartZone) {
            this.isRefreshing = false;
            // this.cateList.splice(0, this.cateList.length);
            // console.log('this.cateList', this.cateList);
            return;
        } else {
            this.listData[this.current].finished = false;
            this.listData[this.current].list = [];
            this.listData[this.current].page = 1;
        }
        this.isRefreshing = true;
        // this.tabLoading = true
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
            // this.tabLoading = false
        }, 0);
    }

    async getTagList() {
        try {
            await GoodsModule.asyncGetTagList();
            const _tagList = GoodsModule.tagList;
            this.listData = [];
            this.tabsList = _tagList.filter((tag) => {
                this.listData.push({ list: [], pages: 1, finished: false, page: 1 });
                return Number(tag.tid) > -1;
            });
            this.tid = this.tid || this.tabsList[0].tid;
            this.swiperCurrent = this.current = this.tabsList.findIndex((item) => item.tid === this.tid);
            this.currentTab = 'tab-' + (this.current - 1);
        } catch (e) {
            console.error('getTagList e=', e);
        }
    }

    // 配件类目列表
    async getPartCate() {
        let parts_type = '';
        let parts_category_name = '';
        const res = await partCate();
        this.cateList = res.children;
        this.cateList2 = this.cateList.filter((item) => item.children);
        this.cateList.forEach((item) => {
            if (Object.keys(this.result).length !== 0 && this.result.constructor === Object) {
                item.isShow = this.result[item.id];
            } else {
                item.isShow = false; // 添加显示展示收起唯一标识
            }
            parts_type = parts_type ? `${parts_type}、${item.name}` : parts_type;
            if (item.children) {
                item.children.map((it) => {
                    parts_category_name = parts_category_name
                        ? `${parts_category_name}、${it.name}`
                        : parts_category_name;
                });
            }
        });
        this.jump(this.cateList.findIndex((v) => v.id == this.partId));
        Utils.reportEvent('parts_type_show', { parts_type, parts_category_name });
        this.isLoaded = true;
    }

    async getGoodsList() {
        try {
            let product_id = '';
            const tid: string = this.tid;
            const index = this.current;
            const page: number = this.listData[index].page;
            const params: Object = {
                tid,
                page,
                page_size: this.isFoldableDevice ? 12 : 12,
                single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
                goods_ids: this.c_goods_val,
            };
            const { list = [], pages = 1 } = await goodsList(params);
            // 计算商品对应的券后价
            // calculateGoodPrice({
            //     goods_combines: JSON.stringify(
            //         list.map((item) => {
            //             return {
            //                 gid: item.gid,
            //                 sid: 0,
            //                 num: 1,
            //                 gini_id: 0,
            //                 price: item.price,
            //             };
            //         }),
            //     ),
            // }).then((couponPriceList) => {
            //     // 将券后价赋值给商品
            //     list.forEach((item, index) => {
            //         item.coupon_price = couponPriceList[index].coupon_price;
            //         this.$set(item, 'coupon_finish', true);
            //     });
            // });
            const size = 12;
            if (page >= pages || size > list.length) {
                this.listData[index].finished = true;
                this.recommendedPage = 1;
                this.recommendedFinished = false;
                this.recommendedLoading = false;
                this.recommendedList = [];

                // // 开始加载推荐商品第一页
                await this.loadRecommendedGoods();
            }
            if (page === 1) {
                this.$set(this.listData, index, {
                    page: 1,
                    list,
                    pages,
                    finished: page >= pages,
                });
            } else {
                this.listData[index].list = this.listData[index].list.concat(list);
            }
            this.listData[index].list.map((item) => {
                product_id = product_id ? `${product_id}、${item.gid}` : product_id;
            });
            Utils.reportEvent('product_show', { product_id });
            this.listData[index].isLoading = false;
            this.isLoaded = true;
            this.listData[index].page++;
        } catch (e) {
            console.error('getGoodsList e=', e);
        }
    }

    // 点击切换头部
    async swichMenu(current, item) {
        const event = {
            2: 'hot_click',
            3: 'mixiangzuhegou_click',
            10: 'xidiji_click',
            11: 'saodiji_click',
            12: 'xichenqi_click',
            13: 'chuifengji_click',
            14: 'jingyinyitiji_click',
            20: 'parts_zone',
        };
        Utils.reportEvent(event[item.tid], {});
        if (this.tid == item.tid) return;
        this.swiperCurrent = this.current = current;
        this.tid = item.tid;
        this.currentTab = 'tab-' + (current - 1);
        // this.jump(0);
        this.$nextTick(async () => {
            if (this.isPartZone) {
                if (this.cateList.length === 0) {
                    await this.init();
                }
            } else {
                await this.init(this.listData[current].list.length === 0);
            }
        });
    }

    async swiperChange(e) {
        // e.detail.source 可能值如下：autoplay 自动播放导致swiper变化。touch 用户划动引起swiper变化。其他原因将用空字符串表示。
        if (!e.detail.source) return; // 避免和切换tab触发的change事件 导致重复调用
        const current = e.detail.current;
        this.swiperCurrent = this.current = current;
        this.tid = this.tabsList[current].tid;
        this.currentTab = 'tab-' + (current - 1);
        this.$nextTick(async () => {
            if (this.isPartZone) {
                if (this.cateList.length === 0) {
                    await this.init();
                }
            } else {
                await this.init(this.listData[current].list.length === 0);
            }
        });
    }

    handleWishList(isWish: number, gid: string) {
        // opt: add / remove
        const opt = isWish ? 'remove' : 'add';
        wishGoods({ goods_id: gid, opt })
            .then(() => {
                // 本地更新状态：直接修改当前列表中目标商品的 is_wish 状态，避免重新拉取列表
                const target = this.listData[this.current].list.find((item) => item.gid === gid);
                if (target) {
                    this.$set(target, 'is_wish', opt === 'add' ? 1 : 0);
                }
            })
            .catch((err) => {
                console.error(err);
            });
    }

    swiperFinish(e) {
        this.enableRefresher = true;
    }

    swiperTransition(e) {
        // console.log('swiperTransition', e);
        this.enableRefresher = false;
    }

    // 预售/自定义 不展示券后价
    showCouponPrice(item: any) {
        return item.is_presale === 0 && item.is_ini === 0;
    }

    gotoGdetailPage(gid: string) {
        console.log('不该跳啊');
        Utils.reportEvent('product_click', { product_id: gid });
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${gid}&from=${this.fromPage}`,
        });
    }

    onreachBottom() {
        console.log(this.listData[this.current].finished);
        if (this.listData[this.current].finished) {
            // 加载推荐商品
            if (!this.recommendedFinished && !this.recommendedLoading) {
                this.loadRecommendedGoods();
            }
            return;
        }

        if (this.listData[this.current].finished) return;
        if (this.listData[this.current].isLoading) return;
        this.listData[this.current].isLoading = true;
        this._getListFactory();
    }

    // 字体样式动态调整
    letterSpacing(index) {
        const name = this.tabsList[index] && this.tabsList[index].name;
        if (name && name.length < 5) {
            return 'tab-item-ls';
        }
        return '';
    }

    async loadRecommendedGoods() {
        try {
            this.recommendedLoading = true;
            // const recommendedId = -1; // 推荐商品的tid
            const recommendedParams = {
                tid: -1,
                page: this.recommendedPage,
                page_size: 12,
                single: 1,
            };

            const { list: recommendedList = [] } = await goodsList(recommendedParams);

            // 如果是第一页，直接赋值；否则追加
            if (this.recommendedPage === 1) {
                this.recommendedList = recommendedList;
            } else {
                this.recommendedList = this.recommendedList.concat(recommendedList);
            }
            // 更新分页状态
            this.recommendedPage++;

            this.recommendedLoading = false;
        } catch (error) {
            console.error('加载推荐商品失败:', error);
            this.recommendedLoading = false;
        }
    }

    getElRect(elClass, dataVal = null) {
        const query = uni.createSelectorQuery().in(this);
        query
            .select('.' + elClass)
            .fields(
                {
                    size: true,
                },
                (res) => {
                    // 如果节点尚未生成，res值为null，循环调用执行
                    if (!res) {
                        setTimeout(() => {
                            this.getElRect(elClass);
                        }, 10);
                        return;
                    }
                    if (dataVal) {
                        this[dataVal] = res.height;
                    }
                },
            )
            .exec();
    }

    toProductRegister() {
        Utils.reportEvent('product_register_click', {
            product_register_status: this.products.length ? '已注册' : '未注册',
        });
        uni.navigateTo({
            url: `/pagesC/serve/serve`,
        });
    }

    // 展开与收起
    moreShow(itemIsShow, index) {
        this.cateList[index].isShow = !itemIsShow;
        this.$forceUpdate();
        const result = this.handleValue(this.cateList);
        this.result = result;
    }

    // 绑定
    handleValue(data) {
        const result = {};

        data.forEach((node) => {
            const id = node.id;
            const isShow = node.isShow;
            result[id] = isShow;
        });

        return result;
    }

    toPartsCate(c_id, name1, name2) {
        Utils.reportEvent('parts_type_click', { parts_type: name1, parts_category_name: name2 });
        uni.navigateTo({
            url: `/pagesA/partsCate/partsCate?id=${c_id}&name1=${name1}&name2=${name2}`,
        });
    }

    flagClick() {
        let parts_type = '';
        this.cateList.map((item) => {
            parts_type = parts_type ? `${parts_type}、${item.name}` : parts_type;
        });
        if (!this.flag) {
            Utils.reportEvent('parts_more', { parts_type });
        } else {
            Utils.reportEvent('parts_less', { parts_type });
        }
        this.flag = !this.flag;
    }

    overlayClick() {
        this.flag = false;
    }

    // 配件专区锚点滑动
    jump(index, item: any = {}) {
        this.activePartZoneTab = index;
        this.flag = false;
        this.$nextTick(() => {
            this.scrollIntoView = 'd_jump-' + index;
        });
        // this.scrollIntoView = 'd_jump-' + index;
    }

    _navigateTo() {
        uni.navigateTo({
            url: '/pagesA/search/search?search_type=' + 1,
        });
        Utils.reportEvent('search_click', {});
    }

    toggleSelected(goods: IGoods, isSelected: boolean) {
        if (isSelected) {
            if (this.goodsList.length >= 18) {
                Utils.Toast('至多挂载18个商品');
                return;
            }
            this.goodsList.push(goods);
        } else {
            this.goodsList = this.goodsList.filter((selectedGoods) => selectedGoods.gid !== goods.gid);
        }
        this.$set(goods, 'checked', isSelected);
    }

    /**
     * @description 该函数的执行时机
     * 1. 滚动请求加载列表新数据时
     * 2. 切换顶部 Tab 时
     */
    resetGoodsSelected() {
        const resetGoodsSelected = (list: Array<IGoods>) => {
            list.forEach((goods) => {
                this.$set(goods, 'checked', false);
                this.goodsList.forEach((selectedGoods) => {
                    if (selectedGoods.gid === goods.gid) {
                        this.$set(goods, 'checked', true);
                    }
                });
            });
        };
        this.listData.forEach(({ list }) => resetGoodsSelected(list));
    }

    removeGoodsSelected(goods: IGoods) {
        this.goodsList = this.goodsList.filter((selectedGoods) => selectedGoods.gid !== goods.gid);
        this.resetGoodsSelected();
    }

    onUnload() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }
}
</script>

<style lang="scss" scoped>
@import './shop.scss';
</style>
