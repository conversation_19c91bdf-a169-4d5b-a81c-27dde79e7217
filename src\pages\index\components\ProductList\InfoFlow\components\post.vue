<template>
     <view class="class-postAct-item">
        <view class="class-item-top">
            <!-- #ifdef H5 -->
             <!-- 视频按钮 -->
             <block v-if="Item.type == 1">
                <view class="contentType"></view>
             </block>
             <!-- 直播按钮 -->
            <block v-if="Item.type == 1">
                <view class="contentTypeVideo"></view>
             </block>

            <LazyImage :src="Item.img_detail[0].img_url" class="lazyImg"> </LazyImage>
            <!-- #endif -->
        </view>
        <view class="class-item-bottom">
            <view class="name u-line-2">
                <view
                    class="text titleW"
                    >{{ Item.title }}</view>
            </view>
            <view class="postWrap">
                <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c57418ce3e5770010857.png" class="lazyImgIcon" > </image>
                <view class="content_title_text_grab_text">{{Item.subtitle}}</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Object, default: { }})
    readonly Item!: any;

    @Prop({ type: String, default: 'vertical' })
    readonly direction!: string;

    @Prop({ type: String, default: 'text' })
    readonly type!: string;
}
</script>
<style lang="scss" scoped>
.class-postAct-item {
    width: calc(calc(100vw - 48rpx)/2);
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    margin-bottom: 16rpx;
    min-height: 530rpx;
     .class-item-top {
                width: 100%;
                padding-top: 100%; // // 图片尺寸 1170*1320 高宽比 1.13
                margin: 0 auto 28rpx;
                position: relative;
                .contentType{
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c6a8cb21c57300012175.png') no-repeat;
                    background-size: cover;
                    width: 48rpx;
                    height: 48rpx;
                    z-index: 99;
                    position: absolute;
                    top: 10rpx;
                    right: 10rpx;
                }
                .contentTypeVideo{
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c6a900c3a10500012153.png') no-repeat;
                    background-size: cover;
                    width: 112rpx;
                    height: 48rpx;
                    z-index: 99;
                    position: absolute;
                    top: 10rpx;
                    right: 10rpx;
                }
                .custom_tag{
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 126rpx;
                    height: 58rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687791bc05b370230010836.png') no-repeat;
                    background-size: cover;
                    border-radius: 0 8rpx 0 0;
                    z-index: 99;
                }
                img {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 8rpx 8rpx 0 0;
                }

            }
            .class-item-bottom {
                padding: 0 20rpx 0rpx;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                flex-direction: column;
                min-height: 120rpx;
                .name {
                    position: relative;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #121212;
                    line-height: 32rpx;
                    text-align: left;
                    .titleW{
                        line-height: 40rpx;
                    }
                    .tag {
                        position: absolute;
                        left: 0;
                        top: 4rpx;
                    }
                    .brand{
                        width: 64rpx;
                        height: 32rpx;
                        // border-radius: 4px;
                        // background-color: #000000;
                        color: #B59A6E;
                        text-align: center;
                        // padding: 6rpx 8rpx;
                        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e272faa3206970149982.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;

                    }
                    .strictly-selected{
                            width: 52rpx;
                        height: 26rpx;
                        border-radius: 4px;
                        background: linear-gradient(121deg, #318AF7 13%, #28B0EE 100%, #2997E3 100%);

                        color: #fff;
                        text-align: center;
                            padding: 6rpx 8rpx;
                    }
                    .ecology{
                            width: 52rpx;
                        height: 26rpx;
                        border-radius: 4px;
                        background-color: #FFF4EF;
                        color: #FB3019;
                        text-align: center;
                            padding: 6rpx 8rpx;
                    }
                }
                .couponText {
                    box-sizing: content-box;
                    width: 100rpx;
                    // height: 36rpx;
                    margin: 16rpx 0 10rpx;
                    padding: 6rpx 0rpx 6rpx 6rpx;
                    background: linear-gradient(
                        89deg,
                        rgba(252, 46, 46, 0.09) 0%,
                        rgba(228, 43, 43, 0) 100%
                    );
                    border-radius: 4rpx 4rpx 4rpx 4rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #ee3c2f;
                    line-height: 24rpx;
                }

                .postWrap {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-family: MiSans;
                    width: 100%;
                    margin-top: 24rpx;
                     .lazyImgIcon{
                        width: 36rpx;
                        height: 36rpx;
                        object-fit: cover;
                        margin-right: 16rpx;
                    }
                    .content_title_text_grab_text{
                        font-family: MiSans;
                        font-weight: 500;

                        text-align: left;
                         white-space: nowrap;
                        /* 可选：超出容器时显示省略号 */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 22rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: 0px;
                        width: calc(calc(100vw - 148rpx)/2);
                        /* NeutralColor中性色/Gray 4 */
                        color: #777777;

                    }
                    .coupon-text {
                        margin-left: 4rpx;
                        font-family: MiSans, MiSans;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #ee3c2f;
                        line-height: 24rpx;
                    }
                }
            }
}
</style>
