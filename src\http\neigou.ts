import http from './index';

// 微笑大使详情
export const getSmileDetail = (params) => http.post('main/user-smile/info', params);

// 微笑大使绑定用户列表
export const getSmileBindUserList = (params) => http.post('main/user-smile/bind-user-list', params);

// 关闭微笑大使弹窗
export const getSmileClose = () => http.post('main/user-smile/received-tips');

// 受邀用户绑定微笑大使
export const getSmileBindUser = (params) => http.post('main/user-bind/smile-bind', params);

// 微笑大使排行榜
export const getSmileRank = (params) => http.post('main/user-smile/rank-list', params);

// 申请成为推广大使
export const getSmileApply = (params) => http.post('main/user-smile/apply', params);
