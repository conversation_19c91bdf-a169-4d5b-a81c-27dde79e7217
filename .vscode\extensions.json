{
  "recommendations": [
    // 基础必备
    "dbaeumer.vscode-eslint", // ESLint 语法检查
    "esbenp.prettier-vscode", // Prettier 格式化
    "eamodio.gitlens", // Git 历史与代码追踪
    "usernamehw.errorlens", // 直接在代码行内显示错误/警告
    "streetsidesoftware.code-spell-checker", // 拼写检查（英文/可加中文词典）
    // Vue 3 支持
    "vue.volar", // Vue 官方推荐语言支持
    "vue.vscode-typescript-vue-plugin", // Vue + TypeScript 支持（配合 Volar）
    // TypeScript / JavaScript 辅助
    "orta.vscode-jest", // 单元测试（Jest）
    "formulahendry.auto-rename-tag", // 自动重命名 HTML/Vue 标签
    "formulahendry.auto-close-tag", // 自动闭合标签
    // CSS / Tailwind 支持
    "bradlc.vscode-tailwindcss", // Tailwind CSS 提示
    "stylelint.vscode-stylelint", // CSS/SCSS/Less 校验
    "csstools.postcss", // PostCSS 支持
    // 其他增强
    "aaron-bond.better-comments", // 高亮注释（TODO/FIXME 等）
    "gruntfuggly.todo-tree" // 在树状视图管理 TODO/FIXME
  ],
  "unwantedRecommendations": [
    "octref.vetur" // 避免和 Volar 冲突
  ]
}
