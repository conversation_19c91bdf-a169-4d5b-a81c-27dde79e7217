<template>
    <view class="layout-container">
        <image v-if="isShowSearch" class="icon-search icon-left"
            src="https://wpm-cdn.dreame.tech/images/202309/64f1415fc48398041221439.png" @click="gotoShopPage" />
        <image v-if="isContrast" class="icon-left-size icon-left"
            src="https://wpm-cdn.dreame.tech/images/202304/245709-1682242764508.png" @click="gotoShopPage" />
        <view class="cart" v-if="isShowCart">
            <image class="icon icon-right" src="https://wpm-cdn.dreame.tech/images/202306/6482e886eede99783267070.png"
                @click="gotoCartPage" />
            <view class="badge" v-if="cartListLen">{{ cartListLen }}</view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { CartModule } from '@/store/modules/cart';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';

@Component({
    components: {},
})
export default class tabNavBarSlot extends Vue {
    // 是否是对比
    @Prop({ type: Boolean, default: false })
    readonly isContrast;

    @Prop({ type: Boolean, default: false })
    readonly isShowCart;

    @Prop({ type: Boolean, default: true })
    readonly isShowSearch;

    get cartListLen(): number {
        return CartModule.list.length || 0;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    gotoCartPage() {
        if (this.wxAuth && this.isPhone) {
            Utils.reportEvent('cart_click', {})
            uni.navigateTo({ url: '/pagesA/cart/cart' });
        } else {
            const target = 'gotoCartPage';
            UserModule.authFlow({ target });
        }
    }

    async gotoShopPage() {
        if (this.isContrast) {
            uni.navigateTo({ url: '/pagesA/product/comparison' });
        } else {
            Utils.reportEvent('search_click', {})
            uni.navigateTo({ url: '/pagesA/search/search?search_type=' + 1 });
        }
    }
}
</script>

<style lang="scss" scoped>
.layout-container {
    display: flex;
    align-items: center;
    margin-left: 26rpx;

    .icon {
        width: 54rpx;
        height: 54rpx;
    }

    .icon-search {
        width: 52rpx;
        height: 52rpx;
    }

    .icon-left {
        margin-right: 30rpx;
    }

    .cart {
        position: relative;
        width: 53rpx;
        height: 53rpx;

        .badge {
            position: absolute;
            top: -4rpx;
            right: -8rpx;
            width: 28rpx;
            line-height: 28rpx;
            background: $fill-color-primary-active;
            border-radius: 50%;
            text-align: center;
            font-size: 20rpx;
            color: #ffffff;
        }
    }

    .icon-left-size {
        width: 58rpx;
        height: 36rpx;
    }
}
</style>
