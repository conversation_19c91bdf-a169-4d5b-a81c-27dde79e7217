$fill-color-bglg: linear-gradient(90deg, rgba(0, 0, 0, 0.09) 0%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg2: linear-gradient(133deg, #fff2cb 0%, #eedec1 98%);
$fill-color-bglg3: linear-gradient(270deg, #ffffff 55%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg4: linear-gradient(243deg, #ee6b00 -9%, #f6a253 97%);
// 文字颜色
$text-color-more: #7d7d7d;
$top: 58rpx;
$bottm: 60rpx;
$total: $bottm + $top;

.right-box {
  width: 100%;
  flex: 1;
  height: 100%;
  background-color: #f5f5f5;

  // #ifdef H5
  // padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: calc(env(safe-area-inset-bottom) + 124rpx);
  // #endif
  -webkit-overflow-scrolling: touch;

  .page-view {
    @include flex(row, space-between, flex-start, wrap);
    padding: 0 16rpx 0;
    flex: 1;

    @media screen and (min-width: 450px) {
      padding: 16rpx 16rpx 0;
    }

    position: relative;
    -webkit-overflow-scrolling: touch;

    .class-item {
      width: calc(calc(100vw - 48rpx)/2);
      height: auto;
      background: #ffffff;
      border-radius: 24rpx;
      overflow: hidden;
      margin-bottom: 16rpx;

      .marketImg {
        height: 228rpx;
      }

      .class-item-top {
        width: 100%;
        padding-top: 100%; // // 图片尺寸 1170*1320 高宽比 1.13
        margin: 0 auto 28rpx;
        position: relative;

        .yushouImg {
          position: absolute;
          top: 0;
          left: -2prx;
          width: 150rpx;
          z-index: 99;
          height: 70rpx;
          // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887b4ebe9fdc9580010955.png');
          background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6baf24410011380.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .hotProImg {
          position: absolute;
          top: 0;
          left: -2rpx;
          width: 150rpx;
          height: 70rpx;
          // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d36c841d8200010937.png');
          background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6adab4380010532.png');

          background-repeat: no-repeat;
          background-size: 100% 100%;
          z-index: 99;
        }

        .newProImg {
          position: absolute;
          top: -0rpx;
          left: -2rpx;
          z-index: 99;
          width: 150rpx;
          height: 70rpx;
          // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d33c15417920012131.png');
          background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6a7414360010826.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .custom_tag {
          position: absolute;
          top: 0;
          right: 0;
          width: 126rpx;
          height: 58rpx;
          background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687791bc05b370230010836.png') no-repeat;
          background-size: cover;
          border-radius: 0 8rpx 0 0;
          z-index: 99;
        }

        .service-content {
          position: absolute;
          top: 20rpx;
          right: 20rpx;
          width: 42rpx;
          height: 36rpx;
          z-index: 99;

          .service1 {
            width: 100%;
            height: 100%;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687780e260d073970010659.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }

          .service1-active {
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687794fe1cfb61190010236.png');
          }
        }

        &.extend {
          padding-bottom: $total;

          img {
            top: $top;
            bottom: $bottm;
            height: calc(100% - $total);
          }

          ::v-deep image {
            top: $top;
            bottom: $bottm;
            height: calc(100% - $total);
          }
        }

        .atmosphereImg {
          position: absolute;
          top: 0;
          right: 0;
          bottom: -2rpx;
          left: 0;
          z-index: 10;
          border-radius: 8rpx 8rpx 0 0;
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        img {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 8rpx 8rpx 0 0;
        }

        ::v-deep image {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 8rpx 8rpx 0 0;
        }
      }

      .class-item-bottom {
        padding: 0 20rpx 12rpx;
        width: 100%;

        .name {
          position: relative;
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 28rpx;
          color: #121212;
          line-height: 32rpx;
          text-align: left;
          min-height: 74rpx;

          //  display: flex;
          // flex-direction: column;
          // justify-content: center;
          .titleW {
            line-height: 40rpx;
          }

          .ysIcon {
            width: 56rpx;
            height: 28rpx;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8c1015740012798.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }

          .htIcon {
            width: 56rpx;
            height: 28rpx;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8bf355730017331.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }

          .newIcon {
            width: 56rpx;
            height: 28rpx;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8c0005730011893.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }

          .tag {
            position: absolute;
            left: 0;
            top: 8rpx;
          }

          .brand {
            width: 64rpx;
            height: 32rpx;
            // border-radius: 4px;
            // background-color: #000000;
            color: #B59A6E;
            text-align: center;
            // padding: 6rpx 8rpx;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e272faa3206970149982.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;

          }

          .strictly-selected {
            width: 52rpx;
            height: 26rpx;
            border-radius: 4px;
            background: linear-gradient(121deg, #318AF7 13%, #28B0EE 100%, #2997E3 100%);

            color: #fff;
            text-align: center;
            padding: 6rpx 8rpx;
          }

          .ecology {
            width: 52rpx;
            height: 26rpx;
            border-radius: 4px;
            background-color: #FFF4EF;
            color: #FB3019;
            text-align: center;
            padding: 6rpx 8rpx;
          }
        }

        .couponText {
          box-sizing: content-box;
          width: 100rpx;
          // height: 36rpx;
          margin: 16rpx 0 10rpx;
          padding: 6rpx 0rpx 6rpx 6rpx;
          background: linear-gradient(89deg,
              rgba(252, 46, 46, 0.09) 0%,
              rgba(228, 43, 43, 0) 100%);
          border-radius: 4rpx 4rpx 4rpx 4rpx;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 20rpx;
          color: #ee3c2f;
          line-height: 24rpx;
        }

        .price {
          display: flex;
          align-items: baseline;
          margin-top: 16rpx;
          font-family: MiSans, MiSans;
          width: 100%;
          color: #FF3654;
          font-size: 28rpx;
          height: 52rpx;
          // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png');
          // background-color: #fbf0ef;
          border-radius: 32rpx;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          position: relative;

          // padding-left: 24rpx;
          .content_title_text_grab_text {
            //    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 126rpx;
            height: 52rpx;
            line-height: 52rpx;
            // position: absolute;
            // bottom: 0rpx;
            // top: -10rpx;
            // right: 0;
            background: linear-gradient(270deg, #FF1C7C 5%, #FF4242 100%);
            color: #fff;
            text-align: center;

            border-radius: 160rpx;
            font-weight: bold;
          }

          .unit {
            height: 24rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #FF3654;
            line-height: 58rpx;
          }

          .optimizePrice {
            height: 58rpx;
            font-weight: 600;
            font-size: 22rpx;
            color: #FF3654;
            line-height: 58rpx;
          }

          .originPrice {
            font-family: MiSans;
            font-size: 22rpx;
            font-weight: normal;
            line-height: normal;
            /* NeutralColor中性色/Gray 5 */
            color: #A6A6A6;
            margin-left: 8rpx;
            text-decoration: line-through;
          }

          .dot {
            height: 32rpx;
            font-family: MiSans, MiSans;
            font-weight: 500;
            font-size: 24rpx;
            color: #404040;
            line-height: 32rpx;
          }

          .primaryPrice {
            height: 32rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #a6a6a6;
            line-height: 32rpx;
            text-decoration-line: line-through;
          }

          .coupon-text {
            margin-left: 4rpx;
            font-family: MiSans, MiSans;
            font-weight: 400;
            font-size: 20rpx;
            color: #ee3c2f;
            line-height: 24rpx;
          }

          .price_skeleton {
            display: flex;

            ::v-deep .u-skeleton {
              flex: 0 0 auto;

              &:nth-child(1) {
                margin-right: 16rpx;
              }
            }
          }
        }
      }

      &:nth-child(even) {
        // transform: translateX(8rpx);
      }
    }

    /* 媒体查询适应折叠屏 */
    @media screen and (min-width: 450px) {
      .class-item {
        width: calc(calc(100vw - 32rpx - 36rpx)/4);

        &:nth-child(even) {
          transform: none;
        }

        &:not(:nth-child(4n+1)) {
          margin-left: 16rpx;
        }
      }
    }

    .list-title {
      position: fixed;
      top: 34rpx;
      right: 0;
      left: 0;
      z-index: 100;
      background: #ffffff00;

      .title-img {
        position: absolute;
        top: 0rpx;
        right: 26rpx;
        z-index: 100;
        // width: 100rpx;
        height: 38rpx;
        display: flex;
        align-items: center;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        line-height: 38rpx;
        text-align: left;

        // background: $fill-color-bglg3;
        .arrowTransform {
          transition: 0.2s;
          transform-origin: center;
        }

        .arrowTransformReturn {
          transition: 0.2s;
          transform-origin: center;
          transform: rotate(180deg);
        }
      }

      .drop-down {
        position: relative;
        top: -38rpx;
        max-height: calc(100% - 94rpx);
        overflow: hidden;
        overflow-y: auto;

        // border-radius: 0px 0px 23rpx 23rpx;
        background: #ffffff;
        transition: all 0.3s ease-in-out;

        .down-box {
          height: 88rpx;
          padding: 0 16rpx;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 32rpx;
          line-height: 88rpx;
          color: $text-color-regular;

          &.active {
            position: relative;
            font-weight: 400;
            color: #8c6533;
            text-transform: none;
            background: rgba(241, 223, 200, 0.27);
            border-radius: 4rpx 4rpx 4rpx 4rpx;

            &::after {
              content: '';
              position: absolute;
              right: 26rpx;
              top: 24rpx;
              width: 40rpx;
              height: 40rpx;
              background-image: url('https://wpm-cdn.dreame.tech/images/202411/672c6a81546803460056226.png');
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }
    }

    .group-list {
      position: relative;
      top: 0;
      z-index: 10;
      overflow-y: auto;
      height: 100%;
      // #ifdef MP-WEIXIN
      padding-bottom: 86rpx;
      // #endif
      // #ifdef H5
      padding-bottom: calc(86rpx);
      // #endif
      -webkit-overflow-scrolling: touch;

      .item {
        .item-title {
          position: sticky;
          top: -1rpx;
          z-index: 10;
          height: 92rpx;
          padding: 0 24rpx;
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 32rpx;
          color: #404040;
          line-height: 40rpx;
          background: #f5f5f5;
          display: flex;
          align-items: center;
        }

        .item-content {
          @include flex(row, flex-start, flex-start, wrap);
          position: relative;
          -webkit-overflow-scrolling: touch;
          padding: 0rpx 8rpx 0;

          .item-content-block {
            width: calc(50% - 4rpx);
            overflow: hidden;
            margin-bottom: 8rpx;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
            background: #ffffff;

            .class-content-top {
              width: 100%;
              padding-top: 113%; // 图片尺寸 1170*1320 高宽比 1.13
              overflow: hidden;
              margin: 0 auto 36rpx;
              position: relative;

              img {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
              }

              ::v-deep image {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
              }
            }

            .class-content-bottom {
              padding: 0rpx 38rpx 46rpx;

              .name {
                height: 64rpx;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 26rpx;
                line-height: 32rpx;
                text-align: left;
                color: #20252b;
              }
            }

            &:nth-child(even) {
              transform: translateX(8rpx);
            }
          }

          @media screen and (min-width: 450px) {
            .item-content-block {
              width: calc(25% - 10rpx);

              &:nth-child(even) {
                transform: none;
              }

              &:not(:nth-child(4n+1)) {
                margin-left: 12rpx;
              }
            }
          }
        }

        .more {
          @include flex(row, center, center, none);
          margin-top: 32rpx;

          .text {
            font-size: 24rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: $text-color-more;
            line-height: 46rpx;
          }

          .downArrow {
            width: 32rpx;
            height: 32rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }

          .upArrow {
            width: 32rpx;
            height: 32rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            transform: rotate(180deg);
          }
        }
      }
    }

    .epmty {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-bottom: 8rpx;
      margin-top: 258rpx;

      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #777777;
        line-height: 38rpx;
      }
    }
  }

  .no-more {
    // margin: 46rpx auto;
    font-size: 28rpx;
    color: $text-color-secondary;
    line-height: 100rpx;
    text-align: center;
  }
}

.box-type {
  height: 30rpx;
  font-size: 22rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: $brand-color-btn-text;
  line-height: 30rpx;
}
