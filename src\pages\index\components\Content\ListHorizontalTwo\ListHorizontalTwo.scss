.product-list-y {
    width: 100%;
    height: auto;
    display: grid;
    align-items: center;
    justify-content: space-between;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10rpx;
    .product-card-y {
        margin-bottom: 10rpx;
        width: calc(50vw - 20rpx);
        overflow: hidden;
        // height: 300rpx;
        background-color: #fff;
        border-radius: 40rpx;
        padding: 24rpx 20rpx 24rpx 24rpx;
        display: flex;
        position: relative;
        flex-direction: column;
        .product-image-y-container {
            width: 100%;
            // width: 282rpx !important;
            height: 252rpx !important;
            border-radius: 32rpx;
            // margin-right: 24rpx;
        }
        .product-image-y {
             width: 100%;
            // width: 282rpx !important;
            height: 252rpx !important;
            border-radius: 32rpx;
        }
        .product-info-y {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .product-name-y {
                font-family: MiSans;
                font-size: 27rpx;
                font-weight: 500;
                color: #000000;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
            // .product-price-content-y {
            //     position: absolute;
            //     bottom: 24rpx;
            //     right: 20rpx;
            // }
            .product-price-y {
                color: #f94863;
                font-weight: 500;
                position: relative;
                .product-price-y-icon {
                    position: absolute;
                    top: -30rpx;
                    left: 15rpx;
                    width: 82rpx;
                    height: 34rpx;
                    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68838664547a73460010678.png');
                    background-size: 100% 100%;
                }
                .product-price-y-text {
                    font-size: 24rpx;
                }
                .product-price-y-price {
                    font-size: 38rpx;
                }
                .product-price-y-price1 {
                    font-size: 24rpx;
                }
            }
            .original_price-y {
                font-size: 19rpx;
                color: #ff2626;
            }
            .product-button-y {
                // width: 376rpx;
                width: 100%;
                height: 44rpx;
                background: linear-gradient(270deg, #ffd0d0 19%, #ffffff 100%);
                border-radius: 2977rpx;
                margin-top: 8rpx;
                position: relative;
                .LineProgress {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    .content_title_text_content_progresss {
                        overflow: hidden;
                        width: 130rpx;
                        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c5846e25b29270011050.png')
                            no-repeat center center;
                        background-size: 100% 100%;
                        border-radius: 200rpx;
                        height: 24rpx;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .progress {
                            height: 24rpx;
                            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png')
                                no-repeat center center;
                            background-size: 100% 100%;
                            border-radius: 200rpx;
                        }
                    }
                }
                .half_price_y {
                    color: #ff0032;
                    text-align: center;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    left: 130rpx;
                    .half_price_y_text {
                        font-family: MiSans;
                        font-size: 12rpx;
                        font-weight: 500;
                    }
                    .half_price_y_price {
                        font-family: MiSans;
                        font-size: 14rpx;
                        font-weight: 500;
                    }
                }
                .qiang {
                    position: absolute;
                    bottom: -24rpx;
                    right: -30rpx;
                    width: 200rpx;
                    height: 120rpx;
                    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68838ce74a8f13050011962.png');
                    background-size: 100% 100%;
                }
            }
        }
    }
}
