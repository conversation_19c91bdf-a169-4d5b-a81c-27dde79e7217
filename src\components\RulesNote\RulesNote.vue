<template>
    <view class="rule-note">
        <view v-if="title" class="activity-rule" @click="getRule" :style="{
            position: fixed ? 'fixed' : 'absolute',
            top: top,
            right,
            background,
            color,
            'border-radius': borderRadius,
            'line-height': lineHeight,
            'font-size': fontSize,
        }">
            <text>{{ title }} </text>
            <view class="icon-right" v-if="showIcon"> <u-icon name="arrow-right" color="#ffffff" size="12"></u-icon></view>
        </view>
        <slot v-else></slot>
        <u-popup :show="showCommodityModal" v-prevent-scroll="showCommodityModal" mode="center" :round="12" zIndex="9999999999999"
            :safe-area-inset-bottom="false" catchtouchmove>
            <view class="product-content">
                <view class="header">
                    <text class="title">{{ text }}</text>
                    <view class="close" @click="close"><img class="close-icon" :src="closeIcon" />
                    </view>
                </view>
                <scroll-view scroll-y class="container">
                    <view class="content" v-html="content" v-if="isHtml"> </view>
                    <text v-else class="content" style="word-break: break-all;" :decode="true">{{ content }}</text>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component({})
export default class RulesNote extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly showCommodityModal;

    @Prop({ type: Boolean, default: false })
    readonly fixed;

    @Prop({ type: Boolean, default: true })
    readonly isHtml;

    @Prop({ type: Boolean, default: true })
    readonly showIcon;

    @Prop({ type: String, default: 'https://wpm-cdn.dreame.tech/images/202305/337787-1683259749911.png' })
    readonly closeIcon;

    @Prop({ type: String, default: '0rpx' })
    readonly right;

    @Prop({ type: String, default: '0rpx 20rpx 0rpx 30rpx' })
    readonly padding;

    @Prop({ type: String, default: '0rpx' })
    readonly top;

    @Prop({ type: String, default: '' })
    readonly text;

    @Prop({ type: String, default: '' })
    readonly title;

    @Prop({ type: String, default: 'rgba(38, 38, 38, 0.3)' })
    readonly background;

    @Prop({ type: String, default: '#ffffff' })
    readonly color;

    @Prop({ type: String, default: '38px 0px 0px 38px' })
    readonly borderRadius;

    @Prop({ type: String, default: '58rpx' })
    readonly lineHeight;

    @Prop({ type: String, default: '24rpx' })
    readonly fontSize;

    @Prop({ type: String, default: '' })
    readonly content;

    @Prop({ type: String, default: 'auto' })
    readonly width;

    @Prop({ type: String, default: 'auto' })
    readonly height;

    close() {
        this.$emit('close', false);
    }

    moveHandle() { }

    getRule() {
        this.$emit('getRule');
    }
}
</script>
<style lang="scss" scoped>
.rule-note {
    .activity-rule {
        display: flex;
        opacity: 1;
        font-weight: 400;

        .icon-right {
            display: flex;
            margin-left: 18rpx;
            margin-top: 1px;
            align-items: center;
        }
    }

    .product-content {
        width: 718rpx;
        padding: 32rpx 36rpx 32rpx 36rpx;

        .header {
            text-align: center;
            position: relative;
            margin-bottom: 38rpx;

            .close {
                position: absolute;
                z-index: 1;
                right: 0rpx;
                top: 50%;
                transform: translateY(-50%);

                .close-icon {
                    width: 46rpx;
                    height: 46rpx;
                }
            }

            .title {
                font-size: 36rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #3d3d3d;
                padding-bottom: 38rpx;
            }
        }
    }

    .container {
        max-height: 806rpx;
        overflow: auto;
        background: #ffffff;

        .content {
            width: 646rpx;
            font-size: 28rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #1d1e20;
            line-height: 46rpx;
            word-break: break-all;
        }
    }
}
</style>
