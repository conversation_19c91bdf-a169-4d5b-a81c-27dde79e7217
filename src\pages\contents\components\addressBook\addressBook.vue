<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="u-flex u-row-between" style="width: 750rpx; padding-right: 30rpx; background: #ffffff">
                    <view class="u-flex" @click="backPage">
                        <img src="@/static/goodDetail/back2.png"
                            style="width: 48rpx; height: 48rpx; margin-left: 20rpx" />
                        <view style="margin-left: 20rpx; font-size: 36rpx; font-weight: 600; color: #121212">邀请通讯录好友</view>
                    </view>
                    <view class="community-search">
                    </view>
                </view>
            </view>
            <view v-if="userList.length">
                <block>
                    <view class="swiper-box">
                        <view class="swiper-item">
                            <scroll-view class="scroll-view" scroll-y>
                                <view class="u-good-box">
                                    <view class="u-user-content" style="margin-bottom: 48rpx;" v-for="(item, index) in userList"
                                        :key="index">
                                        <view class="u-recommend-left">
                                            <view style="width: 96rpx; height: 96rpx; border-radius: 48rpx;">
                                                <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png" style="width: 96rpx; height: 96rpx; border-radius: 48rpx;" />
                                            </view>
                                            <view class="u-flex-col" style="margin-left: 32rpx; width: 320rpx;">
                                                <view style="font-size: 32rpx; color: #121212;">{{ item.name }}
                                                </view>
                                                <view style="font-size: 28rpx; color: #777777;">{{ item.phone }}</view>
                                            </view>
                                        </view>
                                        <view v-if="!item.status">
                                          <image class="class-item-radio" v-if="item.checked" @click.stop="_ => toggleSelected(index, false)" src="@/static/friend/icon_select.png" />
                                          <image class="class-item-radio" v-else @click.stop="_ => toggleSelected(index, true)" src="@/static/friend/icon_round_select.png" />
                                        </view>
                                        <view v-else class="send" @click="handleAlreadySend()">
                                          <img  src="@/static/friend/icon_no-select.png"  />
                                        </view>
                                        <!-- <view class="u-recomment-right" v-if="item.follow_status == 0"
                                            @click="handleUserFollow(item, index)">
                                            <img src="@/static/friend/icon_recomment_add.png"
                                                style="width: 32rpx; height: 32rpx;">
                                            关注
                                        </view>
                                        <view class="u-recomment-right-yes" v-if="item.follow_status == 1"
                                            @click="handleUserFollow(item, index)">
                                            已关注
                                        </view> -->
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </block>
                <view class="allSelect bottom u-flex u-row-between u-col-center">
                    <view @click="onAllSelect" class="sel-btn u-flex u-col-center u-row-center">
                        <view class="check-box">
                            <img class="class-item-radio"  v-if="allSelect"  src="@/static/friend/icon_select.png" />
                            <img class="class-item-radio"   v-else  src="@/static/friend/icon_round_select.png" />
                        </view>
                        <text>全选</text>
                    </view>
                    <view class="invite" v-if="followedUser.length" @click="handleAddUser()"><img src="@/static/friend/icon_add_user.png"/></view>
                    <view class="invite" v-if="!followedUser.length" @click="handleAddUser()"><img src="@/static/friend/icon_add_user_grey.png"/></view>
                </view>
            </view>
            <view v-else class="empty-box u-flex u-flex-col u-row-center u-col-center empty-page"
                >
                <view class="none u-flex u-col-center">
                    <image class="empty-icon" :style="{
                        width: 360 + 'rpx',
                        height: 360 + 'rpx',
                        marginBottom: 16 + 'rpx',
                    }" src="@/static/friend/icon_empty.png" />
                </view>
                <text class="title">——暂时没有找到通讯录朋友——</text>
            </view>
        </view>
          <u-modal :show="sendMsgshow"  width="638rpx" :show-confirm-button="false" :showCancelButton="true" @cancel="sendMsgshow = false" :mask-close-able="true">
            <template #default>
                <view class="modal-content">
                    发送短信邀请通讯录好友加入
                    <img src="@/static/friend/send_msg.png"  @click="handleSendMsg"/>
                </view>
            </template>
            <!-- <template #confirmButton>
               <view class="confirm-button-container">
                <view class="cancel-button" @click="handleCancel"> 取消兑换 </view>
                <view class="confirm-button" @click="handleConfirm"> 确认兑换 </view>
               </view>
            </template> -->
        </u-modal>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import { getNoRegisterPhoneList, sendSms } from '@/http/requestGo/community';

@Component({
    components: {
    },
})
 // watch，此处是监听isShow的变化
export default class addressBook extends Vue {
    // 网络状态
    get isNetworkConnect(): Boolean {
        return true;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    public scrollTop: number = 0; // 控制scroll-view滚动位置
    public flowList: any = [];
    public isLoading: Boolean = true;
    // public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成
    public isRefreshing: Boolean = false;
    // public page: number = 1;
    // public page_size: number = 10;
    // public totalPages: number = 1;
    public timer: any = null;
    public canPullDown: Boolean = true;
    public userList: any = [];
    public checked: Boolean = false;
    public allSelect: Boolean = false;
    public followedUser: any = [];
    public selected: Boolean = false;
    public formatPhoneList: any = [];
    public sendMsgshow: Boolean = false;
    @Watch('userList')
    watchUserList(list: Array<any>, oldVal: Array<any>) {
      console.log(list, 'list')
      if (list.length) {
      //  this.selected = list.some((item) => item.checked)
      //  console.log(this.selected, 'selected')
        this.followedUser = []
        this.userList.forEach(item => {
            if (item.checked && item.status === 0) {
             this.followedUser.push(item.phone)
            }
        });
        console.log(this.followedUser, 'this.followedUser')
      }
    }

    refresherrefresh() {
        // 重置分页信息，重新加载第一页
        // this.page = 1;
        // this.totalPages = 1;
        this.userList = [];
        this.isRefreshing = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
        }, 200);
        // this.$emit('refresh');
    }

    onScrollToLower() {
        // if (this.page >= this.totalPages) {
        //     return;
        // }
        this.loadMore();
    }

    loadMore() {
        // 如果正在加载中或已加载全部数据，不执行操作
        // if (this.isLoading || this.page >= this.totalPages) return;
        // this.page++;
        this.init();
    }

    onLoad(option) {
      this.formatPhoneList = JSON.parse(decodeURIComponent(option.formatPhoneList)) || [];
      if (this.formatPhoneList.length) {
        this.init()
      }
    }

    // onRefresh() {
    //     this.init();
    // }
    // 列表查询
    async init() {
        // if (this.page > this.totalPages) {
        //     this.isLoading = false;
        //     return;
        // }
        try {
            this.isLoading = true;
            const res = await getNoRegisterPhoneList({ contacts_user_info: this.formatPhoneList });
            // const res = await getNoRegisterPhoneList({ contacts_user_info: [{ phone: '19345678909', name: '张232' }, { phone: '17923412345', name: '张232' }, { phone: '16923412345', name: '张232' }, { phone: '18361373360', name: '张232' }] });
            if (res.success) {
                const { list = [] } = res.data;
                this.userList = list
            }
            this.isLoading = false;
        } catch (e) {
            this.isLoading = false;
            console.error('getWlist err=', e);
        }
    }

    get userInfo() {
        return UserModule.userInfo;
    }

    backPage() {
      Utils.goBack();
    }

    // 发送消息
    async handleSendMsg () {
      const res = await sendSms({ phone: this.followedUser });
      if (res.success) {
        Utils.Toast('已发送');
        this.init()
        this.sendMsgshow = false
      }
    }

    toggleSelected(index, isSelected: boolean) {
      console.log(isSelected, 'isSelected')
      this.userList.forEach((element, i) => {
        if (index === i) {
          if (isSelected) {
           this.$set(element, 'checked', isSelected)
          } else if (!isSelected && element.checked) {
            this.$delete(element, 'checked')
          }
          console.log(index, element, 'element')
        }
      })
      console.log(this.userList, 'elementqq')
    }

    // 全选
    onAllSelect() {
        this.allSelect = !this.allSelect
        this.userList.forEach(item => {
           if (this.allSelect) {
              this.$set(item, 'checked', this.allSelect)
           } else {
              this.$delete(item, 'checked')
           }
        })
    }

    // 添加联系人
    handleAddUser() {
      if (!this.followedUser.length) {
        return Utils.Toast('请至少选择一个好友邀请！')
      }
      if (this.followedUser.length > 20) {
        return Utils.Toast('最多可以选择20个好友发送短信')
      }
      this.sendMsgshow = true
    }

    handleAlreadySend () {
      Utils.Toast('用户已发送，不能重复发送')
    }
}
</script>

<style lang="scss" scoped>
@import './addressBook.scss'
</style>
