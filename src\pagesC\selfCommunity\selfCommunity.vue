<template>
    <view class="community_container"
        :style="{ 'padding-top': pagePaddingContent + 'rpx', 'padding-bottom': `${pagePaddingBottom}rpx` }">
        <view class="header">
            <custom-bar2 :title="bar_title">
                <!-- #ifdef H5 -->
                <template #moreBtn>
                    <view v-if="block_status !== 1 && is_cancel === 0" class="more_btn" style="margin-right: 30rpx"
                        @click="handleMore">
                        <image src="https://wpm-cdn.dreame.tech/images/202501/932366-1736236716285.png"></image>
                    </view>
                </template>
                <!-- #endif -->
            </custom-bar2>
        </view>
        <view v-if="isUserInfoLoading" class="user-info-loading" style="background-color: #fff">
            <u-skeleton :animate="true" :title="false" :loading="true" rows="2" :rowsHeight="[85, 65]"
                :rowsWidth="['100%', '100%']"></u-skeleton>
        </view>
        <view v-show="!isUserInfoLoading" class="user-info">
            <view class="u-flex u-row-left u-col-center">
                <view class="avatar u-skeleton-circle u-flex u-col-center u-row-center">
                    <image v-if="is_cancel !== 0" class="image"
                        src="https://wpm-cdn.dreame.tech/images/202307/293492-1689220106815.png" mode="aspectFill">
                    </image>

                    <image v-else-if="userSelfInfo.avatar" class="image" :src="userSelfInfo.avatar"></image>
                    <image v-else class="image" src="https://wpm-cdn.dreame.tech/images/202301/185295-1673921873152.png"
                        mode="aspectFill"></image>
                    <image v-if="userSelfInfo.user_avatar_icon"
                        style="width: 48rpx;height: 48rpx;position: absolute;bottom: 0;right: 0;"
                        :src="userSelfInfo.user_avatar_icon"
                        mode="aspectFill"></image>
                </view>
                <view class="nick u-flex u-row-between u-col-center">
                    <view>
                        <!-- <view v-if="userInfo.phone"> -->
                        <view class="nick_top u-flex">
                            <text class="text u-line-1">{{
                                is_cancel === 0
                                    ? userSelfInfo.nick || userSelfInfo.nick_name || '暂无昵称'
                                    : '用户已注销'
                            }}</text>
                            <view style="margin-bottom: 11rpx;display: flex;align-items: center;">
                                <img v-if="isCEO" style="margin-left: 10rpx;width: 138rpx;height: 36rpx"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png"
                                    alt="" />
                                <img v-else-if="userSelfInfo.is_employee == 1"
                                    style="margin-left: 10rpx;width: 104rpx;height: 36rpx"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68746f87a37d36700014575.png"
                                    alt="" />
                            </view>
                        </view>
                        <template v-if="ban_type !== 3">
                            <view class="nick_con u-flex">
                                <view class="nick_id" @click="IDCopy">
                                    <text style="margin-right: 12rpx">追觅ID:</text>{{
                                        is_cancel === 0
                                            ? creator !== -1 && creator
                                                ? userSelfInfo.uid
                                                : mineBasic.uid
                                            : '-'
                                    }}
                                </view>
                                <view v-if="is_cancel === 0 && isShowCode" @click="qrCode"><img
                                        src="@/static/friend/icon_qr_code.png"
                                        style="width: 32rpx; height: 32rpx; padding-left: 10rpx" alt="" /></view>
                            </view>
                            <view v-if="userIpInfo.province && is_cancel === 0" class="nick_ip">IP属地：{{
                                userIpInfo.province || '未知' }}</view>
                        </template>
                    </view>
                    <!-- #ifdef MP-WEIXIN -->
                    <view v-if="block_status !== 1 && is_cancel === 0" class="more_btn" @click="handleMore">
                        <image src="https://wpm-cdn.dreame.tech/images/202501/932366-1736236716285.png"></image>
                    </view>
                    <!-- #endif -->
                </view>
            </view>
            <view class="nick_bottom u-flex">
                <view class="nick_types u-flex">
                    <view v-for="(item, index) in communityList" :key="item.text" @click="handleList(index)"><text
                            class="value">{{ ban_type === 3 || is_cancel === 1 ? '-' : item.num | numFormat
                            }}</text><text class="label">{{ item.text }}</text></view>
                </view>
                <view v-if="creator && creator !== -1 && userStatusText" class="follow_btn u-flex"
                    :class="{ each_follow: follow_status === 2, followed: follow_status === 1 || block_status === 1 }"
                    @click="handleUserOper">
                    <img style="width: 32rpx;height: 32rpx;margin-right: 4rpx;" v-if="userStatusText === '关注'"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png" />
                    <img style="width: 32rpx;height: 32rpx;margin-right: 4rpx;" v-if="userStatusText === '已关注'"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png" />
                    <text>{{ userStatusText }}</text>
                </view>
            </view>
            <view v-if="
                (creator !== -1 && userSelfInfo.report_num >= 2 && is_cancel === 0 && ban_type !== 30) ||
                (ban_type === 30 && (this.creator === -1 || !this.creator))
            " class="warn_info">
                <image v-if="![3, 30].includes(ban_type) || creator === -1 || !creator"
                    src="https://wpm-cdn.dreame.tech/images/202501/403207-1736326727517.png"></image>
                <text v-if="![3, 30].includes(ban_type)">该用户账号近期多次被举报，可能面临封禁风险。请注意隐私安全，谨慎处理相关信息。</text>
                <template v-else-if="creator === -1 || !creator">
                    <view class="warn_info_text_more">
                        <text>账号因违反社区规定</text>
                        <text>您的账号将被封禁到 {{ formatDate(this.ban_end_at) }}</text>
                    </view>
                </template>
            </view>
        </view>
        <view class="u-tabs-box" :style="{ top: pagePaddingTop + 'rpx' }">
            <tabs-swiper activeColor="#121212" :showBar="true" inactiveColor="#777777" ref="tabs" :list="tabList"
                :current="current" @change="tabsChange" :is-scroll="false" customStyle="justify-content: center"
                :customTabStyle="{ width: '260rpx', flex: 'none' }" font-size="32" bg-color="#fff" height="92"
                bottom="12rpx" :barWidth="46" :barHeight="6"
                :backgroundImage="'https://wpm-cdn.dreame.tech/images/202501/515835-1735883219511.png'"></tabs-swiper>
        </view>
        <view v-if="abnormalStatus" class="box_abnormal_state">
            <image src="https://wpm-cdn.dreame.tech/images/202501/700717-1736407575627.png"></image>
            <text>{{ userStatusTip }}</text>
        </view>
        <swiper v-else class="swiper-box" :current="swiperCurrent" @change="swiperChange"
            @animationfinish="animationfinish">
            <block v-for="(item, index) in tabList" :key="index">
                <swiper-item class="swiper-item swiper-box-has-data">
                    <scroll-view scroll-y class="swiper-item-view" :scroll-top="scrollTop" @scroll="handleScroll"
                        refresher-background="#fff" :refresher-enabled="canPullDown" :upper-threshold="0"
                        :refresher-triggered="isRefreshing" @refresherrefresh="refresherrefresh"
                        @scrolltolower="onreachBottom" @touchstart="touchStart" @touchmove="touchMove"
                        @touchend="touchEnd">
                        <view v-if="listData[index] && listData[index].isLoading" class="u-flex u-col-center"
                            style="background-color: #fff">
                            <u-skeleton :animate="true" :title="false" :loading="true" rows="2" :rowsHeight="[200, 250]"
                                :rowsWidth="['95%', '95%']"></u-skeleton>
                            <u-skeleton :animate="true" :title="false" :loading="true" rows="2" :rowsHeight="[250, 200]"
                                :rowsWidth="['95%', '95%']"></u-skeleton>
                        </view>
                        <view class="page-box" v-else-if="
                            listData && listData[index] && listData[index].list && listData[index].list.length > 0
                        ">
                            <view class="my-waterfall">
                                <my-Waterfall v-if="!isChangeTab" :key="listData[index].isLoading" :finished="finished"
                                    :creatorSelf="index === 0 && (creator === -1 || !creator)"
                                    v-model="listData[index].list" @change="viewDetail" ref="uWaterfall"
                                    paddingBottom="70rpx" @praise="onPraise"></my-Waterfall>
                            </view>
                        </view>
                        <view class="empty_node u-flex-col u-col-center"
                            v-else-if="listData[index] && !listData[index].isLoading">
                            <view class="empty_text">暂无{{ emptyText }}的作品</view>
                        </view>
                    </scroll-view>
                </swiper-item>
            </block>
        </swiper>
        <!-- 举报弹窗 -->
        <BottomOperatePopup :open="operatePopupOpen" :reportParams="reportParams" :showBlock="block_status != 1"
            @close="handleOperateClose" @handleReport="handleReport" @handlePeopleBlock="handlePeopleBlock"
            @handleBlackView="handleBlackView" cancelText="取消" />
        <!-- 用户取消关注拉黑 -->
        <CustomModal :show="operContentOpen" width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;" :title="operContent.titie"
            :content="operContent.tip" :confirmText="operContent.confirmText" :cancelText="operContent.cancelText"
            showCancelButton @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })">
        </CustomModal>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
// import _ from 'lodash';
import { Component, Vue } from 'vue-property-decorator';
import { IOrderList } from '@/store/interface/order';
import Utils from '@/common/Utils';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import { AppModule } from '@/store/modules/app';
import MyWaterfall from '@/pages/contents/components/MyWaterfall.vue';
import {
    getFavList,
    getMyStatistics,
    getOhterFavList,
    getOtherPraiseList,
    getOtherPublishList,
    getOtherStatistics,
    getPraiseList,
    getPublishList,
    getUserIP,
    getUserRela,
    postBlock,
    postCancelBlock,
    postDraftNum,
    userFollow,
    userFollowDelete,
    userOtherDetail,
} from '@/http/requestGo/community';
import { UserInfo, UserModule } from '@/store/modules/user';
import BottomOperatePopup, { ReportParams } from './components/BottomOperatePopup.vue';
import { mineInfo } from '@/http/mine';
import { MineBasicInfo, VIPModule } from '@/store/modules/vip';
export interface OperContent {
    type: 'follow' | 'block' | '';
    titie?: string;
    tip?: '对方将无法查看您的作品，也无法与你互动。对方不会收到拉黑的通知。' | '确定不再关注该用户' | '';
    confirmText?: '确定' | '不再关注' | '';
    cancelText?: '取消' | '不保存' | '';
}

@Component({
    components: {
        TabsSwiper,
        MyWaterfall,
        BottomOperatePopup,
    },
    filters: {
        numFormat(click) {
            if (click === '-') return click;
            else if (Number(click) < 1000) {
                return click;
            } else {
                const item = (Number(click) / 1000).toFixed(1);
                return item + 'k';
            }
        },
    },
})
export default class order extends Vue {
    $refs!: {
        tabs;
    };

    // 举报和举报弹窗
    public reportParams: Partial<ReportParams> = {};
    public operatePopupOpen: boolean = false;
    public deleteModalOpen: boolean = false;
    public isRefreshing: boolean = false;
    public timer: any = null;
    public ban_type: number = 0; // 账号封禁状态 3
    public ban_end_at: number = 0; // 账号封禁时间
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: OperContent = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    private orderData: {
        list: Array<IOrderList | any>;
        pages: number;
    };

    public tabList: Array<any> = [{ name: '动态' }, { name: '收藏' }, { name: '赞过' }];
    public communityList = [
        {
            text: '关注',
            num: 0,
        },
        {
            text: '粉丝',
            num: 0,
        },
        {
            text: '获赞',
            num: 0,
        },
    ];

    public isChangeTab: boolean = false;
    public isLoading: boolean = false;
    public isUserInfoLoading: boolean = false;
    public listData: any = [];
    public current: number = 0;
    public hasPer: number = 0;
    public creator: number = -1;
    public startX: any = 0;
    public startY: any = 0;
    public canPullDown: boolean = true;
    public isLeftMove: boolean = true;
    public swiperCurrent: number = 0;
    public scrollTop: number = 0;
    public old: any = { scrollTop: 0 };
    public finished: Boolean = false; // 是否加载完成
    public page: number = 0; // 由于分页一般页面要+1，所以为了方便分页查询，这里初始设置页码为0
    public page_size: number = 6;
    public showReceiptPop: Boolean = false; // 展示确认收货弹窗
    public showOcancelPop: Boolean = false; // 展示取消订单弹窗
    public orderCdata: Object = { type: 1, order_no: '', deposit_order_no: '' }; // 取消订单信息
    public receiptData: Object = null; // 确认收货数据
    public barStyle: Object = { bottom: '6rpx', backgroundColor: '#7473C5' };
    public flowList: any = [];
    private showLoading: boolean = false; // 加载toast
    public content_id: string = '';
    public bar_title: string = '';
    public last_content_id: string = '';
    public block_status: number = 0; // 拉黑状态 0 未拉黑 1 已拉黑 2 被拉黑
    public follow_status: number = 0; // 关注状态 1 已关注 2 互相关注
    public is_cancel: number = 0; // 注销状态 0 未注销 1 已注销
    public userSelfInfo: any = {}; // 用户信息
    public userIpInfo: any = {}; // 用户ip信息
    public isShowCode: boolean = false; // 展示用户个人信息

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get pagePaddingBottom(): number {
        return AppModule.pagePaddingBottom;
    }

    get pagePaddingContent(): number {
        return Number(AppModule.pagePaddingTop);
    }

    get swiperBoxStyle(): Object {
        return { height: `100vh` };
    }

    get emptyText(): String {
        return ['发布', '收藏', '赞过'][this.swiperCurrent];
    }

    get userInfo(): UserInfo {
        return UserModule.userInfo;
    }

    get mineBasic(): MineBasicInfo {
        return VIPModule.basicInfo;
    }

    get userStatusText(): string {
        if (this.block_status === 2 || this.ban_type === 3 || this.is_cancel === 1) {
            return '';
        } else if (this.block_status === 1) {
            return '解除拉黑';
        } else if (this.follow_status === 1) {
            return '已关注';
        } else if (this.follow_status === 2) {
            return '互相关注';
        } else if (this.block_status === 0 && this.follow_status === 0) {
            return '关注';
        }
        return '';
    }

    get userStatusTip(): string {
        if (this.is_cancel === 1) {
            return '作者已注销，当前内容无法展示';
        } else if (this.block_status === 1) {
            return '您已拉黑对方，无法查看Ta的内容';
        } else if (this.block_status === 2) {
            return '暂无权限查看Ta的内容';
        } else if (this.ban_type === 3) {
            return '该用户账号被封禁，不可查看';
        }
        return '';
    }

    get abnormalStatus(): boolean {
        return this.block_status === 1 || this.block_status === 2 || this.ban_type === 3 || this.is_cancel === 1;
    }

    get isCEO(): boolean {
        const data: any = this.creator !== -1 && this.creator ? this.userSelfInfo.uid : this.mineBasic.uid;
        return data == 'YH888888'
    }

    refresherrefresh() {
        uni.$u.throttle(async () => {
            this.isRefreshing = true;
            this.last_content_id = '';
            this.isChangeTab = true;
            await this.init();
            this.isChangeTab = false;
            this.isRefreshing = false;
        }, 100);
    }

    onLoad(option) {
        console.log(option)
        if (option.creator) {
            this.creator = +option.creator;
            if (this.creator == -1) {
                this.isShowCode = true;
            }
        } else {
            this.bar_title = '我的社区';
        }
        // if (uni.getStorageSync('community_user_status') || option.hasPer == 1) {
        //     this.tabList.unshift({ name: '我发布的' });
        //     this.hasPer = 1;
        // }
        uni.$on('HANDLE_RECEIPT_POP', this.handleReceiptPop);
        uni.$on('HANDLE_ORDER_CANCEL_POP', this.handleOrderCancelPop);
        uni.$on('updateOrderList', this.handleUpdateOrderList);
        if (option.Index) {
            return this.changeIndexFormMine(option.Index);
        }
        if (option.current) {
            const current = this.tabList.length === 4 ? +option.current : +option.current - 1;
            this.current = current;
            this.tabsChange(current);
        }
        this.getFirstData();
    }

    onUnLoad() {
        uni.$off('HANDLE_RECEIPT_POP');
        uni.$off('HANDLE_ORDER_CANCEL_POP');
        uni.$off('updateOrderList');
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            if (this.showReceiptPop) {
                this.showReceiptPop = false;
            } else {
                Utils.goBack();
            }
            return true;
        }
        return false;
    }

    async onShow() {
        // await this.loadSelfInfo();
        // await this.loadCommunityInfo();
        // this.init();
    }

    async getFirstData() {
        this.isUserInfoLoading = true;
        await VIPModule.getBasicInfo();
        await this.loadSelfInfo();
        await this.loadCommunityInfo();
        this.isUserInfoLoading = false;
        this.init();
    }

    viewDetail(item) {
        const { content_id = '', type } = item;
        type !== 'draft' && (this.content_id = item.content_id);
        type !== 'draft' &&
            uni.navigateTo({
                url:
                    type === 1
                        ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                        : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}`,
            });
    }

    beforeDestroy() {
        uni.$off('HANDLE_RECEIPT_POP');
        uni.$off('HANDLE_ORDER_CANCEL_POP');
    }

    async init() {
        this.isChangeTab = true;
        this._initPageData();
        await this._getListFactory();
        this.isChangeTab = false;
    }

    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.creator });
        } else if (this.operContent.type === 'block') {
            res = await postBlock({ blocked_user_id: this.creator });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            this.loadCommunityInfo();
            if (this.operContent.type === 'block') {
                this.block_status = 1;
            }
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 1000);
    }

    handleUpdateOrderList() {
        this.page = 1;
        this.finished = false;
        this._getList();
    }

    changeIndexFormMine(index) {
        this.current = index;
        this.swiperCurrent = index;
    }

    // 初始化页面的数据
    _initPageData() {
        this.finished = false;
        this.last_content_id = '';
        this.page = 0;
        if (this.listData && this.listData.length === 0) {
            this.listData = [];
        }
    }

    onreachBottom() {
        this._getListFactory();
    }

    navToHome() {
        // #ifdef H5
        Utils.messageChannel('navigation', { type: 'home', path: 'home/h5' });
        // #endif

        // #ifdef MP-WEIXIN
        uni.switchTab({ url: '/pages/shop/shop' });
        // #endif
    }

    touchStart(e) {
        this.startX = e.touches[0].pageX;
        this.startY = e.touches[0].pageY;
    }

    touchMove(e) {
        if (!this.isLeftMove) return;
        const moveX = e.touches[0].pageX;
        const moveY = e.touches[0].pageY;
        const diffX = moveX - this.startX;
        const diffY = moveY - this.startY;

        // 如果是左右滑动，禁用下拉刷新
        if (Math.abs(diffX) > Math.abs(diffY)) {
            this.canPullDown = false;
        } else {
            this.isLeftMove = false;
        }
    }

    touchEnd() {
        // 在滑动结束后，启用下拉刷新
        this.canPullDown = true;
        this.isLeftMove = true;
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        if (this.finished) return;
        this.page++;
        await this._getList();
    }

    async loadSelfInfo() {
        // const res2 = await basicInfo()
        // console.log(res2, 'res2');
        // const fn = this.creator && this.creator !== -1 ? userOtherDetail : mineInfo;
        let res2;
        if (!(this.creator && this.creator !== -1)) {
            res2 = await mineInfo();
        }
        const fn = userOtherDetail;
        const res: any = await fn({
            other_user_id: !(this.creator && this.creator !== -1) ? +res2.user_id : +this.creator,
        });
        if (res) {
            // 处理自己账号被封禁的情况
            this.ban_type =
                res.data.ban_type === 3
                    ? !this.creator || this.creator === -1
                        ? 30
                        : res.data.ban_type
                    : res.data.ban_type;
            this.ban_end_at = res.data.ban_end_at;
        }
        this.userSelfInfo = res.data;
    }

    async loadCommunityInfo() {
        try {
            let fn: any = getMyStatistics;
            const params: any = {};
            // 他人主页
            if (this.creator !== -1 && Utils.isInApp()) {
                fn = getOtherStatistics;
                params.other_user_id = +this.creator;
                const res = await getUserRela({
                    to_user_id: +this.creator,
                });
                if (res.success) {
                    this.block_status = res.data.block_status;
                    this.follow_status = res.data.follow_status;
                    this.is_cancel = res.data.is_cancel;
                }
            }
            const res = await fn(params);
            if (res.success) {
                this.communityList[0].num = res.data.follow;
                this.communityList[1].num = res.data.fans_num;
                this.communityList[2].num = res.data.be_praise;
                // this.communityList[3 - (this.hasPer ? 0 : 1)].num = res.data.comment;
            }
            const resIp = await getUserIP({ query_user_id: +this.userSelfInfo.user_id || +this.creator || 0 });
            this.userIpInfo = resIp.data;
        } catch (err) { }
    }

    async _getList() {
        try {
            const lastSwiperIndex = this.swiperCurrent;
            const page: number = this.page;
            const page_size: number = this.page_size;
            const params: any = { page, page_size };
            if (!this.listData[this.swiperCurrent]) {
                this.listData[this.swiperCurrent] = { list: [], isLoading: true };
            }
            let PublishList = getPublishList;
            let FavList = getFavList;
            let PraiseList = getPraiseList;
            // 他人动态
            if (this.creator !== -1) {
                PublishList = getOtherPublishList;
                FavList = getOhterFavList;
                PraiseList = getOtherPraiseList;
                params.creator = this.creator;
                params.other_user_id = this.creator;
                params.last_content_id = this.last_content_id;
                params.group_by_content_id = true;
            }
            const fnList = [PublishList, FavList, PraiseList];
            // const fnList = [getPublishList, getFavList, getPraiseList, postReviewList];
            const res = await fnList[+this.swiperCurrent](params, this.showLoading);
            let { list = [], total = 1, last_content_id = '' } = res.data;
            this.orderData = { list: list || [], pages: Math.ceil(total / page_size) };
            this.last_content_id = last_content_id;
            list = list || [];
            if (this.creator !== -1 && this.swiperCurrent == 0) {
                last_content_id == '' && (this.finished = true);
                // list.length < this.page_size && (this.finished = true);
            } else if (this.page >= this.orderData.pages) {
                this.finished = true;
                // Utils.Toast('没有更多了');
            }
            const _list = this.orderData.list || [];
            if (lastSwiperIndex !== this.swiperCurrent) return;
            if (page > 1) {
                this.listData[this.swiperCurrent].list = this.listData[this.swiperCurrent].list.concat(_list);
                console.log('listData =====', this.listData[this.swiperCurrent].list);
            } else {
                if (this.swiperCurrent === 0 && this.creator === -1) {
                    const draftNums = await postDraftNum();
                    if (draftNums && draftNums.success && draftNums.data && draftNums.data.total) {
                        this.orderData.list.unshift({
                            type: 'draft',
                            content_id: 'draft',
                            num: draftNums.data.total,
                        });
                    }
                }
                this.$set(this.listData, this.swiperCurrent, this.orderData || []);
            }
            this.listData[this.swiperCurrent].isLoading = false;
        } catch (e) {
            console.error('_getList e=', e);
            this.listData[this.swiperCurrent].isLoading = false;
        }
    }

    // tabs通知swiper切换
    tabsChange(index) {
        console.log('index === ', index);
        if (this.userStatusTip) return;
        this.swiperCurrent = index;
        this.scrollGoTop();
        this.refresherrefresh()
    }

    refreshData() {
        uni.$u.throttle(async () => {
            this.finished = false;
            this.last_content_id = '';
            this.isChangeTab = true;
            await this.init();
            this.isChangeTab = false;
        }, 100);
    }

    async swiperChange(e) {
        if (this.userStatusTip) return;
        const current = e.detail.current;
        this.isChangeTab = true;
        this.swiperCurrent = current;
        if (!(this.listData[this.swiperCurrent] && this.listData[this.swiperCurrent].list)) {
            await this.init();
        }
        this.refresherrefresh()
        this.isChangeTab = false;
    }

    animationfinish(e) {
        const current: number = e.detail.current;
        (this.$refs.tabs as any).setFinishCurrent(current);
        this.swiperCurrent = current;
        this.current = current;
    }

    // 回到顶部
    scrollGoTop() {
        this.scrollTop = this.old.scrollTop;
        this.$nextTick(() => {
            this.scrollTop = 0;
        });
    }

    handleScroll(e) {
        this.old.scrollTop = e.detail.scrollTop;
    }

    // 开启/关闭确认收货弹窗
    handleReceiptPop(data) {
        if (!data.order_no) {
            this.receiptData = null;
            return;
        }
        this.receiptData = data;
        this.showReceiptPop = true;
    }

    // 开启取消订单弹窗
    handleOrderCancelPop(data) {
        this.orderCdata = data;
        this.showOcancelPop = true;
    }

    // 复制追觅ID
    IDCopy() {
        const data: any = this.creator !== -1 && this.creator ? this.userSelfInfo.uid : this.mineBasic.uid;
        // #ifdef MP-WEIXIN
        uni.setClipboardData({
            data,
            success() {
                uni.showToast({ title: '追觅ID已复制', icon: 'none' });
            },
        });
        // #endif

        // #ifdef H5
        Vue.prototype.$setClipboardData({
            data,
            success() {
                Utils.Toast('追觅ID已复制');
            },
        });
        // #endif
        Utils.reportEvent('id_copy', {});
    }

    qrCode() {
        Utils.navigateTo('/pages/contents/PersonCodeEnter');
    }

    // 用户操作
    handleUserOper() {
        uni.$u.throttle(async () => {
            let res;
            if (this.block_status === 1) {
                // console.log('取消屏蔽');
                res = await postCancelBlock({ blocked_user_id: +this.creator });
            } else if (this.follow_status === 1 || this.follow_status === 2) {
                // console.log('取消关注');
                this.operContentOpen = true;
                this.operContent = {
                    type: 'follow',
                    titie: '',
                    tip: '确定不再关注该用户',
                    confirmText: '不再关注',
                    cancelText: '取消',
                };
                // operContent
            } else if (this.follow_status === 0 || this.follow_status === 0) {
                res = await userFollow({ followed_user_id: +this.creator });
            }
            // console.log('关注');
            if (res.success) {
                Utils.Toast(
                    this.block_status === 1 ? '已移除黑名单' : this.follow_status === 0 ? '关注成功' : '操作成功',
                );
                this.loadCommunityInfo();
                if (this.block_status === 1) {
                    this.init();
                    this.block_status = 0;
                }
            }
        }, 500);
    }

    //  弹窗
    handleOperateClose() {
        this.operatePopupOpen = false;
    }

    beforeHandleDelete() {
        this.operatePopupOpen = false;
        this.deleteModalOpen = true;
    }

    handleMore() {
        this.reportParams = {
            reportType: this.creator !== -1 ? 'other' : 'self',
        };
        this.operatePopupOpen = true;
    }

    handleReport() {
        uni.navigateTo({
            url: `/pagesC/contentReport/contentReport?id=${this.creator}&reportType=4`,
        });
        this.handleOperateClose();
    }

    formatDate(date) {
        return uni.$u.timeFormat(Number(date), 'yyyy-mm-dd');
    }

    async handlePeopleBlock() {
        this.handleOperateClose();
        this.operContentOpen = true;
        this.operContent = {
            type: 'block',
            titie: `将“${this.userSelfInfo.nick || this.userSelfInfo.nick_name}”拉黑`,
            tip: '对方将无法查看您的作品，也无法与你互动。对方不会收到拉黑的通知。',
            confirmText: '确定',
            cancelText: '取消',
        };
    }

    handleBlackView() {
        Utils.navigateTo(`/pagesC/selfCommunity/userList?type=2`);
        this.handleOperateClose();
    }

    onPraise({ item, isPraise, is_praise, praiseNum }) {
        const index = [0, 1].includes(this.swiperCurrent) ? 2 : 1;
        if (!(this.listData[index] && this.listData[index].list)) return;
        this.listData[index].isLoading = true;
        setTimeout(() => {
            if (index === 2) {
                if (isPraise) {
                    this.setListData(item, +!this.swiperCurrent, {
                        isPraise,
                        praiseNum,
                        is_praise: isPraise ? is_praise : 0,
                    });
                    if (this.listData[index].list.some((i) => i.content_id === item.content_id)) {
                        this.listData[index].isLoading = false;
                        return;
                    }
                    this.$set(this.listData, index, {
                        list: [...(this.listData[index].list || []), { ...item }] || [],
                        isLoading: false,
                    });
                } else {
                    this.setListData(item, +!this.swiperCurrent, {
                        isPraise,
                        praiseNum,
                        is_praise: isPraise ? is_praise : 0,
                    });
                    const arr = this.listData[index].list.filter((i) => i.content_id !== item.content_id);
                    this.$set(this.listData, index, {
                        list: [...arr] || [],
                        isLoading: false,
                    });
                }
            } else {
                this.setListData(item, 0, { isPraise, praiseNum, is_praise: isPraise ? is_praise : 0 });
                this.setListData(item, 1, { isPraise, praiseNum, is_praise: isPraise ? is_praise : 0 });
                this.listData[index].isLoading = false;
            }
            this.listData[index].isLoading = false;
        }, 50);
    }

    setListData(item, index, { isPraise, praiseNum, is_praise }) {
        if (!this.listData[index]) return;
        const findIndex = this.listData[index].list.findIndex((i) => i.content_id === item.content_id);
        if (findIndex === -1) return;
        const teObj = {
            list: [...this.listData[index].list] || [],
            isLoading: false,
        };
        teObj.list[findIndex] = {
            ...this.listData[index].list[findIndex],
            isPraise,
            praiseNum,
            is_praise: isPraise ? is_praise : 0,
        };
        this.$set(this.listData, index, teObj);
    }

    // 跳转列表
    handleList(index) {
        if ((this.creator && this.creator !== -1) || index == 2) return;
        Utils.navigateTo(`/pagesC/selfCommunity/userList?type=${index}&creator=${this.creator}`);
    }

    onHide() {
        // this.listData = [];
    }
}
</script>

<style lang="scss" scoped>
.community_container {
    background-color: #f6f6f6;
    height: 100vh;
    display: flex;
    flex-direction: column;

    ::v-deep .u-popup {
        flex: unset !important;
    }

    .box_abnormal_state {
        display: flex;
        flex: 1;
        background-color: #fff;
        flex-direction: column;
        align-items: center;

        image {
            margin: 150rpx auto 70rpx;
            width: 192rpx;
            height: 155rpx;
        }

        text {
            font-family: MiSans, MiSans;
            font-weight: 400;
            font-size: 27rpx;
            color: #a6a6a6;
            line-height: 37rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
    }

    .swiper-box {
        flex: 1;
        box-sizing: border-box;
        padding: 20rpx;
        background-color: #fff;

        .swiper-item-view {
            height: 100%;
        }

        ::v-deep .waterfall-box {
            // width: 346rpx;
        }

        .empty_node {
            .empty_text {
                margin-top: 25vh;
                font-family: PingFang SC;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 0px;
                /* Text 文字/text_4 */
                /* 样式描述：置灰/禁用 */
                color: #a6a6a6;
            }
        }
    }

    .user-info {
        padding: 30rpx 30rpx 30rpx 46rpx;
        background-color: #fff;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-style: normal;
        text-transform: none;
        margin-bottom: 10rpx;

        .avatar {
            margin-right: 30rpx;
            width: 128rpx;
            height: 128rpx;
            border-radius: 50%;
            position: relative;

            .image {
                border-radius: 50%;
                width: 100%;
                height: 100%;
            }
        }

        .nick {
            display: flex;
            align-items: flex-start;
            flex: 1;

            .nick_top {
                .text {
                    color: #121212;
                    font-size: 35rpx;
                    line-height: 48rpx;
                    margin-bottom: 11rpx;
                }
            }

            .nick_con {
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 19rpx;
                color: #777;
                line-height: 25rpx;
                margin-bottom: 10rpx;
            }

            .nick_ip {
                @extend .nick_con;
            }
        }

        .nick_bottom {
            margin-top: 32rpx;
            justify-content: space-between;

            .nick_types {
                view {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-right: 69rpx;

                    .value {
                        font-size: 31rpx;
                        color: #121212;
                        line-height: 42rpx;
                    }

                    .label {
                        font-weight: 400;
                        font-size: 27rpx;
                        color: #777777;
                        line-height: 38rpx;
                    }
                }
            }

            .follow_btn {
                box-sizing: border-box;
                padding: 0 42rpx;
                height: 62rpx;
                border: 2rpx solid #DBC49A;
                color: #C2A271;
                border-radius: 192rpx 192rpx 192rpx 192rpx;
                display: flex;
                align-items: center;

                text {
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 27rpx;
                    color: #C2A271;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    line-height: 62rpx;
                }

                &.followed,
                &.each_follow {
                    border: 2rpx solid #E2E2E2;
                    color: #A6A6A6;

                    text {
                        color: #A6A6A6;
                    }
                }
            }
        }

        .warn_info {
            margin-top: 38rpx;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;

            image {
                width: 38rpx;
                height: 38rpx;
            }

            .warn_info_text_more {
                display: flex;
                flex-direction: column;
                margin-left: 10rpx;
            }

            text {
                flex: 1;
                margin-left: 8rpx;
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 27rpx;
                color: #bf6e12;
                line-height: 37rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }

    .more_btn {
        margin-left: 30rpx;

        image {
            width: 46rpx;
            height: 46rpx;
        }
    }
}
</style>
