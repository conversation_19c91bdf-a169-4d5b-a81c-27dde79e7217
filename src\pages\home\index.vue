<template>
    <div class="home-page">
        <Header />
        <SearchBar />
        <ClassTabs
            :tabList="tabList"
            :currentTab="currentTab"
            @selected-tab="selectedTab"
        />
        <MyDevice
            :deviceList="deviceList"
            @refresh-list="refreshList"
        />
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { deviceInfo } from './homeInterfaces';
import Header from './components/Header.vue';
import SearchBar from './components/SearchBar.vue';
import ClassTabs from './components/ClassTabs.vue';
import MyDevice from './components/MyDevice.vue';

export default {
    name: 'HomePage',
    components: {
        Header,
        SearchBar,
        ClassTabs,
        MyDevice
    },
    data() {
        return {
            currentTab: 0,
            tabList: [{ name: '我的设备', id: 0 }, { name: '推荐', id: 1 }, { name: '扫地机', id: 2 }, { name: '洗地机', id: 3 }, { name: '吸尘器', id: 4 }, { name: '吹风机', id: 5 }], // 分类
            deviceList: [{ isNoMachine: true }], // 设备列表
        };
    },
    mounted() {
        // 获取我的设备
        this.getDeviceList();
        Utils.newMessageChannel('MQTT', 'subscribe', {});
        const win = window as any;
        win.mqttMsgArrived = (dataInfo: any) => {
            const { payload = '' } = dataInfo;
            const payloadObj = JSON.parse(payload);
            const { data = {}} = payloadObj;
            const { params = [], method = '' } = data;
            if (method === 'properties_changed') {
                // 处理设备状态更新
                let onlineStatus = null;
                let batteryStatus = null;
                let currentDid = null;
                params.forEach(item => {
                    const { siid, piid, did, value } = item;
                    if (siid === 2 && piid === 1) {
                        // 设备在线状态
                        onlineStatus = value;
                        currentDid = did;
                    } else if (siid === 3 && piid === 1) {
                        batteryStatus = value;
                        currentDid = did;
                    }
                });
                if (onlineStatus !== null || batteryStatus !== null) {
                    const newList = this.deviceList.map((item: any) => {
                        if (item.did === currentDid) {
                            // 更新设备状态
                            item.latestStatus = onlineStatus !== null ? onlineStatus : item.latestStatus;
                            item.battery = batteryStatus !== null ? batteryStatus : item.battery;
                        }
                        return item;
                    });
                    this.deviceList = [...newList];
                }
            }
        }
        win.onAppPageShow = () => {
            // 页面显示时获取设备状态
            this.refreshList();
        };
    },
    methods: {
        // 选择分类
        selectedTab(id: number) {
            this.currentTab = id;
        },
        // 获取设备列表
        getDeviceList() {
            const callback = (info: deviceInfo) => {
                const { code = 0, data = '' } = info;
                if (code === 0) {
                    const list = JSON.parse(data as string);
                    if (this.deviceList.length > 1) {
                        // 如果已经有占位符，则不重复添加
                        const newDeviceList = [...this.deviceList];
                        list.forEach((item: any) => {
                            for (let i = 0; i < newDeviceList.length; i++) {
                                const device = newDeviceList[i];
                                if (!device.isNoMachine) {
                                    if (device.did === item.did) {
                                        // 如果设备已存在，则更新
                                        newDeviceList[i] = item; // 按顺序更新
                                        break;
                                    } else if (i === newDeviceList.length - 1) {
                                        // 一个全新的设备，添加到首位
                                        newDeviceList.unshift(item);
                                    }
                                } else {
                                    continue; // 跳过占位符
                                }
                            }
                        });
                        this.deviceList = newDeviceList;
                    } else {
                        this.deviceList = [...list, { isNoMachine: true }]; // 添加一个无设备的占位符
                    }
                }
            };
            Utils.newMessageChannel('DEVICE', 'list', { size: 10, current: 1 }, callback);
        },
        // 刷新设备列表
        refreshList() {
            this.getDeviceList();
        },
    },
};
</script>

<style scoped>
.home-page {
    background: #FFFFFF;
}
.main-content {
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}
.device-info-card {
    width: 44vw;
}
.add-device-card {
    padding: 0rpx 16rpx;
    margin-top: 16rpx;
}
.add-device-title {
    font-size: 16rpx;
    font-weight: 500;
    color: #121212;
    line-height: 22rpx;
    margin-bottom: 16rpx;
}
.image-buttons-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.btn {
    margin-top: 30px;
}
</style>
