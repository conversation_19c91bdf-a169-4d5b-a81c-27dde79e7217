<template>
    <view @click="$emit('onTap')">
        <view class="coupon-item-layout" :style="{ width: width }">
            <view class="text_container">
                <view class="piont_number">新人大礼包&nbsp;{{ itemData.point_value }}积分</view>
                <view class="discount">可抵&nbsp;￥{{ itemData.deductible_price }}</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component({
    components: {},
})
export default class CouponItem extends Vue {
    @Prop({ type: String, default: '' })
    readonly width!: String;

    @Prop({ type: Object, default: null })
    readonly itemData;
}
</script>

<style lang="scss" scoped>
.coupon-item-layout {
    width: 100%;
    height: 231rpx;
    position: relative;
    background-image: url('https://wpm-cdn.dreame.tech/images/202409/66d51a536d9734492475371.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 24rpx 32rpx 18rpx;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    margin: 0 auto;
    .text_container {
        position: absolute;
        top: 60rpx;
        left: 42%;
        .piont_number {
            color: #8c6533;
            font-size: 32rpx;
            font-weight: 600;
            line-height: 44rpx;
            word-break: break-all;
        }
        .discount {
            margin-top: 8rpx;
            color: #777777;
            font-size: 24rpx;
            font-weight: 600;
            line-height: 34rpx;
            word-break: break-all;
        }
    }
}
</style>
