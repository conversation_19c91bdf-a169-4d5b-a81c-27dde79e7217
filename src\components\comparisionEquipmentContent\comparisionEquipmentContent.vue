<template>
    <view :class="title ? 'comparision-quipment-content' : 'comparision-quipment-content_detail'">
        <view class="body-content">
            <view>
                <view class="heard" v-if="!title">
                    <view class="type" v-for="(item, index) in normalList" :key="index">
                        <lazy-image customStyle="width: 182rpx; height: 182rpx;" :lazyLoad="true" border-radius="0"
                            :src="item.image" />
                        <view class="shop-imag">{{ item.name }}</view>
                        <view class="shop-price">￥{{ item.price }}</view>
                    </view>
                </view>
                <view class="title" v-if="title">{{ title }}</view>
                <u-gap class="gap" v-if="title" height="1" bgColor="#E2E2E2"></u-gap>
            </view>
            <view v-if="title">
                <block v-for="(item, index) in list" :key="index">
                    <view class="classify">
                        <view class="type">
                            <view class="name">{{ item.param_name || '--' }}</view>
                            <view class="value">{{ item.param_info || '--' }}</view>
                        </view>
                        <view class="type">
                            <view class="name">{{ item.param_name1 || '--' }}</view>
                            <view class="value">{{ item.param_info1 || '--' }}</view>
                        </view>
                    </view>
                </block>
            </view>
            <view class="list" v-if="!title">
                <block v-for="(item, index) in list" :key="index">
                    <view class="classify_detail">
                        <view :class="{ type_detail_first: index == 0, type_detail_last: index == list.length - 1 }"
                            class="type_detail">
                            <view class="name_detail">{{ item.param_name || '--' }}</view>
                            <view class="value_detail">{{ item.param_info || '--' }}</view>
                        </view>
                        <view :class="{ type_detail_first: index == 0, type_detail_last: index == list.length - 1 }"
                            class="type_detail">
                            <view class="name_detail">{{ item.param_name1 || '--' }}</view>
                            <view class="value_detail">{{ item.param_info1 || '--' }}</view>
                        </view>
                    </view>
                </block>
            </view>
            <view class="all-canshu" v-if="!title" @click="skipDetail">
                查看所有对比参数
                <lazy-image customStyle="width: 46rpx; height: 46rpx;transform: translateY(2rpx);" :lazyLoad="true" border-radius="0"
                    src="https://wpm-cdn.dreame.tech/images/202304/968773-1681280308496.png"></lazy-image>
            </view>
        </view>
        <view class="compare_left"></view>
        <view class="compare_right"></view>
    </view>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import '@/uni.scss'
import Utils from '@/common/Utils';
@Component({
    components: {},
})
export default class comparisionEquipmentContent extends Vue {
    @Prop({ type: Array, default: [] })
    readonly list;

    @Prop({ type: String, default: '' })
    readonly second_cate_id;

    @Prop({ type: Array, default: () => [] })
    readonly normalList;

    @Prop({ type: String, default: '' })
    readonly title;

    public hideSameItem: Boolean = true;

    onLoad() { }

    onShow() { }

    skipDetail() {
        Utils.reportEvent('compare_expand', {
        })
        const sku = [];
        this.normalList.map((item) => {
            sku.push(item.sku);
        });
        uni.navigateTo({
            url: `/pagesA/product/comparisionDetail?c_id=${this.second_cate_id}&sku=${sku.join(',')}`,
        });
    }
}
</script>
<style lang="scss" scoped>
.comparision-quipment-content {
    background-color: $uni-bg-color;
    border-radius: 16rpx;
    padding: 28rpx 38rpx calc(54rpx - 38rpx) 38rpx;

    .title {
        font-size: 28rpx;
        word-break: break-all;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: $text-color-title;
        line-height: 38rpx;
        padding-left: 6rpx;
        padding-bottom: 30rpx;
    }

    .all-canshu {
        width: 185rpx;
        height: 34rpx;
        margin-top: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #121212;
        line-height: 34rpx;
    }

    .classify:nth-child(1) {
        margin-top: 38rpx;
    }

    .classify {
        display: flex;
        justify-content: space-between;
        padding-left: 16rpx;
        padding-right: -4rpx;

        .type {
            width: 238rpx;
            max-width: 238rpx;
            flex: 1;

            .name {
                font-size: 24rpx;
                font-weight: 400;
                color: $text-color-secondary;
                line-height: 34rpx;
                margin-bottom: 16rpx;
                word-break: break-all;
            }

            .value {
                margin-bottom: 38rpx;
                font-weight: 500;
                color: $text-color-primary;
                line-height: 38rpx;
                font-size: 28rpx;
                word-break: break-all;
            }

            .shop-imag {
                font-size: 28rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: $text-color-title;
                margin-top: 22rpx;
                margin-bottom: 24rpx;
            }

            .tips {
                font-size: 24rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                width: 248rpx;
                font-weight: 400;
                color: $text-color-secondary;
                margin-bottom: 38rpx;
            }
        }
    }
}

.comparision-quipment-content_detail {
    background-color: $uni-bg-color;
    border-radius: 16rpx;
    margin: 0rpx -8rpx;
    position: relative;

    .body-content {
        position: relative;
        z-index: 3;
    }

    .all-canshu {
        margin-top: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-size: 24rpx;
        line-height: 34rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 500;
        color: $text-color-primary;
    }

    .heard {
        z-index: 3;
        display: flex;
        justify-content: space-between;
        // margin-left: 34rpx;
        // margin-right: 34rpx;
        margin: 0 calc(25vw - 30rpx - 135rpx);

        .type {
            width: 270rpx;
            height: 366rpx;
            display: flex;
            flex-direction: column;
            align-items: center;

            .shop-image {
                width: 270rpx;
                height: 270rpx;
            }

            .shop-imag {
                margin-top: 22rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 32rpx;
                color: #121212;
                line-height: 42rpx;
                text-align: center;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-all;
            }

            .shop-price {
                margin-top: 8rpx;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 24rpx;
                color: #AB8C5E;
                line-height: 32rpx;
                text-align: center;
            }
        }
    }

    .compare_left {
        width: calc(50% - 12rpx);
        height: calc(100% - 216rpx + 54rpx);
        border-radius: 24rpx;
        position: absolute;
        top: 92rpx;
        z-index: 1;
        background: $fill-color-bg-detail-compare;
    }

    .compare_right {
        width: calc(50% - 12rpx);
        height: calc(100% - 216rpx + 54rpx);
        border-radius: 24rpx;
        position: absolute;
        top: 92rpx;
        right: 0rpx;
        z-index: 1;
        background: $fill-color-bg-detail-compare;
    }

    .list {
        .classify_detail {
            display: flex;
            justify-content: space-between;

            .type_detail_last {
                border-radius: 0rpx 0rpx 24rpx 24rpx;
            }

            .type_detail_first {
                padding-top: 48rpx;
                border-radius: 24rpx 24rpx 0rpx 0rpx;
            }

            .type_detail {
                width: calc(50% - 12rpx);

                .name_detail {
                    margin-bottom: 16rpx;
                    padding-left: 24rpx;
                    padding-right: 24rpx;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 34rpx;
                    word-break: break-all;
                    color: $text-color-secondary;
                }

                .value_detail {
                    margin-bottom: 54rpx;
                    padding-left: 24rpx;
                    padding-right: 24rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 38rpx;
                    word-break: break-all;
                    color: $text-color-primary;
                }

                .shop-imag_detail {
                    font-size: 28rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: $text-color-title;
                    margin-top: 22rpx;
                    margin-bottom: 24rpx;
                }

                .tips_detail {
                    font-size: 24rpx;
                    font-family: PingFang SC-Regular, PingFang SC;
                    width: 248rpx;
                    font-weight: 400;
                    color: $text-color-secondary;
                    margin-bottom: 38rpx;
                }
            }
        }
    }
}
</style>
