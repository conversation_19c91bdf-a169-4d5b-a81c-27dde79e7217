.gold-coins-container {
    min-height: 100vh;
    background-color: #ffc987;
    position: relative;
}
.header-content {
    position: relative;
    height: 1200rpx;
    padding-right: 30rpx;
    padding-left: 16rpx;
    padding-top: var(--status-bar-height, 44px);
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ccd11f40a1280010662.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.header {
    width: 100%;
    height: 92rpx;
    position: relative;
    .left {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 46rpx;
        height: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d3315dc733840015096.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
    .title {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-family: MiSans;
        font-size: 35rpx;
        font-weight: 500;
        color: #121212;
    }
    .right {
        height: 100%;
        display: flex;
        align-items: center;

        .share {
            position: absolute;
            right: 98rpx;
            height: 46rpx;
            width: 46rpx;
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfe5be6f3d9460015941.png')
                no-repeat center center;
            background-size: 100% 100%;
        }

        .rule {
            position: absolute;
            right: 28rpx;
            height: 46rpx;
            width: 46rpx;
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfe6b935116030010946.png')
                no-repeat center center;
            background-size: 100% 100%;
        }
    }
}
.header-coin {
    width: 562rpx;
    height: 170rpx;
    position: absolute;
    left: 50%;
    top: 200rpx;
    transform: translateX(-50%);
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888db8f671ef4220010533.png')
        no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-coin-item {
        text-align: center;
        width: calc(50% - 18rpx);
        margin-top: 50rpx;
        .header-coin-item-title {
            font-family: MiSans;
            font-size: 20rpx;
            color: #000000;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .header-coin-item-value {
            font-family: MiSans;
            font-size: 56rpx;
            font-weight: 600;
            color: #ff2300;
            line-height: 1.2;
        }
    }
    .icon {
        z-index: 9;
        margin-top: 70rpx;
        width: 36rpx;
        height: 36rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ecd8b0db77240010827.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
}
.header-bottom {
    position: absolute;
    left: 0;
    top: 330rpx;
    width: 100%;
    height: 360rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e28121a201380010823.png')
        no-repeat center center;
    background-size: 100% 100%;
    .header-bottom-item,
    .earn-gold-coins {
        position: absolute;
        bottom: 0;
    }
    .header-bottom-item {
        width: 80rpx;
        height: 80rpx;
    }
    .signIn {
        left: 22rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e8434353d2760044733.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
    .invite {
        left: 135rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e855ed9a49730010461.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
    .earn-gold-coins {
        width: 260rpx;
        height: 120rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e5c8bb8217680010272.png')
            no-repeat center center;
        background-size: 100% 100%;
        left: 50%;
        transform: translateX(-50%);
        bottom: -10rpx;
    }
    .videos {
        right: 135rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e867cc7608370010324.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
    .browse {
        right: 22rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e87ad2d768640010511.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
}
.rule-popup-content {
    width: 664rpx;
    height: 824rpx;
    max-height: 824rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f035eb9e49650010648.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx;
    overflow: hidden;

    .rule-popup-header {
        padding: 94rpx 36rpx 30rpx;
        position: relative;
        text-align: center;

        .rule-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            .rule-title-decoration {
                width: 20rpx;
                height: 20rpx;

                // &.left {
                //     margin-right: 16rpx;
                //     background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                //         no-repeat center center;
                //     background-size: 100% 100%;
                //     width: 92rpx;
                //     height: 20rpx;
                // }

                // &.right {
                //     margin-left: 16rpx;
                //     background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                //         no-repeat center center;
                //     background-size: 100% 100%;
                //     width: 92rpx;
                //     height: 20rpx;
                //     transform: rotate(180deg);
                // }
            }

            .rule-title-text {
                font-size: 36rpx;
                font-weight: 600;
                color: #ffb329;
                position: relative;
            }
        }

        .rule-popup-close {
            position: absolute;
            right: 36rpx;
            top: 24rpx;
            width: 46rpx;
            height: 46rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;

            .close-icon {
                width: 46rpx;
                height: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }
    }

    .rule-container {
        padding: 0 36rpx 32rpx;

        .rule-content {
            background: #ffffff;
            border-radius: 32rpx;
            padding: 24rpx 14rpx 24rpx 0rpx;

            .rule-content-text {
                padding: 0rpx 24rpx;
                max-height: 572rpx;
                height: 620rpx;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    display: block;
                    width: 8rpx !important;
                    height: 0rpx !important;
                    opacity: 0; // 不生效
                    transition: height 2s;
                }

                &::-webkit-scrollbar-track {
                    border-radius: 10rpx !important;
                }

                &::-webkit-scrollbar-thumb {
                    background: #d8d8d8 !important;
                    border-radius: 10rpx !important;
                }
            }

            .rule-section {
                margin-bottom: 32rpx;
                font-size: 22rpx;
                color: #3d3d3d;
                line-height: 42rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .rule-section-title {
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #333333;
                    margin-bottom: 16rpx;
                    line-height: 1.4;
                }

                .rule-section-content {
                    .rule-text {
                        font-size: 28rpx;
                        color: #666666;
                        line-height: 1.6;
                        margin-bottom: 12rpx;
                        text-align: justify;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}
.task-center {
    position: absolute;
    left: 0;
    bottom: 20rpx;
    width: calc(100% - 40rpx);
    margin-left: 20rpx;
    height: 458rpx;
    background: #ffffff;
    padding: 24rpx 34rpx 56rpx;
    border-radius: 32rpx;
    .task-center-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            font-family: MiSans;
            font-size: 36rpx;
            color: #000000;
        }
        .right {
            font-family: MiSans;
            font-size: 28rpx;
            color: #727475;
        }
    }
}
.all-task-popup-content {
    width: 100%;
    height: 1200rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888f7aa9f5b26530012246.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx 32rpx 0px 0px !important;
    padding: 22rpx 24rpx;
    .all-task-popup-header {
        height: 75rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            width: 321rpx;
            height: 75rpx;
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889ea0cb497c7400012273.png')
                no-repeat center center;
            background-size: 100% 100%;
        }
        .right {
            background: rgba(255, 255, 255, 0.25);
            width: 52rpx;
            height: 52rpx;
            border-radius: 50%;
            image {
                width: 100%;
                height: 100%;
            }
        }
    }
}
.all-task-popup-body {
    height: calc(100% - 75rpx);
    overflow-y: auto;
    padding-bottom: 20rpx;
    padding-top: 40rpx;
}
.signIn-popup-content {
    width: 644rpx;
    // height: 766rpx;
    border-radius: 40rpx;
    opacity: 1;
    padding: 18rpx 32rpx 24rpx;
    background: #fff1e6;
    position: relative;
    .signIn-popup-header {
        width: 100%;
        height: 90rpx;
        position: absolute;
        left: 50%;
        top: -100rpx;
        transform: translateX(-50%);
        .title-img {
            width: 370rpx;
            height: 89rpx;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
        }
        .line-img {
            width: 260rpx;
            height: 60rpx;
            position: absolute;
            right: 140rpx;
            bottom: 18rpx;
        }
    }
}

.signIn-popup-body {
    .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 180rpx);
        grid-template-rows: repeat(2, 200rpx);
        gap: 20rpx;
        justify-content: center;

        .image-item {
            width: 180rpx;
            height: 200rpx;

            .image-bg-active,
            .image-bg {
                width: 100%;
                height: 100%;
                border-radius: 28rpx;
            }
            .image-bg-active {
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889034a443292790063671.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
            .image-bg {
                position: relative;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68890339d0faf8560010849.png')
                    no-repeat center center;
                background-size: 100% 100%;
                .title {
                    font-family: MiSans;
                    font-size: 24rpx;
                    color: #777777;
                    position: absolute;
                    left: 50%;
                    top: 16rpx;
                    transform: translateX(-50%);
                }
                .number {
                    font-family: MiSans;
                    font-size: 36rpx;
                    font-weight: 600;
                    position: absolute;
                    color: #ff2300;
                    left: 50%;
                    bottom: 16rpx;
                    transform: translateX(-50%);
                }
            }
        }
    }
}
.signIn-popup-close {
    width: 52rpx;
    height: 52rpx;
    opacity: 1;
    box-sizing: border-box;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -90rpx;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    image {
        width: 40rpx;
        height: 40rpx;
    }
}
.image-item-max {
    width: 580rpx;
    height: 200rpx;
    border-radius: 30rpx;
    opacity: 1;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
        width: 50%;
        text-align: center;
        .title {
            font-family: MiSans;
            font-size: 24rpx;
            color: #777777;
        }
        .number {
            font-family: MiSans;
            font-size: 56rpx;
            font-weight: 600;
            color: #ff2300;
        }
    }
    .right {
        width: 50%;
        text-align: center;
        .icon-image {
            width: 244rpx;
            height: 162rpx;
        }
    }
}
.signIn-popup-footer {
    width: 100%;
    display: flex;
    margin-top: 30rpx;
    justify-content: center;
    align-items: center;
    font-family: MiSans;
    font-size: 24rpx;
    color: #777777;
}
