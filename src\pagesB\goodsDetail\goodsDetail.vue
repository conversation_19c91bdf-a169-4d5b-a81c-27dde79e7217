<template>
    <view class="container" v-if="isLoading">
        <APPSHARE :link="link"></APPSHARE>
        <template v-if="isLoaded">
            <CustomBar2
                v-show="$isInApp()"
                :isHeaderTransparent="isHeaderTransparent"
                :isShowService="false"
                :backTrigger="backTrigger"
                :customBack="backTrigger ? true : false"
                @back="closeCard"
                :position="isFixedTop ? 'fixed' : backTrigger ? 'absolute' : 'fixed'"
            >
                <template v-slot:customTitle>
                    <view
                        class="title-scroll"
                        :style="{ display: isFixedTop ? 'none' : 'block' }"
                        @touchstart.prevent="onTouchStart"
                        @touchmove.prevent="onTouchMove"
                        @touchend.prevent="onTouchEnd"
                    ></view>
                </template>
                <template #moreBtn>
                    <view class="more-btn">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c6ba604ba80190010824.png"
                            class="share-icon"
                            @click.stop="handleShareClick"
                        ></image>
                        <view class="line"></view>
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c6bd6b32917340012359.png"
                            class="contact-icon"
                            @click.stop="handleContact"
                        ></image>
                    </view>
                </template>
            </CustomBar2>
            <!-- 顶部客服按钮 -->
            <!-- <view class="service-top-btn" @click="handleContact"></view> -->
            <view class="main u-flex u-flex-col">
                <view
                    class="frosted-glass"
                    :style="{
                        'backdrop-filter': `blur(${blur}rpx)`,
                        background: `rgba(255, 255, 255, ${opacity})`,
                        height: pagePaddingTop + 'rpx',
                    }"
                >
                </view>
                <!-- 轮播图 start -->
                <GoodsGallery />
                <!-- 轮播图 end -->
                <!-- 预售 -->
                <view v-if="iniData.is_presale == 1" class="presaleImg">
                    <view class="presaleLeft">
                        <view class="presaleNumber">
                            <text class="presaleTextStyle">定金</text>
                            <text class="cnyStyle">¥</text><text class="depositStyle">{{ iniData.deposit }}</text>
                            <text v-if="iniData.deposit != iniData.expand_price">
                                <text class="discount">抵</text>
                                <text class="cnyStyle">¥</text
                                ><text class="expandPrice">{{ iniData.expand_price }}</text>
                            </text>
                        </view>
                        <view class="presalePrice">
                            <text class="presaleTextStyle">预售价</text>
                            <text class="cnyStyle">¥</text><text class="presalePriceStyle">{{ iniData.price }}</text>
                        </view>
                    </view>
                    <view class="presaleRight">
                        <view class="endTime">距离结束</view>
                        <view v-if="iniData.presale_time"
                            ><u-count-down
                                :time="iniData.presale_time - currentTime > 0 ? iniData.presale_time - currentTime : 0"
                                format="DD:HH:mm:ss"
                                autoStart
                                millisecond
                                @change="onChange"
                            >
                                <view class="time" v-if="timeShow">
                                    <view class="day">{{ timeData.days }}&nbsp;天 </view>
                                    <view class="time__custom">
                                        <text class="time__custom__item">{{
                                            timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours
                                        }}</text>
                                    </view>
                                    <view class="time__doc">:</view>
                                    <view class="time__custom">
                                        <text class="time__custom__item">{{
                                            timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes
                                        }}</text>
                                    </view>
                                    <view class="time__doc">:</view>
                                    <view class="time__custom">
                                        <text class="time__custom__item">{{
                                            timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
                                        }}</text>
                                    </view>
                                </view>
                            </u-count-down></view
                        >
                    </view>
                </view>
                <!-- 国补价格栏 -->
                <view
                    class="presaleImg2"
                    v-if="
                        goodsData.discount &&
                        goodsData.discount.subsidy &&
                        Number(goodsData.discount.subsidy) > 0 &&
                        goodsData.custom_tag != '一元秒杀'
                    "
                >
                    <view class="presaleLeft">
                        <view class="presaleNumber">
                            <text class="presaleTextStyle1">¥</text>
                            <text class="presaleTextStyle2">{{
                                specsData.discount
                                    ? Number(specsData.discount.price || 0).toFixed(2)
                                    : goodsData.discount.price
                            }}</text>
                            <view class="endTime">国补到手价</view>
                        </view>
                        <view class="presalePrice">
                            <text class="presaleTextStyle">¥{{ goodsData.mprice }}</text>
                            <view class="separator"></view>
                            <text class="sale-shop">已售{{ goodsData.sales }}</text>
                        </view>
                    </view>
                    <view class="back2">
                        <image
                            class="back2Img"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686f8eb3344392140012367.png"
                            mode="widthFix"
                        ></image>
                    </view>
                    <view class="presaleRight">
                        <view class="endTime1">
                            <image
                                class="endTime1Img"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686f8894ad9c57110011221.png"
                                mode="widthFix"
                            ></image>
                        </view>
                        <view class="endTime2"
                            >立减¥{{
                                specsData.discount
                                    ? Number(specsData.discount.subsidy_price || 0).toFixed(2)
                                    : goodsData.discount.subsidy
                            }}</view
                        >
                    </view>
                </view>
                <view class="presaleImg3" v-if="goodsData.custom_tag == '一元秒杀'">
                    <view class="presaleLeft">
                        <view class="presaleNumber">
                            <text class="presaleTextStyle1">¥</text>
                            <text class="presaleTextStyle2">{{ goodsData.price }}</text>
                            <view class="endTime">秒杀价</view>
                        </view>
                        <view class="presalePrice">
                            <text class="presaleTextStyle">¥{{ goodsData.mprice }}</text>
                            <text class="sale-shop"
                                >已补贴 {{ (Number(goodsData.mprice) - Number(goodsData.price)).toFixed(2) }}元</text
                            >
                        </view>
                    </view>
                    <view class="back2">
                        <image
                            class="back2Img"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686f8eb3344392140012367.png"
                            mode="widthFix"
                        ></image>
                    </view>
                    <!-- <view class="presaleRight">
          <view class="endTime1">
            <image class="endTime1Img" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686f8894ad9c57110011221.png" mode="widthFix"></image>
          </view>
          <view class="endTime2">立减¥{{specsData.discount ? (Number(specsData.discount.subsidy_price || 0)).toFixed(2) : goodsData.discount.subsidy }}</view>
        </view> -->
                </view>
                <!-- 好友半价分享 -->
                <!-- <view class="half-price" @click="toHalfPrice">
                  <img
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68822af51b1dd1110010328.png"
                      alt=""
                  />
              </view> -->

                <view class="group-buy-list" v-if="isGroupGoods && groupBuyList.length > 0">
                    <view class="group-buy-title">
                        <text>{{ groupBuyList.length }}人正在拼，</text>
                        <text class="done">参与可立即拼成</text>
                    </view>

                    <!-- 上下滚动的团购列表 -->
                    <swiper
                        class="group-buy-swiper"
                        :vertical="true"
                        :interval="3000"
                        :duration="300"
                        :current="currentGroupSwiperIndex"
                        :circular="true"
                        :skip-hidden-item-layout="true"
                        :easing-function="'easeInOutCubic'"
                        :autoplay="true"
                        @change="onGroupSwiperChange"
                    >
                        <swiper-item v-for="(item, index) in groupBuyList" :key="index" class="group-buy-item">
                            <view class="group-buy-card" @click="handleGroupBuyClick(item)">
                                <img :src="item.member_avatars[0].avatar" class="card-avatar" mode="aspectFill" />
                                <view class="card-name">{{ item.member_avatars[0].nick_name }}</view>
                                <view class="card-time">
                                    <view class="card-time-text">拼团购即将结束：</view>
                                    <view>
                                        <u-count-down
                                            :time="item.group_end_time * 1000 - Date.now()"
                                            format="HH:mm:ss"
                                            autoStart
                                            millisecond
                                            @change="onEndTimeChange"
                                        >
                                        </u-count-down>
                                    </view>
                                </view>
                                <view class="card-btn">直接拼成</view>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>

                <!-- 商品信息 start -->
                <GoodsInfo
                    :pointsMall="pointsMall"
                    @handleSharePop="handleSharePop"
                    @showCouponHandle="showCouponHandle"
                    :isGroupGoods="isGroupGoods"
                    :specsData="specsData"
                    :iniData="iniData"
                    :paramsInfoList="paramsInfoList"
                    @updateGood="handleUpdateIniData"
                    @paramsInfoHandle="paramsInfoHandle"
                    :showReceiveCoupon="shouldShowCoupon && goodsData.is_coupons == '1'"
                />
                <!-- 商品信息 end -->

                <!-- 领取优惠卷弹窗 start -->
                <u-popup
                    :show="showCoupon"
                    v-prevent-scroll="showCoupon"
                    round="16"
                    mode="bottom"
                    :closeOnClickOverlay="false"
                    :safeAreaInsetBottom="false"
                >
                    <view class="coupon_Container">
                        <view class="coupon_title"
                            >优惠券
                            <view class="icon-close u-flex u-col-center u-row-center">
                                <image class="icon" src="@/static/close.png" @click="showCoupon = false" />
                            </view>
                        </view>
                        <u-tabs
                            :list="couponTabList"
                            lineWidth="62rpx"
                            lineHeight="21rpx"
                            @change="tabsChange"
                            :lineColor="`url(${lineColor}) 100% 100% no-repeat`"
                            :current="current"
                            lineBgSize="62rpx 21rpx"
                            :activeStyle="{
                                fontWweight: 500,
                                color: '#AB8C5E',
                                lineHeight: '38rpx',
                                transform: 'scale(1.05)',
                            }"
                            :itemStyle="{
                                width: '337rpx',
                                height: '81rpx',
                                fontSize: '26rpx',
                            }"
                        ></u-tabs>
                        <swiper
                            class="swiper-box"
                            @change="swiperChange"
                            :style="{ height: `calc(70vh - 267rpx )` }"
                            :current="current"
                            :duration="300"
                            :skip-hidden-item-layout="true"
                            :autoplay="true"
                            :easing-function="'easeInOutCubic'"
                            @animationfinish="animationfinish"
                        >
                            <block v-for="(item, index) in couponTabList" :key="index">
                                <swiper-item class="swiper-item" style="overflow: scroll" :key="index">
                                    <scroll-view scroll-y class="swiper-item-view">
                                        <view class="coupon_list">
                                            <block v-for="Coupon in couponList[index]" :key="Coupon.id">
                                                <view class="coupon-box">
                                                    <couponItem
                                                        :Coupon="Coupon"
                                                        @receiveCoupon="receiveCoupon"
                                                        @showRule="handleShowRule"
                                                    >
                                                    </couponItem>
                                                </view>
                                            </block>
                                            <view v-if="couponList[index].length === 0">
                                                <DefaultPage
                                                    :show="true"
                                                    icon="https://wpm-cdn.dreame.tech/images/202306/178397-1687834429297.png"
                                                    imgWidth="508"
                                                    imgHeight="508"
                                                    imgMbottom="0"
                                                    tipStyle="font-size:28rpx;color: rgba(29,30,32,0.4);"
                                                    tip="暂无优惠券"
                                                />
                                            </view>
                                        </view>
                                    </scroll-view>
                                </swiper-item>
                            </block>
                        </swiper>
                    </view>
                </u-popup>
                <!-- 领取优惠卷弹窗   end-->

                <!-- 预售信息 -->
                <PresaleInfo v-if="iniData.is_presale == 1" :iniData="iniData"></PresaleInfo>

                <!-- 拼团规则 start -->
                <!-- <GroupPurchaseRule :show="!!group_purchase_id"
                    @showRule="showGroupPurchase = true" /> -->
                <!-- 拼团规则   end-->

                <!-- 热销配件 团购活动商品不显示-->
                <view
                    class="hot-part"
                    v-if="tiedSaleList.length > 0 && !mainId && !pointsMall && !group_activity_id && !group_purchase_id"
                >
                    <text class="title">选择配件</text>
                    <view class="hot-part-main">
                        <view
                            class="main-item"
                            v-for="(item, index) in tiedSaleList"
                            :key="index"
                            :class="disabled ? 'goods-out-block' : ''"
                            v-show="isShow ? index < 3 : index + 1"
                            @click="gotoGdetailPage(item.gid)"
                        >
                            <view class="item">
                                <view class="img">
                                    <lazy-image
                                        customStyle="width:100%;height:100%"
                                        :src="item.cover_image"
                                    ></lazy-image>
                                </view>
                                <view class="item-right">
                                    <view class="name u-line-2">{{ item.name }}</view>
                                    <view>
                                        <view style="display: flex; justify-content: space-between">
                                            <view class="price">
                                                <text class="sign">¥</text>
                                                <text class="num">{{ item.price }}</text>
                                            </view>
                                            <view @tap.stop="handleStopClick">
                                                <number-box
                                                    :value.sync="item.num"
                                                    :min="0"
                                                    :max="item.limit_num != '0' ? item.limit_num : item.stock"
                                                    bgColor="#fff"
                                                    inputWidth="48rpx"
                                                    buttonWidth="44rpx"
                                                    buttonSize="44rpx"
                                                    fontSize="20rpx"
                                                    :integer="true"
                                                    color="#1D1E20"
                                                    @change="valChange(index, $event)"
                                                    @blur="valChange(index, $event)"
                                                >
                                                </number-box>
                                            </view>
                                        </view>
                                        <view class="separate"></view>
                                        <view class="item-bottom">
                                            <view class="ncategoryame u-line-1"
                                                >适配&nbsp;&nbsp;{{ item.machine_type }}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view :class="disabled ? 'goods-out' : ''"></view>
                        </view>
                        <view class="more" v-if="tiedSaleList.length > 3" @click="moreShow()">
                            <text>{{ isShow ? '更多' : '收起' }}</text>
                            <view :class="isShow ? 'downArrow' : 'upArrow'"> </view>
                        </view>
                    </view>
                </view>

                <!-- 服务说明 -->
                <!-- <view class="serve" v-if="!pointsMall && isPhone && goodsData.is_internal_purchase != 1">
        <view class="serve-main">
          <view class="level-peanuts">
            <lazy-image
              customStyle="width: 138rpx; height: 138rpx;"
              class="image"
              :lazyLoad="true"
              :src="urlMap[mineBasic.level.level]"
            ></lazy-image>
          </view>
          <view class="dreame">亲爱的{{ benefitList.name }}，您可享受以下权益:</view>
          <view
            class="serve-item"
            v-for="(item, index) in benefitList.benefits"
            :key="index"
            @click="popupBtn(item.serviceDescribe)"
          >
            <view class="left">
              <image class="image" :src="item.serviceIcon"></image>
              <text>{{ item.serviceTitle }}</text>
            </view>
            <view class="right-img">
              <lazy-image
                customStyle="width: 38rpx; height: 38rpx;"
                :lazyLoad="true"
                src="https://wpm-cdn.dreame.tech/images/202306/6482d92870e114623225756.png"
              ></lazy-image>
            </view>
          </view>
        </view>
      </view> -->
                <view
                    class="serve_box"
                    v-if="!pointsMall && isPhone && goodsData.is_internal_purchase != 1 && $isInApp()"
                >
                    <view>
                        <view class="level-peanuts">
                            <lazy-image
                                customStyle="width: 138rpx; height: 138rpx;"
                                class="image"
                                :lazyLoad="true"
                                :src="urlMap[mineBasic.level.level]"
                            ></lazy-image>
                        </view>
                        <view class="dreame">亲爱的{{ benefitList.name }}，您可享受以下权益:</view>
                    </view>
                    <view class="serve-item">
                        <view class="left-box"></view>
                        <view class="right-box">
                            <view
                                class="item"
                                v-for="(item, index) in benefitList.benefits"
                                :key="index"
                                @click="popupBtn(item.serviceDescribe)"
                            >
                                <view class="title"
                                    >{{ item.name }}
                                    <lazy-image
                                        customStyle="width: 38rpx; height: 38rpx;"
                                        :lazyLoad="true"
                                        src="https://wpm-cdn.dreame.tech/images/202306/6482d92870e114623225756.png"
                                    ></lazy-image>
                                </view>
                                <view class="desc">{{ item.levelBenefitDescri }}</view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 服务说明弹出框 -->
                <u-popup
                    :show="popupShow"
                    v-prevent-scroll="popupShow"
                    round="16"
                    mode="bottom"
                    :closeOnClickOverlay="false"
                    :safeAreaInsetBottom="false"
                >
                    <view class="popup">
                        <view class="u-content">
                            <u-parse
                                :content="serviceDescribe"
                                :previewImg="false"
                                :style="{ width: isFoldableDevice ? '450px' : '100%', margin: '0 auto' }"
                            />
                        </view>
                        <view class="image" @click="popupShow = false">
                            <lazy-image
                                customStyle="width: 46rpx; height: 46rpx;display: block;"
                                :lazyLoad="true"
                                src="https://wpm-cdn.dreame.tech/images/202305/337787-1683259749911.png"
                            ></lazy-image>
                        </view>
                        <view class="title">服务说明</view>
                    </view>
                </u-popup>

                <view v-if="comparasionList.length && !pointsMall" class="goods-params">
                    <view class="goods-info-contrast">
                        <text class="text">同类商品对比</text>
                    </view>
                    <view class="goods-contract-detail">
                        <comparision-equipment-content
                            :second_cate_id="second_cate_id"
                            :normalList="comparasionList"
                            :list="parameterList"
                        ></comparision-equipment-content>
                    </view>
                </view>

                <!-- 评价 -->
                <view class="goods-evaluate" v-if="goodsData.is_internal_purchase != 1">
                    <goods-evaluate
                        :evaluateList="evaluateList"
                        :evaluateTotal="evaluateTotal"
                        :pointsMall="pointsMall"
                    />
                </view>

                <view class="goods-info-title" :class="{ 'point-mall': pointsMall, 'not-point-mall': !pointsMall }">
                    <text class="text">深入了解</text>
                </view>
                <!-- 富文本 start -->
                <view
                    class="details"
                    :style="{ 'padding-bottom': toBeListed || isSoldOut || isNotSold ? '344rpx' : '294rpx' }"
                >
                    <!-- #ifdef H5 -->
                    <view v-html="goodsData.detail" v-lazy-html></view>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <u-parse
                        :content="goodsData.detail"
                        :lazyLoad="true"
                        loadingImg="https://wpm-cdn.dreame.tech/images/202307/713952-1690162496004.png"
                    ></u-parse>
                    <!-- #endif -->
                </view>
                <!-- 富文本 end -->
            </view>

            <!-- 按钮 start -->

            <view class="navigation-wrapper" v-if="!pointsMall">
                <img
                    :src="discountActivityImg"
                    class="discount-activity-tip"
                    v-if="!!discountActivityImg && showDiscountActivity"
                    @click="handleDiscountActivityClick"
                />

                <view class="navigation" v-if="activity_name !== 'small_store'">
                    <!-- 价格 -->
                    <view class="navigation-left u-flex">
                        <view class="price u-flex">
                            <text class="label" :style="{ color: isGroupGoods ? '#FF2968' : '#404040' }">{{ isGroupGoods ? '拼团价' : '合计' }}：</text>
                            <CustomAmount
                                unitRight="0"
                                :uniColor="isGroupGoods ? '#FF2968' : '#404040'"
                                unitWeight="500"
                                :color="isGroupGoods ? '#FF2968' : '#404040'"
                                :totalPrice="total"
                                :iniData="iniData"
                                :totalStyle="{
                                    'font-size': '42rpx',
                                    'line-height': '56rpx',
                                    'font-weight': 500,
                                    'align-items': 'baseline',
                                }"
                                unitSize="24"
                                unitLineHeight="32"
                            />
                        </view>
                    </view>

                    <!-- 按钮组 -->
                    <view class="navigation-right u-flex">
                        <!-- 底部去除客服显示 -->
                        <view class="service-content">
                            <view class="service1" :class="{ 'service1-active': isGoodsWish }" @click="handleWishList">
                                <view class="badge" v-if="wishTimes">{{ wishTimes }}</view>
                            </view>
                            <view class="customer-service1">我想要</view>
                        </view>
                        <view class="service-content">
                            <view
                                class="service2"
                                :class="{ 'service2-active': isGoodsRecommended }"
                                @click="handleRecommend"
                            >
                                <view class="badge" v-if="recommendTimes">{{ recommendTimes }}</view>
                            </view>
                            <view class="customer-service2">推荐</view>
                        </view>
                        <view class="car-content">
                            <view v-show="!storekeeper" class="cart" @click="gotoCartPage">
                                <view class="badge" v-if="cartListLen">{{ cartListLen }}</view>
                            </view>
                            <view v-show="!storekeeper" class="shopping-car">购物车</view>
                        </view>

                        <!-- 团购商品-未发起团购 -->
                        <block v-if="isGroupGoods">
                            <view
                                class="right-item btn-cart"
                                style="font-size: 28rpx"
                                :class="{ 'grey-out': disabled }"
                                hover-class="btn-hover"
                                @click="addCart(0)"
                            >
                                立即购买</view
                            >
                            <view
                                class="right-item btn-buy group-buy"
                                style="margin-left: 24rpx; font-size: 28rpx"
                                :class="{ 'grey-out-buy': disabled }"
                                hover-class="btn-hover"
                                @click="addCart(4)"
                            >
                                立即拼团
                                <view class="tip">{{ `享${groupRate}折` }}</view>
                            </view>
                        </block>

                        <!-- 团购商品-已发起团购 -->
                        <!-- <block v-else-if="group_purchase_id">
                      <view @click="addCart(4)">
                          <custom-button
                              :disabled="disabled"
                              customStyle="border-radius: 15rpx;padding: 0 106rpx;height:82rpx;line-height: 82rpx;font-size: 32rpx;font-weight:500"
                          >
                              立即购买</custom-button
                          >
                      </view>
                  </block> -->

                        <block v-else>
                            <view
                                v-if="!storekeeper"
                                class="right-item btn-cart"
                                :class="{ 'grey-out': disabled || isOneLink }"
                                hover-class="btn-hover"
                                @click="addCart(1)"
                            >
                                加入购物车</view
                            >
                            <view
                                v-if="iniData.is_presale == 1"
                                style="margin-left: 24rpx"
                                class="right-item-presale btn-buy"
                                @click="addCart(0)"
                                :class="{ 'grey-out-buy': disabled }"
                            >
                                <view class="pay">立即抢购</view>
                                <view class="already">已预订{{ iniData.scheduled_number_now || 0 }}</view>
                            </view>
                            <view
                                v-else
                                class="right-item btn-buy"
                                style="margin-left: 24rpx"
                                :class="{ 'grey-out-buy': disabled }"
                                hover-class="btn-hover"
                                @click="addCart(0)"
                            >
                                {{ canAchieveCoupon ? '领券购买' : '立即购买' }}
                            </view>
                        </block>
                    </view>
                </view>
                <view class="navigation-small-store" v-if="activity_name === 'small_store'">
                    <view class="navigation-small-store-left" @click="handleSmallStore" :class="{ 'grey-out-buy': disabled }">立即铺货</view>
                </view>
            </view>

            <view v-else class="navigation1">
                <view class="navigation-right u-flex">
                    <view class="service-content">
                        <view class="service" @click="handleContact"></view>
                        <view class="customer-service">客服</view>
                    </view>
                    <view @click="jumpExchange(0)">
                        <custom-button
                            :disabled="noDisabled"
                            customStyle="border-radius: 15rpx;width:454rpx;height:82rpx;font-size: 32rpx;font-weight:500"
                            >{{ goodsData.alert_info.button_msg }}</custom-button
                        >
                    </view>
                </view>
            </view>
            <view v-if="toBeListed" class="no-store1" :style="{bottom: activity_name === 'small_store' ? '194rpx' : '266rpx'}">
                <view class="no-inventory">{{ goodsData.online_time_tag }}</view>
            </view>
            <view v-else-if="isNotSold" class="no-store" :style="{bottom: activity_name === 'small_store' ? '194rpx' : '266rpx'}"
                ><u-icon size="16" color="#BF6E12" name="error-circle"></u-icon>
                <view class="no-inventory">此商品已下架, 要不要瞧瞧别的~</view>
            </view>
            <view v-else-if="isSoldOut" class="no-store" :style="{bottom: activity_name === 'small_store' ? '194rpx' : '266rpx'}">
                <u-icon size="16" color="#BF6E12" name="error-circle"></u-icon>
                <view class="no-inventory">商品暂无库存, 非常抱歉!</view>
            </view>
            <!-- 按钮 end -->
        </template>
        <template v-else>
            <view>
                <!-- <u-skeleton :animate="true" :title="false" :loading="true" rows="1" :rowsHeight="[414]"
        :rowsWidth="['100%']"></u-skeleton> -->
                <view style="padding: 32rpx 32rpx 32rpx">
                    <u-skeleton
                        :animate="true"
                        :title="false"
                        :loading="true"
                        rows="4"
                        :rowsHeight="[414, 38, 44, 316, 316]"
                        :rowsWidth="['100%', '100%', '100%', '100%', '100%']"
                    ></u-skeleton>
                </view>
            </view>
        </template>
        <!-- 团购规则 -->
        <CustomModal
            :show="showGroupPurchase"
            v-if="showGroupPurchase"
            width="616rpx"
            height="700rpx"
            :showCancelButton="false"
            customModalStyle
            :showConfirmButton="false"
            :closeable="true"
            @close="showGroupPurchase = false"
            title="拼团规则"
            titleStyle="font-family: PingFang SC;font-weight: 500;font-size: 36rpx;color: #121212;"
        >
            <!-- #ifdef H5 -->
            <view style="width: 100%; word-break: break-all">
                <view v-html="group_rules" v-lazy-html></view>
            </view>
            <!-- #endif -->
            <!-- #ifdef MP-WEIXIN -->
            <view style="width: 100%; word-break: break-all">
                <u-parse
                    :content="group_rules"
                    :lazyLoad="true"
                    loadingImg="https://wpm-cdn.dreame.tech/images/202307/713952-1690162496004.png"
                ></u-parse>
            </view>
            <!-- #endif -->
        </CustomModal>
        <CustomModal
            :show="pointsModal"
            v-if="pointsModal"
            width="616rpx"
            :showCancelButton="true"
            @cancel="pointsModal = false"
            @confirm="confirm"
            confirmText="去赚积分"
            :cancelStyle="{ width: '242rpx', background: '#EEEEEE', height: '92rpx', 'border-radius': '192rpx' }"
            :confirmStyle="{ width: '242rpx', height: '92rpx', 'border-radius': '192rpx' }"
        >
            <text style="font-size: 32rpx; font-weight: 400; color: #404040; line-height: 42rpx; text-align: center">
                {{ goodsData.alert_info.alert_msg }}
            </text>
        </CustomModal>
        <CustomModal
            :show="showRuleModel"
            width="616rpx"
            contentStyle="color:#1D1E20;font-weight:500;font-size: 28rpx;"
            title="提示"
            :content="rule"
            confirmText="我知道了"
            :showCancelButton="false"
            :boxShadow="'rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.1) 0px 2px 4px -2px'"
            confirmStyle="width: 524rpx; background: #E8DEC1;color: #8C6533;"
            @confirm="showRuleModel = false"
        >
        </CustomModal>
        <CustomPay
            :storekeeper="storekeeper"
            :isGroupGoods="isGroupGoods"
            :isShow_Pay_Group="isShow_Pay_Group"
            :initSelectAvIds="initSelectAvIds"
            :pointsMall="pointsMall"
            :isShow.sync="showPay"
            :point="point"
            :activityInfo="{
                group_purchase_id,
                group_activity_id,
                group_purchase_id_select,
                group_purchase_info: goodsData.group_purchase_info,
            }"
            :activityType="activityType"
            :type="1"
            :orderType="ORDER_RELATION_TYPE"
            :unionSource="unionSource"
            :goodsData="goodsData"
            :buyType="buyType"
            :tiedSaleList="tiedSaleList"
            :isSoldOut="isSoldOut"
            :isNotSold="isNotSold"
            @updateSpecs="handleUpdateSpecs"
            @change="change"
        />
        <Params :isShow.sync="showParams" :params="paramsInfoList"></Params>
        <!-- <TimeoutTips ref="TimeoutTips"></TimeoutTips> -->
        <EarnMoneySpendTimeoutTips
            v-if="$isInApp()"
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '240rpx' }"
            :task-code="fromPage=='goldCoins'?'viewGoodsGold':'viewGoodsMoney'"
            key="goodsDetail"
            :fromPage="fromPage"
            :watchTime="60000"
        />
        <custom-toast ref="customToast" />
        <custom-share :isShow.sync="showSharePop" :showPoster.sync="showPosterPop" />
        <pop-poster v-if="showPosterPop" :isShow.sync="showPosterPop" />

        <!-- 心愿单弹窗 -->
        <WishPop v-if="showWishModal" @closePop="showWishModal = false"></WishPop>
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin ref="wxLogin" @success="init"></WxLogin>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
        <customer-service-picker />
        <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'default'" />
    </view>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { CheckAppJump } from '@/common/decorators';
import GoodsInfo from './components/GoodsInfo.vue';
import PresaleInfo from './components/PresaleInfo.vue';
import CustomPay from '@/components/CustomPay/CustomPay.vue';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
// import TimeoutTips from '@/components/MemberTasks/TimeoutTips.vue';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';
import { GoodsModule, IGoodsInfo, ISpecsInfo, ITiedSale } from '@/store/modules/goods';
import { UserModule } from '@/store/modules/user';
import { CartModule } from '@/store/modules/cart';
import { AppModule } from '@/store/modules/app';
import { boundGuide, getUserInfo, browse, userBind } from '@/http/user';
import WishPop from './components/wish.vue';
import {
    specsInfo,
    benefitInfo,
    paramsInfo,
    tiedSaleList,
    goodsCouponList,
    reciveCoupon,
    getGroupPurchaseGoodsDetail,
    getGroupPurchaseRules,
    userRecommend,
    wishGoods,
    postCancelCollect,
    getShareContent,
} from '@/http/goods';
import { waresSpecsInfo } from '@/http/pointsMall';
import Utils from '@/common/Utils';
import GoodsGallery from './components/GoodsGallery.vue';
import Params from './components/Params.vue';
import Constants from '@/common/Constants';
import CustomShare from './components/CustomShare.vue';
import PopPoster from './components/PopPoster/PopPoster.vue';
import comparisionEquipmentContent from '@/components/comparisionEquipmentContent/comparisionEquipmentContent.vue';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import { goodsDetailCompare } from '@/http/comparison';
import { VIPModule, MineBasicInfo } from '@/store/modules/vip';
import NumberBox from './components/NumberBox/NumberBox.vue';
import couponItem from './components/couponItem.vue';
import GoodsEvaluate from './components/GoodsEvaluate.vue';
import GroupPurchaseRule from './components/GroupPurchaseRule.vue';
import { goodsReviewList } from '@/http/evaluate';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import { shareRcode } from '@/http/mine';
import { getGroupingGoodList, GroupingItem } from '@/http/groupGoods';
import shareDialog from '@/components/shareDialog/shareDialog.vue';
import ScrollMixin from './ScrollMixin';
import { IUnionSource } from '@/store/interface/order';
// import { getTaskInfo, doGoldTask } from '@/http/vip';

@Component({
    components: {
        GoodsInfo,
        WxLogin,
        CustomPay,
        Params,
        // TimeoutTips,
        PresaleInfo,
        comparisionEquipmentContent,
        GoodsGallery,
        CustomShare,
        PopPoster,
        NumberBox,
        couponItem,
        TabsSwiper,
        GoodsEvaluate,
        GroupPurchaseRule,
        WishPop,
        shareDialog,
        mixins: [ScrollMixin],
        EarnMoneySpendTimeoutTips,
    },
    mixins: [ScrollMixin],
})
export default class GoodsDetail extends Vue {
    $refs!: {
        wxLogin: WxLogin;
        // TimeoutTips;
        EarnMoneySpendTimeoutTips;
        customToast;
        tabs;
    };

    private bgColor: String = 'transparent'; // 导航栏背景
    public lineColor: string = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAWCAYAAABwvpo0AAAAAXNSR0IArs4c6QAAA+RJREFUWEftl09sFHUUx7/fN0NtoDEViSTIgZgeiGkU0gtaTFgu0BSCEBCMJrhCA9HFQlMtMZr+CJauAQoHSmkCVgNGqIfaxIMHlW3iARIuJD30sFEPxNTYmj3UsEB3xvxmdteZ3dk/tAst4CRz2dnZ33uf3+f33lviCb/4hOeP/wE8zgZcVcoMKTVdLMeiBii1zly2cHUnwdGWj3quPEqwRob7d1N4jmD0tc0tRwrFXhRAf7T1Isi3SYKkavmwp+APzSc4I8P9nSAUoeMGRKgam4MhFARwLtp6CsDBdPIagL4vG1OT4bD6MjmfEs7EcnWwtwYLjD4K3E3TD9y4IRTV2LwnbwMDAfQd+6AbxOGc5NMQ8DsN7nr34Inr8wnCz0NnGwhcBlmXjTttQAYCtQlNfgh5APq6I52wRWl8wQD0K0zSwAkm/uqaaxsGB3trnjXsDlLaQVT7YvYYkPlcm/BKUzhrgg/A2a73O0CJOvIUA5DWi8JxoXF4d2v3Vw/bBl3hU/VL3gLsz0guz+5yWnn3CLg1IPcZLKvp1ea9Pzh8vIH3dkWGSL5eNoD/FhuFyHlr8o/+B23EwICqXrbwmZ0kPyGR1T0YQH7yJGN3ktwa2hpO5AFQ6p3q56pqviVkU1kGeGm7xCcg/M6yeOW3v82YKtGDy7VG7/btF59uFMvpSNtB1maKXEbtMg34/u6fsiMUDmeLeF4N0L1/adVLRwsXQedsOOoE1QhPIBNCxEj+Akuu376DsfAh5VAvdQ0NqFpDqutpmg0CrAO5luQS//l29S4OwGMAeObeoluHQiH/YFS4DX7eugc2zpD0FxZ31dIAAgCBnCDlFolxEkkhpgDRSdSk71qAdRTWBgHO22XPGgUNECYJRNZuarkQBL74IHQsshKG+Q3JVV76swDgsUYXILdHB+5iDsD85EsbAGDUInaENu8bm9EkqF/SdeH5RYs7AGcucGyYCwDZdX1VvtARwDTJ09bk+KehsCo6tJX9b/DCybaXbZtKd4m5AFC+AfgR06kDoe0HCu6614ayAWRe+qKnfQtABcB3LPI0Dq4BMz4CpQwgeQ2wo+u3RYZLFdlZAciCON2xgba935kbgs5xhQEUMSAmYqj1294buZ/EM9+9bwNyFzl/vL2+6qkFOwHsIlmXDbTCAPwGIC40LqXse19vfKMtPpPEKwbAu/jAqY9XmVXYQsgagmtIuANL+vbvYvldAGRCyGuExFKwfmp+s+3GbJKuyBEoFYBSynxh6d2Vpm020MAKgdSRWA5wBYWm0/fhdBV9J0BOkUgQnKJInGAcwjhT9s0bv/4zVqmpMjfufwF0aukXQLnpKAAAAABJRU5ErkJggg==`;
    public goodsDataStyle: Object = {
        font: 'display:block;padding-left:44rpx;padding-right:41rpx;word-break:break-all',
        video: 'width:750rpx',
    };

    public activity_name: string = '';

    // 立即拼团购的时候传true 出现团购标志
    public isShow_Pay_Group: Boolean = false;

    public evaluateList: Array<any> = [];

    public pointsModal: Boolean = false;

    public initSelectAvIds: Boolean = false;
    public show: Boolean = false;

    public timeData: Object = {};

    public productInfo: any = {
        name: '',
        desc: '',
        image: '',
        imageBg: '',
        price: '',
    };

    public showPosterPop: Boolean = false; // 是否展示分享海报

    public showSharePop: Boolean = false;

    private goodsGid: string = ''; // 商品gid
    public group_purchase_id: string = ''; // 团购id
    public group_purchase_id_select: string = ''; // 详情页点击正在拼列表选中的团购id
    public group_activity_id: string = ''; // 活动id
    public group_rules: string = ''; // 团购规则
    public ORDER_RELATION_TYPE: string = '';
    /** 活动折扣类型 */
    // public discountActiveType: DiscountType = DiscountType.NULL;
    /** 活动折扣商品id */
    // public discountGid: string = DiscountType.NULL;

    public currentTime: number = +new Date();
    public commission_rate: number = 0;
    public isLoading: Boolean = false; // 加载中

    public isLoaded: Boolean = false; // 加载完成
    public currentNum: number = 0; // 对标之前的data函数返回的对象
    public parameterList: any = [];
    public comparasionList: any = [];
    public second_cate_id: any = '';

    public showPay: Boolean = false; // 展示购买弹窗
    public buyType: number = 0; // 购买类型
    public isHeaderTransparent: boolean = true; // header是否透明
    public showParams: boolean = false; // 参数弹窗
    public point: number = 0; // 用户当前积分

    public specs: Array<any> = []; // 当前的已选
    public selectAvIds: Array<any> = []; // 当前的已选id
    public timeShow: boolean = false;

    public paramsInfoList: Array<any> = []; // 参数弹窗数据
    public tiedSaleList: Array<ITiedSale> = []; // 搭售配件列表

    public showCoupon: boolean = false; // 领取优惠卷弹窗
    public current: number = 0;
    /** 拼团swiper的index */
    public currentGroupSwiperIndex: number = 0;
    public couponlistHeight: number = 0; // 弹窗内内高度
    public barStyle: Object = {
        bottom: '10rpx',
        backgroundImage: 'url(https://wpm-cdn.dreame.tech/images/202307/609564-1688537989905.png)',
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
    };

    public showGroupPurchase: boolean = false;

    public showRuleModel: boolean = false;
    public rule: string = '';
    public showWishModal: boolean = false; // 心愿单弹窗

    public couponList: Array<any> = [[], []];
    public shouldShowCoupon: Boolean = false;
    public shareContentList: Array<any> = [];
    public storekeeper: string = '';
    public unionSource: IUnionSource = IUnionSource.EMPTY;
    public inviter_id: number = 0;
    public shareFrom: string = '';

    get canAchieveCoupon(): Boolean {
        return this.goodsData.is_coupons == '1' && this.couponList[0].filter((item) => item.status === 3).length > 0;
    }

    public couponTabList: Array<any> = [{ name: '可领取优惠券' }, { name: '已领取优惠券' }];
    public specsData: ISpecsInfo = {
        id: '',
        gid: '',
        sku: '',
        price: '',
        av_ids: '',
        image: '',
        num: 0,
        gini_id: 0,
        gini_etime: 0,
        is_ini: 0,
        gini_tag: '',
        presale_info: false,
        label_tag: '',
        online_time_tag: '',
        sid: '',
        point: '',
    };

    public iniData: any = {
        is_ini: 0,
        gini_tag: '',
        price: 0,
        gini_etime: '',
        is_presale: '0',
        presale_time: 0,
        deposit: '0',
        expand_price: '0',
        scheduled_number_now: 0,
        start_payment: 0,
        end_payment: 0,
    };

    public isJoinGroupPurchase: number = 1; // 是否参与团购 1未参与 2已参与 其他值不是团购商品

    public benefitList: any = { name: '' }; // 服务说明
    public isShow: Boolean = true; // 是否展开
    public popupShow: Boolean = false;
    public blur: number = 0; // 滤镜模糊程度
    public opacity: number = 0; // 毛玻璃效果透明度
    public showDiscountActivity: boolean = true; // 是否需要隐藏折扣活动
    public animationFrameId: any = null;
    public timer: any = null;
    public serviceDescribe: String = '';
    public mainId: String = '';
    public pointsMall: Boolean = false;
    public mainSku: String = '';
    public redirect: string = '';
    public evaluateTotal: number = 0;
    public isAddCartLoading: Boolean = false; // 加载中
    public isGroupGoods: Boolean = false; // 加载中
    public urlMap: Object = {
        v1: 'https://wpm-cdn.dreame.tech/images/202308/64e32e578c7aa5751389568.png',
        v2: 'https://wpm-cdn.dreame.tech/images/202308/64e32ea3d8b1b8871349777.png',
        v3: 'https://wpm-cdn.dreame.tech/images/202308/64e32ecd82d7d5351366930.png',
        v4: 'https://wpm-cdn.dreame.tech/images/202308/64e32eee82c0e5351366993.png',
        v5: 'https://wpm-cdn.dreame.tech/images/202308/64e32f074647b2871349726.png',
    };

    public jumpUrl: string = '';
    public link: string = '';
    public timeout: any = null; // 添加timeout属性声明
    public fromPage: string = '';
    public activityType: string = ''; // 活动类型

    @Watch('showPay')
    isShowChange(newVal: number, oldVal: number) {
        if (!newVal) {
            this.group_purchase_id_select = '';
        }
    }

    get noDisabled(): Boolean {
        if (this.pointsMall) {
            if (!this.goodsData.alert_info) {
                return false;
            }
            return !this.goodsData.alert_info.can_purchase;
        }
        return false;
    }

    get mineBasic(): MineBasicInfo {
        return VIPModule.basicInfo;
    }

    get goodsData(): IGoodsInfo {
        return GoodsModule.goodsInfo;
    }

    get cartListLen(): number {
        return CartModule.list.length || 0;
    }

    get isNotSold(): Boolean {
        if (
            String(GoodsModule.goodsInfo.status) === Constants.STATUS_SOLD_OUT ||
            String(GoodsModule.goodsInfo.is_del) === Constants.STATUS_DEL
        ) {
            return true;
        }
        return false;
    }

    get toBeListed(): Boolean {
        if (this.goodsData.online_time_tag) {
            return true;
        }
        return false;
    }

    get disabled(): any {
        return this.isNotSold || this.isSoldOut;
    }

    // 判断是否库存卖完  或者预售商品的库存为0
    get isSoldOut(): Boolean {
        if (
            (Number(GoodsModule.goodsInfo.stock) === 0 && GoodsModule.goodsInfo.is_presale != '1') ||
            (Number(GoodsModule.goodsInfo.pre_stock) === 0 && GoodsModule.goodsInfo.is_presale == '1')
        ) {
            return true;
        }
        return false;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    get total(): boolean {
        let v: any;
        // 预售 团购 自定义 不展示券后价
        if (this.group_activity_id || this.group_purchase_id) {
            v = (
                Number(this.specsData.price || this.goodsData.price) * this.goodsData.group_purchase_info.group_rate
            ).toFixed(2);
        } else if (this.goodsData.is_presale == '1' || this.goodsData.is_ini == 1) {
            v = this.specsData.price || this.goodsData.price;
        } else {
            v = this.specsData.discount
                ? this.specsData.discount.price
                : this.specsData.price
                ? this.specsData.price
                : this.goodsData.discount
                ? this.goodsData.discount.price
                : this.goodsData.price;
        }
        this.tiedSaleList.map((item) => {
            if (item.num) {
                console.log('%c item.num: ', 'font-size:16px;background-color: #6EC1C2;color:#fff;', item.num);
                v = (Number(v) + Number(item.price) * Number(item.num)).toFixed(2);
            }
        });
        return v;
    }

    // 是否是一元链接
    get isOneLink(): Boolean {
        return this.goodsData.type === '99';
    }

    // 商品是否已加入心愿单
    get isGoodsWish(): boolean {
        return !!GoodsModule.goodsInfo.is_wish;
    }

    // 心愿单次数
    get wishTimes(): number {
        return Number(GoodsModule.goodsInfo.wish_number) || 0;
    }

    // 推荐次数
    get recommendTimes(): number {
        return Number(GoodsModule.goodsInfo.goods_recommend_times) || 0;
    }

    // 商品是否已被当前用户推荐
    get isGoodsRecommended(): boolean {
        // 后端字段可能为 0/1，统一转为布尔值
        return !!GoodsModule.goodsInfo.goods_recommend_status;
    }

    get rCode(): string {
        if (this.wxAuth && this.isPhone && uni.$u.test.isEmpty(GoodsModule.recommendCode)) {
            GoodsModule.asyncGetShareScode().then(() => {
                return GoodsModule.recommendCode;
            });
        }
        return GoodsModule.recommendCode;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get specsText(): string {
        if (this.goodsData.attr_cnf && this.goodsData.attr_cnf.length > 0) {
            const specsText = [];
            this.specs.forEach((s) => {
                specsText.push(s.at_val);
            });
            return specsText.join(',') || '请选择';
        } else {
            return this.goodsData.name;
        }
    }

    get isFoldableDevice() {
        return Utils.isFoldableDevice();
    }

    /** 返回动态的团购拼团swiper的高度，一个卡片的固定高度是76rpx，写死的 */
    // get currentGroupSwiperHeight() {
    //     return this.groupedBuyList[this.currentGroupSwiperIndex].length === 2 ? 152 : 76;
    // }

    get groupRate() {
        return parseFloat((Number(this.goodsData.group_purchase_info.group_rate) * 10).toFixed(10));
    }

    /** 是否支持六折购 */
    get isSixDiscount() {
        return this.goodsData.group_purchase_info.discount_activity_id == process.env.VUE_APP_SIX_DISCOUNT_ACTIVITY_ID;
    }

    /** 是否支持八折购 */
    get isEightDiscount() {
        return (
            this.goodsData.group_purchase_info.discount_activity_id == process.env.VUE_APP_EIGHT_DISCOUNT_ACTIVITY_ID
        );
    }

    get discountActivityImg() {
        return this.isSixDiscount
            ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/034745-1754386479612.png'
            : this.isEightDiscount
            ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/531026-1754388253248.png'
            : '';
    }

    handleSmallStore() {
        if (this.disabled) {
            return;
        }
        const mergedList = [
            ...GoodsModule.linkGoodsList,
            { ...this.goodsData, commission_rate: this.commission_rate },
        ];
        const uniqueListMap = new Map();
        mergedList.forEach(item => {
            if (item && item.gid !== undefined && item.gid !== null) {
                uniqueListMap.set(item.gid, item);
            }
        });
        const uniqueList = Array.from(uniqueListMap.values());
        GoodsModule.setLinkGoodsList(uniqueList);
        uni.navigateTo({
            url: '/pagesC/shopProduct/shop?is_selected=1&open_model=small_store',
        });
    }

    change() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_PRODUCT_DETAIL_TO_CAR,
                id: Number(this.goodsData.gid),
                user_id: UserModule.user_id || 0,
            },
            true,
        );
        Utils.Toast('已加入购物车', 2000, 'https://wpm-cdn.dreame.tech/images/202306/650852-1687162544299.png');
    }

    async getShareContentData() {
        const res = await getShareContent({ tid: this.goodsData.tids.length > 0 ? this.goodsData.tids[0] : -1 });
        this.shareContentList = res;
    }

    @CheckAppJump()
    showCouponHandle() {
        this.showCoupon = true;
    }

    @CheckAppJump()
    handleShowRule(rule) {
        this.rule = rule;
        this.showRuleModel = true;
    }

    @CheckAppJump()
    animationfinish(e) {
        const current: number = e.detail.current;
        this.current = current;
    }

    async getGoodsReviewList() {
        try {
            const goods_type = this.pointsMall ? 2 : 1;
            const res = await goodsReviewList({ page: 1, page_size: 3, gid: this.goodsGid, goods_type });
            this.evaluateList = res.list;
            this.evaluateTotal = res.total;
        } catch (err) {
            console.log(err);
        }
    }

    async getGoodsCouponList(gid) {
        try {
            const res = await goodsCouponList({ gid });
            if (res.length === 0) {
                this.shouldShowCoupon = false;
                return;
            }
            this.shouldShowCoupon = true;
            const reveivedCoupon = res
                .filter((item: any) => {
                    return item.status === 3 || item.status === 4;
                })
                .sort((a: any, b: any) => a.status - b.status);
            const notReveivedCoupon = res
                .filter((item: any) => {
                    return item.status === 1 || item.status === 2;
                })
                .sort((a: any, b: any) => a.status - b.status);
            this.couponList = [reveivedCoupon, notReveivedCoupon];
        } catch (error) {
            console.error(error);
        }
    }

    @CheckAppJump()
    swiperChange(e) {
        const current = e.detail.current;
        this.current = current;
    }

    @CheckAppJump()
    tabsChange(e: any) {
        this.current = e.index;
    }

    @CheckAppJump()
    async receiveCoupon(data, msg?: string) {
        try {
            await reciveCoupon(data);
            Utils.Toast(
                msg || '领取成功，祝您购物愉快',
                2000,
                '',
                'font-weight: 500;font-size: 27rpx;color: #121212;width:388rpx',
            );
            await this.getGoodsCouponList(this.goodsGid);
        } catch (error) {
            console.log(error);
        }
    }

    onChange(e) {
        this.timeData = e;
        setTimeout(() => {
            this.timeShow = true;
        }, 100);
    }

    confirm() {
        this.pointsModal = false;
        Utils.navigateTo('/pagesA/missionCenter/missionCenter');
    }

    @CheckAppJump()
    handleSharePop() {
        this.showSharePop = true;
    }

    onPageScroll(e) {
        const topDistance = 200;
        // #ifdef H5
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        this.showDiscountActivity = e.scrollTop < 200;
        this.animationFrameId = requestAnimationFrame(() => {
            const num = e.scrollTop;
            if (num < topDistance) {
                this.blur = (40 / topDistance) * num;
                this.opacity = (0.7 / topDistance) * num;
            } else {
                this.blur = 40;
                this.opacity = 0.7;
            }
        });
        // #endif
        // #ifdef MP-WEIXIN
        const num = e.scrollTop;
        if (num < topDistance) {
            this.blur = (40 / topDistance) * num;
            this.opacity = (0.7 / topDistance) * num;
        } else {
            this.blur = 40;
            this.opacity = 0.7;
        }
        // #endif
    }

    onShareAppMessage(res) {
        let path = `/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&rcode=${encodeURIComponent(
            this.rCode,
        )}&type=${this.pointsMall ? 'pointsMall' : ''}`;
        if (!uni.$u.test.isEmpty(AppModule.guideEncrypt)) {
            // 导购场景分享
            console.log('onShareAppMessage 导购场景分享');
            path = `/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&scode=${encodeURIComponent(
                AppModule.guideEncrypt,
            )}&type=${this.pointsMall ? 'pointsMall' : ''}`;
        }
        // 完成分享任务
        Utils.getTaskInfo('mall/dreame/share_goods');
        shareRcode({ gid: this.goodsData.gid, r_code: this.rCode });
        console.log('goodsDetail page onShareAppMessage path=', path);
        return {
            title: this.goodsData.name,
            imageUrl: this.goodsData.cover_image,
            path,
        };
    }

    async setBrowser() {
        await browse({
            inviter_id: this.inviter_id,
            type: 'shareGoodsMoney',
        });
    }

    onLoad(options) {
        this.fromPage = options.from || '';
        this.onLoadProxy(options);
    }

    async onLoadProxy(options) {
        console.log('options9999', options);
        this.commission_rate = options.commission_rate || 0;
        this.activity_name = options.activity_name || '';
        this.storekeeper = options.storekeeper || '';
        this.unionSource = options.unionSource || IUnionSource.EMPTY;
        this.link = options.link || '';
        this.inviter_id = options.inviter_id || 0;
        this.shareFrom = options.from || '';
        this.activityType = options.activityType || '';
        UserModule.setInviterId(this.inviter_id);
        this.inviter_id && this.setBrowser();
        if (options.from === 'plugin') {
            uni.setStorageSync('from', 'plugin');
        }

        // 判断合伙人分享进来的
        if (options.employee) {
            userBind({ employee_uid: options.employee })
        }

        this.group_purchase_id = options.group_purchase_id || '';
        this.group_activity_id = options.group_activity_id || '';
        this.goodsGid = options.gid || '';
        this.ORDER_RELATION_TYPE = options.orderType || '';
        // this.discountActiveType = options.discountActiveType;
        // this.discountGid = options.discountGid;
        if (options.gid) {
            Utils.logTrace(
                {
                    module: Constants.LOG_TRACE_MODULE_DREAME,
                    event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                        .LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_PRODUCT_DETAIL_LOAD,
                    id: Number(options.gid),
                    user_id: UserModule.user_id || 0,
                },
                true,
            );
        }

        if (this.group_activity_id) {
            getGroupPurchaseRules({ activity_id: this.group_activity_id }).then((res) => {
                this.group_rules = res.details;
                // #ifdef H5
                this.group_rules = this.group_rules.replaceAll(
                    /img src=/g,
                    'img src="https://wpm-cdn.dreame.tech/images/202307/713952-1690162496004.png" class="_img" data-src=',
                );
                console.log(this.group_rules);
                // #endif
                // #ifdef MP-WEIXIN
                this.group_rules = this.group_rules
                    .replaceAll(/<p>/g, '<p style="word-break: break-all">')
                    .replaceAll(/<br\/>/g, '');
                // #endif
            });
        }
        // 通过团购分享链接进入的 带了 group_purchase_id 通过接口校验是否可参团
        if (this.group_purchase_id) {
            getGroupPurchaseGoodsDetail({ group_purchase_id: this.group_purchase_id })
                .then((res) => {
                    getGroupPurchaseRules({ group_purchase_id: this.group_purchase_id }).then((res) => {
                        this.group_rules = res.details;
                        // #ifdef H5
                        this.group_rules = this.group_rules.replaceAll(
                            /img src=/g,
                            'img src="https://wpm-cdn.dreame.tech/images/202307/713952-1690162496004.png" class="_img" data-src=',
                        );
                        // #endif
                        // #ifdef MP-WEIXIN
                        this.group_rules = this.group_rules
                            .replaceAll(/<p>/g, '<p style="word-break: break-all">')
                            .replaceAll(/<br\/>/g, '');
                        // #endif
                    });
                })
                .catch(() => {
                    this.group_purchase_id = ''; // 异常则清空，页面按照普通商品处理
                });
        }
        GoodsModule.setIsInshop(!!options.isInShop);
        if (this.goodsGid == '135') {
            this.goodsGid = '206';
        }
        // if (this.goodsGid == '152') {
        //   this.goodsGid = '173';
        // }

        // 获取链接进入的redirect
        this.redirect = options.redirect || '';

        if (options.type == 'pointsMall') {
            this.pointsMall = true;
        } else {
            this.pointsMall = false;
        }

        // this.shopParameterHandling()
        this.mainId = options.mainId;
        this.mainSku = options.mainSku;
        if (options.scode) {
            AppModule.setGuideEncrypt(decodeURIComponent(options.scode) || ''); // 通过导购小程序进入携带的分享加密串参数
        }
        if (options.rcode) {
            AppModule.setRecEncrypt(decodeURIComponent(options.rcode) || ''); // 通过推荐有礼页面进入携带的分享加密串参数
            console.log('==========> 通过推荐有礼页面进入携带的分享加密串参数');
        }
        if (options.q) {
            // 通过小程序进入携带的参数
            const _scene: any = decodeURIComponent(options.q).split('?')[1].split('&');
            console.log('==========> 通过小程序进入携带的参数', _scene);
            const _id = _scene[1].substring(4);
            this.goodsGid = _id || '';
            const _o = _scene[0].substring(2);
            AppModule.setGuideEncrypt(decodeURIComponent(_o) || '');
            console.log('==========> 通过小程序进入携带的参数');
        }
        if (this.wxAuth && this.isPhone && (options.rcode || options.q)) {
            if (options.rcode) {
                console.log('==========> 已登录，通过推荐有礼页面进入携带的分享加密串参数，走loginAuto');
            } else {
                console.log('==========> 已登录，通过小程序进入携带的参数，走loginAuto');
            }
            UserModule.asyncReLogin({ r_code: AppModule.recEncrypt }).then(() => {
                // 有分享加密串的情况下走完自动登录后重新走弹框逻辑
                this.$refs.wxLogin.showPopRegistered = true;
                this.$refs.wxLogin.showInBindPop();
            });
        }
        this.isLoading = true;
        // #ifdef H5
        // this.init();
        // #endif
        this.init();
        this.jumpUrl = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.goodsGid}`;
    }

    // 商品参数对比数据结构处理
    shopParameterHandling() {
        const arr2 = this.comparasionList[0]?.params || [];
        const arr1 = this.comparasionList[1]?.params || [];
        const list = arr1.reduce((pre, cur) => {
            const target = pre.find((ee) => ee.param_id == cur.param_id && ee.categroup_id == cur.categroup_id);
            if (target) {
                // 对数据中items进行处理
                Object.assign(target, { param_info1: cur.param_info, param_name1: cur.param_name });
            } else {
                pre.push(cur);
            }
            return pre;
        }, arr2);
        this.parameterList = list;
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }

    async onShow() {
        this.isLoaded && this.getGoodsInfo();
        // 获取优惠卷列表
        this.getGoodsCouponList(this.goodsGid);
        this.initSelectAvIds = !this.initSelectAvIds;
        if (Utils.isInApp()) {
            setTimeout(() => {
                // 确保组件完全重置后再启动
                this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
            }, 1000);
        }
    }

    onUnload() {
        AppModule.setGuideEncrypt('');
        AppModule.setRecEncrypt(''); // 通过推荐有礼页面进入携带的分享加密串参数
        GoodsModule.setUseCouponId('');
        GoodsModule.setRecommendCode('');
        const specsDefalutData: ISpecsInfo = {
            id: '',
            gid: '',
            sku: '',
            price: '',
            av_ids: '',
            sid: '',
            image: '',
            num: 0,
            gini_etime: 0,
            is_ini: 0,
            gini_id: 0,
            gini_tag: '',
            presale_info: false,
            label_tag: '',
            online_time_tag: '',
            point: '',
        };
        GoodsModule.setSpecsInfo(specsDefalutData);
        // this.$refs.TimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    beforeDestroy() {
        uni.$off('phoneAuth');
    }

    async init() {
        await this.getGoodsInfo();
        // 评价列表
        this.getGoodsReviewList();
        if (!this.pointsMall) {
            this.getParamsInfo();
            this.handleBoundGuide();
            this.getUserInfo();
            this.loadBasicInfo();
            this.getBenefitInfo();
            this.getParamsData();
            this.getTiedSale();
            CartModule.loadList();
            this.getShareContentData();
            // this.getUserTaskInfo();
        }
    }

    @CheckAppJump()
    handleUpdateIniData() {
        if (this.specsData.id) {
            (this.pointsMall
                ? waresSpecsInfo({
                      gid: this.goodsData.id,
                      av_ids: JSON.stringify(this.selectAvIds),
                      num: 1,
                  })
                : specsInfo({
                      gid: this.goodsData.id,
                      av_ids: JSON.stringify(this.selectAvIds),
                      num: 1,
                  })
            ).then((res) => {
                this.iniData = {
                    is_ini: res.is_ini,
                    gini_tag: res.gini_tag,
                    price: res.price,
                    gini_etime: res.gini_etime,
                };
            });
        } else {
            this.getGoodsInfo();
        }
    }

    // 处理导购加密串
    async handleBoundGuide() {
        try {
            if (AppModule.guideEncrypt === '') {
                console.log('=========> 沒有导购员信息');
                return;
            }
            await boundGuide(AppModule.guideEncrypt);
            console.log('=========> 成功绑定导购员');
        } catch (e) {
            console.error('handleBoundGuide e=', e);
        }
    }

    // 搭配购买
    async getTiedSale() {
        if (this.goodsData.main_sku) {
            tiedSaleList({ sku: this.goodsData.main_sku }).then((res) => {
                this.tiedSaleList = res;
            });
        }
    }

    // 展开与收起
    @CheckAppJump()
    moreShow() {
        this.isShow = !this.isShow;
    }

    @CheckAppJump()
    popupBtn(serviceDescribe) {
        serviceDescribe = serviceDescribe.replace(/<img[^>]*height="[^"]*"[^>]*>/g, (match) => {
            return match.replace(/height="[^"]*"/g, '');
        });
        this.serviceDescribe = serviceDescribe;
        this.popupShow = true;
    }

    @CheckAppJump()
    handleStopClick() {
        console.log('阻止冒泡行为');
    }

    @CheckAppJump()
    valChange(index, e) {
        this.tiedSaleList[index].num = e.value || '0';
    }

    // 服务说明
    async getBenefitInfo() {
        if (this.isPhone) {
            this.benefitList = await benefitInfo();
        }
    }

    @CheckAppJump()
    gotoGdetailPage(gid: string) {
        if (this.disabled) return true;
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${gid}&mainId=${this.goodsGid}&mainSku=${GoodsModule.goodsInfo.main_sku}`,
        });
    }

    // 会员基本信息接口
    async loadBasicInfo() {
        this.isPhone && (await VIPModule.getBasicInfo());
    }

    // 处理用户已选
    handleUpdateSpecs(data) {
        this.specsData = data.specsData;
        this.specs = [];
        this.selectAvIds = data.selectAvIds;
        data.selectAvIds.forEach((id) => {
            this.goodsData.attr_cnf.forEach((attr) => {
                const val = attr.val.filter((v) => v.id === id)[0] || null;
                if (val) {
                    this.specs.push(val);
                }
            });
        });
        this.iniData.is_ini = this.specsData.is_ini;
        this.iniData.gini_tag = this.specsData.gini_tag;
        this.iniData.price = this.specsData.price;
        this.iniData.gini_etime = this.specsData.gini_etime;
    }

    // 获取商品详情
    async getGoodsInfo() {
        this.showPay = false;
        let params = {} as any;
        if (this.pointsMall) {
            params = { gid: this.goodsGid };
        } else {
            // 如果是团购商品，传group_activity_id，后端会区分团购和普通商品的限购数
            params = {
                gid: this.goodsGid,
                single: 1,
                group_activity_id: this.group_activity_id,
                group_purchase_id: this.group_purchase_id,
            };
        }
        if (GoodsModule.isInshop) {
            params.is_internal_purchase = 1;
        }
        await GoodsModule.asyncGetGoodsInfo({ params, pointsMall: this.pointsMall });
        // 如果有回调，在没货或者下架的时候跳转
        if (this.redirect && (this.isNotSold || this.isSoldOut)) {
            uni.redirectTo({ url: decodeURIComponent(this.redirect) });
        }
        this.isLoaded = true;
        this.iniData = {
            is_ini: this.goodsData.is_ini,
            gini_tag: this.goodsData.gini_tag,
            price: this.goodsData.price,
            gini_etime: this.goodsData.gini_etime,
            is_presale: this.goodsData.is_presale,
            presale_time: this.goodsData.presale_time,
            deposit: this.goodsData.deposit,
            expand_price: this.goodsData.expand_price,
            scheduled_number_now: this.goodsData.scheduled_number_now,
            start_payment: this.goodsData.start_payment,
            end_payment: this.goodsData.end_payment,
        };
        this.isGroupGoods = !!this.goodsData.group_purchase_info?.is_group_goods;
        this.group_activity_id =
            this.goodsData.group_purchase_info?.activity_id == 0
                ? ''
                : String(this.goodsData.group_purchase_info?.activity_id);
        this.isGroupGoods && this.getGroupingList();
        Utils.setPageTitle(this.goodsData.name);
    }

    /* 获取商品参数详情 */
    async getParamsData() {
        this.second_cate_id = this.goodsData.cate[1];
        const result = await goodsDetailCompare({ sku: this.goodsData.main_sku });
        const temArr = result.items || [];
        // 按照skus顺序对返回数据排序
        this.comparasionList = temArr.sort((cur) => {
            return this.goodsData.main_sku == cur.sku ? -1 : 1;
        });
        if (this.comparasionList.length) {
            this.shopParameterHandling();
        }
    }

    async getUserInfo() {
        const res = await getUserInfo({});
        this.point = res.point;
    }

    disableClick() {
        Utils.Toast('商品库存不足或已下架');
    }

    @CheckAppJump()
    async addCart(type: number = 0) {
        // 0: 购买 1.: 加入购物车 2: 直接发起拼团 3: 下单并发起拼团 4 立即购买-拼团商品
        if (type === 1 && this.isOneLink) {
            return Utils.Toast('一元链接请直接购买');
        }
        if (this.disabled) return true;
        if (this.isAddCartLoading) return true;

        if (type === 0 && this.canAchieveCoupon && this.iniData.is_presale != 1) {
            this.isAddCartLoading = true;
            const couponArr = this.couponList[0].filter((item) => item.status === 3);
            const param = {
                market_ids: couponArr.map((item) => item.market_id).join(','),
                ids: couponArr.map((item) => item.ac_id).join(','),
            };
            try {
                await this.receiveCoupon(param, `已成功领取${couponArr.length}张优惠券`);
                await this.getGoodsInfo();
            } catch (error) {
                console.error(error);
            } finally {
                console.log('领取优惠券结束');
                this.isAddCartLoading = false;
            }
        }

        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            const target = 'showPopPay';
            this.checkWxAuto(target).then(() => {
                this.showPay = true;
            });
            return;
        }
        if (this.showPay) return;
        // #endif
        this.buyType = type;
        this.showPay = true;
    }

    @CheckAppJump()
    jumpExchange(type) {
        if (!this.noDisabled) {
            // #ifdef MP-WEIXIN
            if (!this.wxAuth || !this.isPhone) {
                const target = 'showPopPay';
                this.checkWxAuto(target).then(() => {
                    this.showPay = true;
                });
                return;
            }
            if (this.showPay) return;
            // #endif
            this.buyType = type;
            this.showPay = true;
            return true;
        }
        if (this.goodsData.alert_info.button_msg == '积分不足') {
            this.pointsModal = true;
            return true;
        }
        if (this.goodsData.alert_info.alert_msg) {
            Utils.Toast(this.goodsData.alert_info.alert_msg);
        }
    }

    checkWxAuto(target) {
        return new Promise<void>((resolve) => {
            UserModule.authFlow({ target });
            uni.$once('phoneAuth', (val: string) => {
                if (target === val) {
                    console.log('==========> checkWxAuto 用户已授权手机号');
                    resolve();
                }
            });
        });
    }

    @CheckAppJump()
    gotoCartPage() {
        const hasJumped = Utils.jumpToDreameApp(this.link);
        if (hasJumped) {
            return; // 如果执行了 window.location.href，则不继续执行
        }
        if (this.wxAuth && this.isPhone) {
            Utils.reportEvent('cart_click', {});
            uni.navigateTo({ url: '/pagesA/cart/cart' });
        } else {
            const target = 'gotoCartPage';
            UserModule.authFlow({ target });
        }
    }

    @CheckAppJump()
    handleContact(e) {
        if (this.wxAuth && this.isPhone) {
            // 判断分类
            let buType = BuType.BuType_NONE;
            const tids = this.goodsData.tids || [];
            // 11 扫地机器人 10 智能洗地机 12.吸尘器
            if (tids.includes('11')) {
                buType = BuType.BuType_SDJ;
            } else if (tids.includes('10')) {
                buType = BuType.BuType_XDJ;
            } else if (tids.includes('12')) {
                buType = BuType.BuType_XCQ;
            } else if (tids.includes('13')) {
                buType = BuType.BuType_CFJ;
            } else if (tids.includes('14')) {
                buType = BuType.BuType_JSQ;
            } else if (tids.includes('31')) {
                buType = BuType.BuType_XYJ;
            } else if (tids.includes('19')) {
                buType = BuType.BuType_CD;
            }

            console.log(JSON.parse(JSON.stringify(this.goodsData)));
            const { id, name: title, cover_image: img } = this.goodsData;

            const price = this.specsData.discount
                ? this.specsData.discount.price
                : this.goodsData.discount
                ? this.goodsData.discount.price
                : this.goodsData.price;

            const visitorExtraInfo = { type: '' };
            switch (buType) {
                case BuType.BuType_SDJ:
                    visitorExtraInfo.type = '扫地机';
                    break;
                case BuType.BuType_XDJ:
                    visitorExtraInfo.type = '洗地机';
                    break;
                case BuType.BuType_XCQ:
                    visitorExtraInfo.type = '吸尘器';
                    break;
                case BuType.BuType_CFJ:
                    visitorExtraInfo.type = '吹风机';
                    break;
                case BuType.BuType_JSQ:
                    visitorExtraInfo.type = '净水器';
                    break;
                case BuType.BuType_XYJ:
                    visitorExtraInfo.type = '洗衣机';
                    break;
                case BuType.BuType_CD:
                    visitorExtraInfo.type = '厨电';
                    break;
                case BuType.BuType_OTHER:
                    visitorExtraInfo.type = '其他产品';
                    break;
            }

            const linkCard = {
                productList: [
                    {
                        title,
                        amount: 'x1',
                        price: `￥${price}`,
                        img,
                        visitorUrl: `dreame://mall/pagesB/goodsDetail/goodsDetail?gid=${id}`,
                        pagePath: `dreame://mall/pagesB/goodsDetail/goodsDetail?gid=${id}`,
                    },
                ],
            };

            Utils.decryptContact(e, buType, {
                linkCard: JSON.stringify(linkCard),
                visitorExtraInfo: encodeURIComponent(JSON.stringify(visitorExtraInfo)),
            });
        } else {
            const target = 'handleContact';
            UserModule.authFlow({ target });
        }
    }

    @CheckAppJump()
    handleWishList() {
        if (!this.wxAuth || !this.isPhone) {
            const target = 'handleWishList';
            UserModule.authFlow({ target });
            return;
        }

        // opt: add / remove
        const isWish = (GoodsModule.goodsInfo as any).is_wish;
        const opt = isWish ? 'remove' : 'add';
        wishGoods({ goods_id: this.goodsData.gid, opt })
            .then(() => {
                // 本地更新状态
                const is_wish = opt === 'add' ? 1 : 0;
                // 更新详情页数据
                const goodsInfo: any = GoodsModule.goodsInfo;
                goodsInfo.is_wish = is_wish;

                // 本地维护心愿单次数
                const currentTimes = Number(goodsInfo.wish_number) || 0;
                goodsInfo.wish_number = opt === 'add' ? currentTimes + 1 : Math.max(currentTimes - 1, 0);

                // 向全局广播商品心愿单状态变更，ProductList 等组件可监听并即时更新
                uni.$emit('wishGoodsChange', { gid: this.goodsData.gid, is_wish });

                // 如果是添加到心愿单，显示弹窗
                if (opt === 'add') {
                    this.showWishModal = true;
                }
            })
            .catch((err) => {
                console.error(err);
            });
    }

    goToWishPage() {
        this.showWishModal = false;
        uni.navigateTo({
            url: '/pagesA/wish/wish',
        });
    }

    @CheckAppJump()
    handleRecommend() {
        const hasJumped = Utils.jumpToDreameApp(this.link);
        if (hasJumped) {
            return; // 如果执行了 window.location.href，则不继续执行
        }
        if (!this.wxAuth || !this.isPhone) {
            // 需要先授权
            const target = 'handleRecommend';
            UserModule.authFlow({ target });
            return;
        }

        const type = GoodsModule.goodsInfo.goods_recommend_status ? 0 : 1;
        postCancelCollect({ gid: this.goodsData.gid });
        userRecommend({ gid: this.goodsData.gid, type })
            .then(() => {
                // 本地同步维护推荐状态与次数
                const goodsInfo: any = GoodsModule.goodsInfo;
                goodsInfo.goods_recommend_status = type;

                const currentTimes = Number(goodsInfo.goods_recommend_times) || 0;
                goodsInfo.goods_recommend_times = type === 1 ? currentTimes + 1 : Math.max(currentTimes - 1, 0);

                // 推荐成功后跳转到分享页面
                if (type === 1) {
                    const item = {
                        gid: this.goodsData.gid,
                        name: this.goodsData.name,
                        cover_image: this.goodsData.cover_image,
                        price: this.goodsData.price,
                        mprice: this.goodsData.mprice,
                        subsidy_price: this.goodsData.discount.subsidy,
                        deposit: this.goodsData.discount.coin.can_coin,
                        coin_price: this.goodsData.discount.coin.coin_price,
                        tids: this.goodsData.tids || [],
                        introduce: this.goodsData.introduce,
                        inviter_id: UserModule.user_id || 0,
                        share_price: this.total,
                        is_group_goods: this.isGroupGoods,
                        share_from: this.shareFrom,
                    };
                    uni.navigateTo({
                        url: `/pagesA/sharePage/sharePage?item=${encodeURIComponent(JSON.stringify(item))}`,
                    });
                }
            })
            .catch((err) => {
                console.error(err);
            });

        // 点赞推荐商品
        // if (!this.isGoodsRecommended) {
        //   const url = `pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}`;
        //   const data = {
        //       ...Constants.GEN_SHARE_LINK_TYPE,
        //       jumpLink: url,
        //   };
        //   Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
        //       const params = {
        //           target: 'wechat,qq,sina',
        //           type: 'web',
        //           content: {
        //               url: res.data,
        //               share_image: this.goodsData.cover_image + '?x-oss-process=image/resize,w_200',
        //               share_title: '邀请您加入追觅会员官方商城小程序社区 ' + this.goodsData.name,
        //           },
        //       };
        //       Utils.messageChannel('share', params);
        //   });
        // }
    }

    handleSwiperChange(e) {
        this.currentNum = e.current;
    }

    onHide() {
        // this.$refs.TimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    @CheckAppJump()
    paramsInfoHandle() {
        this.showParams = true;
    }

    getParamsInfo() {
        paramsInfo({ sku: this.goodsData.main_sku }).then((res) => {
            this.paramsInfoList = res.items;
        });
    }

    toHalfPrice() {
        uni.navigateTo({ url: '/pagesC/billionsOfShoppingGold/billionsOfShoppingGold' });
    }

    public groupingTime = '';

    // 团购列表数据 - 用于上下滚动展示
    public groupBuyList: GroupingItem[] = [];

    getGroupingList() {
        getGroupingGoodList({
            activity_id: this.group_activity_id,
            page: 1,
            page_size: 10,
            type: 1,
            gid: this.goodsData.gid,
        }).then((res) => {
            this.groupBuyList = res.list;
            const defaultAvatar = require('@/static/todo.png');
            this.groupBuyList.map((v) => {
                v.member_avatars =
                    v.member_avatars.length === 0
                        ? [{ avatar: defaultAvatar, nick_name: '匿名用户' }]
                        : v.member_avatars;
            });
        });
    }

    // 将团购列表按每组2条进行分组
    // get groupedBuyList() {
    //     const chunks = [];
    //     for (let i = 0; i < this.groupBuyList.length; i += 2) {
    //         chunks.push(this.groupBuyList.slice(i, i + 2));
    //     }
    //     return chunks;
    // }

    onEndTimeChange(timeData: any) {
        this.groupingTime = timeData;
    }

    @CheckAppJump()
    handleGroupBuyClick(item: any) {
        this.group_purchase_id_select = item.group_purchase_id;
        this.addCart(4);
    }

    @CheckAppJump()
    onGroupSwiperChange(e) {
        this.currentGroupSwiperIndex = e.detail.current;
    }

    /* 分享商品 */
    handleShareClick() {
        // const sharePageUrl = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}`;
        // const data = {
        //     ...Constants.GEN_SHARE_LINK_TYPE,
        //     jumpLink: sharePageUrl,
        // };
        // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
        //     const params = {
        //         target: 'wechat,weixin_circle,qq,sina',
        //         type: 'web',
        //         content: {
        //             url:
        //                 `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?link=` +
        //                 res.data +
        //                 '&gid=' +
        //                 this.goodsData.gid,
        //             share_image: this.goodsData.cover_image + '?x-oss-process=image/resize,w_200',
        //             share_title: this.goodsData.name,
        //             share_desc: this.goodsData.introduce || this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
        //         },
        //     };
        //     Utils.messageChannel('share', params);
        // });
        this.show = true;
        this.productInfo = {
            name: this.goodsData.name,
            desc:
                this.goodsData.introduce || !this.pointsMall ? this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value : '',
            image: this.goodsData.cover_image,
            imageBg: '',
            // price: this.goodsData.discount.price || this.goodsData.price,
            price: this.pointsMall ? `${this.goodsData.point}积分+ ${this.goodsData.price}` : this.goodsData.discount.price || this.goodsData.price,
        };
        if (this.isGroupGoods) {
            Utils.taskComplete('shareGroupGoods');
        } else if (this.shareFrom === 'shopStore') {
            Utils.taskComplete('shareShopGoodsFriend');
        } else {
            Utils.taskComplete('shareGoodsFriendMoney');
        }
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.cardShare(type, 'goods')(
            {
                target: 'image_template,download,wechat,weixin_circle,qq,sina',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${
                    this.goodsData.gid
                }&inviter_id=${UserModule.user_id || 0}`,
                jumpLink: `${
                    process.env.VUE_APP_BASE_URL
                }front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&inviter_id=${
                    UserModule.user_id || 0
                }`,
            },
            {
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&inviter_id=${UserModule.user_id || 0}`,
                    title: this.pointsMall ? `【${this.goodsData.point}积分+${this.goodsData.price}元】${this.goodsData.name}` : `【${this.total}元】${this.goodsData.name}`,
                    desc: this.productInfo.desc,
                    image: this.goodsData.cover_image + '?x-oss-process=image/resize,w_200',
                },
                extras: {
                    type: 'goods',
                    id: this.goodsData.gid,
                    goods: {
                        name: this.productInfo.name,
                        desc: this.productInfo.desc,
                        image: this.productInfo.image,
                        price: this.pointsMall ? this.goodsData.mprice : this.goodsData.discount.price || this.goodsData.price,
                        priceColor: '#FF7D01',
                        descColor: '#C59245',
                    },
                    copyText: (this.pointsMall ? `${this.goodsData.point}积分+${this.goodsData.price}元` : `${this.goodsData.discount.price}元`) + `, ${this.goodsData.name}`
                },
            },
        );
    }

    handleDiscountActivityClick() {
        if (this.isSixDiscount) {
            uni.navigateTo({
                url: `/pagesC/sixDiscount/sixDiscountPage?discount_activity_id=${this.goodsData.group_purchase_info.discount_activity_id}&discount_gid=${this.goodsData.group_purchase_info.discount_gid}`,
            });
        } else {
            uni.navigateTo({
                url: `/pagesC/eightDiscount/eightDiscountPage?discount_activity_id=${this.goodsData.group_purchase_info.discount_activity_id}&discount_gid=${this.goodsData.group_purchase_info.discount_gid}`,
            });
        }
    }

    // /* 用户进入页面判断用户是否可以执行浏览商城任意商品60秒，获得消费金奖励, 如果可以创建60s倒计时，结束时请求接口，并清除倒计时 */
    // getUserTaskInfo() {
    //     getTaskInfo({ taskCode: 'mall/dreame/view_goods_money' }).then((res) => {
    //         console.log(
    //             '%c 用户进入页面判断用户是否可以执行浏览商城任意商品60秒，获得消费金奖励, 如果可以创建60s倒计时，结束时请求接口，并清除倒计时: ',
    //             'font-size:16px;background-color: #6EC1C2;color:#fff;',
    //             res,
    //         );
    //         if (!res.completed) {
    //             /* 创建60s倒计时 */
    //             this.createTimeout();
    //         }
    //     });
    // }

    // async doTask() {
    //     await doGoldTask({ type: 'viewGoodsMoney' });
    //     /* 清除倒计时 */
    //     clearTimeout(this.timeout);
    // }

    // createTimeout() {
    //     this.timeout = setTimeout(() => {
    //         console.log('60s倒计时结束');
    //         this.doTask();
    //     }, 60000);
    // }
}
</script>

<style lang="scss" scope>
@import './goodsDetail.scss';

::v-deep .uni-swiper-item {
    overflow: scroll;
    overscroll-behavior: contain;
}

::v-deep .u-tabs__wrapper__nav__line {
    bottom: 0rpx;
}
</style>
