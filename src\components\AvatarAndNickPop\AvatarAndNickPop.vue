<template>
  <u-popup :show="isShow" @close="onClose" mode="bottom" :round="32">
    <view class="form">
      <view class="tip">为了更好的显示效果，请设置您的头像和昵称</view>
      <view class="item">
        <view class="label">头像</view>
        <view class="right-content u-flex-1 u-flex u-row-right">
          <button class="avatar-wrapper  u-flex u-row-between" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
            <image class="avatar" :src="avatarUrl"></image>
            <u-icon name="arrow-right" color="#333" size="20"></u-icon>
          </button>
        </view>
      </view>
      <u-gap height="2rpx" bgColor="#f0f2f3"></u-gap>
      <view class="item">
        <view class="label">昵称</view>
        <view class="right-content u-flex-1">
          <input type="nickname" class="weui-input" placeholder="请输入昵称" v-model="nickname"
            @change="handleInputChange" />
        </view>
      </view>
      <u-gap height="2rpx" bgColor="#f0f2f3"></u-gap>
      <u-gap height="20rpx" bgColor="transparent"></u-gap>
      <view class="item">
        <view class="btn" hover-class="btn-hover" @click="handleCancel">取消</view>
        <view class="btn confirm" hover-class="btn-hover" @click="handleUpdate">确定</view>
      </view>
    </view>
  </u-popup>
</template>
<script lang="ts">
import { Prop, Vue, Component, Watch } from 'vue-property-decorator';
import { uploadFile } from '@/http/upload';
import { modify } from '@/http/user';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
@Component
export default class PopPoster extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly isShow!: Boolean;

    @Prop({ type: String, default: '' })
    readonly avatar: string;

    @Prop({ type: String, default: '' })
    readonly nick: string;

    public avatarUrl: string = '';
    public nickname: string = '';

    @Watch('isShow') // watch，此处是监听isShow的变化
    async isShowChange(newVal: Boolean, oldVal: Boolean) {
        if (newVal) {
            this.nickname = this.nick;
            this.avatarUrl = this.avatar;
        }
    }

    onChooseAvatar(e) {
        const { avatarUrl } = e.detail;
        this.uploadImage(avatarUrl);
    }

    async uploadImage(avatarUrl) {
        try {
            uni.showLoading({});
            const res = await uploadFile(avatarUrl, {});
            this.avatarUrl = res.bigImg;
            uni.hideLoading();
        } catch (error) {
            uni.hideLoading();
            Utils.Toast('头像上传失败');
        }
    }

    handleInput(e) {
        this.nickname = e.value;
    }

    handleInputChange(e) {
        this.nickname = e.detail.value;
    }

    handleCancel() {
        this.$emit('success');
        this.onClose();
    }

    async handleUpdate() {
        const params = {
            nick: this.nickname,
            avatar: this.avatarUrl,
        };

        if (params.nick.length == 0) {
            return Utils.Toast('昵称不能为空');
        }
        if (params.nick.length > 20) {
            return Utils.Toast('昵称长度能超过20个字符');
        }

        await modify(params);
        await UserModule.asyncReLogin();
        // const info = UserModule.userInfo
        // info.nick = this.nickname
        // info.avatar = this.avatarUrl
        // UserModule.setUserInfo(info)
        Utils.Toast('修改成功');
        this.$emit('success');
        this.onClose();
    }

    onClose() {
        this.$emit('update:isShow', false);
    }
}
</script>

<style lang="scss" scoped>
.form {
    width: 100%;
    background-color: #fff;
    height: 460rpx;
    border-radius: 24rpx 24rpx 0 0;
    padding: 60rpx 60rpx 20rpx;

    .tip {
        color: #333;
        font-size: 24rpx;
        line-height: 34rpx;
        margin-bottom: 34rpx;
    }

    .item {
        width: 100%;
        height: 100rpx;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 18rpx 0;
        color: #333;

        .label {
            width: 120rpx;
        }

        .avatar-wrapper {
            flex: 1;
            padding: 0 !important;
            background-color: transparent;

            &::after {
                border: none;
            }
        }

        .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
        }

        .btn {
            width: 222rpx;
            font-size: 26rpx;
            color: #7c7bc8;
            text-align: center;
            line-height: 70rpx;
            border-radius: 10rpx;
            background-color: #fff;
            padding: 0;
            margin: 34rpx;
            border: 2rpx solid #7c7bc8;

            &.confirm {
                color: #fff;
                background: #7c7bc8;
            }
        }
    }
}
</style>
