<template>
    <view>
        <view class="u-waterfall">
            <view id="u-left-column" class="u-column u-left-column">
                <MyGoodsDemo
                    :list="leftList"
                    @change="viewDeatail"
                    @praise="onPraise"
                    :showView="showView"
                    :creatorSelf="creatorSelf"
                ></MyGoodsDemo>
            </view>
            <view v-if="isFoldableDevice" id="u-center-column" class="u-column u-center-column">
                <MyGoodsDemo
                    :list="centerList"
                    @change="viewDeatail"
                    @praise="onPraise"
                    :showView="showView"
                    :creatorSelf="creatorSelf"
                ></MyGoodsDemo>
            </view>

            <view id="u-right-column" class="u-column u-right-column">
                <MyGoodsDemo
                    :list="rightList"
                    @change="viewDeatail"
                    @praise="onPraise"
                    :showView="showView"
                    :creatorSelf="creatorSelf"
                ></MyGoodsDemo>
            </view>
        </view>
        <view class="no_more" :style="{ 'padding-bottom': paddingBottom }" v-if="finished && render_finished"
            >没有更多了~
        </view>
    </view>
</template>

<script>
/**
 * waterfall 瀑布流
 * @description 这是一个瀑布流形式的组件，内容分为左右两列，结合uView的懒加载组件效果更佳。相较于某些只是奇偶数左右分别，或者没有利用vue作用域插槽的做法，uView的瀑布流实现了真正的 组件化，搭配LazyLoad 懒加载和loadMore 加载更多组件，让您开箱即用，眼前一亮。
 * @tutorial https://www.uviewui.com/components/waterfall.html
 * @property {Array} flow-list 用于渲染的数据
 * @property {String Number} add-time 单条数据添加到队列的时间间隔，单位ms，见上方注意事项说明（默认200）
 * @example <u-waterfall :flowList="flowList"></u-waterfall>
 */
import MyGoodsDemo from './MyGoodsDemo.vue';
import Utils from '@/common/Utils';
// import _ from 'lodash';
const { windowWidth } = uni.getSystemInfoSync();
export default {
    components: { MyGoodsDemo },
    name: 'MyWaterfall',
    props: {
        value: {
            // 瀑布流数据
            type: Array,
            required: true,
            default: function () {
                return [];
            },
        },
        finished: {
            type: Boolean,
            default: function () {
                return false;
            },
        },
        // 每次向结构插入数据的时间间隔，间隔越长，越能保证两列高度相近，但是对用户体验越不好
        // 单位ms
        addTime: {
            type: [Number, String],
            default: 10,
        },
        // id值，用于清除某一条数据时，根据此idKey名称找到并移除，如数据为{idx: 22, name: 'lisa'}
        // 那么该把idKey设置为idx
        idKey: {
            type: String,
            default: 'id',
        },
        paddingBottom: {
            type: String,
            default: '',
        },
        // 草稿箱不展示访问量
        showView: {
            type: Boolean,
            default: true,
        },
        creatorSelf: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            leftList: [],
            centerList: [],
            rightList: [],
            tempList: [],
            children: [],
            timer: null,
            render_finished: false,
            renderTimes: [],
        };
    },
    computed: {
        // 破坏flowList变量的引用，否则watch的结果新旧值是一样的
        copyFlowList() {
            return this.cloneData(this.value);
        },
        isFoldableDevice() {
            return Utils.isFoldableDevice();
        }
    },
    watch: {
        async copyFlowList(nVal, oVal) {
            // 取差值，即这一次数组变化新增的部分
            const startIndex = Array.isArray(oVal) && oVal.length > 0 ? oVal.length : 0;
            // 拼接上原有数据
            this.tempList = this.tempList.concat(this.cloneData(nVal.slice(startIndex)));
            await this.splitData();
            const changedItems = nVal.filter((newItem) =>
                oVal.some(
                    (oldItem) =>
                        oldItem.content_id === newItem.content_id &&
                        JSON.stringify(oldItem) !== JSON.stringify(newItem),
                ),
            );
            if (changedItems && changedItems.length) {
                changedItems.forEach((i) => {
                    const leftIndex = this.leftList.findIndex((item) => item.content_id === i.content_id);
                    leftIndex !== -1 && this.$set(this.leftList, leftIndex, { ...i });
                    const rightIndex = this.rightList.findIndex((item) => item.content_id === i.content_id);
                    rightIndex !== -1 && this.$set(this.rightList, rightIndex, { ...i });
                });
            }
        },
    },
    mounted() {
        this.tempList = this.cloneData(this.copyFlowList);
        this.splitData();
    },
    beforeDestroy() {
        // 在组件销毁前，使用 clearTimeout 函数来取消 setTimeout
        this.clearRendertimes();
    },
    methods: {
        // handlePraise(id) {
        //     this.$emit('praise', id);
        // },
        viewDeatail(item) {
            this.$emit('change', item);
        },
        onPraise(data) {
            this.$emit('praise', data);
        },

        async splitData() {
            clearTimeout(this.timer);
            this.timer = setTimeout(async () => {
                this.render_finished = false;
                for (let i = 0; i < this.tempList.length; i++) {
                    await this.render(this.tempList[i]);
                }
                this.tempList = [];
                this.render_finished = true;
            }, 300);
        },

        dataDeduplication(arr) {
            for (let i = 0; i < arr.length; i++) {
                for (let j = i + 1; j < arr.length; j++) {
                    if (arr[i].content_id === arr[j].content_id) {
                        arr.splice(j, 1);
                        j = j - 1;
                    }
                }
            }
        },

        clearRendertimes() {
            clearTimeout(this.timer);
            this.renderTimes.forEach(clearTimeout); // 使用 clearTimeout 函数来取消所有的 setTimeout
            this.renderTimes = []; // 清空 renderTimes 数组
        },
        getImageHeight: (cover_image) => {
            return new Promise((resolve, reject) => {
                uni.getImageInfo({
                    src: cover_image, // 图片路径，可以是网络图片或本地路径
                    success: function (res) {
                        resolve({ height: res.height, width: res.width });
                    },
                    fail: function () {
                        resolve({ height: 100, width: 100 });
                    },
                });
                setTimeout(() => {
                    resolve({ height: 100, width: 100 });
                }, 200);
            });
        },
        render(item) {
            // eslint-disable-next-line no-async-promise-executor
            return new Promise(async (resolve, reject) => {
                this.renderTimes.push(
                    setTimeout(async () => {
                        if (!item) return;

                        const leftRect = await this.$uGetRect('#u-left-column');
                        const rightRect = await this.$uGetRect('#u-right-column');

                        let centerRect = { height: Infinity, width: Infinity };
                        if (this.isFoldableDevice) {
                            centerRect = await this.$uGetRect('#u-center-column');
                        }

                        try {
                            const imageWidth = (windowWidth - 48) / (this.isFoldableDevice ? 3 : 2) - (windowWidth / 750) * 16;
                            const imageRect = await this.getImageHeight(item.cover_image);
                            const imageHeight = (imageWidth / imageRect.width) * imageRect.height;
                            item.cover_height = imageHeight;
                        } finally {
                            // 检查数据是否已经存在于任何一列中
                            if (
                                this.leftList.some((i) => i.content_id === item.content_id) ||
                                this.centerList.some((i) => i.content_id === item.content_id) ||
                                this.rightList.some((i) => i.content_id === item.content_id)
                            ) {
                                // eslint-disable-next-line no-unsafe-finally
                                return resolve();
                            }
                            console.log('leftList', this.leftList.length, 'centerList', this.centerList.length, 'rightList', this.rightList.length)
                            // 左列为空，添加到左列
                            if (!this.leftList.length) {
                                this.leftList.push(item);
                            } else if (this.isFoldableDevice && !this.centerList.length) {
                                // 折叠屏下, 中列最短或相等，添加到中列
                                this.centerList.push(item);
                            } else if (!this.rightList.length) {
                                // 右列最短，添加到右列
                                this.rightList.push(item);
                            } else {
                                console.log('leftHeight', leftRect.height, 'centerHeight', centerRect.height, 'rightHeight', rightRect.height)
                                if (leftRect && centerRect && rightRect) {
                                    // 移除刚才添加的项
                                    this.leftList = this.leftList.filter(i => i.content_id !== item.content_id);
                                    this.centerList = this.centerList.filter(i => i.content_id !== item.content_id);
                                    this.rightList = this.rightList.filter(i => i.content_id !== item.content_id);

                                    // 根据高度添加到最矮的列
                                    const minHeight = Math.min(leftRect.height, centerRect.height, rightRect.height);

                                    if (minHeight === leftRect.height) {
                                        this.leftList.push(item);
                                    } else if (this.isFoldableDevice && minHeight === centerRect.height) {
                                        this.centerList.push(item);
                                    } else if (minHeight === rightRect.height) {
                                        this.rightList.push(item);
                                    }
                                }
                            }

                            // 去重处理
                            this.dataDeduplication(this.leftList);
                            this.dataDeduplication(this.centerList);
                            this.dataDeduplication(this.rightList);

                            resolve();
                        }
                    }, this.addTime),
                );
            });
        },
        // 复制而不是引用对象和数组
        cloneData(data) {
            return JSON.parse(JSON.stringify(data));
        },
        // 清空数据列表
        clear() {
            this.leftList = [];
            this.rightList = [];
            // 同时清除父组件列表中的数据
            this.$emit('input', []);
            this.tempList = [];
        },
        // 清除某一条指定的数据，根据id实现
        remove(id) {
            // 如果findIndex找不到合适的条件，就会返回-1
            let index = -1;
            index = this.leftList.findIndex((val) => val[this.idKey] == id);
            if (index != -1) {
                // 如果index不等于-1，说明已经找到了要找的id，根据index索引删除这一条数据
                this.leftList.splice(index, 1);
            } else {
                if (this.isFoldableDevice) {
                    // 检查中间列
                    index = this.centerList.findIndex((val) => val[this.idKey] == id);
                    if (index != -1) {
                        this.centerList.splice(index, 1);
                    } else {
                        // 同理于上方面的方法
                        index = this.rightList.findIndex((val) => val[this.idKey] == id);
                        if (index != -1) this.rightList.splice(index, 1);
                    }
                } else {
                    index = this.rightList.findIndex((val) => val[this.idKey] == id);
                    if (index != -1) this.rightList.splice(index, 1);
                }
            }
            // 同时清除父组件的数据中的对应id的条目
            index = this.value.findIndex((val) => val[this.idKey] == id);
            // eslint-disable-next-line vue/no-mutating-props
            if (index != -1) this.$emit('input', this.value.splice(index, 1));
        },
        // 修改某条数据的某个属性
        modify(id, key, value) {
            // 如果findIndex找不到合适的条件，就会返回-1
            let index = -1;
            index = this.leftList.findIndex((val) => val[this.idKey] == id);
            if (index != -1) {
                // 如果index不等于-1，说明已经找到了要找的id，修改对应key的值
                this.leftList[index][key] = value;
            } else {
                if (this.isFoldableDevice) {
                    // 检查中间列
                    index = this.centerList.findIndex((val) => val[this.idKey] == id);
                    if (index != -1) {
                        this.centerList[index][key] = value;
                    } else {
                        // 同理于上方面的方法
                        index = this.rightList.findIndex((val) => val[this.idKey] == id);
                        if (index != -1) this.rightList[index][key] = value;
                    }
                } else {
                    index = this.rightList.findIndex((val) => val[this.idKey] == id);
                    if (index != -1) this.rightList[index][key] = value;
                }
            }
            // 修改父组件的数据中的对应id的条目
            index = this.value.findIndex((val) => val[this.idKey] == id);
            if (index != -1) {
                // 首先复制一份value的数据
                const data = this.cloneData(this.value);
                // 修改对应索引的key属性的值为value
                data[index][key] = value;
                // 修改父组件通过v-model绑定的变量的值
                this.$emit('input', data);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.u-waterfall {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;

    .u-left-column,
    .u-right-column {
        width: calc(50% - 4rpx);
        width: calc(50% - 6rpx);
    }

    @media screen and (min-width: 450px) {
        .u-left-column,
        .u-center-column,
        .u-right-column {
            width: calc(33.33% - 6rpx);
        }
    }

}

.no_more {
    text-align: center;
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(29, 30, 32, 0.4);
    line-height: 33px;
    margin-top: 50rpx;
}

.u-column {
    display: flex;
    flex-direction: column;
    height: auto;
}

.u-image {
    width: 100%;
}
</style>
