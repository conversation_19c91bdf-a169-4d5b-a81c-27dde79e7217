<template>
    <view v-show="isShow" class="floating-wrapper" :class="currentState">
        <view class="background-layer">
            <view class="left-circle-bg"></view>
            <view class="right-rect-bg"></view>
        </view>
        <view class="content-layer">
            <view class="coin-wrapper">
                <svg class="border-svg" viewBox="0 0 118 118">
                    <circle
                        cx="59"
                        cy="59"
                        r="56"
                        fill="none"
                        stroke="#FFA218"
                        stroke-width="6"
                        stroke-linecap="round"
                        :stroke-dasharray="circumference"
                        :stroke-dashoffset="strokeDashoffset"
                        transform="rotate(-90 59 59)"
                    />
                </svg>
                <image
                    class="gold-icon"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fddf20fae10640080168.png"
                ></image>
                <view
                    class="reward-text-overlay"
                    :class="{ show: showRewardText, hide: !showRewardText && rewardTextVisible }"
                    v-show="(showRewardText || rewardTextVisible) && !isCompleted"
                >
                    <image
                        class="reward-text-overlay-icon"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a298bde22139260010475.png"
                    ></image>
                    <text>+20</text>
                </view>
            </view>
            <view class="text-wrapper">
                <text class="line-one">看视频</text>
                <text class="line-two">赚3600金币</text>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { viewVideo15s } from '@/http/requestGo/community';
import { getTaskInfo } from '@/http/vip';
import { VIPModule } from '@/store/modules/vip';

@Component({
    name: 'VideoIntegralTimeoutTips',
})
export default class VideoIntegralTimeoutTips extends Vue {
    // Data properties
    currentState: string = 'initial';
    initialTime: number = 30000;
    time: number = 30000;
    timer: number | null = null;
    // 新增：奖励文字显示控制
    showRewardText: boolean = false;
    isCompleted: boolean = false;
    isShow: boolean = false;
    rewardTextVisible: boolean = false;
    // 新增：记录当前循环开始时间
    currentCycleStartTime: number = 0;
    public taskInfoMap: any = {
        watchVideo: 'friend/user_event_app/watch_video_gold', // 观看视频30秒
        watchVideoState: 'friend/user_event_app/watch_video_gold', //
    };

    // Computed properties
    get circumference(): number {
        return 2 * Math.PI * 56;
    }

    get strokeDashoffset(): number {
        const progress = (this.initialTime - this.time) / this.initialTime;
        return this.circumference * (1 - progress);
    }

    get uid(): any {
        return VIPModule.basicInfo.uid;
    }

    // Lifecycle hooks
    mounted(): void {
        // 先获取任务状态
        this.getTaskInfo(this.taskInfoMap.watchVideoState).then((res) => {
            this.isCompleted = res.completed;
            // 如果任务已完成，不启动任何动画
            if (this.isCompleted) {
                console.log('任务已完成，停止所有动画', res.completed);
                return;
            }

            // 任务未完成，正常启动动画
            this.startTimer();
            this.$nextTick(() => {
                this.currentState = 'expanding';
            });

            setTimeout(() => {
                if (!this.isCompleted) {
                    this.currentState = 'shrinking';
                }
            }, 2000);

            setTimeout(() => {
                if (!this.isCompleted) {
                    this.currentState = 'reward';
                }
            }, 30000);
        });
    }

    beforeDestroy(): void {
        this.stopTimer();
    }

    public remainingSeconds = 30;

    // Methods
    startTimer(): void {
        console.log('startTimer');
        clearInterval(this.timer);
        this.timer = setInterval(() => {
            console.log('timer', this.isCompleted);
            // 如果任务已完成，停止定时器
            if (this.isCompleted) {
                this.stopTimer();
                return;
            }

            if (this.time > 0) {
                this.time -= 100;
                // 检查是否完成一圈（进度为100%）
                if (this.time === 0) {
                    this.showRewardTextForOneSecond();
                }
            } else {
                this.time = 30000;
                // 重新开始新的循环，记录新的开始时间
                console.log('倒计时结束!');
            }
        }, 100) as unknown as number;
    }

    showRewardTextForOneSecond(): void {
        // 如果任务已完成，不显示奖励文字
        if (this.isCompleted) {
            return;
        }

        // 显示奖励文字
        this.rewardTextVisible = true;
        this.showRewardText = true;
        this.getVideoGoldStatus();

        // 1.5秒后立即隐藏
        setTimeout(() => {
            if (!this.isCompleted) {
                this.showRewardText = false;
                this.rewardTextVisible = false;
            }
        }, 1500);
    }

    async getVideoGoldStatus() {
        // 如果任务已完成，不调用相关API
        if (this.isCompleted) {
            return;
        }

        await viewVideo15s({ code: this.taskInfoMap.watchVideo, uid: this.uid });
        this.getTaskInfo(this.taskInfoMap.watchVideoState);
    }

    // 获取任务信息
    async getTaskInfo(code: String) {
        try {
            const res = await getTaskInfo({ taskCode: code });
            console.log('res====getTaskInfo', res);
            this.isCompleted = res.completed;
            this.isShow = !res.completed;
            this.$emit('isCompleted', this.isCompleted);
            // 如果任务已完成，立即停止所有动画
            if (this.isCompleted) {
                this.stopAllAnimations();
            }

            return res;
        } catch (error) {
            console.error('获取任务信息失败:', error);
            return null;
        }
    }

    // 停止所有动画
    stopAllAnimations(): void {
        // 隐藏奖励文字
        this.showRewardText = false;
        this.rewardTextVisible = false;
        // 清除奖励文字定时器
        // 停止定时器
        this.stopTimer();

        // 重置状态
        this.currentState = 'initial';
        this.time = this.initialTime;

        console.log('已停止所有动画');
    }

    // 停止定时器
    stopTimer(): void {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    // 暂停动画和计时
    pauseAnimationAndTimer(): void {
        // 停止定时器
        this.stopTimer();

        // 计算当前循环内已经计时的秒数
        const remainingSeconds = Math.floor(this.time / 1000);

        this.$emit('remain', remainingSeconds);

        // 保持当前状态，不清除SVG圆环状态
        // this.currentState = 'initial';
        // this.time = this.initialTime;

        // 隐藏奖励文字
        this.showRewardText = false;
        this.rewardTextVisible = false;

        console.log('已暂停VideoIntegralTimeoutTips的3秒动画和计时');
    }

    // 恢复动画和计时
    resumeAnimationAndTimer(): void {
        // 如果任务已完成，不恢复动画
        if (this.isCompleted) {
            console.log('任务已完成，无法恢复动画');
            return;
        }

        // 重新开始计时器
        this.startTimer();

        console.log('已恢复VideoIntegralTimeoutTips的3秒动画和计时');
    }
}
</script>

<style lang="scss" scoped>
.floating-wrapper {
    position: fixed;
    top: 326rpx;
    left: 28rpx;
    z-index: 999;
    width: 289rpx;
    height: 107rpx;

    &.expanding {
        .right-rect-bg {
            width: 289rpx;
            opacity: 1;
        }
        .text-wrapper {
            opacity: 1;
        }
    }

    &.shrinking,
    &.reward {
        .right-rect-bg {
            width: 107rpx;
            opacity: 0;
        }
        .text-wrapper {
            opacity: 0;
        }
    }

    &.reward {
        .reward-text-overlay {
            opacity: 1;
        }
    }
}

.background-layer,
.content-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.left-circle-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 107rpx;
    height: 107rpx;
    border-radius: 50%;
    background-color: rgba(45, 45, 45, 0.85);
    z-index: 1;
}

.right-rect-bg {
    position: absolute;
    top: 0;
    left: 0rpx;
    width: 107rpx;
    height: 107rpx;
    background-color: rgba(45, 45, 45, 0.85);
    border-top-right-radius: 90rpx;
    border-top-left-radius: 90rpx;
    border-bottom-right-radius: 90rpx;
    border-bottom-left-radius: 90rpx;
    opacity: 1;
    transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1), opacity 0.5s cubic-bezier(0.65, 0, 0.35, 1);
}

.content-layer {
    z-index: 2;
}

.coin-wrapper {
    position: absolute;
    width: 107rpx;
    height: 107rpx;
    border-radius: 50%;
    position: relative;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translateZ(0);
    backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .border-svg {
        position: absolute;
        width: 107rpx;
        height: 107rpx;
        z-index: -1;
    }

    .gold-icon {
        width: 82rpx;
        height: 82rpx;
        position: absolute;
    }
}

.text-wrapper {
    position: absolute;
    top: 50%;
    left: 119rpx;
    width: 132rpx;
    height: 68rpx;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    line-height: 1.3;
    color: #ffffff;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease 0.2s;
    font-family: MiSans;
    font-size: 24rpx;
    font-weight: normal;
    line-height: 140%;
    letter-spacing: 0rpx;
    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
}

.reward-text-overlay {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 55rpx;
    height: 40rpx;
    font-family: MiSans;
    font-size: 28rpx;
    font-weight: 600;
    letter-spacing: 0rpx;
    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    color: #cb2533;
    opacity: 0;
    transform: scale(0.8) translateY(10rpx);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

    &.show {
        opacity: 1;
        transform: scale(1.1) translateY(0);
        animation: rewardBounce 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        filter: drop-shadow(0 0 8rpx rgba(203, 37, 51, 0.6));
    }

    &.hide {
        opacity: 0;
        transform: scale(0.8) translateY(-10rpx);
    }

    @keyframes rewardBounce {
        0% {
            transform: scale(0.8) translateY(10rpx);
            opacity: 0;
        }
        50% {
            transform: scale(1.2) translateY(-5rpx);
            opacity: 1;
        }
        100% {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
    }

    .reward-text-overlay-icon {
        position: absolute;
        width: 107rpx;
        height: 107rpx;
        z-index: 99;
    }

    text {
        z-index: 999;
    }
}
</style>
