<template>
    <u-popup
        :show="reallyShow"
        mode="center"
        :safeAreaInsetBottom="false"
        :closeOnClickOverlay="false"
        :round="12"
        :mask="true"
        @open="open"
        @close="close"
        @touchmove="preventScroll"
        class="customer-service-picker"
    >
        <view class="content">
            <view class="close">
                <image src="@/static/customer_close.png" @click="close" mode="aspectFit"></image>
            </view>
            <view class="title">请选择咨询相关业务</view>
            <view class="bu-list">
                <view v-for="(item, index) in buList" :key="index" @click="clickService(item.type)" class="bu-item">
                    <view class="bu-title">{{ item.name }}</view>
                    <view class="bu-title-arrow">
                        <image src="@/static/customer_arrow.png" mode="aspectFit" class="arrow-img"></image>
                    </view>
                </view>
            </view>
        </view>
    </u-popup>
</template>
<script>
import { AppModule } from '@/store/modules/app';
import { BuType } from './customer-butype';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

export default {
    name: 'CustomerServicePicker',
    mixins: [uni.$u.mpMixin, uni.$u.mixin],
    data() {
        return {
            isShow: false,
            currentPageRoute: '',
            buList: [
                {
                    name: '扫地机咨询',
                    type: BuType.BuType_SDJ,
                },
                {
                    name: '洗地机咨询',
                    type: BuType.BuType_XDJ,
                },
                {
                    name: '吸尘器咨询',
                    type: BuType.BuType_XCQ,
                },
                {
                    name: '其他产品咨询',
                    type: BuType.BuType_OTHER,
                },
            ],
        };
    },
    computed: {
        customerServiceShow() {
            return AppModule.customerServiceShow;
        },

        reallyShow() {
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const route = currentPage.route;
            if (this.currentPageRoute.length > 0) {
                return this.isShow && route.includes(this.currentPageRoute);
            }
            return this.isShow;
        },
    },
    watch: {
        customerServiceShow(val) {
            if (val) {
                this.isShow = true;
            } else {
                this.isShow = false;
            }
        },
    },
    created() {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        this.currentPageRoute = route;
    },
    beforeDestroy() {
        AppModule.setCustomerService({ isShow: false });
        // #ifdef H5
        const el = this.$el;
        el.parentNode && el.parentNode.removeChild(el);
        // 获取所有 class 为 'customer-service-picker' 的元素
        const popups = document.querySelectorAll('.customer-service-picker');
        // 遍历所有元素并从 document.body 中移除
        popups.forEach(popup => {
            popup.parentNode.removeChild(popup);
        });
        // #endif
        uni.$off(Constants.TABBAR_CHANGE, this.close);
    },

    mounted() {
        uni.$on(Constants.TABBAR_CHANGE, this.close);
    },
    methods: {
        show() {
            this.isShow = true;
        },

        close() {
            this.isShow = false;
            AppModule.setCustomerService({ isShow: false });
            this.$emit('close');
        },

        open() {
            this.isShow = true;
        },

        clickService(butype) {
            this.close();
            Utils.decryptContact({}, butype);
        },
        preventScroll(event) {
            if (!event) {
                return;
            }
            event.stopPropagation();
            event.preventDefault();
        },
    },
};
</script>
<style scoped lang="scss">
.content {
    width: 616rpx;
    height: 626rpx;

    .close {
        position: absolute;
        right: 22rpx;
        top: 22rpx;
        width: 46rpx;
        height: 46rpx;

        image {
            display: block;
            width: 46rpx;
            height: 46rpx;
        }
    }

    .title {
        margin-top: 54rpx;
        text-align: center;
        font-size: 34rpx;
        color: $text-color-primary;
        font-weight: 500;
    }

    .bu-list {
        margin-top: 38rpx;

        .bu-item {
            margin: 0 40rpx 16rpx 40rpx;
            border-radius: 12rpx;
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 30rpx;
            font-weight: 500;
            background-color: $fill-color-bg-gray;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .bu-title {
                margin-left: 30rpx;
                color: $text-color-primary;
            }

            .bu-title-arrow {
                margin-right: 12rpx;
                width: 38rpx;
                height: 38rpx;
                display: flex;
                justify-content: center;
                align-items: center;

                .arrow-img {
                    width: 38rpx;
                    height: 38rpx;
                    object-fit: contain;
                }
            }
        }
    }
}
</style>
