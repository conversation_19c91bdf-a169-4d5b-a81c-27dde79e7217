import http from './index';

/**
 * 获取邀请规则信息
 */
export function getInviteInfo(params: any) {
    return http.post('/main/invite/info', params);
}
/* 半价飘屏 */
export function getMemberActivityInviteRecord(params: any) {
    return http.post('/main/member-activity/invite-record', params);
}
/** 赚钱花飘屏幕 */
export function getConsumeMoneyRoll(params: any) {
    return http.post('/main/invite/consume-money-roll', params);
}
/** 保存活动记录 */
export function getUserDictSave(params: any) {
    return http.post('/main/user-dict/save', params);
}
/** 获取活动记录 */
export function getUserDictGet(params: any) {
    return http.post('/main/user-dict/get', params);
}

/** 获取消费金额提示 */
export function getConsumeMoneyTip(params: any) {
    return http.post('/main/invite/consume-money-tip', params);
}
/** 关闭消费金额提示 */
export function getConsumeMoneyCloseTip(params: any) {
    return http.post('/main/invite/consume-money-close-tip', params);
}
