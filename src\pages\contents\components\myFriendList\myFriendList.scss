::v-deep .uni-scroll-view-refresher {
    background-color: #ffffff !important;
}

.swiper-box {
    height: 100vh;

    // #ifdef H5
    ::v-deep .uni-swiper-wrapper {
        overflow: hidden !important;
    }

    // #endif
    .swiper-item {
        width: 100%;
        height: 100%;
        .scroll-view {
            height: 100%;
            background-color: #ffffff;

            // #ifdef H5
            ::v-deep .uni-scroll-view {
                .uni-scroll-view-content {
                    height: fit-content;
                }
            }

            // #endif
            .u-search-top {
                background-color: #F6F6F6;
                height: 68rpx;
                border-radius: 16rpx;
                margin: 24rpx;
                padding: 24rpx;

                .u-search {
                    margin-left: 10rpx;
                    font-size: 28rpx;
                }
            }

            .u-avatur-top {
                width: 100%;

                .u-avatar {
                    width: 302rpx;
                    height: 302rpx;
                    border-radius: 24rpx;
                    background-color: #ffffff;
                    overflow: hidden;
                    box-sizing: content-box;
                    border: 4rpx solid #E1CEAB;
                }

                .u-title {
                    font-size: 32rpx;
                    font-weight: 500;
                    color: #121212;
                    margin-top: 16rpx;
                }

                .u-id {
                    font-size: 26rpx;
                    color: #777777;
                }

                .u-weixin {
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                    padding: 24rpx;

                    .u-weixin-title {
                        font-size: 32rpx;
                        font-weight: 500;
                        color: #121212;
                    }

                    .u-weixin-name {
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #777777;
                    }
                }
            }

            .u-recommend-box {
                width: 100%;
                display: flex;
                flex-direction: column;

                .u-recommend-title {
                    font-size: 36rpx;
                    color: #111111;
                    font-weight: 600;
                    padding: 0 32rpx;
                }

                .u-recommend-content {
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                    padding: 24rpx 20rpx;

                    .u-recommend-left {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                    }

                    .u-recomment-right {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        border: 2rpx solid #DBC49A;
                        height: 64rpx;
                        width: 192rpx;
                        border-radius: 32rpx;
                        font-size: 28rpx;
                        color: #C2A271;
                    }
                    .u-recomment-right-yes {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        border: 2rpx solid #A6A6A6;
                        height: 64rpx;
                        width: 192rpx;
                        border-radius: 32rpx;
                        font-size: 28rpx;
                        color: #A6A6A6;
                        font-weight: 500;
                    }
                }
            }

            .u-good-box {
                display: flex;
                flex-direction: column;

                .u-good-title {
                    font-size: 36rpx;
                    color: #111111;
                    font-weight: 600;
                    padding: 0 32rpx;
                }

                .u-good-content {
                    @include flex(row, space-between, flex-start, wrap);
                    padding: 0 8rpx 0;
                }

                .class-item {
                    width: calc(calc(100vw - 48rpx)/2);
                    background: #ffffff;
                    border-radius: 24rpx;
                    margin-bottom: 16rpx;
                    overflow: hidden;

                    .class-item-top {
                        width: 100%;
                        padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
                        margin: 0 auto 36rpx;
                        position: relative;
                        // background-color: #f5f5f5; // 添加默认背景色
                        overflow: hidden; // 防止内容溢出

                        img {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            z-index: 2; // 确保在占位层之上
                        }

                        ::v-deep image {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            border-radius: 8rpx 8rpx 0 0;
                            object-fit: cover;
                            z-index: 2; // 确保在占位层之上
                        }
                    }

                    .class-item-bottom {
                        padding: 0 32rpx 12rpx;
                        .u-good-title-img {
                            overflow: hidden;
                            .text {
                                display: inline;
                                /* 确保文本环绕 */
                                text-align: justify;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 2;
                                text-overflow: ellipsis;
                                // text-indent: 67rpx;
                                overflow: hidden;
                                position: relative;

                                .indent-icon {
                                    width: 52rpx;
                                    height: 26rpx;
                                    margin-top: 10rpx;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                }
                            }
                        }
                    }
                }
                .u-good-bottom {
                    background-color: #F5EDD6;
                    color: #8C6533;
                    height: 88rpx;
                    border-radius: 44rpx;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    font-size: 32rpx;
                    font-weight: 500;
                    margin: 60rpx 24rpx 208rpx 24rpx;
                }
            }
        }
    }
}

.contents-container {
    position: relative;
    background: #ffffff;
    margin-bottom: 208rpx;

    ::v-deep .uni-swiper-wrapper {
        overflow: auto;
    }

    .title {
        display: flex;
        flex-direction: column;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        z-index: 3;
        color: $color-2A2A2A;
        display: flex;
        background: #ffffff;
        align-items: unset;
        // #ifdef H5
        justify-content: center;
        // #endif
        // #ifdef MP-WEIXIN
        justify-content: flex-start;
        // #endif

        .status-bar-left {
            flex-direction: column;
            margin-right: auto;
            justify-content: start;
            align-items: start;

            .text {
                margin-top: 10rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 27rpx;
                color: #121212;
                line-height: 38rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }

        .status-bar-image {
            padding: 2rpx 30rpx 0 38rpx;
            background: #f6f6f6;
            display: flex;
            align-items: center;

            .left-bar {
                .back_bar {
                    padding-right: 20rpx;

                    img,
                    image {
                        height: 50rpx;
                    }
                }
            }

            .title-image {
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202502/720574-1740109971236.png') no-repeat;
                background-size: cover;
                width: 212rpx;
                height: 29.3rpx;
            }

            .text {
                font-size: 25rpx;
            }

            text {
                margin-left: 14rpx;
                font-family: MiSans;
                font-size: 40rpx;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0;
                font-variation-settings: 'opsz' auto;
                color: #040000;
            }
        }

        .status-bar {
            width: 100%;
        }
    }
}

.community-search,
.community-publish {
    margin-left: 30rpx;

    img {
        display: block;
        height: 46rpx;
        width: 46rpx;
    }
}

.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}

.u-share-weixin {
    background-color: #fff;
    height: 748rpx;
    width: 640rpx;
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    padding: 56rpx 48rpx 40rpx 48rpx;
    gap: 48rpx;
    overflow: hidden;
    .u-share-weixin-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #121212;
        position: relative;
        text-align: center;
        .u-share-weixin-title-close {
            position: absolute;
            right: 0;
            top: 0;
            width: 48rpx;
            height: 48rpx;
        }
    }
    .u-share-weixin-content {
        background-color: #ffffff;
        .u-share-weixin-content-avatar {
            width: 204rpx;
            height: 204rpx;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;
            margin-top: 0rpx;
            img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
            }
        }
        .u-share-weixin-content-password {
            font-size: 26rpx;
            font-weight: 500;
            color: #555555;
            height: 72rpx;
            background-color: #F6F6F6;
            border-radius: 16rpx;
            text-align: center;
            margin-top: 64rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 16rpx 32rpx;
        }
        .u-share-weixin-content-description {
            font-size: 26rpx;
            font-weight: 500;
            color: #777777;
            text-align: center;
            margin-top: 36rpx;
        }
        .u-share-weixin-content-button {
            width: 544rpx;
            height: 100rpx;
            margin: 0 auto;
            margin-top: 48rpx;
            img {
                height: 100%;
                width: 100%;
            }
        }
    }
}