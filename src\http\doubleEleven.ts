import http from './index';

/*
 * 设备控制
 * @returns
 */
export const activityCheckDetail = () => http.post('main/activity/activity-check-detail');

/*
 * 会员中心-活动打卡
 * @returns
 */
export const activityCheckInIn = () => http.post('main/member-center/check-in-in');

/*
 * 抽奖活动-活动详情
 * @returns
 */
export const activityList = (param) => http.post('main/draw/activity-list', param);

/*
 * 抽奖活动-获取抽奖次数
 * @returns
 */
export const getDrawTimes = (param) => http.post('main/draw/draw-times', param);

/*
 * 抽奖活动-每日赠送抽奖次数
 * @returns
 */
export const drawDoActivityTask = (param) => http.post('main/draw/do-activity-task', param);

/*
 * 抽奖活动-每日赠送抽奖次数
 * @returns
 */
export const drawTaskList = (param) => http.post('main/draw/task-list', param);

/*
 * 抽奖活动-中奖记录
 * @returns
 */
export const drawRecord = (param) => http.post('main/draw/draw-record', param);
export const drawRecordV2 = (param) => http.post('main/draw/draw-record-v2', param);

/*
 * 抽奖活动-抽奖
 * @returns
 */
export const doDrawActivity = (param) => http.post('main/draw/do-draw-activity', param);

/*
 * 活动-双11等活动规则
 * @returns
 */
export const activityRules = () => http.post('main/activity/rules');

/*
 * 活动-双11等活动规则
 * @returns
 */
export const recommendGoods = () => http.post('main/activity/recommend-goods');

// 拼团活动-校验资格
export const verifyQualification = () => http.post('main/group-purchase/verify-qualification');

// 拼团活动-获取按钮状态
export const getPopup = () => http.post('main/group-purchase/get-popup');

// 自定义配置活动
export const getThemeActivity = (param) => http.post('main/member-activity/info', param)

// 自定义配置活动-打卡
export const checkIn = (param) => http.post('main/member-center/check-in-in', param)
// 自定义配置活动-打卡
export const getStock = (param) => http.post('main/draw/stock', param)
/*
 * 抽奖活动-抽奖
 * @returns
 */
export const doNewDrawActivity = (param) => http.post('main/draw/do-new-draw-activity', param);
/*
 * 抽奖活动-抽奖
 * @returns
 */
export const getIsStart = (param) => http.post('main/member-activity/is-start', {});

/*
 * 抽奖活动-中奖记录
 * @returns
 */
export const drawNewRecord = (param) => http.post('main/draw/new-draw-record', param);

/*
 * 校验用户是不是新用户
 * @returns
 */
export const checkNewUser = () => http.post('main/draw/check-new-user');

export const submitgolddraw = (data) => http.post('main/draw/submit-gold-draw', data)
