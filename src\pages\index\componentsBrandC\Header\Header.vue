<template>
    <view class="header-container" :class="{ 'header-hidden': !isVisible }" :style="headerStyle">
        <view class="header-box">
            <!-- #ifdef H5 || APP-PLUS -->

            <!-- 品牌logo -->
             <TypeIcon></TypeIcon>
             <view class="u-flex search-box">
                <MemberLevel v-if="isMemberLevel"></MemberLevel>
                <Search v-if="isSearch"></Search>
                <Car v-if="isCar"></Car>
            </view>
            <!-- #endif -->
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import TypeIcon from './components/TypeIcon.vue';
// #ifdef H5
import Search from './components/Search.vue';
import MemberLevel from './components/MemberLevel.vue';

// #endif

@Component({
    components: {
        // #ifdef H5
        Search,
        MemberLevel,
        // #endif
        TypeIcon,
    },
})
export default class Header extends Vue {
    @Prop({ type: Boolean, default: true })
    readonly isHeaderTransparent!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isCar!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isBrand!: Boolean;

    @Prop({ type: Boolean, default: false })
    readonly isSearch!: Boolean;

    @Prop({ type: Boolean, default: true })
    readonly isShowCoin!: Boolean;

     @Prop({ type: Boolean, default: false })
    readonly isMemberLevel!: Boolean;

    @Prop({ type: String, default: '255, 255, 255, 1' })
    readonly backgroundColor!: String;

    @Prop({ type: Boolean, default: true })
    readonly isVisible!: Boolean;

    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    public style: Object = { height: '212rpx', 'padding-top': '88rpx' };

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get headerStyle(): any {
        return {
            'padding-top': `${this.statusBarHeight}rpx`,
            backgroundColor: this.backgroundColor
        };
    }

    moreShow() {
        this.$emit('moreShow')
    }
}
</script>
<style lang="scss" scoped>
// 阴影颜色
$shadow-color: rgba(0, 0, 0, 0.20);

.header-container {
    width: 100%;
    padding: 0 16rpx 0rpx 32rpx;
    // z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    // #ifdef H5 || APP-PLUS
    backdrop-filter: blur(192rpx);
    // #endif
    transition: transform 0.3s ease-in-out;
    transform: translateY(0);

    &.header-hidden {
        transform: translateY(-100%);
    }

    .header-box {
        height: 74rpx;
        position: relative;
        // background-color: #ffffff;
        top: 0;
        right: 0;
        @include flex(row, space-between, center, none);
        .search-box{
          background: #F7F7F7;
          border-radius: 32rpx;
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        // padding: 12rpx 0;
        .gap-18 {
            gap:36rpx;
        }
    }
}
</style>
