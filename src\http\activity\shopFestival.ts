import http from '../index';
// 2025 年货节
/*
 * 设备控制
 * @returns
 */
export const activityCheckDetail = () => http.post('main/activity/activity-check-detail');

/*
 * 会员中心-活动打卡
 * @returns
 */
export const activityCheckInIn = (param?: any) => http.post('main/member-center/check-in-in', param);

/*
 * 获得我的券码列表
 * @returns
 */
export const activityMyCouponCode = (param) => http.post('main/member-activity/get-lucky-signs', param);

/*
 * 设置是否签到提醒
 * @returns
 */
export const activitySubscribe = (param) => http.post('main/my/subscribe', param);

/*
 * 获取签到提醒信息
 * @returns
 */
export const activitySubscribeInfo = (param) => http.post('main/my/subscribe-info', param);

/*
 * 抽奖活动-活动详情
 * @returns
 */
export const activityList = (param) => http.post('main/draw/activity-list', param);

/*
 * 抽奖活动-获取抽奖次数
 * @returns
 */
export const getDrawTimes = (param) => http.post('main/draw/draw-times', param);

/*
 * 抽奖活动-每日赠送抽奖次数
 * @returns
 */
export const drawDoActivityTask = (param) => http.post('main/draw/do-activity-task', param);

/*
 * 抽奖活动-每日赠送抽奖次数
 * @returns
 */
export const drawTaskList = (param) => http.post('main/draw/task-list', param);

/*
 * 抽奖活动-中奖记录
 * @returns
 */
export const drawRecord = (param) => http.post('main/draw/draw-record', param);

/*
 * 抽奖活动-抽奖
 * @returns
 */
export const doDrawActivity = (param) => http.post('main/draw/do-draw-activity', param);

/*
 * 活动-双11等活动规则
 * @returns
 */
export const activityRules = () => http.post('main/activity/rules');

/*
 * 活动-双11等活动规则
 * @returns
 */
export const recommendGoods = () => http.post('main/activity/recommend-goods');

// 拼团活动-校验资格
export const verifyQualification = () => http.post('main/group-purchase/verify-qualification');

// 拼团活动-获取按钮状态
export const getPopup = () => http.post('main/group-purchase/get-popup');

/*
* 我的机器
* @returns
*/
export const checkMyMachine = () => http.post('main/activity/check-my-machine');

/*
* 免息商品 配件商品 积分商品 秒杀商品
* @returns
*/
export const allGoodsInfo = () => http.post('main/activity/recommend-goods');

