<template>
  <!-- 用户和服务区域 -->
  <view class="user-service-section">
    <view class="user-card" @click="handleUserProfile">
      <view class="avatar">
        <image class="avatar-img" :src="levelIcon" mode="widthFix" />
      </view>
      <view class="user-info">
        <text class="member-type">{{ levlName }}</text>
        <!-- <text class="member-type">会员等级{{ level }}</text> -->
        <text class="member-desc">已达到</text>
      </view>
    </view>
    <view class="service-card" @click="handleCustomerService">
      <view class="avatar">
        <image class="avatar-img" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b57c357a973590010696.png" mode="widthFix"></image>
      </view>
      <view class="service-info">
        <text class="service-title">客服</text>
        <text class="service-desc">24h在线</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import MemberLevel from './MemberLevel.vue';
import { Vue, Component } from 'vue-property-decorator';
import { VIPModule } from '@/store/modules/vip';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';

@Component({
  components: {
    MemberLevel
  },
})
export default class InfoCard extends Vue {
  data() {
    return {
      // 组件数据
    }
  }

  // 用户资料
  handleUserProfile() {
      Utils.navigateTo('/pages/vipCenter/vipCenter');
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
      });
    // uni.navigateTo({
    //   url: '/pagesA/userInfo/userInfo'
    // })
  }

  // 客服
  handleCustomerService() {
    Utils.decryptContact({}, BuType.BuType_NONE);
  }

  // 会员等级
  get levlName(): String {
      return VIPModule.basicInfo.level.name || '';
  }

  get level(): String {
      return VIPModule.basicInfo.level.level || '';
  }

  get levelIcon(): string {
      const level:any = VIPModule.basicInfo.level.level;
      // 定义等级与图片的映射关系
      const levelIcons: { [key: string]: string } = {
        v1: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687637a03aa022400011297.png',
        v4: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763749bba6d7690013034.png',
        v2: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763765cf8708500011384.png',
        v5: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763782964976160022687.png',
        v3: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763725e57ce9400010630.png',
      };

      // 默认使用v1等级的图片
      return levelIcons[level] || '';
  }

  // 根据等级获取对应的文本颜色
  get levelColor(): string {
      const level:any = VIPModule.basicInfo.level.level;
      const levelColors: { [key: string]: string } = {
          v1: '#CA7538', // 铜牌颜色（原颜色）
          v2: '#9E9E9E', // 银牌颜色（灰色系）
          v3: '#57412B', // 金牌颜色（金色系）
          v4: '#325F83', // 白金颜色（银色系）
          v5: '#DBBB71' // 钻石颜色（浅蓝色系）
      };

      return levelColors[level] || levelColors.v1;
  }

  handlePointsClick() {
    Utils.navigateTo('/pages/vipCenter/vipCenter');
    Utils.logTrace({
        module: Constants.LOG_TRACE_MODULE_DREAME,
        event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
    });
  }

  // 组件创建时调用获取会员信息
  created() {
    this.fetchVIPInfo();
  }

  // 监听组件显示事件
  onShow() {
    this.fetchVIPInfo();
  }

  // 封装获取会员信息的方法
  async fetchVIPInfo() {
    try {
      await VIPModule.getBasicInfo();
    } catch (error) {
      console.error('获取会员信息失败:', error);
      // 可以添加错误提示或重试逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.user-service-section {
  display: flex;
  justify-content: space-between;
  gap: 26rpx;
  padding: 0 24rpx;
  margin-bottom: 30rpx;
  margin-top: 40rpx;
  box-sizing: border-box;

  .user-card,
  .service-card {
    // flex: 1;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 40rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    gap: 20rpx;
    width: 340rpx;
    height: 158rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .avatar {
      width: 84rpx;
      height: 84rpx;
      border-radius: 50%;
      overflow: hidden;

      .avatar-img {
        width: 84rpx;
        height: 84rpx;
      }
    }

    .user-info,
    .service-info {
      flex: 1;

      .member-type,
      .service-title {
        font-size: 32rpx;
        color: #fff;
        display: block;
        margin-bottom: 4rpx;
      }

      .member-desc,
      .service-desc {
        font-size: 28rpx;
        color: #f0f0f0;
        display: block;
      }
    }
  }
}
</style>
