<template>
    <view class="jiemi-zhuanqu">
        <!-- 骨架屏加载状态 -->
        <view v-if="isLoading" class="skeleton-container">
            <view class="flex-container">
                <view class="column right-column" v-for="items in 2" :key="items">
                    <view class="skeleton-item" v-for="item in 6" :key="item">
                        <!-- 图片骨架屏 -->
                        <u-skeleton :animate="true" :title="false" :rowsHeight="[218]" :loading="true" rows="1"
                            :rowsWidth="['100%']"></u-skeleton>
                        <!-- 标题骨架屏 -->
                        <view style="margin-top: 22rpx; padding: 0 23rpx 0 21rpx">
                            <u-skeleton :animate="true" :title="false" :rowsHeight="[38]" :loading="true" rows="1"
                                :rowsWidth="['80%']"></u-skeleton>
                        </view>
                        <!-- 底部信息骨架屏 -->
                        <view style="
                                                            margin-top: 20rpx;
                                                            padding: 0 23rpx 0 21rpx;
                                                            display: flex;
                                                            justify-content: space-between;
                                                            align-items: center;
                                                        ">
                            <view style="display: flex; align-items: center">
                                <!-- 头像骨架屏 -->
                                <u-skeleton :animate="true" :title="false" :rowsHeight="[38]" :loading="true" rows="1"
                                    :rowsWidth="[38]" style="border-radius: 50%; margin-right: 16rpx"></u-skeleton>
                                <!-- 用户名骨架屏 -->
                                <u-skeleton :animate="true" :title="false" :rowsHeight="[40]" :loading="true" rows="1"
                                    :rowsWidth="[60]" style="margin-right: 16rpx"></u-skeleton>
                            </view>
                            <!-- 点赞数骨架屏 -->
                            <u-skeleton :animate="true" :title="false" :rowsHeight="[40]" :loading="true" rows="1"
                                :rowsWidth="[40]"></u-skeleton>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 暂无数据状态 -->
        <DefaultPage v-else-if="!productIdList.length" :show="true" mode="page"
            icon="https://wpm-cdn.dreame.tech/images/202412/674ef25ad58d98751106040.png" tip="暂无内容" imgWidth="200"
            imgHeight="200" imgMbottom="40" />
        <!-- 数据列表 -->
        <view v-else class="product-list">
            <view class="product-list-item" v-for="(item, index) in productIdList" :key="index">
                <view style="position: relative" @click="viewDetail(item)">
                    <image class="comment_item_imgs_item" mode="aspectFill" :src="item.cover_image" />
                    <view class="comment_item_video_icon">
                        <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a48214fe783270014821.png"
                            alt="" />
                    </view>
                </view>
                <view class="product-list-item-title">{{ item.title }}</view>
                <view style="
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: space-between;
                                                ">
                    <view class="comment_item_top_left" @click="jumpHome(item)">
                        <view class="avatar">
                            <img :src="item.author_avatar" mode="aspectFill" class="comment_item_top_left_img" />
                            <img v-if="item.user_avatar_icon" :src="item.user_avatar_icon" class="comment_item_top_left_tag" />
                        </view>
                        <view class="comment_item_top_left_name u-line-1">{{
                            item.author
                        }}</view>
                    </view>
                    <view class="oper_item" @click="handleOperFlow(item, 'praise')"><img :src="!item.is_praise
                        ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68674e1ac77418170019079.png'
                        : 'https://wpm-cdn.dreame.tech/images/2024010/449434-1730107172922.png'
                        " />
                        <view class="oper_num">{{ clicks(item.praise, 0) }}</view>
                    </view>
                </view>
            </view>
        </view>
        <custom-toast ref="customToast" />
    </view>
</template>

<script lang="ts">
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';
import {
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
    detailContent
} from '@/http/requestGo/community';
import { CheckAppJump } from '@/common/decorators';
@Component({
    components: {
        DefaultPage
    },
    filters: {
        clicks(click) {
            if (Number(click) < 1000) {
                const item = click;
                return item;
            } else {
                const item = (Number(click) / 1000).toFixed(1);
                return item + 'k';
            }
        },
    },
})
export default class VideoContent extends Vue {
    @Prop({ type: Boolean, default: false })
    isLoading: boolean;

    @Prop({ type: Array, default: () => [] })
    productIdList: any[]

    public content_id: string = '';
    public officialAccountsId: any = process.env.VUE_APP_VIDEO_LIVE_STREAMING_ID; // 公众号id uat环境2 线上环境 37

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    mounted() {
    }

    async contentIdDeatail() {
        if (this.content_id) {
            const res = await detailContent({ content_id: this.content_id });
            if (res.success) {
                this.productIdList.map(item => {
                    if (item.content_id == this.content_id) {
                        item.praise = res.data.praise;
                    }
                })
            }
        }
    }

    clicks(val, index) {
        if (Number(val) === 0) {
            return ['点赞', '收藏', '评论'][index];
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    jumpHome(item) {
        Utils.jumpPersonHome(item.creator);
    }

    viewDetail(item) {
        const { content_id = '', type } = item;
        this.content_id = item.content_id;
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this
                    .officialAccountsId}`,
        );
    }

    async handleOper(item, type) {
        let flag, res;
        // const defaultPraise = item.is_praise;
        if (item[type === 'praise' ? 'is_praise' : 'is_favorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = flag;
            item[type === 'praise' ? 'praise' : 'favorite'] += +flag ? 1 : -1;
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = res.data.id;
        }
        // this.$emit('praise', id);
    }

    @CheckAppJump()
    async handleOperFlow(item, type) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                await this.handleOper(item, type);
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
        Utils.reportEvent('give_like', { title: item.title });
        // #ifdef H5
        await this.handleOper(item, type);
        // #endif
    }
}
</script>
<style lang="scss" scoped>
.jiemi-zhuanqu {
    overflow: auto;
    padding: 0rpx 0rpx 0;
    background-color: rgb(245, 245, 245);

    .skeleton-container {
        padding: 0rpx 24rpx;

        .flex-container {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .column {
            width: calc(50% - 9rpx);
        }

        .right-column {
            padding: 0;
        }

        .skeleton-item {
            background-color: #fff;
            padding-bottom: 30rpx;
            border-radius: 8rpx;
            margin-bottom: 24rpx;
            overflow: hidden;
        }
    }

    .product-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 0rpx 24rpx;
        margin-bottom: 24rpx;

        .product-list-item {
            background-color: #fff;
            display: inline-block;
            width: calc(50% - 9rpx);
            padding-bottom: 30rpx;
            overflow: hidden;
            border-radius: 8rpx;
            margin-bottom: 24rpx;

            .comment_item_imgs_item {
                width: 100%;
                height: 420rpx;
                object-fit: cover;
                object-position: center;
            }

            .comment_item_video_icon {
                position: absolute;
                top: 22rpx;
                right: 18rpx;
                width: 48rpx;
                height: 48rpx;

                image,
                img {
                    width: 48rpx;
                    height: 48rpx;
                }
            }

            .live-item {
                position: absolute;
                top: 22rpx;
                right: 20rpx;
                object-fit: cover;
                object-position: center;

                image,
                img {
                    width: 108rpx;
                    height: 46rpx;
                }
            }

            .product-list-item-title {
                padding: 0rpx 23rpx 0rpx 21rpx;
                padding-top: 22rpx;
                font-size: 27rpx;
                color: #404040;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: normal;
                text-align: left;
                font-style: normal;
                font-weight: 500;
                margin-bottom: 20rpx;
            }

            .comment_item_top_left {
                padding-left: 23rpx;
                display: flex;
                align-items: center;
                position: relative;

                .avatar {
                    height: 38rpx;
                    position: relative;
                }

                img,
                image {
                    object-fit: cover;
                    width: 38rpx;
                    height: 38rpx;
                    border-radius: 50%;
                    vertical-align: top;
                }

                .comment_item_top_left_tag {
                    position: absolute;
                    width: 18rpx;
                    height: 18rpx;
                    left: 24rpx;
                    bottom: 0;
                }

                ::v-deep .custom_imag {
                    object-fit: contain;
                    border-radius: 0rpx !important;
                }

                &_name {
                    margin-left: 15rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 23rpx;
                    color: #777777;
                    line-height: 40rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    max-width: 380rpx;
                }
            }

            .oper_item {
                display: flex;
                align-items: center;

                &:not(:last-child) {
                    padding-right: 32rpx;
                }

                img,
                image {
                    width: 31rpx;
                    height: 31rpx;
                    margin-right: 8rpx;
                    object-fit: contain;
                }

                .oper_num {
                    min-width: 25px;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 23rpx;
                    color: #777777;
                    line-height: 40rpx;
                    text-align: center;
                    font-style: normal;
                    text-transform: none;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .my-waterfall {
        z-index: 1;

        .community_content {
            min-height: calc(100vh - 795rpx);
            padding: 0rpx 0rpx 70rpx;
            background-color: rgb(245, 245, 245);
        }
    }
}
</style>
