# 图片压缩功能使用指南

## 概述

为了提高应用性能，减少图片加载时间和流量消耗，我们为 `LazyImage` 组件添加了智能图片压缩功能。该功能支持多种 CDN 和 OSS 服务，能够自动识别图片源并应用相应的压缩参数。

## 功能特性

- 🚀 **智能压缩**: 自动识别 OSS、CDN 等图片源
- 📱 **自适应优化**: 根据设备屏幕尺寸调整压缩参数
- 🎯 **格式优化**: 支持 WebP、JPEG、PNG 格式转换
- ⚡ **性能提升**: 显著减少图片体积和加载时间
- 🔧 **灵活配置**: 支持自定义压缩参数

## 使用方法

### 基础用法

```vue
<template>
  <LazyImage
    :src="imageUrl"
    :enableCompression="true"
    :quality="80"
    :maxWidth="300"
    :maxHeight="300"
    format="webp"
  />
</template>
```

### 高级用法

```vue
<template>
  <LazyImage
    :src="imageUrl"
    :enableCompression="true"
    :quality="75"
    :maxWidth="200"
    :maxHeight="200"
    format="webp"
    mode="aspectFill"
    :customStyle="{ borderRadius: '8rpx' }"
  />
</template>
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enableCompression` | Boolean | `false` | 是否启用压缩 |
| `quality` | Number | `80` | 图片质量 (1-100) |
| `maxWidth` | Number | `0` | 最大宽度 (0表示不限制) |
| `maxHeight` | Number | `0` | 最大高度 (0表示不限制) |
| `format` | String | `'webp'` | 输出格式 ('webp' \| 'jpeg' \| 'png') |

## 压缩策略

### OSS 图片压缩

对于阿里云 OSS 图片，使用 `x-oss-process` 参数进行压缩：

```
原始URL: https://example.oss-cn-beijing.aliyuncs.com/image.jpg
压缩后: https://example.oss-cn-beijing.aliyuncs.com/image.jpg?x-oss-process=image/resize,w_300,h_300/quality,q_80/format,webp
```

### CDN 图片压缩

对于其他 CDN 图片，使用标准参数进行压缩：

```
原始URL: https://cdn.example.com/image.jpg
压缩后: https://cdn.example.com/image.jpg?w=300&h=300&q=80&fm=webp
```

## 最佳实践

### 1. 评论图片压缩

```vue
<LazyImage
  mode="aspectFill"
  class="comment_item_imgs_item"
  :src="img.url"
  :enableCompression="true"
  :quality="75"
  :maxWidth="200"
  :maxHeight="200"
  format="webp"
/>
```

### 2. 商品图片压缩

```vue
<LazyImage
  :src="product.cover_image"
  :enableCompression="true"
  :quality="85"
  :maxWidth="400"
  :maxHeight="400"
  format="webp"
  mode="aspectFill"
/>
```

### 3. 头像图片压缩

```vue
<LazyImage
  :src="user.avatar"
  :enableCompression="true"
  :quality="90"
  :maxWidth="100"
  :maxHeight="100"
  format="webp"
  mode="aspectFill"
/>
```

## 性能优化建议

### 1. 根据使用场景调整参数

- **缩略图**: `quality: 70-75`, `maxWidth/maxHeight: 150-200`
- **商品图**: `quality: 80-85`, `maxWidth/maxHeight: 300-400`
- **高清图**: `quality: 90-95`, `maxWidth/maxHeight: 500+`

### 2. 网络自适应

```javascript
import { getAdaptiveCompressionOptions } from '@/utils/imageCompression';

// 根据网络状况获取压缩参数
const options = getAdaptiveCompressionOptions();
```

### 3. 预加载优化

```javascript
import { preloadCompressedImage } from '@/utils/imageCompression';

// 预加载压缩后的图片
await preloadCompressedImage(imageUrl, {
  quality: 80,
  maxWidth: 300,
  format: 'webp'
});
```

## 注意事项

1. **兼容性**: WebP 格式在较老的浏览器中可能不支持，建议提供降级方案
2. **缓存**: 压缩后的图片 URL 会被浏览器缓存，提高后续访问速度
3. **质量平衡**: 在文件大小和图片质量之间找到平衡点
4. **网络状况**: 在慢速网络环境下可以进一步降低质量参数

## 工具函数

### compressImageUrl

```javascript
import { compressImageUrl } from '@/utils/imageCompression';

const compressedUrl = compressImageUrl(originalUrl, {
  quality: 80,
  maxWidth: 300,
  maxHeight: 300,
  format: 'webp'
});
```

### getAdaptiveCompressionOptions

```javascript
import { getAdaptiveCompressionOptions } from '@/utils/imageCompression';

const options = getAdaptiveCompressionOptions();
// 根据设备屏幕尺寸返回合适的压缩参数
```

## 示例项目

完整的示例代码可以参考以下文件：

- `src/components/LazyImage/LazyImage.vue` - 组件实现
- `src/utils/imageCompression.ts` - 工具函数
- `src/pages/contents/components/community/components/comment.vue` - 使用示例 