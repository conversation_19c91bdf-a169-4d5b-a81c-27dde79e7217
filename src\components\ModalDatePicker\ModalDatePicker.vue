<template>
    <view>
        <CustomModal :show="showDatePicker" width="615rpx" :showCancelButton="true" @close="close" :title="title" :confirmText = "confirmText"
            @cancel="cancel" @confirm="handleDatePickerConfirm">
            <picker-view class="birth_picker" :value="currentValue" indicator-class="indicator" @change="bindChange">
                <picker-view-column class="year">
                    <view class="item"
                        :style="{ fontWeight: year === item ? 500 : 400, fontSize: year === item ? '46rpx' : '36rpx' }"
                        v-for="(item, index) in years" :key="index">{{ item }}</view>
                </picker-view-column>
                <picker-view-column class="month">
                    <view class="item"
                        :style="{ fontWeight: month === item ? 500 : 400, fontSize: month === item ? '46rpx' : '36rpx' }"
                        v-for="(item, index) in months" :key="index">{{ item }}</view>
                </picker-view-column>
                <picker-view-column class="day">
                    <view class="item"
                        :style="{ fontWeight: day === item ? 500 : 400, fontSize: day === item ? '46rpx' : '36rpx' }"
                        v-for="(item, index) in days" :key="index">{{ item }}</view>
                </picker-view-column>
            </picker-view>
        </CustomModal>
    </view>
</template>
<script>
export default {
    name: 'ModalDatePicker',
    props: {
        showDatePicker: {
            type: Boolean,
            default: () => false,
        },
        time: {
            type: Array,
            default: () => [new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()],
        },
        title: {
            type: String,
            default: () => '',
        },
        confirmText: {
            type: String,
            default: () => uni.$u.props.modal.confirmText,
        },
    },
    data() {
        return {
            days: [],
            months: [],
            years: [],
            year: new Date().getFullYear(), // 年
            month: new Date().getMonth() + 1, // 月
            day: new Date().getDate(), // 日
            date: new Date(),
            currentValue: [],
        };
    },
    async created() {
        await this.genDateList();
        this.getTimeList();
        this.$nextTick(() => {
            if (this.currentValue.length === 0) {
                this.year = this.time[0];
                this.month = this.time[1];
                this.day = this.time[2];
                const index_year = this.years.indexOf(this.year);
                const index_month = this.months.indexOf(this.month);
                const index_day = this.days.indexOf(this.day);
                this.currentValue = [index_year, index_month, index_day];
            }
        })
    },
    methods: {
        cancel() {
            this.$emit('cancel', false);
        },
        close() {
            this.$emit('close', false);
        },
        bindChange(e) {
            this.currentValue = e.detail.value;
            const val = e.detail.value;
            this.year = this.years[val[0]];
            this.month = this.months[val[1]];
            this.day = this.days[val[2]];
            this.getTimeList();
        },
        handleDatePickerConfirm() {
            const buy_time = +new Date(`${this.year}/${this.month}/${this.day}`);
            this.$emit('handleDatePickerConfirm', {
                buy_time: buy_time,
                year: this.year,
                month: this.month,
                day: this.day,
            });
        },
        async genDateList() {
            for (let i = 1960; i <= this.date.getFullYear(); i++) {
                this.years.push(i);
            }
            for (let i = 1; i <= 12; i++) {
                this.months.push(i);
            }
            for (let i = 1; i <= 31; i++) {
                this.days.push(i);
            }
        },
        getTimeList() {
            // 将选择的年月日变为number形式，便于比较之用
            const y = parseInt(this.year);
            const m = parseInt(this.month);

            // 选择不同月份显示的天数不同
            if (m == 1 || m == 3 || m == 5 || m == 7 || m == 8 || m == 10 || m == 12) {
                if (this.days.length != 31) {
                    this.days = [];
                    for (let i = 1; i <= 31; i++) {
                        this.days.push(i);
                    }
                }
            } else if (m == 4 || m == 6 || m == 9 || m == 11) {
                if (this.days.length != 30) {
                    this.days = [];
                    for (let i = 1; i <= 30; i++) {
                        this.days.push(i);
                    }
                }
            } else if (m == 2) {
                if ((y % 4 == 0 && y % 100 != 0) || y % 400 == 0) {
                    // 闰年
                    if (this.days.length != 29) {
                        this.days = [];
                        for (let i = 1; i <= 29; i++) {
                            this.days.push(i);
                        }
                    }
                } else {
                    // 平年
                    if (this.days.length != 28) {
                        this.days = [];
                        for (let i = 1; i <= 28; i++) {
                            this.days.push(i);
                        }
                    }
                }
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.birth_picker {
    width: 100%;
    height: 360rpx;
    position: relative;

    .item {
        font-size: 46rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #1d1e20;
        line-height: 86rpx;
        text-align: center;
    }
}

::v-deep .indicator {
    background: #f6f6f6;
    z-index: 0;

    &::before,
    &::after {
        border: none !important;
    }

    &::after {
        display: block;
        position: absolute;
        height: 28rpx;
        width: 28rpx;
        top: 100%;
        right: 0;
        bottom: 0;
        left: 100%;
        color: rgba(29, 30, 32, 0.4);
        font-size: 28rpx;
        font-weight: 500;
    }
}

.year {
    ::v-deep .indicator {
        border-radius: 43rpx 0 0 43rpx;
        position: relative;
        height: 86rpx;

        &::before {
            content: '年';
            color: rgba(29, 30, 32, 0.4);
            font-size: 28rpx;
            line-height: 38rpx;
            top: 30rpx;
            left: 86%;
            transform: translate(0, -100%);
        }
    }
}

.month {
    ::v-deep .indicator {
        position: relative;
        height: 86rpx;

        &::before {
            content: '月';
            color: rgba(29, 30, 32, 0.4);
            font-size: 28rpx;
            line-height: 38rpx;
            top: 30rpx;
            left: 68%;
            transform: translate(0, -100%);
        }
    }
}

.day {
    ::v-deep .indicator {
        border-radius: 0 43rpx 43rpx 0;
        position: relative;
        height: 86rpx;

        &::before {
            content: '日';
            color: rgba(29, 30, 32, 0.4);
            font-size: 28rpx;
            line-height: 38rpx;
            top: 30rpx;
            left: 68%;
            transform: translate(0, -100%);
        }
    }
}
</style>
