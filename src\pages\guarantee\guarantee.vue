<template>
    <view class="view_container u-flex u-flex-col u-col-center u-row-left" :style="{ 'padding-top': pagePaddingTop + 'rpx' }">
        <custom-bar :title="title"></custom-bar>

        <view class="guarantee-card" v-for="item in Products" :key="item.id">
            <view class="top-row u-flex u-col-center u-row-between">
                <image class="logo" src="https://wpm-cdn.dreame.tech/images/202203/621ddc4a6d2fc4471393142.png" mode="" />
                <view class="btn-after-sale u-flex u-col-center u-row-center" @tap="handleAfterSaleTap">
                    <image src="https://wpm-cdn.dreame.tech/images/202203/621ddd845d1383811871873.png" mode="" />
                    <text>售后服务</text>
                </view>
            </view>

            <view class="main u-flex u-col-top u-row-left">
                <image class="prod-img" :src="item.image" mode="" />
                <view class="prod-info u-flex-1 u-flex u-flex-col u-col-top u-row-left">
                    <view class="row-1"> 免费延长保修期一年 </view>
                    <view class="row-2">产品名称: {{ item.name }}</view>
                    <view class="row-2">注册时间: {{ item.create_time | date('yyyy.mm.dd') }}</view>
                </view>
            </view>
        </view>

        <DefaultPage :show="!Products.length" mode="page"
            icon="https://wpm-cdn.dreame.tech/images/202203/621df39ba806a6881392764.png" tip="您还没有已绑定的产品"></DefaultPage>
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Constants from '@/common/Constants';
import { ServeModule } from '@/store/modules/serve';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import { AppModule } from '@/store/modules/app';

@Component({
    components: {
        DefaultPage,
    },
})
export default class Points extends Vue {
    protected title: String = '延保卡';

    // 已注册产品列表
    get Products() {
        return ServeModule.Products;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    async onLoad() {
        if (!ServeModule.isPullProducts) {
            ServeModule.asyncProducts({ is_act: 0 });
        }
    }

    handleAfterSaleTap() {
        uni.navigateToMiniProgram({
            appId: Constants.MINIPROGRAM.AFTER_SALE,
        });
    }
}
</script>
<style lang="scss">
.view_container {
    width: 100vw;
    min-height: 100vh;
    padding: 26rpx;
    background-color: #faf8f5;

    .guarantee-card {
        width: 698rpx;
        background: #ffffff;
        border-radius: 8rpx;
        margin-bottom: 16rpx;
        background-image: url('https://wpm-cdn.dreame.tech/images/202203/621ddd7764e3d4131574757.png');
        background-repeat: no-repeat;
        background-size: 170rpx 198rpx;
        background-position: right bottom;

        .top-row {
            width: 100%;
            height: 69rpx;
            font-size: 30rpx;
            border-bottom: 1rpx solid rgba(240, 242, 243, 1);
            padding: 0 22rpx;

            .logo {
                width: 130rpx;
                height: 30rpx;
            }

            .btn-after-sale {
                font-size: 26rpx;
                color: rgba(34, 34, 34, 0.5);
                line-height: 36rpx;

                image {
                    width: 28rpx;
                    height: 28rpx;
                    margin-right: 2rpx;
                }
            }
        }

        .main {
            width: 100%;
            height: 220rpx;
            padding: 18rpx 20rpx 22rpx;

            .prod-img {
                width: 180rpx;
                height: 180rpx;
                margin-right: 22rpx;
            }

            .prod-info {
                width: 100%;
                padding-top: 18rpx;

                .row-1 {
                    font-size: 34rpx;
                    color: #aa6300;
                    line-height: 48rpx;
                    margin-bottom: 12rpx;
                }

                .row-2 {
                    font-size: 24rpx;
                    color: #b39874;
                    line-height: 34rpx;
                    margin-bottom: 10rpx;
                }

                .row-1,
                .row-2 {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}
</style>
