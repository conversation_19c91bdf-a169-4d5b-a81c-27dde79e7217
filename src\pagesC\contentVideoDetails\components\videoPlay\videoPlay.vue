<template>
    <view class="video-container" :style="{ paddingBottom: `${pagePaddingBottom}rpx` }">
        <swiper
            class="swiper-box"
            :style="{ height: `100vh` }"
            :vertical="true"
            :current="current"
            :disable-touch="!$isInApp"
            @change="swiperChange"
            easing-function="linear"
            duration="150"
            @animationfinish="animationfinish"
            @touchstart="touchstart"
        >
            <swiper-item v-for="(item, i) in list" :key="item.content_id" :id="item.content_id" class="swiper-item">
                <!-- #ifdef H5 -->
                <view v-show="bottomShow" class="oper_more" :style="{ top: `${statusBarHeight}rpx` }">
                    <!-- <button
                        open-type="share"
                        plain
                        class="share-btn"
                        :data-id="item.content_id"
                        @click="handleShare(item)"
                    >
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686779b58877a5590011292.png"
                        ></image>
                    </button> -->

                    <image
                        src="https://wpm-cdn.dreame.tech/images/2024010/685131-1728896673880.png"
                        @click="() => handleReport('detail', item.creator)"
                    ></image>
                </view>

                <view class="oprate-list">
                    <!-- 关注 -->
                    <view
                        v-if="user_id != item.creator"
                        class="relation-btn"
                        :class="{ follow: [1, 2].includes(item.relationStatus) }"
                    >
                        <img :src="item.author_avatar" class="avatar" @click="handleUserAvater(item, i)" />

                        <img
                            v-if="item.relationStatus === 0"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/464685-1752742689088.png"
                            class="icon"
                            @click="handleUserFollow(item, i)"
                        />
                        <img
                            v-else
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/318522-1752742720483.png"
                            class="icon"
                            @click="handleUserFollow(item, i)"
                        />
                    </view>

                    <!-- 点赞 -->
                    <view class="like item" @click="handleOper(item, 'praise')">
                        <img
                            v-show="item.isPraise"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6892149f0cfcc0530010957.png"
                        />
                        <img
                            v-show="!item.isPraise"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689214821dc721220028543.png"
                        />
                        <text>{{ clicks(item.praiseNum, 0) }}</text>
                    </view>

                    <!-- 评论 -->
                    <view class="comment item" @click="handleOperComment(operType.Comment, false, item, false)">
                        <img
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689214b604d850200012291.png"
                        />
                        <text>{{ clicks(item.comment, 2) }}</text>
                    </view>

                    <!-- 收藏 -->
                    <view class="collect item" @click="handleOper(item, 'fav')">
                        <img
                            v-show="item.isFavorite"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689214e5730994710013512.png"
                        />
                        <img
                            v-show="!item.isFavorite"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689214d70eb5b0600010368.png"
                        />
                        <text>{{ clicks(item.favoriteNum, 1) }}</text>
                    </view>

                    <!-- 分享 -->
                    <view class="item">
                        <button
                            open-type="share"
                            plain
                            class="share-btn"
                            :data-id="item.content_id"
                            @click="handleShare(item)"
                        >
                            <image
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6892151b722674680010716.png"
                            >
                            </image>
                        </button>
                        <text>分享</text>
                    </view>
                </view>

                <!-- #endif -->
                <view
                    class="video_box"
                    :style="{
                        height: `calc(100vh - ${statusBarHeight}rpx - ${pagePaddingBottom}rpx)`,
                        marginBottom: `${pagePaddingBottom}rpx`,
                        marginTop: `${statusBarHeight}rpx`,
                    }"
                >
                    <view class="video_content" @click.stop="handleVideo(item)">
                        <VideoCom
                            ref="VideoComRef"
                            :commentHeight="commentHeight"
                            :videoSrc="item.url"
                            :videoPlay="item.videoPlay"
                            :videoIndex="item.videoIndex"
                        ></VideoCom>
                    </view>
                    <view v-show="!item.videoPlay" class="stop_icon" @click.stop="handleVideo(item)">
                        <image src="https://wpm-cdn.dreame.tech/images/2024010/020766-1728893184160.png"></image>
                    </view>
                    <view class="video_bottom">
                        <view class="video_bottom_info" :class="bottomShow ? 'video_bottom_info_show' : ''">
                            <view v-show="bottomShow" class="video_bottom_info_user">
                                <view class="video_bottom_info_user_detail">
                                    <view class="u-flex" @click="jumpPersonHome(item.creator)">
                                        <!-- <view class="avator_video">
                                            <img :src="item.author_avatar" />
                                            <img v-if="item.author_tag" class="avator_icon" :src="item.author_tag" />
                                        </view> -->
                                        <view class="u-line-1">{{ item.author_name }}</view>
                                        <img v-if="item.user_avatar_icon" class="avator_icon" :src="item.user_avatar_icon" />
                                    </view>
                                    <!-- <view
                                        v-if="user_id != item.creator"
                                        class="relation-btn"
                                        :class="{ follow: [1, 2].includes(item.relationStatus) }"
                                        @click="handleUserFollow(item, i)"
                                    >
                                        <img
                                            style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                            v-if="relationText(item.relationStatus) === '关注'"
                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                                        />
                                        <img
                                            style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                            v-if="relationText(item.relationStatus) === '已关注'"
                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png"
                                        />
                                        {{ relationText(item.relationStatus) }}</view
                                    > -->
                                    <view style="display: none">{{ data.author }}</view>
                                </view>
                                <view class="video_bottom_info_user_more">
                                    <!-- #ifdef MP-WEIXIN -->
                                    <button
                                        open-type="share"
                                        plain
                                        class="share-btn"
                                        :data-id="item.content_id"
                                        @click="handleShare(item)"
                                    >
                                        <image
                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686779b58877a5590011292.png"
                                        >
                                        </image>
                                        <text style="color: #404040; font-size: 28rpx; margin-left: 8rpx">分享</text>
                                    </button>
                                    <image
                                        src="https://wpm-cdn.dreame.tech/images/2024010/685131-1728896673880.png"
                                        @click="() => handleReport('detail', item.creator)"
                                    ></image>
                                    <!-- #endif -->
                                </view>
                            </view>
                            <view
                                class="video_bottom_info_text"
                                @click="handleOperComment(operType.Comment, true, item, false)"
                            >
                                <view
                                    class="video_bottom_info_text_title bold"
                                    :class="item.body ? 'u-line-1' : 'u-line-2'"
                                    :id="`text_${i}`"
                                    >{{ item.title }}
                                </view>
                                <view v-if="item.body" class="video_bottom_info_text_title u-line-1" :id="`text_${i}`">
                                    {{ item.body }}</view
                                >
                                <view v-if="item.moreLine" class="video_bottom_info_text_more"> 展开 </view>
                            </view>
                            <!-- 添加挂链 -->
                            <template v-if="item.goods_ids && item.goods_ids.length">
                                <CardLink :goodsIds="item.goods_ids" @showGoodsMask="showGoodsMask" />
                            </template>
                        </view>

                        <view class="video_bottom_comment" @click="openCommentInput">
                            <img
                                class="video_bottom_comment_icon"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68871548992156270010818.png"
                            />
                            <view class="video_bottom_comment_input" placeholder="说点什么...">说点什么</view>
                        </view>

                        <!-- <view class="video_bottom_oper">
                            <view class="video_bottom_oper_comment">
                                <image
                                    src="https://wpm-cdn.dreame.tech/images/2024010/405398-1728894535301.png"
                                ></image>
                                <text @click="handleOperComment(operType.Comment, false, item, true)">说点什么...</text>
                            </view>
                            <view class="video_bottom_oper_detail">
                                <view class="video_bottom_oper_detail_like item" @click="handleOper(item, 'praise')">
                                    <img
                                        v-show="item.isPraise"
                                        src="https://wpm-cdn.dreame.tech/images/202411/405183-1731393085336.png"
                                    />
                                    <img
                                        v-show="!item.isPraise"
                                        src="https://wpm-cdn.dreame.tech/images/202411/641136-1731393066480.png"
                                    />
                                    <text>{{ clicks(item.praiseNum, 0) }}</text>
                                </view>
                                <view class="video_bottom_oper_detail_collectio item" @click="handleOper(item, 'fav')">
                                    <img
                                        v-show="item.isFavorite"
                                        src="https://wpm-cdn.dreame.tech/images/202411/476606-1731393161420.png"
                                    />
                                    <img
                                        v-show="!item.isFavorite"
                                        src="https://wpm-cdn.dreame.tech/images/202411/242419-1731393142067.png"
                                    />
                                    <text>{{ clicks(item.favoriteNum, 1) }}</text>
                                </view>
                                <view
                                    class="video_bottom_oper_detail_comment item"
                                    @click="handleOperComment(operType.Comment, false, item, false)"
                                >
                                    <image
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6867b1eba7f216880012511.png"
                                    ></image>
                                    <text>{{ clicks(item.comment, 2) }}</text>
                                </view>
                            </view>
                        </view> -->
                    </view>
                </view>
            </swiper-item>
        </swiper>
        <Comment
            ref="CommentRef"
            :isShow.sync="showCommentPop"
            :permisionType="permissionType"
            @changeHeight="
                (val) => {
                    commentHeightNum = val;
                }
            "
            @commentNum="commentNumChange"
        ></Comment>
        <BottomOperatePopup
            :open="operatePopupOpen"
            :reportParams="reportParams"
            cancelText="取消"
            @close="operatePopupOpen = false"
            @handleConfirm="handleOperatePopupConfirm"
        />
        <!-- 我的内容操作弹窗 -->
        <MyOperate
            :open="myOperateOpen"
            :reportStatus="data.report_status"
            @close="myOperateOpen = false"
            @myOperateEdit="myOperateEdit"
            @myOperatePermission="myOperatePermission"
            @myOperateDelete="myOperateDelete"
        />
        <!-- 回复弹窗 -->
        <ReplyPopup
            v-if="replyPopupOpen"
            @close="handleReplyPopupClose"
            :replyHolder="replyHolder"
            :replyInput.sync="replyPopupInput"
            @handleSend="handleSend"
        />
        <!-- 权限弹窗 -->
        <PermissionPopup
            :open="permissionOpen"
            :type="permissionType"
            @close="permissionOpen = false"
            @handlePermissionClick="handlePermissionClick"
        />

        <!-- 评论输入框 -->
        <CommentInput ref="CommentInputRef" @send="sendInputComment" />

        <!-- 删除作品确认弹窗 -->
        <CustomModal
            :show="deleteContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            content="是否确认删除"
            confirmText="确认"
            cancelText="取消"
            showCancelButton
            @confirm="handleContenDelete"
            @cancel="deleteContentOpen = false"
        >
        </CustomModal>

        <!-- 用户取消关注拉黑 -->
        <CustomModal
            :show="operContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            :title="operContent.titie"
            :content="operContent.tip"
            :confirmText="operContent.confirmText"
            :cancelText="operContent.cancelText"
            showCancelButton
            @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })"
        >
        </CustomModal>

        <GoodsCard ref="GoodsCard" />
        <GoodsList
            ref="GoodsList"
            :goods_ids="list[current] ? list[current].goods_ids : []"
            @buyGoods="showGoodsMask"
        />
    </view>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import Comment from '../comment/comment.vue';
import VideoCom from './videoCom.vue';
import CommentInput from '../commentInput/commentInput.vue';
import BottomOperatePopup, {
    ReportParams,
} from '../../../contentDetails/components/BottomOperatePopup/BottomOperatePopup.vue';
import { UserModule } from '@/store/modules/user';
import {
    BannedPermissionRes,
    ContentPermissionType,
    bannedPermission,
    createBrowse,
    createReview,
    delContent,
    getExVideoList,
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
    updatePermission,
    userFollow,
    userFollowDelete,
    userRelation,
    getrandomVideoList,
} from '@/http/requestGo/community';
import Utils from '@/common/Utils';
import ReplyPopup from '@/pagesC/contentDetails/components/ReplyPopup/ReplyPopup.vue';
import { FOLLOW_BTN_OPTION } from '@/pagesC/contentDetails/components/contant';
import { PATH } from '../../../createContent/constant';
import MyOperate from '@/pagesC/contentDetails/components/MyOperate/index.vue';
import PermissionPopup from '@/pagesC/components/PermissionPopup/index.vue';
import { OperContent } from '@/pagesC/selfCommunity/selfCommunity.vue';
import { CheckAppJump } from '@/common/decorators';
import CardLink from '@/pagesC/contentDetails/components/CardLink/CardLink.vue';
import GoodsCard from '@/pagesC/contentDetails/components/CardLink/GoodsCard.vue';
import GoodsList from '@/pagesC/contentDetails/components/CardLink/GoodsList.vue';
// import { praise } from '@/http/contents';
// import Utils from '@/common/Utils';
// import Constants from '@/common/Constants';
// import { UserModule } from '@/store/modules/user';
const sysInfo = uni.getSystemInfoSync();
type ReportType = 'comment' | 'detail';

@Component({
    components: {
        Comment,
        VideoCom,
        CommentInput,
        BottomOperatePopup,
        ReplyPopup,
        MyOperate,
        PermissionPopup,
        CardLink,
        GoodsCard,
        GoodsList,
    },
})
export default class videoPlay extends Vue {
    $refs: {
        CommentInputRef: CommentInput;
        CommentRef: Comment;
        cellRef: any;
        VideoComRef: VideoCom;
        GoodsCard: GoodsCard;
        GoodsList: GoodsList;
    };

    @Prop({ type: Number, default: 1 }) permissionTypeProp!: ContentPermissionType;
    @Prop({ type: String, default: '' }) fromPage!: string;

    @Watch('permissionTypeProp')
    onPermissionType(val) {
        this.permissionType = val;
    }

    // #ifdef MP-WEIXIN

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingBottom(): number {
        return AppModule.pagePaddingBottom;
    }

    public topic_id: any = null;
    public page: number = 1;
    public page_size: number = 3;
    private showLoading: boolean = false; // 加载toast
    public finishedEnd: Boolean = false; // 是否加载完成
    public finished: Boolean = false; // 是否加载完成
    public reportType: ReportType = 'detail';
    public operatePopupOpen: boolean = false;
    public myOperateOpen: boolean = false;
    public permissionOpen: boolean = false;
    public deleteContentOpen: boolean = false;
    public current: number = 0;
    public beforeCurrent: number = 0;
    public startY: number = 0;
    public videoPlayer: any = null;
    public videoId: string = 'myVideo';
    public beforeContentId: string = '';
    public showCommentPop: boolean = false;
    public commentHeightNum: number = 0;
    public data: any = {};
    public flowList: any = [];
    public reportParams: Partial<ReportParams> = {};
    public fixedHeight: number = 0;
    public bottomShow: boolean = false; // 视频底部标签加载
    public replyPopupOpen: boolean = false;
    public user_id: any = UserModule.user_id;
    public replyHolder = '说点什么';
    public replyPopupInput = '';
    public permissionType: ContentPermissionType = ContentPermissionType.PUBLIC;
    public commentInputVisible: boolean = false;
    public last_content_id: any = null;
    public direction: number = null;
    public list: any = [
        // { videoPlay: false, id: 2 },
    ];

    public heightScreen: number = sysInfo.screenHeight;
    public widthScreen: number = sysInfo.screenWidth;
    public toView: string = '';
    public operType: any = {
        // 1点赞 2 收藏 3 评论
        Like: 1,
        Collect: 2,
        Comment: 3,
    };

    public userBanPermission = {} as BannedPermissionRes;
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: OperContent = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    public getList: Function;

    async onLoad(options) {
        this.fromPage = options.from || '';
    }

    get getListMethod() {
        if (this.fromPage === 'goldCoins') {
            return getrandomVideoList;
        } else {
            return getExVideoList;
        }
    }

    get commentHeight(): number {
        return this.showCommentPop ? this.commentHeightNum : 0;
    }

    relationText(status) {
        return FOLLOW_BTN_OPTION.find((v) => v.value === status)?.label || '关注';
    }

    clicks(val, index) {
        const num = val;
        if (!num) {
            return ['点赞', '收藏', '评论'][index];
        } else if (Number(num) < 10000) {
            return num;
        } else {
            const item = (Number(num) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    commentNumChange(num) {
        this.$set(this.list[this.current], 'comment', num);
    }

    //  初始化
    async init(data, topic_id) {
        this.data = { ...data };
        this.last_content_id = this.data.content_id;
        const isContentCreator = UserModule.user_id == this.data.creator;
        this.$set(this.list, 0, {
            ...data,
            videoPlay: false,
            url: data.video_media_list[0] ? data.video_media_list[0].url : '',
            author_tag: data.author_tag_icon,
            author_name: data.author,
            isPraise: data.is_praise,
            is_praise: data.is_praise,
            praiseNum: data.praise,
            isFavorite: data.is_favorite,
            is_favorite: data.is_favorite,
            favoriteNum: data.favorite,
            videoIndex: 1,
            isContentCreator: UserModule.user_id == this.data.creator,
            goods_ids: data.goods_ids,
        });

        this.permissionType = data.permission_type;
        uni.setStorageSync('createContent', {
            selectFans: this.permissionType === ContentPermissionType.PARTIALLY ? data.user_list : [],
            unSelectFans: this.permissionType === ContentPermissionType.NOT_ALLOW ? data.user_list : [],
        });
        // #ifdef MP-WEIXIN
        if (this.isPhone && this.wxAuth) {
            // #endif
            !isContentCreator &&
                Utils.isInApp() &&
                userRelation({ user_id: Number(UserModule.user_id), to_user_id: Number(this.data.creator) }).then(
                    (res) => {
                        data.relationStatus = res.data.follow_status;
                        this.$set(this.list[0], 'relationStatus', res.data.follow_status);
                    },
                );
            // #ifdef h5
            if (Utils.isInApp()) {
                // #endif
                bannedPermission().then((res) => {
                    this.userBanPermission = res.data;
                });
                // #ifdef H5
            }
            // #endif
            // #ifdef MP-WEIXIN
        }
        // #endif

        this.$nextTick(async () => {
            let line: number = 2;
            let res: any = false;
            if (this.list[0].body) {
                line = 1;
                res = await this.checkTextOverflow(this.list[0].body, 1);
                this.$set(this.list[0], 'moreLine', res);
            }
            if (!res) {
                res = await this.checkTextOverflow(this.list[0].title, line);
            }
            this.$set(this.list[0], 'moreLine', res);
        });
        this.reportParams = {
            id: data.content_id,
            hasReply: false,
            hasReport: true,
            hasDelete: false,
            reportType: 'detail',
        };
        // #ifdef MP-WEIXIN
        this.list[0].videoPlay = true;
        uni.setStorageSync('allowVideo', 1);
        // #endif
        if (+topic_id && (this.topic_id = topic_id)) {
            await this.getMoreData(1);
            await this.getMoreData(0);
        }
        setTimeout(() => {
            this.bottomShow = true;
        }, 30);

        console.log(UserModule.user_id, this.data.creator, 'UserModule.user_id');
    }

    // 切换到下一个视频
    switchToNextVideo() {
        try {
            // 如果当前不是最后一个视频，直接切换到下一个
            if (this.current < this.list.length - 1) {
                this.current = this.current + 1;
                this.swiperChange({ detail: { current: this.current }});
            } else {
                // 如果已经是最后一个视频，尝试获取更多视频
                this.getMoreData(0).then(() => {
                    console.log('this.list', this.list);
                    if (this.list.length > this.current + 1) {
                        this.current = this.current + 1;
                        this.swiperChange({ detail: { current: this.current }});
                    } else {
                        Utils.Toast('暂无更多视频');
                    }
                });
            }
        } catch (error) {
            console.error('切换视频失败:', error);
            Utils.Toast('切换视频失败');
        }
    }

    // 查询是上下是否有视频
    async getMoreData(direction = 0) {
        try {
            // const page: number = this.page;
            const page_size: number = this.page_size;
            const params = {
                page_size: page_size,
                topic_id: +this.topic_id,
                direction,
                last_content_id: this.last_content_id,
            };
            const type = this.fromPage === 'goldCoins' ? { type: 2 } : { content_type: 2 };
            const res = await this.getListMethod({ ...params, ...type }, this.showLoading);
            console.log('res', res);

            if (res.success) {
                const { list = [] } = res.data;
                if (list === null || list.length < page_size) {
                    direction ? (this.finishedEnd = true) : (this.finished = true);
                }
                let videoList = [];
                videoList = list ? list.filter((i) => i.content_id !== this.data.content_id) : [];
                for (let i = 0; i < videoList.length; i++) {
                    const param = { ...videoList[i] };
                    videoList[i] = {
                        ...param,
                        videoPlay: false,
                        url: param?.video_media_list[0]?.url,
                        author_name: param.author,
                        isPraise: param.is_praise,
                        is_praise: param.is_praise,
                        praiseNum: param.praise,
                        isFavorite: param.is_favorite,
                        is_favorite: param.is_favorite,
                        favoriteNum: param.favorite,
                        body: param.body,
                        title: param.title,
                        isContentCreator: UserModule.user_id == this.data.creator,
                        goods_ids: param.goods_ids,
                    };
                    // #ifdef MP-WEIXIN
                    if (this.isPhone && this.wxAuth) {
                        // #endif

                        // 关注关系
                        !videoList[i].isContentCreator &&
                            Utils.isInApp() &&
                            userRelation({
                                user_id: Number(UserModule.user_id),
                                to_user_id: Number(videoList[i].creator),
                            }).then((res) => {
                                videoList[i].relationStatus = res.data.follow_status;
                            });
                        // #ifdef MP-WEIXIN
                    }
                    // #endif
                    // 字数超行
                    let line: number = 2;
                    let res: any = false;
                    if (videoList[i].body) {
                        line = 1;
                        res = await this.checkTextOverflow(videoList[i].body, 1);
                        videoList[i].moreLine = res;
                    }
                    if (!res) {
                        res = await this.checkTextOverflow(videoList[i].title, line);
                    }
                    videoList[i].moreLine = res;
                }
                if (direction === 0) {
                    this.list = [...this.list, ...videoList];
                } else {
                    this.list = [...videoList, ...this.list];
                    this.current = this.current + videoList.length;
                }
                this.page++;
            }
        } catch (e) {
            console.error('getWlist err=', e);
        }
    }

    // 视频播放
    handleVideo(item) {
        this.$emit('videoPlay', item.videoPlay);
        // 允许播放视频
        uni.setStorageSync('allowVideo', 1);
        // this.$refs.VideoComRef[this.current].setVideo(!item.videoPlay);
        this.$set(this.list[this.current], 'videoPlay', !item.videoPlay);
    }

    // 上下滑动
    swiperChange(e) {
        console.log('e', e);
        this.$emit('swiperChange');
        if (!Utils.isInApp()) {
            return;
        }
        this.last_content_id = this.list[e.detail.current].content_id;
        if (e.detail.current === 0) {
            this.getMoreData(1);
        } else if (e.detail.current === this.list.length - 1) {
            this.getMoreData(0);
        }

        this.bottomShow = false;
        const current = e.detail.current;
        this.current = current;
        this.showView();
        (this.$refs.VideoComRef as any).forEach((element, index) => {
            // element.setVideo(false);
            this.list[index].videoPlay = false;
        });
        // this.$refs.VideoComRef[this.beforeCurrent].setVideo(false);
        if (uni.getStorageSync('allowVideo') == 1) {
            // this.$refs.VideoComRef[this.current].setVideo(true);
            this.list[this.current].videoPlay = true;
        }
        this.beforeCurrent = current;
    }

    // 停止所有视频播放
    stopAllVideos() {
        if (this.$refs.VideoComRef) {
            (this.$refs.VideoComRef as any).forEach((element, index) => {
                if (element && element.setVideo) {
                    element.setVideo(false);
                }
                if (this.list[index]) {
                    this.list[index].videoPlay = false;
                }
            });
        }
    }

    // 页面隐藏时停止视频播放
    onHide() {
        this.stopAllVideos();
    }

    // 页面卸载时停止视频播放
    onUnload() {
        this.stopAllVideos();
    }

    touchstart(e) {
        const isInApp = Utils.isInApp();
        if (!isInApp) {
            e.stopPropagation();
            e.preventDefault();
            return false;
        }
    }

    showView() {
        if (this.list && this.list[this.current] && !this.list[this.current].isContentCreator) {
            // #ifdef MP-WEIXIN
            if (this.isPhone && this.wxAuth) {
                // #endif
                // #ifdef h5
                if (Utils.isInApp()) {
                    // #endif
                    userRelation({
                        user_id: Number(UserModule.user_id),
                        to_user_id: Number(this.list[this.current].creator),
                    }).then((res) => {
                        this.$set(this.list, this.current, {
                            ...this.list[this.current],
                            relationStatus: res.data.follow_status,
                        });
                    });
                    // #endif
                }
                // #endif
                // #ifdef MP-WEIXIN
            }
            // #endif
        }
    }

    // 动画结束后显示操作栏
    animationfinish(e) {
        const moreNum = 2;
        if (
            ((this.current >= this.list.length - moreNum && !this.finishedEnd) ||
                (this.current <= moreNum && !this.finishedEnd)) &&
            this.beforeContentId !== this.list[this.current].content_id &&
            this.list.length >= this.page_size
        ) {
            this.beforeContentId = this.list[this.current].content_id;
            this.topic_id && this.getMoreData(this.current <= moreNum ? 0 : 1);
        }
        this.list[this.current].audit_status === 2 && createBrowse({ content_id: this.list[this.current].content_id });

        if (this.bottomShow === false) {
            setTimeout(() => {
                this.bottomShow = true;
            }, 30);
        }
    }

    @CheckAppJump()
    handleReport(type: ReportType, creator: string): void {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                this.reportType = type;
                if (UserModule.user_id == creator) {
                    this.myOperateOpen = true;
                } else this.operatePopupOpen = true;
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif

        // #ifdef H5
        this.reportType = type;
        if (UserModule.user_id == creator) {
            this.myOperateOpen = true;
        } else {
            this.operatePopupOpen = true;
        }
        // #endif
    }

    @CheckAppJump()
    handleOperatePopupConfirm() {
        uni.navigateTo({ url: `/pagesA/contentReport/contentReport?reportType=1` });
        this.operatePopupOpen = false;
    }

    @CheckAppJump()
    async handleOper(item, type) {
        const that = this;
        uni.$u.throttle(async () => {
            // #ifdef MP-WEIXIN
            // try {
            //     if (that.wxAuth && that.isPhone) {
            //         await that.setOper(item, type);
            //     } else {
            //         const target = 'service';
            //         // 进行授权
            //         UserModule.authFlow({ target });
            //     }
            // } catch (e) {
            //     console.error('getDetailData err=', e);
            // }
            // #endif
            Utils.reportEvent('give_like', { title: item.title });
            // #ifdef H5
            await that.setOper(item, type);
            // #endif
        }, 500);
    }

    @CheckAppJump()
    async setOper(item, type) {
        let flag, res;
        // const defaultPraise = item.is_praise;
        if (item[type === 'praise' ? 'isPraise' : 'isFavorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            this.$set(item, type === 'praise' ? 'isPraise' : 'isFavorite', flag);
            this.$set(
                item,
                type === 'praise' ? 'praiseNum' : 'favoriteNum',
                item[type === 'praise' ? 'praiseNum' : 'favoriteNum'] + (+flag ? 1 : -1),
            );
            this.$set(item, type === 'praise' ? 'is_praise' : 'is_favorite', res.data.id);
        }
    }

    // 用户操作
    @CheckAppJump()
    handleOperComment(type: number, showIntro: boolean, item, showInput) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1 && !showIntro) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        // #ifdef MP-WEIXIN
        if (showInput === true) {
            try {
                if (this.wxAuth && this.isPhone) {
                    if (type === this.operType.Comment) {
                        this.replyPopupOpen = true;
                    }
                } else {
                    const target = 'service';
                    // 进行授权
                    UserModule.authFlow({ target });
                }
            } catch (e) {
                console.error('getDetailData err=', e);
            }
        } else if (type === this.operType.Comment) {
            this.showCommentPop = true;
            this.$refs.CommentRef.initReviewList(item, showIntro);
            console.log(this.$refs.CommentRef, 'CommentRef');
        }
        // #endif
        // #ifdef H5
        if (showInput) {
            if (type === this.operType.Comment) {
                this.replyPopupOpen = true;
            }
        } else {
            this.showCommentPop = true;
            this.$refs.CommentRef.initReviewList(item, showIntro);
            console.log(this.$refs.CommentRef, 'CommentRef');
        }
        // #endif
    }

    @CheckAppJump()
    handleShare(item) {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 3) {
            Utils.Toast('作品审核未通过，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.$emit('share', item);
    }

    // 判断行数是否超长
    checkTextOverflow(textContent, maxLines = 2) {
        return new Promise((resolve) => {
            this.$nextTick(() => {
                // #ifdef MP-WEIXIN
                const query = uni.createSelectorQuery().in(this);
                query
                    .select('#text_0')
                    .boundingClientRect((rect) => {
                        if (rect?.width) {
                            const containerWidth = rect?.width;
                            // const maxLines = 2; // 限制的行数
                            // 文本宽度计算
                            const textWidth = this.calculateTextWidth(textContent);
                            const lines = textWidth / containerWidth; // 计算行数
                            // 判断行数是否超过最大行数
                            if (lines >= maxLines) {
                                resolve(true);
                            } else {
                                resolve(false);
                            }
                        } else {
                            resolve(false);
                        }
                    })
                    .exec();

                // #endif
                // #ifdef H5
                if (this.widthScreen) {
                    const containerWidth = this.widthScreen - (this.widthScreen / 750) * 30 * 2;
                    // const maxLines = 2; // 限制的行数
                    // 文本宽度计算
                    const textWidth = this.calculateTextWidth(textContent);
                    const lines = textWidth / containerWidth; // 计算行数
                    // 判断行数是否超过最大行数
                    if (lines >= maxLines) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                } else {
                    resolve(false);
                }
                // #endif
            });
        });
    }

    // 计算文本实际宽度（考虑中英文字符）
    calculateTextWidth(text) {
        let baseWidth;
        // #ifdef MP-WEIXIN
        baseWidth = 15.3;
        // #endif
        // #ifdef H5
        baseWidth = 13.9;
        // #endif
        let width = 0;
        for (const char of text) {
            if (char.match(/[A-Za-z0-9]/)) {
                // 英文字符宽度较窄，这里假设为 7
                width += baseWidth / 2;
            } else {
                // 中文字符宽度较宽，假设为 14
                width += baseWidth;
            }
        }
        return width;
    }

    public sendLoading: boolean = false;
    handleSend(content: string, imageUrl: string[]) {
        if (!content && imageUrl.length === 0) {
            Utils.Toast('评论内容不能为空');
            return;
        }

        if (this.sendLoading) return;
        this.sendLoading = true;

        const { user_id, sessid } = UserModule;

        createReview({ content, content_id: this.list[this.current].content_id, sessid, user_id, image_url: imageUrl })
            .then((res) => {
                this.replyPopupOpen = false;
                this.replyPopupInput = '';
                this.$set(this.list[this.current], 'comment', this.list[this.current].comment + 1);
                Utils.Toast('发送成功');
            })
            .catch((err) => console.log('err', err))
            .finally(() => {
                this.sendLoading = false;
            });
    }

    handleReplyPopupClose() {
        this.replyPopupOpen = false;
    }

    @CheckAppJump()
    handleUserFollow(item, index) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                // #endif
                uni.$u.throttle(async () => {
                    const user_id = Number(UserModule.user_id);
                    const other_id = Number(item.creator);
                    if (item.relationStatus === 0) {
                        await userFollow({ user_id, followed_user_id: other_id });
                        const res = await userRelation({ user_id, to_user_id: other_id });
                        // item.relationStatus = res.data.follow_status;
                        // this.list[index].relationStatus = res.data.follow_status
                        // this.list[this.current].relationStatus = res.data.follow_status;
                        this.$set(this.list, this.current, {
                            ...this.list[this.current],
                            relationStatus: res.data.follow_status,
                        });
                        Utils.Toast('关注成功');
                    } else {
                        this.operContentOpen = true;
                        this.operContent = {
                            type: 'follow',
                            titie: '',
                            tip: '确定不再关注该用户',
                            confirmText: '不再关注',
                            cancelText: '取消',
                        };
                    }
                }, 500);
                // #ifdef MP-WEIXIN
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
    }

    @CheckAppJump()
    handleUserAvater(item, index) {
        console.log(item, index, 'item, index');
        Utils.jumpPersonHome(item.creator);
    }

    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.list[this.current].creator });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            userRelation({ user_id: +UserModule.user_id, to_user_id: +this.list[this.current].creator }).then((res) => {
                this.list[this.current].relationStatus = res.data.follow_status;
            });
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    myOperateEdit() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.report_status === 1) {
            Utils.Toast('作品被举报，平台正在审核，不可编辑');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.myOperateOpen = false;
        uni.redirectTo({
            url: `/pagesC/createContent/index?contentType=video&content_id=${this.list[this.current].content_id}`,
        });
    }

    myOperatePermission() {
        if (this.data.report_status === 2) {
            Utils.Toast('作品已下架，暂不可用');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.permissionOpen = true;
    }

    myOperateDelete() {
        if (this.data.report_status === 1) {
            Utils.Toast('作品被举报，平台正在审核，不可删除');
            return;
        }

        if (this.data.audit_status === 1) {
            Utils.Toast('作品审核中，暂不可用');
            return;
        }

        this.myOperateOpen = false;
        this.deleteContentOpen = true;
    }

    handleContenDelete() {
        delContent({ content_id: this.data.content_id }).then(() => {
            this.delListContent(this.data.content_id);
            Utils.Toast('删除成功');
            Utils.goBack();
        });
    }

    delListContent(content_id) {
        // #ifdef H5
        Utils.sharedStorage('set', 'del_content_id', content_id);
        // #endif
        // #ifdef MP-WEIXIN
        const pages = getCurrentPages();
        const prevPage: any = pages[pages.length - 2];
        const _pre_route: any = prevPage && prevPage.route;
        // if (_pre_route === 'pagesC/selfCommunity/selfCommunity') {
        //     prevPage.$vm.listData[prevPage.$vm.swiperCurrent].list = prevPage.$vm.listData[
        //         prevPage.$vm.swiperCurrent
        //     ].list.filter((i) => i.content_id !== content_id);
        // } else
        if (_pre_route === 'pages/contents/contents') {
            if (prevPage.$vm.tabsIndex === 0) {
                prevPage.$vm.flowList = prevPage.$vm.flowList.filter((i) => i.content_id !== content_id);
            } else if (prevPage.$vm.tabsIndex === 1) {
                prevPage.$vm.$refs.exploreRef[0].flowList = prevPage.$vm.$refs.exploreRef[0].flowList.filter(
                    (i) => i.content_id !== content_id,
                );
            } else if (prevPage.$vm.tabsIndex === 2) {
                prevPage.$vm.$refs.CommunityRef[0].flowList = prevPage.$vm.$refs.CommunityRef[0].flowList.filter(
                    (i) => i.content_id !== content_id,
                );
            }
        }
        // #endif
    }

    handlePermissionClick(type) {
        this.permissionOpen = false;
        this.myOperateOpen = false;
        if ([ContentPermissionType.PUBLIC, ContentPermissionType.PRIVATE].includes(type)) {
            this.permissionType = type;
            uni.setStorageSync('createContent', {
                ...uni.getStorageSync('createContent'),
                selectPermission: type,
                selectFans: [],
                unSelectFans: [],
            });
            updatePermission({
                content_id: this.data.content_id,
                permission_type: this.permissionType,
                user_ids: this.data.user_list?.map((v) => v.user_id) || [],
            });
        } else {
            uni.navigateTo({
                url: `${PATH.permission}?type=${type}&modifyType=edit&contentId=${this.data.content_id}`,
            });
        }
    }

    jumpPersonHome(creator) {
        // #ifdef H5
        const win: any = window;
        win.onAppPageShow = () => {
            this.showView();
        };
        // #endif
        Utils.jumpPersonHome(creator);
    }

    showGoodsMask(options) {
        if (options) {
            (this.$refs.GoodsCard as any).showGoodsMask(options.gid);
            (this.$refs.GoodsList as any).toggleVisible();
            return;
        }

        const goods_ids = this.list[this.current].goods_ids;
        console.log(goods_ids);
        if (goods_ids.length === 1) {
            (this.$refs.GoodsCard as any).showGoodsMask(goods_ids[0]);
        } else {
            (this.$refs.GoodsList as any).toggleVisible();
        }
    }

    // 打开评论输入框
    openCommentInput() {
        (this.$refs.CommentInputRef as any).toggleVisible();
    }

    // 处理评论输入框发送
    async sendInputComment(content: string, imageUrl: string[]) {
        if (!content && imageUrl.length === 0) {
            Utils.Toast('评论内容不能为空');
            return;
        }

        if (content.trim().length > 500) {
            Utils.Toast(
                '评论内容过长，请精简',
                null,
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688740f4806505260011295.png',
            );
            return;
        }

        if (this.sendLoading) return;
        this.sendLoading = true;

        const { user_id, sessid } = UserModule;

        await createReview({
            content,
            content_id: this.list[this.current].content_id,
            sessid,
            user_id,
            image_url: imageUrl,
        });
        this.$set(this.list[this.current], 'comment', this.list[this.current].comment + 1);
        Utils.Toast(
            '发送成功',
            null,
            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887414427f4e1640012115.png',
        );

        this.sendLoading = false;
        (this.$refs.CommentInputRef as any).toggleVisible({ isSend: true });
    }
}
</script>

<style lang="scss" scoped>
.video-container {
    display: unset !important;
    background-color: #000;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;

    .oper_more {
        z-index: 99999;
        height: 108rpx;
        position: absolute;
        display: flex;
        align-items: center;
        padding-right: 18rpx;
        right: 0;

        image {
            width: 64rpx;
            height: 64rpx;
        }
    }

    .oprate-list {
        z-index: 99999;
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        right: 18rpx;
        bottom: 226rpx;
        width: 92rpx;

        /* .avator_video {
            position: relative;
            height: 69rpx;
            width: 69rpx;
            margin-right: 16rpx;

            image,
            img {
                height: 69rpx;
                width: 69rpx;
                border-radius: 50%;
            }
        } */

        .relation-btn {
            margin-bottom: 40rpx;
            position: relative;

            .avatar {
                width: 84rpx;
                height: 84rpx;
                border-radius: 50%;
                border: 4rpx solid white;
            }

            .icon {
                width: 40rpx;
                height: 40rpx;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -20rpx;
            }
        }

        .item {
            font-size: 24rpx;
            line-height: 32rpx;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
            z-index: 1;
            margin-top: 12rpx;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-bottom: 32rpx;

            img {
                width: 64rpx;
                height: 64rpx;
                margin-bottom: 8rpx;
            }
        }

        .share-btn {
            display: flex;
            align-items: center;
            padding: 0;
            border: unset !important;

            image {
                width: 64rpx;
                height: 64rpx;
            }
        }
    }

    .video_box {
        box-sizing: border-box;
        position: relative;
        height: 100%;
        width: 100%;

        .video_content {
            z-index: 9 !important;
            height: 100%;
            width: 100%;
        }

        .stop_icon {
            width: 139rpx;
            height: 139rpx;
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);

            image {
                height: 100%;
                width: 100%;
            }
        }

        .video_bottom {
            z-index: 999 !important;
            display: flex;
            flex-direction: column;
            position: absolute;
            bottom: 24rpx;
            // bottom: 160rpx;
            left: 0;
            width: 100%;
            font-family: MiSans, MiSans;
            font-weight: 400;
            font-size: 27rpx;
            color: #eeeeee;
            line-height: 37rpx;
            text-align: left;
            font-style: normal;
            text-transform: none;

            &_info {
                box-sizing: border-box;
                z-index: 10 !important;
                padding: 10rpx 30rpx 0;
                width: 100%;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.17) 14%, #000000 100%);
                opacity: 0;
                transition: all 0.2s;

                &_user {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    &_detail {
                        display: flex;
                        align-items: center;

                        .avator_icon {
                            /* position: absolute; */
                            width: 31rpx;
                            height: 31rpx;
                            border-radius: 50%;
                            margin-left: 12rpx;
                            /* right: -5rpx;
                            bottom: 0;
                            margin-right: 0; */
                        }
                    }

                    &_more {
                        margin-left: 20rpx;
                        display: flex;
                        align-items: center;

                        .share-btn {
                            margin-right: 46rpx;
                            display: flex;
                            padding: 0;
                            border: unset !important;
                            width: 46rpx;
                            height: 46rpx;
                        }

                        image {
                            width: 46rpx;
                            height: 46rpx;
                        }
                    }
                }

                &_text {
                    margin: 22rpx 0 12rpx;

                    &_title {
                        height: fit-content;

                        &.bold {
                            font-weight: 500;
                        }
                    }

                    &_more {
                        text-align: right;
                        color: #a6a6a6;
                    }
                }

                &_show {
                    opacity: 1;
                }
            }

            &_oper {
                padding: 0 30rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 123rpx;
                width: 100%;
                background-color: #000;

                &_detail {
                    display: flex;
                    align-items: center;

                    .item {
                        display: flex;
                        align-items: center;
                        margin-left: 28rpx;

                        image,
                        img {
                            width: 46rpx;
                            height: 46rpx;
                            margin-right: 10rpx;
                        }

                        text {
                            text-align: center;
                            min-width: 54rpx;
                        }
                    }
                }

                &_comment {
                    padding: 18rpx 25rpx;
                    background: #404040;
                    border-radius: 194rpx 194rpx 194rpx 194rpx;

                    image {
                        width: 37rpx;
                        height: 37rpx;
                        margin-right: 15rpx;
                    }

                    text {
                        color: #a6a6a6;
                    }
                }
            }

            &_progress {
                height: 2rpx;
                width: 100%;
                background: #505050;
                // background: red;
            }
            &_comment {
                display: flex;
                align-items: center;
                margin: 20rpx auto 0;
                padding: 0 24rpx;
                width: 716rpx;
                height: 64rpx;
                border-radius: 64rpx;
                background: rgba(255, 255, 255, 0.1);

                &_icon {
                    width: 32rpx;
                    height: 32rpx;
                }
                &_input {
                    margin-left: 8rpx;
                    height: 32rpx;
                    line-height: 32rpx;
                    font-size: 24rpx;
                    color: #777777;
                }
            }
        }
    }
}

::v-deep .cart-card {
    width: 604rpx;
    margin: 24rpx 0 0 0;
}
</style>
