import http from './index';

// 优惠券接口
export const getActivityDetail = (params) => http.post('main/activity/activity-detail', params);

/**
 * 分享券分享优惠券列表
 * @parma {string} type  系列机型
 */
export const shareCouponList = (params) => http.post('main/share-coupon/share-list', params);

/**
 * 分享券分享优惠券code
 * @parma {string} type  系列机型
 * @parma {string} remark  用户分享券的备注
 */
export const shareCouponCode = (params) => http.post('main/share-coupon/share-coupon-code', params);

/**
 * 分享券领取优惠券列表
 * @parma {string} share_coupon_code  分享券code
 */
export const shareCouponDrawList = (params) => http.post('main/share-coupon/draw-list', params);

/**
 * 分享券领取
 * @parma {string} share_coupon_code  分享券code
 */
export const shareCouponDraw = (params) => http.post('main/share-coupon/draw', params);

// 发放优惠券
export const distributeCoupon = () => http.post('main/trade-in/distribute-coupon');
