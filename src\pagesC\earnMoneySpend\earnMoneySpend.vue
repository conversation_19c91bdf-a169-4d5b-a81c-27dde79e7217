<template>
    <view>
        <APPSHARE :link="link"></APPSHARE>
        <view class="earnMoneySpend_container" :style="{ 'margin-top': $isInApp() ? '0' : '96rpx' }">
            <!-- 页面头部导航栏 -->
            <CustomBar2
                v-show="$isInApp()"
                :isHeaderTransparent="isHeaderTransparent"
                :BackIcon="'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d3315dc733840015096.png'"
                :title="'赚钱花'"
                :background="customBarBg"
                :isBack="isBack"
            >
                <template #moreBtn>
                    <view class="more-btn">
                        <view class="share" @click="(_) => shareActivity()"></view>
                        <view class="rule" @click="openRulePopup"></view>
                    </view>
                </template>
            </CustomBar2>
            <!-- 页面内容 -->
            <view class="earnMoneySpend_content" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="invite_count_box" v-if="$isInApp()">
                    <text class="invite_count_box_desc">已赚: ¥</text>
                    <text class="invite_count_box_title">{{ invite_info.total_consume_money || 0.0 }}</text>
                </view>
                <view class="invite_count_box" v-if="!$isInApp()">
                    <text class="invite_count_box_desc">已赚: ¥</text>
                    <text class="invite_count_box_title">???</text>
                </view>
                <view class="view_detail" @click="goInviteRecord">
                    查看明细
                    <view class="invite_listimgs"></view>
                </view>
                <!-- 开店赚钱、分享朋友圈 864rpx :style="{ 'margin-top': '905rpx' }"-->
                <view class="activity-time-down" :style="{ 'margin-top': '905rpx' }">
                    <!-- 开小店 -->
                    <view class="wxShop1" v-if="isOpenStore === true" @click="goShop('isHas')" />
                    <view class="wxShop" v-if="isOpenStore === false || !$isInApp()" @click="goShop('isNo')" />
                    <view class="earnBlindBox" @click="goToViewBlindBox">
                        <view class="earnBlindBox_gif"></view>
                    </view>
                    <!-- 分享朋友圈 -->
                    <view class="wxShares" @click="taskShareClick('like')"></view>
                    <view class="wxShareFriend" @click="taskShareClick('invite')"></view>
                </view>
            </view>
            <!-- 排行榜 -->
            <RankingListCard ref="rankingListCard"></RankingListCard>
            <taskPopup
                ref="taskPopup"
                @taskSubmit="onLoad"
                @taskShare="taskShareClick"
                @allTaskPopupClose="allTaskPopupClose"
                :isOpenStore="isOpenStore"
            ></taskPopup>
        </view>
        <!-- 活动规则弹窗 -->
        <u-popup
            :show="showRulePopup"
            v-prevent-scroll="showRulePopup"
            mode="center"
            :round="18"
            :safe-area-inset-bottom="false"
            catchtouchmove
        >
            <view class="rule-popup-content">
                <view class="rule-popup-header">
                    <view class="rule-title">
                        <!-- <view class="rule-title-decoration left"></view> -->
                        <text class="rule-title-text">活动规则</text>
                        <!-- <view class="rule-title-decoration right"></view> -->
                    </view>
                    <view class="rule-popup-close" @click="closeRulePopup">
                        <view class="close-icon"></view>
                    </view>
                </view>
                <scroll-view scroll-y class="rule-container">
                    <view class="rule-content">
                        <view class="rule-content-text">
                            <view class="rule-section" v-html="baseInfo.rule"> </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </u-popup>
        <view class="oneYuanFlashSale_mask" v-if="isShowMask">
            <view class="inner_popup">
                <view class="btn-close" @click="closeMask">知道了</view>
                <!-- <view class="icon-close" @click="isShowMask = false"></view> -->
                <!-- <image
                    class="popup-content"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689bf70ecb6398330015247.png"
                /> -->
                <view class="popup-content-title">
                    {{ consume_money_tip.remark }}
                </view>
                <view class="popup-content-desc">
                    获得
                    <view class="popup-content-desc-money">{{ (consume_money_tip.money / 100).toFixed(2) }}</view>
                    元
                </view>
            </view>
        </view>
        <ShareDialog
            :show.sync="show"
            :productInfo="productInfo"
            :shareType="shareType === 'goods' ? 'default' : 'active'"
            @share="handleShare"
        ></ShareDialog>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import Utils from '@/common/Utils';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import { getInviteInfo, getUserDictSave, getConsumeMoneyTip, getConsumeMoneyCloseTip } from '@/http/halfPrice';
import taskPopup from './taskPopup.vue';
import { getThemeActivity } from '@/http/memberActivity';
import { joinInvite, likeInvite, checkUserCanBeInvited } from '@/http/oneYuanFlashSalePage';
import RankingListCard from './components/rankingList.vue';
import { UserModule } from '@/store/modules/user';
import { goodsList } from '@/http/goods';
import { salesStoreDetail } from '@/http/order';
import { CheckAppJump } from '@/common/decorators';

@Component({
    name: 'EarnMoneySpend',
    components: {
        ShareDialog,
        taskPopup,
        RankingListCard,
    },
    filters: {},
})
export default class EarnMoneySpend extends Vue {
    $refs!: {
        productListRef;
        taskPopup;
        rankingListCard;
    };

    public link: string = '';
    public isHeaderTransparent: boolean = true;
    public coinCount = 0;
    public goldConversion = 0;
    public invite_info: any = {
        invite_count: 0,
        like_count: 0,
        total_consume_money: 0.0,
    };

    public baseInfo: any = {};
    public showRulePopup = false;
    public countdownTime = 0;
    public inviter_id = '';
    public isShowMask = false;
    public productList: any = [];
    public invite_money = 0;
    public like_money = 0;
    // public storeDetail: any = {};
    public isOpenStore: boolean = null;
    public jumpUrl = '';
    public shareType = 'active';
    public show = false;
    public productInfo = {};
    public shareProduct: any = {};
    public shareId = '';
    public customBarBg = 'transparent';
    public isBack: boolean = true;
    public consume_money_tip: any = {};
    onPageScroll(e) {
        if (e.scrollTop > 0) {
            this.customBarBg =
                'linear-gradient(180deg, #FEE9E4 50%, rgba(254, 233, 228, 0.84) 72%, rgba(254, 233, 228, 0) 100%)';
        } else {
            this.customBarBg = 'transparent';
        }
    }

    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    get statusBarHeight() {
        return AppModule.statusBarHeight;
    }

    // // 获取小数点前的整数部分
    // get invite_count_box_title() {
    //     const totalAmount =
    //         this.coinCount > 0 && this.goldConversion > 0
    //             ? Number(this.coinCount / this.goldConversion) + Number(this.invite_info.total_consume_money)
    //             : 0;
    //     return Math.floor(totalAmount).toString();
    // }

    // // 获取小数点后的部分
    // get invite_count_box_desc() {
    //     const totalAmount =
    //         this.coinCount > 0 && this.goldConversion > 0
    //             ? Number(this.coinCount / this.goldConversion) + Number(this.invite_info.total_consume_money)
    //             : 0;
    //     const decimalPart = (totalAmount % 1).toFixed(2).substring(1); // 获取小数点后的部分，包含小数点
    //     return decimalPart;
    // }

    isSuccessResponse(response) {
        // 成功：返回对象（且不是数组）
        return !Array.isArray(response) && typeof response === 'object' && response !== null;
    }

    async onLoad(options: any) {
        if (options.isNoBack) {
            this.isBack = false;
        }
        this.link = options.link;
        Utils.setPageTitle('赚钱花');
        const { modules, base_info } = await getThemeActivity({
            id: Number(options.active_id || process.env.VUE_APP_OFF_SPEED_ACTIVITY_ID),
        });
        if (modules.length > 0) {
            if (modules[0].extra?.consume_money_return) {
                this.invite_money = modules?.[0].extra?.consume_money_return ?? 0;
            }
            if (modules[0].extra?.consume_money_like_return) {
                this.like_money = modules?.[0].extra?.consume_money_like_return ?? 0;
            }
            modules.forEach((item) => {
                if (item.extra && item.extra?.goods?.length > 0 && item.module_code === 'CONSUME_MONEY') {
                    this.productList = item.extra.goods.map((r) => {
                        r.consume_money_rate = item?.extra?.consume_money_rate || 0;
                        r.consume_money_return = item?.extra?.consume_money_return || 0;
                        return r;
                    });
                }
            });
        }
        this.baseInfo = base_info;
        this.countdownTime =
            new Date(base_info.end_time * 1000).getTime() - new Date().getTime() > 0
                ? new Date(base_info.end_time * 1000).getTime() - new Date().getTime()
                : 0;
        if (options.inviter_id) {
            this.inviter_id = options.inviter_id;
            const { share } = options;

            if (share === 'like') {
                likeInvite({
                    invite_type: 5,
                    inviter_id: this.inviter_id,
                    relate_id: 5,
                }).then((_) => {
                    this.isShowMask = true;
                });
            } else {
                checkUserCanBeInvited({
                    inviter_id: this.inviter_id,
                    relate_id: 5,
                    invite_type: 5,
                }).then((res) => {
                    console.log('%c res: ', 'font-size:16px;background-color: #465975;color:#fff;', res);
                    if (res.can_invite) {
                        /* 不需要弹窗，是新用户直接助力 */
                        joinInvite({
                            invite_type: 5,
                            inviter_id: this.inviter_id,
                            relate_id: 5,
                        });
                    }
                });
            }
        }
        this.getInviteInfoList();
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY,
            },
            true,
        );
        this.jumpUrl = options.link;
    }

    /**
     * 需要页面加载，重新调用查询(申请小店|我的小店)
     */
    onShow() {
        if (Utils.isInApp()) {
            this.handleStoreDetail();
            const win: any = window;
            win.onAppPageShow = () => {
                this.$refs.taskPopup.$refs.MemberTasks.fetchTaskList();
                this.getInviteInfoList();
                this.$refs.rankingListCard.startPolling();
            };
        }
    }

    mounted() {
        const win: any = window;
        win.onAppPageShow = () => {
            this.$refs.taskPopup.$refs.MemberTasks.fetchTaskList();
            this.getInviteInfoList();
            this.$refs.rankingListCard.startPolling();
        };
    }

    async handleStoreDetail() {
        if (this.user_id) {
            const storeDetail = await salesStoreDetail({ store_id: this.user_id });
            // this.storeDetail = res;
            this.isOpenStore = storeDetail.status === '1';
        } else {
            this.isOpenStore = false;
        }
    }

    closeRulePopup() {
        this.showRulePopup = false;
    }

    openRulePopup() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_RULE_CLICK,
            },
            true,
        );
        this.showRulePopup = true;
    }

    @CheckAppJump()
    goInviteRecord() {
        Utils.navigateTo('/pagesC/earnMoneySpend/inviteRecord');
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_RECORD_CLICK,
            },
            true,
        );
    }

    getInviteInfoList() {
        getInviteInfo({
            invite_type: 5,
            relate_id: 5,
        }).then((res) => {
            this.invite_info = res;
        });
        getConsumeMoneyTip({
            user_id: this.user_id,
        }).then((res) => {
            if (res && res.remark && res.money) {
                this.consume_money_tip = res;
                this.isShowMask = true;
            }
            console.log('%c res: ', 'font-size:16px;background-color: #465975;color:#fff;', res);
        });
        const params = {
            dict_key: 'text',
            content: JSON.stringify({
                value: 4,
            }),
        };
        getUserDictSave(params);
    }

    closeMask() {
        getConsumeMoneyCloseTip({
            user_id: this.user_id,
        });
        this.isShowMask = false;
    }

    @CheckAppJump()
    goShop(flag: 'isHas' | 'isNo') {
        if (flag === 'isHas') {
            Utils.navigateTo('/pagesC/shopStore/index');
        } else {
            Utils.navigateTo('/pagesC/createShop/index');
        }
        // Utils.navigateTo('/pagesC/shopProduct/recommendationShop');
    }

    @CheckAppJump()
    goToViewBlindBox() {
        Utils.navigateTo('/pagesC/goldCoins/drawPrize');
    }

    /* 分享活动 */
    async shareActivity() {
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        await this.getHomeRandomGoods();
        this.show = true;
        this.shareType = 'active';
        this.shareId = this.shareProduct.gid;
        this.productInfo = {
            name: this.shareProduct.name,
            desc: this.shareProduct.introduce,
            image: this.shareProduct.cover_image,
            price: this.shareProduct.price,
            priceColor: '#FF1F0E',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689836936eef54540012279.png',
        };
    }

    async shareGoods() {
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        await this.getHomeRandomGoods();
        this.show = true;
        this.shareType = 'goods';
        this.shareId = this.shareProduct.gid;
        this.productInfo = {
            name: this.shareProduct.name,
            desc: this.shareProduct.introduce,
            image: this.shareProduct.cover_image,
            price: this.shareProduct.price,
            priceColor: '#FF1F0E',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689836936eef54540012279.png',
        };
    }

    /* 朋友圈邀请好友点赞 */
    async shareTowxFriend(share: 'like' | 'invite') {
        console.log('%c shareTowxFriend: ', 'font-size:16px;background-color: #4b4b4b;color:#fff;');
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_CLICK,
            },
            true,
        );
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        await this.getHomeRandomGoods();
        this.show = true;
        this.shareType = share;
        this.shareId = this.shareProduct.gid;
        this.productInfo = {
            name: this.shareProduct.name,
            desc: this.shareProduct.introduce,
            image: this.shareProduct.cover_image,
            price: this.shareProduct.price,
            priceColor: '#FF1F0E',
            descColor: '#C59245',
            imageBg:
                share === 'like'
                    ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689836936eef54540012279.png'
                    : 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68987f87aaf4a7000012110.png',
        };
    }

    async handleShare(type: 'web' | 'image') {
        this.show = false;
        if (this.shareType === 'active') {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}`,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887a231e180f9240019837.png',
                        title:
                            Number(this.invite_info.total_consume_money) > 0
                                ? `我在追觅App赚了${this.invite_info.total_consume_money}元，邀你一起来赚钱`
                                : '来追觅和我一起赚钱！',
                        desc:
                            Number(this.invite_info.total_consume_money) > 0
                                ? '好友助力就能赚钱，搞钱真的能上瘾！'
                                : '躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: '来追觅和我一起赚钱！躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                },
            );
        } else if (this.shareType === 'goods') {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/goodsDetail/goodsDetail?gid=${this.shareId}`,
                        image: this.shareProduct.cover_image,
                        title: this.shareProduct.name,
                        desc: this.shareProduct.introduce,
                    },
                    extras: {
                        type: 'goods',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: `${this.shareProduct.price}元 ,${this.shareProduct.name}`,
                    },
                },
            );
        } else {
            Utils.cardShare(type)(
                {
                    target: 'wechat,weixin_circle,qq,sina,image_template,download',
                    link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}&gid=${this.shareId}`,
                    jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/sharePage?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}&gid=${this.shareId}`,
                },
                {
                    content: {
                        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/earnMoneySpend/earnMoneySpend?relate_id=5&inviter_id=${this.user_id}&share=${this.shareType}&gid=${this.shareId}`,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887a231e180f9240019837.png',
                        title:
                            Number(this.invite_info.total_consume_money) > 0
                                ? `我在追觅App赚了${this.invite_info.total_consume_money}元，邀你一起来赚钱`
                                : '来追觅和我一起赚钱！',
                        desc:
                            Number(this.invite_info.total_consume_money) > 0
                                ? '好友助力就能赚钱，搞钱真的能上瘾！'
                                : '躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                    extras: {
                        type: 'activity',
                        id: this.shareId,
                        goods: this.productInfo,
                        copyText: '来追觅和我一起赚钱！躺着都能赚钱的App，搞钱真的能上瘾！',
                    },
                },
            );
        }
    }

    /* 获取首页随机商品 */
    async getHomeRandomGoods() {
        const { list = [] } = await goodsList({ tid: -1, type: -2, page: 1, page_size: 10, single: 1 });
        this.shareProduct = list[Math.floor(Math.random() * list.length)];
    }

    debounce(fn: Function, delay: number) {
        let timer: number | null = null;
        return function (this: any, ...args: any[]) {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => fn.apply(this, args), delay);
        };
    }

    // @CheckAppJump()
    // taskShareClick(shareType: string) {
    //   switch (shareType) {
    //     case 'invite':
    //       this.debounce(() => this.shareTowxFriend('invite'), 500);
    //       break;
    //     case 'like':
    //       this.debounce(() => this.shareTowxFriend('like'), 500);
    //       break;
    //     case 'goods':
    //       this.debounce(() => this.shareGoods(), 500);
    //       break;
    //   }
    // }

    @CheckAppJump()
    taskShareClick(shareType: any) {
        switch (shareType) {
            case 'invite':
                this.shareTowxFriend('invite');
                break;
            case 'like':
                this.shareTowxFriend('like');
                break;
            case 'goods':
                this.shareGoods();
                break;
        }
    }

    allTaskPopupClose() {
        /* 关闭全部任务弹窗，重新获取用户的赚钱花数据 */
        // getInviteInfo({
        //     invite_type: 5,
        //     relate_id: 5,
        // }).then((res) => {
        //     this.invite_info = res;
        // });
        this.getInviteInfoList();
    }

    onUnload() {
        this.$refs.rankingListCard.stopPolling();
    }

    onHide() {
        this.$refs.rankingListCard.stopPolling();
    }
}
</script>
<style lang="scss" scoped>
@import './earnMoneySpend.scss';
</style>
