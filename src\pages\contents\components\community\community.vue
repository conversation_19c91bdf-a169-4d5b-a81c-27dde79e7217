<template>
    <view class="community_container">
        <APPSHARE :link="link"></APPSHARE>
        <view class="community_content">
            <Comment
                ref="CommentRef"
                :topicId="3"
                :flowList="ListData"
                :finished="finished"
                :currentIndex="tabsIndex"
                @share="handleShare"
                @cancelFollow="cancelFollow"
                @showGoodsMask="showGoodsMask"
                @changeFollowStatus="changeFollowStatus"
                @updateContentStatus="updateContentStatus"
                :updatedContentResult="updatedContentResult"
            ></Comment>
        </view>
        <custom-toast ref="customToast" />
    </view>
</template>

<script lang="ts">
import { ITag } from '@/store/modules/goods';
import { Component, Prop, Vue } from 'vue-property-decorator';
import Comment from './components/comment.vue';
import { createShare } from '@/http/requestGo/community';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { CheckAppJump } from '@/common/decorators';

@Component({
    components: {
        Comment,
    },
    filters: {
        clicks(val) {
            if (Number(val) < 10000) {
                return val;
            } else {
                const item = (Number(val) / 10000).toFixed(1);
                return item + 'w';
            }
        },
    },
})
export default class Contents extends Vue {
    $refs: {
        CommentRef;
    };

    @Prop({ type: Array })
    public ListData: Array<any>;

    @Prop({ type: Object })
    public updatedContentResult: object;

    @Prop({ type: Number })
    public tabsIndex: number;

    public creator: any = -1;

    public finished: Boolean = false; // 是否加载完成
    public page: number = 1;
    public flowList: any = [];
    public content_id: String = '';
    public tabList: Array<ITag> = [];
    public item: any = {};
    public link: string = '';
    public currActiveId: string = process.env.VUE_APP_TOPIC_ID;

    @CheckAppJump()
    changeFollowStatus(item, status) {
        this.flowList.forEach((i) => {
            if (i.creator === item.creator) {
                i.follow_status = status;
            }
        });
    }

    @CheckAppJump()
    cancelFollow(item, index) {
        this.$emit('cancelFollow', item, index);
    }

    onload(options) {
        const link = options.link;
        this.link = link;
    }

    @CheckAppJump()
    viewDetail(item) {
        // #ifdef H5
        if (item.jump_url) {
            Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
            return;
        }
        // #endif
        const { content_id = '', type } = item;
        this.content_id = item.content_id;
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${+this
                      .currActiveId}`,
        );
    }

    showGoodsMask(ids, contentId) {
        this.$emit('showGoodsMask', ids, contentId);
    }

    async handleShare(item) {
        // #ifdef H5
        await createShare({ content_id: item.content_id });
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FORWARD_BUTTON_CLICK,
        });
        const url =
            item.type === 1
                ? `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/contentDetails/contentDetails?content_id=${item.content_id}`
                : `${
                      process.env.VUE_APP_BASE_URL
                  }front/AppMall/?isNavBar=0#/pagesC/contentVideoDetails/contentVideoDetails?content_id=${
                      item.content_id
                  }&topic_id=${+this.currActiveId}`;
        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };
        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            const params = {
                target: 'wechat,qq,sina,weixin_circle',
                type: 'web',
                content: {
                    url: `${url}&link=${res.data}`,
                    share_image: item.cover_image + '?x-oss-process=image/resize,w_200',
                    share_title: item.title,
                    share_desc: item.body,
            },
            };
            Utils.messageChannel('share', params);
        });
        // #endif
    }

    @CheckAppJump()
    updateContentStatus(params) {
        this.$emit('updateContentStatus', params);
    }
}
</script>

<style lang="scss">
.community_container {
    height: auto;
    min-height: 100%;
    padding: 0;
    padding-bottom: 228rpx;
    // background-color: rgb(245, 245, 245);

    .title {
        font-family: MiSans, MiSans;
        font-weight: 500;
        font-size: 38rpx;
        color: #121212;
        line-height: 52rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }

    .community_title {
        font-family: MiSans, MiSans;
        font-weight: 500;
        font-size: 38rpx;
        padding: 46rpx 0rpx 0rpx 32rpx;
        color: #121212;
        line-height: 52rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background-color: #fff;
    }

    .community_recommond {
        width: 100%;
        padding: 38rpx 30rpx 50rpx;
        background: #f8f8f8 url('https://wpm-cdn.dreame.tech/images/2024010/589748-1728632643512.png') no-repeat;
        background-size: cover;

        &_list {
            width: 100%;
            &_item {
                margin-top: 35rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .recommond_container {
                    display: flex;
                    align-items: center;
                    .recommond_img {
                        width: 125rpx;
                        height: 85rpx;
                        img,
                        image {
                            max-height: 100%;
                            max-width: 100%;
                            border-radius: 8rpx 8rpx 8rpx 8rpx;
                        }
                    }

                    .recommond_box {
                        margin-left: 15rpx;
                        display: flex;
                        flex-direction: column;
                        .recommond_title {
                            font-family: MiSans, MiSans;
                            font-weight: 500;
                            font-size: 27rpx;
                            color: #20252b;
                            line-height: 37rpx;
                            text-align: left;
                            font-style: normal;
                            text-transform: none;
                            max-width: 390rpx;
                        }

                        .recommond_info {
                            display: flex;
                            align-items: center;
                            &_icon {
                                display: flex;
                                position: relative;
                                top: -2rpx;
                                image {
                                    width: 30rpx;
                                    height: 30rpx;
                                }
                            }

                            &_tip {
                                margin-left: 5rpx;
                                font-family: MiSans, MiSans;
                                font-weight: 400;
                                font-size: 23rpx;
                                color: #777777;
                                line-height: 31rpx;
                                text-align: left;
                                font-style: normal;
                                text-transform: none;
                            }
                        }
                    }
                }

                .community_recommond_item_right_icon {
                    margin-right: 15rpx;
                    image {
                        width: 12rpx;
                        height: 24rpx;
                    }
                }
            }
        }
    }

    .community_content {
        min-height: calc(100vh - 795rpx);
        padding: 0rpx 0rpx 70rpx;
    }

    .stickyContent {
        position: sticky;
        top: -2rpx;
        width: 100%;
        background-color: #fff;
        z-index: 100;
        padding: 40rpx 0rpx 52rpx 32rpx;
        margin-bottom: 8rpx;
        transition: background 0.1s ease-in-out;
        .tab {
            -webkit-overflow-scrolling: touch;

            .tab-view {
                -webkit-overflow-scrolling: touch;
                width: 100%;
                overflow-x: scroll;
                align-items: flex-start;

                // #ifdef H5
                ::v-deep .uni-scroll-view-content {
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: flex-start;
                    align-items: flex-start;
                }
                ::v-deep .uni-swiper-wrapper {
                    overflow: hidden !important;
                }
                // #endif

                .tab-item {
                    margin-right: 24rpx;
                    flex-shrink: 0;
                    font-size: 28rpx;
                    color: $text-color-regular;
                    line-height: 38rpx;
                    position: relative;
                    transition: all 0.1s ease-in-out;
                    text-align: center;
                    padding: 12rpx 32rpx;
                    height: 56rpx;
                    border-radius: 160rpx;
                    font-family: MiSans;
                    font-size: 28rpx;
                    font-weight: 500;
                    line-height: 32rpx;
                    letter-spacing: 0em;
                    box-sizing: border-box;
                    border: 1px solid #121212;
                    font-variation-settings: 'opsz' auto;
                    border-color: #d8d8d8;
                    color: #6a6a6a;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .tab-item-active {
                    color: #121212;
                    border-color: #121212;
                    font-weight: 600;
                }
            }

            .search {
                width: 146rpx;
                height: 70rpx;
                display: flex;
                align-items: center;
                position: absolute;
                top: 0;
                right: 0;
                padding: 12rpx 30rpx 20rpx 20rpx;
                box-shadow: -16rpx 0rpx 20rpx 0rpx #f4f4f4;
                background: $fill-color-bg-gray;
                margin-top: 8rpx;

                image {
                    width: 38rpx;
                    height: 38rpx;
                }

                .search-text {
                    font-size: 28rpx;
                    color: $text-color-regular;
                    line-height: 38rpx;
                }
            }

            .tab-header {
                width: 100%;
                height: 78rpx;
                font-size: 28rpx;
                color: $text-color-regular;
                line-height: 38rpx;
                justify-content: flex-start;

                .price-point {
                    margin-right: 54rpx;
                    margin-left: 16rpx;

                    image {
                        width: 32rpx;
                        height: 32rpx;
                    }
                }

                .exchange {
                    margin-right: 54rpx;
                    border-radius: 8rpx;
                    padding: 4rpx 16rpx;
                }

                .exchangeBgc {
                    background: $fill-color-light;
                }

                .exchange-active {
                    background: $fill-color-bg-white;
                    border: 2rpx solid $fill-color-primary-active;
                    color: $fill-color-primary-active;
                }
            }
        }
    }
}
</style>
