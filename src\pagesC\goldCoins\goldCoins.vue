<template>
    <view>
        <APPSHARE :link="link"></APPSHARE>
        <view class="gold-coins-container" :style="{ 'margin-top': $isInApp() ? '0' : '96rpx' }">
            <!-- 头部导航栏 -->
            <view class="header-content" :style="{ paddingTop: `${statusBarHeight}rpx` }">
                <view class="header">
                    <view v-show="$isInApp()" class="left" @click="goToBack"></view>
                    <view class="title">赚金币</view>
                    <view class="right">
                        <view v-show="$isInApp()" class="share" @click="(_) => shareActivity()"></view>
                        <view class="rule" @click="showRulePopup = true"></view>
                    </view>
                </view>
                <view class="header-coin">
                    <view class="header-coin-item">
                        <view class="header-coin-item-title" @click="goToMyGoldCoins">
                            <text>我的金币</text>
                            <image
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888edfa70d5e4620011950.png"
                                style="width: 18rpx; height: 18rpx; margin-left: 2rpx"
                            ></image>
                        </view>
                        <view class="header-coin-item-value">{{ $isInApp() ? coinCount : '???' }}</view>
                    </view>
                    <view class="icon"></view>
                    <view class="header-coin-item">
                        <view class="header-coin-item-title">折合现金（元）</view>
                        <view class="header-coin-item-value">{{
                            $isInApp()
                                ? coinCount > 0 && goldConversion > 0
                                    ? truncateTwoDecimals(coinCount / goldConversion)
                                    : 0
                                : '???'
                        }}</view>
                    </view>
                </view>
                <view class="header-bottom">
                    <view
                        class="header-bottom-item signIn"
                        :class="{ signIned: todaySigned }"
                        @click="openSignInPopup"
                    ></view>
                    <view class="header-bottom-item draw-gold" @click="openDrawGoldPopup"></view>
                    <!-- <view class="header-bottom-item invite" @click="inviteHandler"></view> -->
                    <view
                        v-show="!mainBtnTaskInfo.noData"
                        class="earn-gold-coins"
                        :class="{ disabled: !isShowCanGainBtn }"
                        @click="clickMainBtn"
                    >
                        <view class="earn-gold-coins-title">
                            {{ isShowCanGainBtn ? '赚金币' : '收集中' }}
                        </view>
                        <view class="earn-gold-coins-desc" v-if="isShowCanGainBtn">
                            <view class="small-gold-icon"></view>
                            <view class="small-gold-icon-num">+{{ randCoins }}</view>
                        </view>
                        <view class="earn-gold-coins-desc" v-if="!isShowCanGainBtn">
                            <u-count-down :time="mainBtnCountdownTime" format="HH:mm:ss" @finish="getMainBtnTaskInfo">
                            </u-count-down>
                            <view class="earn-gold-coins-desc-text"> 后再来 </view>
                        </view>
                        <view class="finger-gif" v-if="isShowCanGainBtn"></view>
                    </view>
                    <view class="header-bottom-item videos" @click="goToVideos"></view>
                    <view class="header-bottom-item browse" @click="_navigateTo">
                        <view class="browse-text" v-if="goldCoin > 0 && !searchStatus">+{{ goldCoin }}</view>
                    </view>
                </view>
                <!-- <MemberTasks :isMore="false" :limit="3" @refresh="init"></MemberTasks> -->
                <!-- 任务中心 -->
                <view class="task-center-list">
                    <view class="task-center-title">
                        <view class="left">做任务赚金币</view>
                        <view class="right" @click="goToAllTask">全部任务></view>
                    </view>
                    <MemberTasks
                        @DrawGold="openDrawGoldPopup"
                        @openSignInPopup="openSignInPopup"
                        :isMore="false"
                        :limit="3"
                        @refresh="init"
                    ></MemberTasks>
                </view>
            </view>
            <!-- 页面内容区域 -->
            <view class="content_list">
                <view class="search_box" @click="_navigateTo">
                    <!-- 左侧图标 -->
                    <image
                        class="search_box_left"
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fde0d744594760012053.png"
                        mode="widthFix"
                    ></image>

                    <!-- 中间输入框 -->
                    <view class="search_box_center"> X40 Pro扫地机器人 </view>

                    <!-- 右侧文字 -->
                    <view class="search_box_right">
                        <text>搜索</text>
                    </view>
                </view>
                <ProductListOne ref="productListRef" :isGoldCoinPage="true" />
            </view>
            <!-- 全部任务弹窗 -->
            <u-popup :show="allTaskPopup" mode="bottom" :round="20" v-prevent-scroll="allTaskPopup" catchtouchmove>
                <view class="all-task-popup-content">
                    <view class="all-task-popup-header">
                        <view class="left"></view>
                        <view class="right" @click="allTaskPopup = false">
                            <image
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888f9c4a01aa6560061478.png"
                            >
                            </image>
                        </view>
                    </view>
                    <view class="all-task-popup-body">
                        <MemberTasks
                            @DrawGold="openDrawGoldPopup"
                            @openSignInPopup="openSignInPopup"
                            :marginBottom="20"
                        ></MemberTasks>
                    </view>
                </view>
            </u-popup>
            <!-- 签到弹窗 -->
            <u-popup
                :show="signInPopup"
                mode="center"
                :round="30"
                class="signIn-popup"
                v-prevent-scroll="signInPopup"
                catchtouchmove
                :customStyle="{ backgroundColor: 'transparent' }"
                :overlayOpacity="0.6"
            >
                <view class="signIn-popup-content">
                    <view class="signIn-popup-header">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed33c981136230451824.png"
                            class="title-img"
                        ></image>
                    </view>
                    <view class="signIn-title">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689edad6c108a7910011128.png"
                        >
                        </image>
                    </view>
                    <view class="signIn-popup-body">
                        <GoldCoinsCheckIn
                            ref="jcalendar"
                            :yearMonth="targetDate"
                            :signDataTotal="signDataTotal"
                            @dateChange="getData"
                            :isShowDetail="false"
                            @signIn="clickSign"
                            :dateList="dateList"
                            :todaySigned="todaySigned"
                        />
                    </view>
                    <view class="signIn-popup-close" @click="closeSignInPopup">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889012760f323970031451.png"
                        >
                        </image>
                    </view>
                </view>
            </u-popup>
            <!-- 设置签到提醒弹窗 -->
            <u-popup
                :show="showRemindPopup"
                mode="center"
                :round="30"
                class="remind-popup"
                v-prevent-scroll="showRemindPopup"
                catchtouchmove
                :customStyle="{ backgroundColor: 'transparent', height: '670rpx' }"
            >
                <RemindPopupBody />
            </u-popup>
            <!-- 积分到账弹窗 -->
            <u-popup
                :show="showRewardPopup"
                mode="center"
                class="reward-popup"
                v-prevent-scroll="showRewardPopup"
                catchtouchmove
                :customStyle="{ backgroundColor: 'transparent' }"
            >
                <RewardPopupBody :rewardNum="rewardPopupRewardNum" @closeRewardPopup="closeRewardPopup" />
            </u-popup>
            <!-- 抽金币弹窗 -->
            <u-popup
                :show="showDrawGoldPopup"
                mode="center"
                class="draw-gold-popup"
                v-prevent-scroll="showDrawGoldPopup"
                catchtouchmove
                :customStyle="{ backgroundColor: 'transparent' }"
                :overlayOpacity="0.6"
            >
                <DrawGoldPopupBody
                    @closeDrawGoldPopup="closeDrawGoldPopup"
                    :goldPrizeDrawItem="goldPrizeDrawItem"
                    @showRewardPopup="handleShowRewardPopup"
                    @showRulePopup="showRulePopup = true"
                    :drawGoldPopupUserList="drawGoldPopupUserList"
                />
            </u-popup>
            <!-- 活动规则弹窗 -->
            <u-popup
                :show="showRulePopup"
                v-prevent-scroll="showRulePopup"
                mode="center"
                :round="18"
                :safe-area-inset-bottom="false"
                catchtouchmove
                class="rule-popup"
                :customStyle="{ backgroundColor: 'transparent' }"
            >
                <view class="rule-popup-content">
                    <view class="rule-popup-header">
                        <view class="rule-title">
                            <view class="rule-title-decoration left"></view>
                            <text class="rule-title-text">活动规则</text>
                            <view class="rule-title-decoration right"></view>
                        </view>
                        <view class="rule-popup-close" @click="showRulePopup = false">
                            <view class="close-icon"></view>
                        </view>
                    </view>
                    <scroll-view scroll-y class="rule-container">
                        <view class="rule-content">
                            <view class="rule-content-text">
                                <img
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889d064b830b7540017252.png"
                                    class="rule-img"
                                />
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </u-popup>
        </view>
        <ShareDialog :show.sync="show" :productInfo="productInfo" :shareType="'active'" @share="handleShare">
            <template #active_tag>
                <img
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png"
                    style="
                        width: 78rpx;
                        height: 56rpx;
                        position: absolute;
                        left: 20rpx;
                        top: 10rpx;
                        transform: translate(-50%, -50%);
                    "
                />
            </template>
        </ShareDialog>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import MemberTasks from './MemberTasks.vue';
import { VIPModule } from '@/store/modules/vip';
import ProductListOne from './components/ProductList.vue';
import { getCoinConfig, getScoreGet, getCoin } from '@/http/vip';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { joinInvite } from '@/http/oneYuanFlashSalePage';
import { UserModule } from '@/store/modules/user';
import {
    checkIsFirstComeIn,
    getContinueDays,
    getMonthCheckDetail,
    getRandCoins,
    getSignInStatus,
    getTaskInfo,
    presentedGoldCoins,
    signInDaliy,
    getScoreUncollect,
    getLimitGold,
} from '@/http/checkIn';
import GoldCoinsCheckIn from './components/GoldCoinsCheckIn.vue';
import RemindPopupBody from './components/RemindPopupBody.vue';
import RewardPopupBody from './components/RewardPopupBody.vue';
import DrawGoldPopupBody from './components/DrawGoldPopupBody.vue';
import { CheckAppJump } from '@/common/decorators';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import truncateTwoDecimals from './helpers/truncateTwoDecimals';

@Component({
    name: 'GoldCoins',
    components: {
        MemberTasks,
        ProductListOne,
        GoldCoinsCheckIn,
        ShareDialog,
        RemindPopupBody,
        RewardPopupBody,
        DrawGoldPopupBody,
    },
    filters: {},
})
export default class GoldCoins extends Vue {
    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get searchStatus(): Boolean {
        return VIPModule.taskList.find((item) => item?.code === 'search_view_goods')?.completed;
    }

    $refs!: {
        jcalendar;
        productListRef;
    };

    public showRulePopup = false;
    public baseInfo: any = {};
    public allTaskPopup = false;
    public signInPopup = false;
    public showRemindPopup = false;
    public showRewardPopup = false;
    // 是否展示抽金币弹窗
    public showDrawGoldPopup = false;
    // 当前是否开启了签到提醒
    public isOpenedRemind = false;
    // 是否可以点击赚金币按钮
    public isShowCanGainBtn = true;
    // 主按钮任务信息
    public mainBtnTaskInfo: any = { noData: true };
    public mainBtnCountdownTime = 0;
    // 积分到账弹窗奖励数值
    public rewardPopupRewardNum = 0;
    // 签到弹窗中的日期列表
    public dateList = [];
    // 主按钮上显示的随机金币
    public randCoins = 0;
    // 抽老虎机奖的item信息
    public goldPrizeDrawItem = {};
    // 抽老虎机奖的user列表
    public drawGoldPopupUserList = [];
    public coinCount = 0;
    public goldConversion = 0;
    public relate_id = '';
    public inviter_id = '';
    public signData = [];
    public startTime = '';
    public endTime = '';
    public continueDay = 0;
    public todaySigned = false;
    public addPoint = 1000;
    public signInStatus = {};
    public totalPoints = 0;
    public _ScoreGet: any = {};
    public link = '';
    public show = false;
    public productInfo = {};
    public shareType = 'active';
    public shareId = '';

    @CheckAppJump()
    goToMyGoldCoins() {
        // uni.navigateTo({
        //     url: '/pagesC/goldCoins/myGoldCoins',
        // });
        Utils.navigateTo('/pagesC/goldCoins/myGoldCoins');
    }

    async onShow() {
        this.init();
    }

    async getCoinConfigHandler() {
        const res = await getCoinConfig({ ac_id: 30 });
        this.goldConversion = Number(res.gold_conversion);
    }

    get user_id(): number | string {
        return UserModule.user_id;
    }

    getData(currentYearAndMonth: any) {
        // this.getSignInDetail();
    }

    closeSignInPopup() {
        this.signInPopup = false;
        // this.getSignInStatus();
        // 更新分数
        this.getScoreGet();
    }

    demoClick() {
        Utils.callCalendar();
    }

    toIOSDate(strDate) {
        // iso不认识"-"拼接的日期，所以转/
        return strDate ? strDate.replace(/-/g, '/') : strDate;
    }

    truncateTwoDecimals = truncateTwoDecimals;

    @CheckAppJump()
    _navigateTo() {
        uni.navigateTo({
            url: '/pagesA/search/search?search_type=' + 1 + '&from=goldCoins&goldCoin=' + this.goldCoin,
        });
        Utils.reportEvent('search_click', {});
    }

    async getSignInDetail() {
        const calendarDays = this.$refs.jcalendar.calendarDays.filter((d) => d.isThisMonth);
        // this.startTime = (new Date(this.toIOSDate(calendarDays[0].fullDate)).getTime() / 1000).toString();
        // 获取当前日期
        const currentDate = new Date(this.toIOSDate(calendarDays[0].fullDate));
        // 减去3天（3天的毫秒数 = 3 * 24 * 60 * 60 * 1000）
        const threeDaysAgo = new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000);
        // 转换为Unix时间戳（秒）并转为字符串
        this.startTime = Math.floor(threeDaysAgo.getTime() / 1000).toString();
        this.endTime = (
            new Date(this.toIOSDate(calendarDays[calendarDays.length - 1].fullDate)).getTime() / 1000
        ).toString();
        const params = {
            start_time: this.startTime,
            end_time: this.endTime,
            type: 3,
        };
        this.signData = [];
        const signInDetail = await getMonthCheckDetail(params);
        this.signData = signInDetail.reduce(function (a, b) {
            return a.concat(b);
        });
        this.signDataTotal = {
            random: +new Date(),
            dataSource: [...this.signData],
            continueDay: this.continueDay,
            todaySigned: this.todaySigned,
            addPoint: this.addPoint,
        };
    }

    public targetDate =
        parseInt(new Date().getFullYear().toString()) + '-' + parseInt((new Date().getMonth() + 1).toString());

    public signDataTotal: any = {
        random: +new Date(),
        dataSource: [],
        continueDay: 0,
        todaySigned: false,
        addPoint: 1000,
    };

    async getScoreGet() {
        const res = await getScoreGet();
        this.coinCount = Number(res.totalGold);
    }

    // 获取主按钮任务信息, 设置主按钮状态和倒计时
    async getMainBtnTaskInfo() {
        const getTaskInfoPromise = getTaskInfo({
            taskCode: 'mall/dreame/gold_receive_prize_ten',
        });
        const getRandCoinsPromise = getRandCoins();

        const [res, randCoinsRes] = await Promise.all([getTaskInfoPromise, getRandCoinsPromise]);

        this.mainBtnTaskInfo = res || {};

        this.randCoins = randCoinsRes?.randCoins || 0;

        const currentTime = new Date().getTime();
        if (this.mainBtnTaskInfo?.lastEventTime) {
            const diff = Math.abs(currentTime - this.mainBtnTaskInfo.lastEventTime);
            if (diff >= 10 * 60 * 1000) {
                // if (diff >= 10 * 1000) {
                this.isShowCanGainBtn = true;
            } else {
                this.isShowCanGainBtn = false;
                this.mainBtnCountdownTime = 10 * 60 * 1000 - diff;
            }
        }
    }

    // 点击赚金币按钮
    @CheckAppJump()
    async clickMainBtn() {
        if (this.isShowCanGainBtn) {
            // 领取金币
            const res = await presentedGoldCoins({
                type: 3,
            });

            this.rewardPopupRewardNum = res.total_coins;
            this.showRewardPopup = true;

            // 更新主按钮任务信息
            this.getMainBtnTaskInfo();
        }
    }

    handleShowRewardPopup(res) {
        this.rewardPopupRewardNum = res;
        this.showRewardPopup = true;
    }

    @CheckAppJump()
    goToViewShop() {
        Utils.navigateTo('/pages/shop/shop');
    }

    @CheckAppJump()
    goToVideos() {
        Utils.navigateTo('/pagesC/contentVideoDetails/contentVideoDetails?from=goldCoins');
        // Utils.newMessageChannel('PAGE', 'tab', { tabType: 'mall_discovery' });
        // Utils.messageChannel('closeWebView', '');
    }

    @CheckAppJump()
    async openSignInPopup() {
        this.allTaskPopup = false;
        this.signInPopup = true;

        await this.getSignInStatus();
        await this.getSignInDetail();
    }

    @CheckAppJump()
    async openDrawGoldPopup() {
        // 先去查老虎机接口的数据item
        await this.getScoreUncollect();
        this.allTaskPopup = false;
        this.showDrawGoldPopup = true;
    }

    closeRewardPopup() {
        this.showRewardPopup = false;
        // 更新分数
        this.getScoreGet();
        // if (!this.isOpenedRemind) {
        //     this.showRemindPopup = true;
        // }
    }

    closeDrawGoldPopup() {
        this.showDrawGoldPopup = false;
    }

    setRemind() {
        // TODO 设置签到提醒，设置成功后，关闭弹窗
        this.showRemindPopup = false;
        this.isOpenedRemind = true;
    }

    closeRemind() {
        // TODO 关闭签到提醒，关闭弹窗
        this.isOpenedRemind = false;
    }

    @CheckAppJump()
    goToAllTask() {
        this.allTaskPopup = true;
    }

    async onLoad(params) {
        Utils.setPageTitle('赚金币');
        const win: any = window;
        this.link = params.link;

        this.getMainBtnTaskInfo();

        this.init();
        this.checkShowSignInPopup();
        this.getLimitGold();

        win.onAppPageShow = async () => {
            this.init();
        };

        const { sharerUid } = params;
        if (sharerUid && sharerUid !== String(this.user_id)) {
            joinInvite({
                invite_type: 7,
                inviter_id: sharerUid,
                relate_id: 7,
            }).then((res) => {
                console.log(res);
            });
        }
        console.log(params);
    }

    async init() {
        Utils.isInApp() ? VIPModule.getCoinTaskList() : VIPModule.noUserIdGetCoinTaskList();
        this.getCoinConfigHandler();
        this.getScoreGet();
        // this.getSignInStatus();
        this.getGoldCoin();
    }

    public goldCoin = 0;

    getGoldCoin() {
        const params = {
            group_code: 'mall',
            type_code: 'dreame',
            code: 'search_view_goods',
            taskCode: 'mall/dreame/search_view_goods',
        };
        getCoin(params).then((res) => {
            this.goldCoin = res.gold;
        });
    }

    // 检查是否需要弹出签到弹窗
    async checkShowSignInPopup() {
        const signInStatus = await getSignInStatus({ type: 3 });
        this.signInStatus = signInStatus;
        this.todaySigned = signInStatus.todaySignedIn;
        this.dateList = signInStatus.dateList;

        const isFirstComeIn = await checkIsFirstComeIn();

        // 第一次进页面，isFirstComeIn?.is_first 为 false, 后面为true
        if (!signInStatus.todaySignedIn && !isFirstComeIn?.is_first) {
            this.openSignInPopup();
        }
    }

    // 获取老虎机的抽奖结果item
    async getScoreUncollect() {
        const res = await getScoreUncollect({ type: 3 });
        const goldPrizeDrawItem = (res || []).find((item) => {
            return item.eventType === 'mall/dreame/gold_prize_draw';
        });
        this.goldPrizeDrawItem = goldPrizeDrawItem || {};
    }

    // 获取老虎机奖的user列表
    async getLimitGold() {
        const res = await getLimitGold({
            event_types: 'mall/dreame/gold_prize_draw',
        });
        this.drawGoldPopupUserList = res?.records || [];
    }

    async checkRemindStatus() {
        // TODO 检查是否开启了签到提醒
        this.isOpenedRemind = true;
    }

    async getSignInStatus() {
        const signInStatus = await getSignInStatus({ type: 3 });
        this.signInStatus = signInStatus;
        this.todaySigned = signInStatus.todaySignedIn;
        this.dateList = signInStatus.dateList;

        if (!signInStatus.todaySignedIn) {
            // 今天没签到
            // this.clickSign();
        } else {
            this.signDataTotal = {
                random: +new Date(),
                dataSource: [...this.signData],
                continueDay: this.continueDay,
                todaySigned: this.todaySigned,
                addPoint: this.addPoint,
            };
        }
    }

    async clickSign() {
        const signResult = await signInDaliy({
            type: 3,
        });

        if (signResult.lastSignInTime) {
            // 签到成功手动计算积分
            // this.totalPoints = Number(this._ScoreGet.totalPoints) + Number(this.addPoint);
            await this.fetchContinueDays();
            await this.getSignInDetail();
            await this.getSignInStatus();
        }

        // 关闭签到弹窗
        this.signInPopup = false;
        // 设置签到成功的奖励数值
        this.rewardPopupRewardNum = this.dateList.find((item) => item.isToday)?.goldValue || 0;
        // 弹出积分到账弹窗
        this.showRewardPopup = true;
    }

    async fetchContinueDays() {
        const continueDays = await getContinueDays();
        this.continueDay = continueDays.arriveContinueNeedDays;
        this.addPoint = continueDays.todaySignInPoint;
    }

    @CheckAppJump()
    inviteHandler() {
        // const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?sharerUid=${this.user_id}`;
        // const data = {
        //     ...Constants.GEN_SHARE_LINK_TYPE,
        //     jumpLink: url,
        // };
        // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
        //     const params = {
        //         target: 'wechat,weixin_circle,qq,sina',
        //         type: 'web',
        //         content: {
        //             url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoinsShare?link=${res.data}`,
        //             share_image:
        //                 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889be0d0dc8a0560012127.png',
        //             share_title: `天天赚金币`,
        //             share_desc: `看视频赚金币，新款扫地机0元带走！`,
        //         },
        //     };
        //     Utils.messageChannel('share', params);
        // });
        // if (UserModule.sdkVersion < 13) {
        //     Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
        //     return;
        // }
        this.show = true;
        const shareProductList = this.$refs.productListRef.exposeProductList();
        const shareProductInfo = shareProductList[Math.floor(Math.random() * shareProductList.length)];
        this.shareId = shareProductInfo.gid;
        this.productInfo = {
            name: shareProductInfo.name,
            desc: shareProductInfo.introduce,
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#FF3654',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109e9d32158650013549.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.cardShare(type)(
            {
                target: 'wechat,weixin_circle,qq,sina,image_template,download',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?sharerUid=${this.user_id}`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?sharerUid=${this.user_id}`,
            },
            {
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?sharerUid=${this.user_id}`,
                    image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889be0d0dc8a0560012127.png',
                    title: '天天赚金币',
                    desc: '看视频赚金币，新款扫地机0元带走！',
                },
                extras: {
                    type: 'activity',
                    id: this.shareId,
                    goods: this.productInfo,
                    copyText: `追觅好物，轻松赚金币，购物抵现金！`,
                },
            },
        );
    }

    goToBack() {
        Utils.goBack();
    }

    shareActivity() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_EARN_MONEY_INVITE_CLICK,
            },
            true,
        );
        if (UserModule.sdkVersion < 13) {
            console.log('%c UserModule: ', 'font-size:16px;background-color: #ED9EC7;color:#fff;', UserModule);
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        this.show = true;
        const shareProductList = this.$refs.productListRef.exposeProductList();
        const shareProductInfo = shareProductList[Math.floor(Math.random() * shareProductList.length)];
        console.log('%c shareProductInfo: ', 'font-size:16px;background-color: #465975;color:#fff;', shareProductInfo);
        this.shareId = shareProductInfo.gid;
        this.productInfo = {
            name: shareProductInfo.name,
            desc: shareProductInfo.introduce,
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#FF3654',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109e9d32158650013549.png',
            badge: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891f4330ab560440011775.png',
        };
        // // 直接生成分享页面的链接，避免双重跳转
        // const sharePageUrl = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins`;
        // const data = {
        //     ...Constants.GEN_SHARE_LINK_TYPE,
        //     jumpLink: sharePageUrl,
        // };
        // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
        //     const params = {
        //         target: 'wechat,weixin_circle,qq,sina',
        //         type: 'web',
        //         content: {
        //             url:
        //                 `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?link=` +
        //                 res.data,
        //             share_image:
        //                 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887a231e180f9240019837.png',
        //             share_title: '天天赚金币',
        //             share_desc: '看视频赚金币，新款扫地机0元带走！',
        //         },
        //     };
        //     Utils.messageChannel('share', params);
        // });
    }
}
</script>

<style lang="scss" scoped>
@import './goldCoins.scss';

@font-face {
    font-family: 'YouSheBiaoTiHei';
    // 字体中的文字是：+0123456789金币到账
    src: url('@/static/fonts/YouSheBiaoTiHei.ttf') format('truetype');
}

.gold-coins-container {
    min-height: 100vh;
    background-color: #ffc987;
    position: relative;
}

::v-deep .page-view {
    padding: 0 !important;
}

.rule-img {
    width: 100%;
}

.header-content {
    position: relative;
    height: 1200rpx;
    padding-right: 30rpx;
    padding-left: 16rpx;
    padding-top: var(--status-bar-height, 44px);
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ccd11f40a1280010662.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.header {
    width: 100%;
    height: 92rpx;
    position: relative;

    .left {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 46rpx;
        height: 46rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d3315dc733840015096.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .title {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-family: MiSans;
        font-size: 35rpx;
        font-weight: 500;
        color: #121212;
    }
}

.header-coin {
    width: 562rpx;
    height: 170rpx;
    position: absolute;
    left: 50%;
    top: 200rpx;
    transform: translateX(-50%);
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888db8f671ef4220010533.png')
        no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-coin-item {
        text-align: center;
        width: calc(50% - 18rpx);
        margin-top: 50rpx;

        .header-coin-item-title {
            font-family: MiSans;
            font-size: 20rpx;
            color: #000000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .header-coin-item-value {
            font-family: MiSans;
            font-size: 56rpx;
            font-weight: 600;
            color: #ff2300;
            line-height: 1.2;
        }
    }

    .icon {
        z-index: 9;
        margin-top: 70rpx;
        width: 36rpx;
        height: 36rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ecd8b0db77240010827.png')
            no-repeat center center;
        background-size: 100% 100%;
    }
}

.header-bottom {
    position: absolute;
    left: 0;
    top: 330rpx;
    width: 100%;
    height: 360rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e28121a201380010823.png')
        no-repeat center center;
    background-size: 100% 100%;

    .header-bottom-item,
    .earn-gold-coins {
        position: absolute;
        bottom: 0;
    }

    .header-bottom-item {
        width: 80rpx;
        height: 80rpx;
    }

    .signIn {
        left: 24rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a52566efbdd9820011333.png')
            no-repeat center center;
        background-size: 100% 100%;

        &.signIned {
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a52566ed3629720011292.png')
                no-repeat center center;
            background-size: 100% 100%;
        }
    }

    .draw-gold {
        left: 132rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a52566ecb929700021019.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .invite {
        margin-left: 32rpx;
        left: 135rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888e855ed9a49730010461.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .earn-gold-coins {
        width: 260rpx;
        height: 120rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2be5b0e3f60580010782.png')
            no-repeat center center;
        background-size: 100% 100%;
        left: 50%;
        transform: translateX(-50%);
        bottom: -10rpx;
        z-index: 1;

        &.disabled {
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2be5b0e3ea0580010612.png')
                no-repeat center center;
            background-size: 100% 100%;
        }
    }

    .finger-gif {
        width: 220rpx;
        height: 220rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a4220e89ac75640011268.gif')
            no-repeat center center;
        background-size: 100% 100%;
        position: absolute;
        left: 40%;
        bottom: -90rpx;
    }

    .earn-gold-coins-title {
        font-family: MiSans;
        height: 58rpx;
        line-height: 58rpx;
        font-size: 44rpx;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        margin-top: 16rpx;
    }

    .earn-gold-coins-desc {
        font-family: MiSans;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        height: 20rpx;
        line-height: 20rpx;
        font-size: 20rpx;

        ::v-deep .u-count-down__text {
            color: #ffffff;
            font-size: 20rpx;
            line-height: 20rpx;
        }
    }

    .earn-gold-coins-desc-text {
        margin-left: 4rpx;
    }

    .small-gold-icon {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2c18573fa14750011584.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 20rpx;
        height: 20rpx;
        margin-right: 4rpx;
    }

    .small-gold-icon-num {
        font-family: MiSans;
        font-size: 20rpx;
        font-weight: 600;
        height: 20rpx;
        line-height: 20rpx;
    }

    .videos {
        right: 132rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a52566ee5a79760010540.png')
            no-repeat center center;
        background-size: 100% 100%;
    }

    .browse {
        right: 24rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a52566ef6fe9810029068.png')
            no-repeat center center;
        background-size: 100% 100%;
        .browse-text {
            position: absolute;
            left: 35rpx;
            bottom: 55rpx;
            min-width: 40rpx;
            padding: 0 10rpx;
            height: 31rpx;
            border-radius: 20rpx;

            /* 自动布局 */
            text-align: center;
            line-height: 31rpx;
            background: #ff2300;
            font-family: MiSans;
            font-size: 20rpx;
            font-weight: 500;
            font-variation-settings: 'opsz' auto;
            font-feature-settings: 'kern' on;
            color: #ffffff;
        }
    }
}

.rule-popup-content {
    width: 664rpx;
    height: 824rpx;
    max-height: 824rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f035eb9e49650010648.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx;
    overflow: hidden;

    .rule-popup-header {
        padding: 94rpx 36rpx 30rpx;
        position: relative;
        text-align: center;

        .rule-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            .rule-title-decoration {
                width: 20rpx;
                height: 20rpx;

                // &.left {
                //     margin-right: 16rpx;
                //     background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                //         no-repeat center center;
                //     background-size: 100% 100%;
                //     width: 92rpx;
                //     height: 20rpx;
                // }

                // &.right {
                //     margin-left: 16rpx;
                //     background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                //         no-repeat center center;
                //     background-size: 100% 100%;
                //     width: 92rpx;
                //     height: 20rpx;
                //     transform: rotate(180deg);
                // }
            }

            .rule-title-text {
                font-size: 36rpx;
                font-weight: 600;
                color: #ffb329;
                position: relative;
            }
        }

        .rule-popup-close {
            position: absolute;
            right: 36rpx;
            top: 24rpx;
            width: 46rpx;
            height: 46rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;

            .close-icon {
                width: 46rpx;
                height: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }
    }

    .rule-container {
        padding: 0 36rpx 32rpx;

        .rule-content {
            background: #ffffff;
            border-radius: 32rpx;
            padding: 24rpx 14rpx 24rpx 0rpx;

            .rule-content-text {
                padding: 0rpx 24rpx;
                max-height: 572rpx;
                height: 620rpx;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    display: block;
                    width: 8rpx !important;
                    height: 0rpx !important;
                    opacity: 0; // 不生效
                    transition: height 2s;
                }

                &::-webkit-scrollbar-track {
                    border-radius: 10rpx !important;
                }

                &::-webkit-scrollbar-thumb {
                    background: #d8d8d8 !important;
                    border-radius: 10rpx !important;
                }
            }

            .rule-section {
                margin-bottom: 32rpx;
                font-size: 22rpx;
                color: #3d3d3d;
                line-height: 42rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .rule-section-title {
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #333333;
                    margin-bottom: 16rpx;
                    line-height: 1.4;
                }

                .rule-section-content {
                    .rule-text {
                        font-size: 28rpx;
                        color: #666666;
                        line-height: 1.6;
                        margin-bottom: 12rpx;
                        text-align: justify;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}

.task-center-list {
    position: absolute;
    left: 0;
    bottom: 20rpx;
    width: calc(100% - 40rpx);
    margin-left: 20rpx;
    height: 480rpx;
    background: #ffffff;
    padding: 24rpx 0 56rpx;
    border-radius: 32rpx;
    .task-center-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 34rpx;
        .left {
            font-family: MiSans;
            font-size: 36rpx;
            font-weight: 600;
            color: #121212;
        }
        .right {
            font-family: MiSans;
            font-size: 28rpx;
            color: #727475;
        }
    }
}

.all-task-popup-content {
    z-index: 9999;
    width: 100%;
    height: 1200rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888f7aa9f5b26530012246.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx 32rpx 0px 0px !important;
    padding: 22rpx 24rpx;

    .all-task-popup-header {
        height: 75rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            width: 321rpx;
            height: 75rpx;
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888f9bcd759c8820012134.png')
                no-repeat center center;
            background-size: 100% 100%;
        }

        .right {
            background: rgba(255, 255, 255, 0.25);
            width: 52rpx;
            height: 52rpx;
            border-radius: 50%;

            image {
                width: 100%;
                height: 100%;
            }
        }
    }
}

.content_list {
    width: 100%;
    background: linear-gradient(180deg, #ffffff 0%, #f2f2f2 10%);
    border-radius: 16rpx 16rpx 0 0;
    padding: 24rpx 16rpx 0rpx;

    .search_box {
        width: 100%;
        height: 66rpx;
        background-color: #fff;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        padding: 0 24rpx;
        border: 2rpx solid #ff3c00;
        margin-bottom: 24rpx;
        position: sticky;
        top: 80rpx;
        z-index: 999;

        .search_box_center {
            flex: 1;
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            font-size: 28rpx;
            color: #727475;
        }

        .search_box_left {
            width: 40rpx;
            margin-right: 16rpx;
        }

        .search_box_right {
            text {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                color: #ff3c00;
            }
        }
    }
}

.all-task-popup-body {
    height: calc(100% - 75rpx);
    overflow-y: auto;
    padding-bottom: 20rpx;
    padding-top: 40rpx;
}

.signIn-popup-content {
    width: 644rpx;
    // height: 800rpx;
    border-radius: 40rpx;
    opacity: 1;
    padding: 32rpx 32rpx 24rpx;
    background: #fff1e6;
    position: relative;

    .signIn-popup-header {
        width: 100%;
        height: 90rpx;
        position: absolute;
        left: 50%;
        top: -60rpx;
        transform: translateX(-50%);

        .title-img {
            width: 542rpx;
            height: 112rpx;
            position: absolute;
            left: 50%;
            top: -20rpx;
            transform: translateX(-50%);
        }

        .line-img {
            width: 260rpx;
            height: 60rpx;
            position: absolute;
            right: 140rpx;
            bottom: 0;
        }
    }
}

.signIn-title {
    height: 37rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24rpx;

    image {
        width: 344rpx;
        height: 37rpx;
    }
}

.signIn-popup-body {
    .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 180rpx);
        grid-template-rows: repeat(2, 200rpx);
        gap: 20rpx;
        justify-content: center;

        .image-item {
            width: 180rpx;
            height: 200rpx;

            .image-bg-active,
            .image-bg {
                width: 100%;
                height: 100%;
                border-radius: 28rpx;
            }

            .image-bg-active {
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889034a443292790063671.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }

            .image-bg {
                position: relative;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68890339d0faf8560010849.png')
                    no-repeat center center;
                background-size: 100% 100%;

                .title {
                    font-family: MiSans;
                    font-size: 24rpx;
                    color: #777777;
                    position: absolute;
                    left: 50%;
                    top: 16rpx;
                    transform: translateX(-50%);
                }

                .number {
                    font-family: MiSans;
                    font-size: 36rpx;
                    font-weight: 600;
                    position: absolute;
                    color: #ff2300;
                    left: 50%;
                    bottom: 16rpx;
                    transform: translateX(-50%);
                }
            }
        }
    }
}

.signIn-popup-close {
    width: 52rpx;
    height: 52rpx;
    opacity: 1;
    box-sizing: border-box;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -90rpx;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        width: 40rpx;
        height: 40rpx;
    }
}

.image-item-max {
    width: 580rpx;
    height: 200rpx;
    border-radius: 30rpx;
    opacity: 1;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
        width: 50%;
        text-align: center;

        .title {
            font-family: MiSans;
            font-size: 24rpx;
            color: #777777;
        }

        .number {
            font-family: MiSans;
            font-size: 56rpx;
            font-weight: 600;
            color: #ff2300;
        }
    }

    .right {
        width: 50%;
        text-align: center;

        .icon-image {
            width: 244rpx;
            height: 162rpx;
        }
    }
}
</style>
