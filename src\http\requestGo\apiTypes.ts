export interface IResponse<T> {
    code: number;
    data: T;
    msg: string;
    requestId: string;
    success: boolean;
}

export interface IRequestConfig<P> {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: P;
    header?: Record<string, string>;
    timeout?: number;
    [key: string]: any;
}

export interface ITencentResponse<T> {
    status: number;
    data: T;
    message: string;
    request_id: string;
}
