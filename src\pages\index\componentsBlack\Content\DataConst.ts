const firstTabs = [
    {
        name: '推荐',
        tid: '10000',
        type: -2,
        tag_img: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅品牌',
        tid: '10001',
        type: 0,
        tag_img: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅生态链品牌',
        tid: '10002',
        type: 1,
        tag_img: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    // {
    //     name: '追觅严选',
    //     tid: '10003',
    //     type: 2,
    //     tag_img: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    // }
]

const brandLists = [
     {
        name: '推荐',
        tid: '99',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅品牌',
        tid: '100',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅生态链品牌',
        tid: '101',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅严选',
        tid: '102',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
     {
        name: '推荐',
        tid: '99',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅品牌',
        tid: '100',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅生态链品牌',
        tid: '101',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    },
    {
        name: '追觅严选',
        tid: '102',
        image: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
    }
]

const productBlockLists = [
  {
    gid: '1424',
    sku: '222222222222',
    name: '\u6d4b\u8bd5\u8fdb\u5ea6\u6761\u663e\u793a',
    cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/639438-1753530405920.png',
    market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/878201-1753530410950.png',
    mprice: '60000.00',
    price: '60000.00',
    is_presale: 0,
    presale_time: '',
    is_internal: 0,
    is_internal_purchase: '0',
    is_ini: 0,
    gini_id: 0,
    is_trade_in: 0,
    tids: [
        '2',
        '11'
    ],
    custom_tag: '',
    deposit: '0.00',
    expand_price: '0.00',
    atmosphere_img: '',
    coupon_price: '60000.00',
    subsidy_price: '0.00',
    goods_recommend_times: 0,
    is_wish: false,
    goods_tag_name: '\u54c1\u724c',
    can_use_coin: 3564,
    deduction_rate: '100',
    stock: 10,
    sales: 0
},
  {
    gid: '1138',
    sku: '111111111112',
    name: '\u3010\u52ff\u5220\u3011\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011\u8d2d\u7269\u91d1\u62b5\u626350',
    cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/976466-1753341995326.jpg',
    market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/150665-1753341989986.jpg',
    mprice: '160.00',
    price: '157.00',
    is_presale: 0,
    presale_time: '',
    is_internal: 0,
    is_internal_purchase: '0',
    is_ini: 0,
    gini_id: 0,
    is_trade_in: 0,
    tids: [
        '2'
    ],
    custom_tag: '',
    deposit: '0.00',
    expand_price: '0.00',
    atmosphere_img: '',
    coupon_price: '157.00',
    subsidy_price: '0.00',
    goods_recommend_times: 4,
    is_wish: false,
    goods_tag_name: '\u54c1\u724c',
    can_use_coin: 3564,
    deduction_rate: '100',
    stock: 100,
    sales: 0
},
  {
    did: '950923713',
    model: 'dreame.vacuum.r2507',
    ver: '4.3.9_1625',
    customName: 'S50 Ultra失礼了',
    property: {
        iotId: '2iROUYfgpqUwIIcEPGYb000000',
        lwt: 1,
        mac: '70:C9:32:97:A0:75'
    },
    sn: 'R2507052JCN0132533',
    master: true,
    masterUid: 'EP595300',
    masterName: null,
    permissions: '',
    bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
    sharedStatus: 1,
    sharedTimes: 0,
    lang: '',
    updateTime: '2025-04-22 11:29:25',
    deviceInfo: {
        productId: '10616',
        categoryPath: '/lifeapps/vacuum',
        model: 'dreame.vacuum.r2507',
        remark: '',
        feature: 'fastCommand',
        videoDynamicVendor: true,
        permit: 'video',
        scType: 'WIFI',
        status: 'Live',
        displayName: 'S50 Ultra',
        mainImage: {
            imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2507/f16d7ef75eaf30d90a2bf3c638eced5f20250210055438.png',
            smallImageUrl: '',
            imageFile: null
        }
    },
    online: false,
    latestStatus: 3,
    battery: 24,
    lastWill: null,
    keyDefine: {
        ver: 5,
        url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2507_iotKeyValue_translate_5.json'
    },
    btnMode: null,
    latestStatusStr: null,
    isLocalCache: false,
    commonBtnProtol: null,
    deviceRepairInfo: null,
    basicProtocolData: [],
    featureCode: -1,
    featureCode2: -1,
    iotId: null,
    vendor: 'ali',
    videoStatus: {
        operType: 'end',
        operation: 'monitor',
        result: 0,
        status: 0
    },
    monitorStatus: null,
    cleanArea: null,
    cleanTime: null,
    charingStatus: null,
    fastCommandList: null
},
    {
        gid: '854',
        sku: '23453254365464',
        name: '\u9884\u552e\u5546\u54c17/22\u4e13\u7528',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/503895-1753167177390.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/561834-1753167183748.jpg',
        mprice: '99.00',
        price: '10.00',
        is_presale: 0,
        presale_time: 1753515000000,
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2',
            '11'
        ],
        custom_tag: '',
        deposit: '1.00',
        expand_price: '4.00',
        atmosphere_img: '',
        coupon_price: '10.00',
        subsidy_price: '0.00',
        goods_recommend_times: 3,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 400,
        deduction_rate: '100',
        stock: 1108,
        sales: 0
    },
    {
        gid: '1280',
        sku: '11111111112',
        name: '\u3010\u52ff\u52a8\u3011\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011\u9884\u552e\u5546\u54c1',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/139273-1753431247328.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/941207-1753431251892.jpg',
        mprice: '299.00',
        price: '289.00',
        is_presale: 0,
        presale_time: 1753459200000,
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2'
        ],
        custom_tag: '',
        deposit: '50.00',
        expand_price: '100.00',
        atmosphere_img: '',
        coupon_price: '289.00',
        subsidy_price: '0.00',
        goods_recommend_times: 0,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 3564,
        deduction_rate: '100',
        stock: 0,
        sales: 0
    },
    {
        gid: '1138',
        sku: '111111111112',
        name: '\u3010\u52ff\u5220\u3011\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011\u8d2d\u7269\u91d1\u62b5\u626350',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/976466-1753341995326.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/150665-1753341989986.jpg',
        mprice: '160.00',
        price: '157.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '157.00',
        subsidy_price: '0.00',
        goods_recommend_times: 4,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 3564,
        deduction_rate: '100',
        stock: 100,
        sales: 0
    },
    {
        gid: '1137',
        sku: '11111111111',
        name: '\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011_>\u666e\u901a\u5546\u54c1\uff01\uff01',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/307724-1753253244177.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/005805-1753253248947.jpg',
        mprice: '299.00',
        price: '299.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2',
            '11'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '299.00',
        subsidy_price: '0.00',
        goods_recommend_times: 3,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 3564,
        deduction_rate: '100',
        stock: 100,
        sales: 0
    },
    {
        gid: '1136',
        sku: '1111111111111',
        name: '\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011\u8d2d\u7269\u91d1\u53ef\u5168\u90e8\u62b5\u6263',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/945047-1753252835808.png',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/265384-1753252840758.png',
        mprice: '1000.00',
        price: '1000.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '1',
            '2'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '1000.00',
        subsidy_price: '0.00',
        goods_recommend_times: 3,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 3564,
        deduction_rate: '100',
        stock: 20,
        sales: 0
    },
    {
        gid: '1134',
        sku: '11111111111111',
        name: '\u3010\u534a\u4ef7\u8d2d\u6d4b\u8bd5\u3011\u652f\u6301\u56fd\u8865\u5546\u54c1',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/952603-1753250407418.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/687315-1753250411868.jpg',
        mprice: '500.00',
        price: '499.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2',
            '11'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '499.00',
        subsidy_price: '49.90',
        goods_recommend_times: 1,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 3564,
        deduction_rate: '100',
        stock: 97,
        sales: 3
    },
    {
        gid: '310',
        sku: '2309',
        name: '2309\u65b0\u5efa',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/158629-1753100325284.png',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/919038-1753100328651.png',
        mprice: '20.00',
        price: '5.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2',
            '11'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '5.00',
        subsidy_price: '0.00',
        goods_recommend_times: 3,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 200,
        deduction_rate: '100',
        stock: 81,
        sales: 0
    },
    {
        gid: '309',
        sku: '2307',
        name: '2307\u65b0\u5efa',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/158629-1753100325284.png',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/919038-1753100328651.png',
        mprice: '20.00',
        price: '5.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2',
            '11'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '5.00',
        subsidy_price: '0.00',
        goods_recommend_times: 2,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 200,
        deduction_rate: '100',
        stock: 83,
        sales: 0
    },
    {
        gid: '308',
        sku: '12345678',
        name: '7.21\u6d4b\u8bd5\u5546\u54c1',
        cover_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/335069-1753109975202.jpg',
        market_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/900971-1753109978568.jpg',
        mprice: '100.00',
        price: '10.00',
        is_presale: 0,
        presale_time: '',
        is_internal: 0,
        is_internal_purchase: '0',
        is_ini: 0,
        gini_id: 0,
        is_trade_in: 0,
        tids: [
            '2'
        ],
        custom_tag: '',
        deposit: '0.00',
        expand_price: '0.00',
        atmosphere_img: '',
        coupon_price: '10.00',
        subsidy_price: '0.00',
        goods_recommend_times: 2,
        is_wish: false,
        goods_tag_name: '\u54c1\u724c',
        can_use_coin: 400,
        deduction_rate: '100',
        stock: 0,
        sales: 0
    }
]

const deviceListsMock = [
  {
    did: '2014996591',
    model: 'dreame.vacuum.r2508',
    ver: '4.3.9_2526',
    customName: 'S50 Pro',
    property: {
        lwt: 1,
        mac: '70:C9:32:D8:A3:58'
    },
    sn: 'R2508056QCNZ079601',
    master: true,
    masterUid: 'EP595300',
    masterName: null,
    permissions: '',
    bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
    sharedStatus: 1,
    sharedTimes: 0,
    lang: '',
    updateTime: '2025-07-10 13:07:56',
    deviceInfo: {
        productId: '10617',
        categoryPath: '/lifeapps/vacuum',
        model: 'dreame.vacuum.r2508',
        remark: '',
        feature: 'fastCommand',
        videoDynamicVendor: true,
        permit: 'video',
        scType: 'WIFI',
        status: 'Live',
        displayName: 'S50 Pro测试测测测测测测测测测测恶策',
        mainImage: {
            imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2508/2c0647f9794eec2ad01a83f6e4aeeb5320250210055412.png',
            smallImageUrl: '',
            imageFile: null
        }
    },
    online: true,
    latestStatus: 13,
    battery: 100,
    lastWill: null,
    keyDefine: {
        ver: 5,
        url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2508_iotKeyValue_translate_5.json'
    },
    btnMode: null,
    latestStatusStr: null,
    isLocalCache: false,
    commonBtnProtol: null,
    deviceRepairInfo: null,
    basicProtocolData: [],
    featureCode: -1,
    featureCode2: 63,
    iotId: null,
    vendor: '',
    videoStatus: {
        operType: 'end',
        operation: 'monitor',
        result: 0,
        status: 0
    },
    monitorStatus: null,
    cleanArea: null,
    cleanTime: null,
    charingStatus: null,
    fastCommandList: null
},
  {
    did: '2014996591',
    model: 'dreame.vacuum.r2508',
    ver: '4.3.9_2526',
    customName: 'S50 Pro测试测测测测测测测测测测恶策',
    property: {
        lwt: 1,
        mac: '70:C9:32:D8:A3:58'
    },
    sn: 'R2508056QCNZ079601',
    master: true,
    masterUid: 'EP595300',
    masterName: null,
    permissions: '',
    bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
    sharedStatus: 1,
    sharedTimes: 0,
    lang: '',
    updateTime: '2025-07-10 13:07:56',
    deviceInfo: {
        productId: '10617',
        categoryPath: '/lifeapps/vacuum',
        model: 'dreame.vacuum.r2508',
        remark: '',
        feature: 'fastCommand',
        videoDynamicVendor: true,
        permit: 'video',
        scType: 'WIFI',
        status: 'Live',
        displayName: 'S50 Pro 测试测测测测测测测测测测恶策',
        mainImage: {
            imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2508/2c0647f9794eec2ad01a83f6e4aeeb5320250210055412.png',
            smallImageUrl: '',
            imageFile: null
        }
    },
    online: true,
    latestStatus: 13,
    battery: 100,
    lastWill: null,
    keyDefine: {
        ver: 5,
        url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2508_iotKeyValue_translate_5.json'
    },
    btnMode: null,
    latestStatusStr: null,
    isLocalCache: false,
    commonBtnProtol: null,
    deviceRepairInfo: null,
    basicProtocolData: [],
    featureCode: -1,
    featureCode2: 63,
    iotId: null,
    vendor: '',
    videoStatus: {
        operType: 'end',
        operation: 'monitor',
        result: 0,
        status: 0
    },
    monitorStatus: null,
    cleanArea: null,
    cleanTime: null,
    charingStatus: null,
    fastCommandList: null
},
   {
        did: '-110948176',
        model: 'dreame.airp.u2401',
        ver: '1.7.1_1028',
        customName: '',
        property: {
            mac: '90:EB:48:14:C0:02'
        },
        sn: '',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-10 09:01:41',
        deviceInfo: {
            productId: '10435',
            categoryPath: '/lifeapps/airp',
            model: 'dreame.airp.u2401',
            remark: '',
            feature: '',
            videoDynamicVendor: false,
            permit: '',
            scType: 'WIFI_BLE',
            status: 'Live',
            displayName: '空气净化器PM10',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/null/8a3662a42a0c5991311bbf8d69f6e86720250724072554.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 1,
        battery: 0,
        lastWill: null,
        keyDefine: {
            ver: 4,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/a51c2e1562efb3e76b9aef245aa6b569238b5323_dreame.airp.u2401_iotKeyValue_translate_4.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: []
    },
     {
        did: '605923942',
        model: 'dreame.vacuum.r2228',
        ver: '4.3.9_3032',
        customName: '扫地机',
        property: {
            lwt: 1,
            mac: '5C:C5:63:EA:DF:D9'
        },
        sn: null,
        master: false,
        masterUid: 'VC758018',
        masterName: 'VC758018',
        permissions: null,
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2024-10-07 09:49:03',
        deviceInfo: {
            productId: '10062',
            categoryPath: '/lifeapps/hold', // '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2228',
            remark: '',
            feature: 'video,fastCommand',
            videoDynamicVendor: false,
            permit: '',
            scType: 'WIFI_BLE',
            status: 'Live',
            displayName: '追觅S10',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/dreame-public/prod/products/dreame.vacuum.r2228/images/76ac34c2d41229b6957fd83f446d6482.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: false,
        latestStatus: 2,
        battery: 0,
        lastWill: null,
        keyDefine: null,
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: 1,
        featureCode2: 7,
        iotId: null,
        vendor: '',
        videoStatus: null,
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },

    {
        did: '2014996591',
        model: 'dreame.vacuum.r2508',
        ver: '4.3.9_2526',
        customName: 'S50 Pro 测试测测测测测测测测测测恶策',
        property: {
            lwt: 1,
            mac: '70:C9:32:D8:A3:58'
        },
        sn: 'R2508056QCNZ079601',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-10 13:07:56',
        deviceInfo: {
            productId: '10617',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2508',
            remark: '',
            feature: 'fastCommand',
            videoDynamicVendor: true,
            permit: 'video',
            scType: 'WIFI',
            status: 'Live',
            displayName: 'S50 Pro 测试测测测测测测测测测测恶策',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2508/2c0647f9794eec2ad01a83f6e4aeeb5320250210055412.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 13,
        battery: 100,
        lastWill: null,
        keyDefine: {
            ver: 5,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2508_iotKeyValue_translate_5.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: 63,
        iotId: null,
        vendor: '',
        videoStatus: {
            operType: 'end',
            operation: 'monitor',
            result: 0,
            status: 0
        },
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '836581962',
        model: 'dreame.vacuum.r2401',
        ver: '4.3.9_1589',
        customName: 'X40ultra坦克🐂🍺',
        property: {
            iotId: 'ix6AKFo2gUdZmYOxOabv000000',
            lwt: 1,
            mac: '70:C9:32:4E:2D:10'
        },
        sn: null,
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 1,
        lang: '',
        updateTime: '2025-07-10 09:01:41',
        deviceInfo: {
            productId: '10231',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2401',
            remark: '',
            feature: 'video_ali,fastCommand',
            videoDynamicVendor: true,
            permit: 'video',
            scType: 'WIFI',
            status: 'Live',
            displayName: 'X40 Pro Ultra',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2401/aa63f6c0d30078bc6af11fe2cf2b278b20240419014038.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 13,
        battery: 100,
        lastWill: null,
        keyDefine: {
            ver: 3,
            url: 'https://cnbj2.fds.api.xiaomi.com/000000-public/file/60fd169c193a53ef267943af601f7f09c7723d53_dreame.vacuum.r2401_iotKeyValue_translate_3.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: 31,
        iotId: null,
        vendor: 'ali',
        videoStatus: {
            operType: 'end',
            operation: 'monitor',
            result: 0,
            status: 0
        },
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '-110948176',
        model: 'dreame.airp.u2401',
        ver: '1.7.1_1028',
        customName: '',
        property: {
            mac: '90:EB:48:14:C0:02'
        },
        sn: '',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-10 09:01:41',
        deviceInfo: {
            productId: '10435',
            categoryPath: '/lifeapps/airp',
            model: 'dreame.airp.u2401',
            remark: '',
            feature: '',
            videoDynamicVendor: false,
            permit: '',
            scType: 'WIFI_BLE',
            status: 'Live',
            displayName: '空气净化器PM10',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/null/8a3662a42a0c5991311bbf8d69f6e86720250724072554.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 1,
        battery: 0,
        lastWill: null,
        keyDefine: {
            ver: 4,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/a51c2e1562efb3e76b9aef245aa6b569238b5323_dreame.airp.u2401_iotKeyValue_translate_4.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: []
    },
    {
        did: '989020449',
        model: 'dreame.vacuum.r2513',
        ver: '4.3.9_2532',
        customName: 'X50 MatriX',
        property: {
            iotId: 'MPqrTws6oEDfzYrkPKP3000000',
            lwt: 1,
            mac: '70:C9:32:B4:12:B8'
        },
        sn: 'R25130557CN0401033',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-10 09:01:41',
        deviceInfo: {
            productId: '10783',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2513',
            remark: '',
            feature: 'fastCommand',
            videoDynamicVendor: true,
            permit: 'video',
            scType: 'WIFI',
            status: 'Live',
            displayName: 'Matrix10 Ultra',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2513/140a46b9a15b49e1de8498aca98a8df120250522092226.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 21,
        battery: 100,
        lastWill: null,
        keyDefine: {
            ver: 9,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/a809cfc6f0562da6c376ca28da86eef235d004a9_dreame.vacuum.r2513_iotKeyValue_translate_9.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: 63,
        iotId: null,
        vendor: 'ali',
        videoStatus: {
            operType: 'end',
            operation: 'monitor',
            result: 0,
            status: 0
        },
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '996864471',
        model: 'dreame.vacuum.r2551a',
        ver: '4.3.9_1049',
        customName: 'L40s Ultra',
        property: {
            lwt: 1,
            mac: '70:C9:32:C3:9E:7E'
        },
        sn: 'R2551R554RU0011233',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-09 15:31:21',
        deviceInfo: {
            productId: '10663',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2551a',
            remark: '',
            feature: 'fastCommand',
            videoDynamicVendor: false,
            permit: '',
            scType: 'WIFI',
            status: 'Testing',
            displayName: 'L40s Ultra',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2551a/fe39d8e6a9d185b412c37ecf4dbd341120250603015759.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: true,
        latestStatus: 13,
        battery: 100,
        lastWill: null,
        keyDefine: {
            ver: 1,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/54587b0364cdd763deba93a974ef5aa05cbe7dcc_dreame.vacuum.r2551a_iotKeyValue_translate_1.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: 10,
        iotId: null,
        vendor: '',
        videoStatus: null,
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '2014569558',
        model: 'dreame.vacuum.r2508',
        ver: '4.3.9_2301',
        customName: 'S50 Pro 测试测测测测测测测测测测恶策',
        property: {
            lwt: 1,
            mac: '70:C9:32:D2:1B:57'
        },
        sn: 'R25080567CNG076601',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-07-02 13:55:46',
        deviceInfo: {
            productId: '10617',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2508',
            remark: '',
            feature: 'fastCommand',
            videoDynamicVendor: true,
            permit: 'video',
            scType: 'WIFI',
            status: 'Live',
            displayName: 'S50 Pro 测试测测测测测测测测测测恶策',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2508/2c0647f9794eec2ad01a83f6e4aeeb5320250210055412.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: false,
        latestStatus: 2,
        battery: 99,
        lastWill: null,
        keyDefine: {
            ver: 5,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2508_iotKeyValue_translate_5.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: 63,
        iotId: null,
        vendor: 'tx',
        videoStatus: {
            operType: 'end',
            operation: 'monitor',
            result: 0,
            status: 0
        },
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '950923713',
        model: 'dreame.vacuum.r2507',
        ver: '4.3.9_1625',
        customName: 'S50 Ultra失礼了',
        property: {
            iotId: '2iROUYfgpqUwIIcEPGYb000000',
            lwt: 1,
            mac: '70:C9:32:97:A0:75'
        },
        sn: 'R2507052JCN0132533',
        master: true,
        masterUid: 'EP595300',
        masterName: null,
        permissions: '',
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2025-04-22 11:29:25',
        deviceInfo: {
            productId: '10616',
            categoryPath: '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2507',
            remark: '',
            feature: 'fastCommand',
            videoDynamicVendor: true,
            permit: 'video',
            scType: 'WIFI',
            status: 'Live',
            displayName: 'S50 Ultra',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/pub/pic/000000/ali_dreame/dreame.vacuum.r2507/f16d7ef75eaf30d90a2bf3c638eced5f20250210055438.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: false,
        latestStatus: 3,
        battery: 24,
        lastWill: null,
        keyDefine: {
            ver: 5,
            url: 'https://oss.iot.dreame.tech/pub/device_status_multi_lang/000000/ali_dreame/b2fc15bd5564b736a1660d588f2170ec86256ac2_dreame.vacuum.r2507_iotKeyValue_translate_5.json'
        },
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: -1,
        featureCode2: -1,
        iotId: null,
        vendor: 'ali',
        videoStatus: {
            operType: 'end',
            operation: 'monitor',
            result: 0,
            status: 0
        },
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    },
    {
        did: '605923942',
        model: 'dreame.vacuum.r2228',
        ver: '4.3.9_3032',
        customName: '扫地机',
        property: {
            lwt: 1,
            mac: '5C:C5:63:EA:DF:D9'
        },
        sn: null,
        master: false,
        masterUid: 'VC758018',
        masterName: 'VC758018',
        permissions: null,
        bindDomain: '10000.mt.cn.iot.dreame.tech:19973',
        sharedStatus: 1,
        sharedTimes: 0,
        lang: '',
        updateTime: '2024-10-07 09:49:03',
        deviceInfo: {
            productId: '10062',
            categoryPath: '/lifeapps/hold', // '/lifeapps/vacuum',
            model: 'dreame.vacuum.r2228',
            remark: '',
            feature: 'video,fastCommand',
            videoDynamicVendor: false,
            permit: '',
            scType: 'WIFI_BLE',
            status: 'Live',
            displayName: '追觅S10',
            mainImage: {
                imageUrl: 'https://oss.iot.dreame.tech/dreame-public/prod/products/dreame.vacuum.r2228/images/76ac34c2d41229b6957fd83f446d6482.png',
                smallImageUrl: '',
                imageFile: null
            }
        },
        online: false,
        latestStatus: 2,
        battery: 0,
        lastWill: null,
        keyDefine: null,
        btnMode: null,
        latestStatusStr: null,
        isLocalCache: false,
        commonBtnProtol: null,
        deviceRepairInfo: null,
        basicProtocolData: [],
        featureCode: 1,
        featureCode2: 7,
        iotId: null,
        vendor: '',
        videoStatus: null,
        monitorStatus: null,
        cleanArea: null,
        cleanTime: null,
        charingStatus: null,
        fastCommandList: null
    }
]

export {
    firstTabs,
    brandLists,
    productBlockLists,
    deviceListsMock
}
