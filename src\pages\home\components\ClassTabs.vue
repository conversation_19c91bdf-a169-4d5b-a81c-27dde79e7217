<template>
    <div class="tab-view">
        <div class="tabs-card">
            <div
                v-for="(tab, index) in tabList"
                :key="tab.id"
                class="tab-item"
                :class="{ active: currentTab === index }"
                @click="selectedTab(tab.id)"
            >
                {{ tab.name }}
            </div>
        </div>
        <div
            class="more-class-card"
            @click="goitoGoodsTypePage"
        >
            <img src="https://wpm-cdn.dreame.tech/images/202306/225448-1686107296428.png" class="type-icon" alt="分类" />
            <div class="type-text">分类</div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: 'ClassTabs',
    props: {
        tabList: {
            type: Array,
            default: () => [{ name: '我的设备', id: 0 }, { name: '推荐', id: 1 }, { name: '扫地机', id: 2 }, { name: '洗地机', id: 3 }, { name: '吸尘器', id: 4 }], // 分类
        },
        currentTab: {
            type: Number,
            default: 0
        }
    },
    methods: {
        selectedTab(id: number) {
            this.$emit('selected-tab', id);
        },
        goitoGoodsTypePage() {
            console.log('跳转到商品分类页面.................');
        }
    }
}
</script>

<style scoped>
.tab-view {
    display: flex;
    padding: 0 32rpx;
}
.tabs-card {
    flex: 1;
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    box-shadow: inset -10px 0 15px -10px rgba(255, 0, 255, 0.5);
}
.tabs-card::-webkit-scrollbar {
  display: none;
}
.tab-item {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #121212;
    margin-right: 32rpx;
}
.active {
    font-size: 32rpx;
    font-weight: 500;
}
.more-class-card {
    display: flex;
    align-items: center;
    justify-content: center;
}
.type-icon {
    width: 32rpx;
    height: 32rpx;
    mragin-right: 4rpx;
}
.type-text {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #121212;
}
</style>
