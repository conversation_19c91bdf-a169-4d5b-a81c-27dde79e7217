<template>
    <view class="clean-card">
        <view class="info" @click="gotoDevice('detail')">
            <view class="info-title">
                <view class="title">{{ name }}</view>
            </view>
            <view class="info-image">
                <image class="info-image" :src="image" alt="设备图片" mode="aspectFit" />
            </view>
        </view>
        <view class="more" @click="gotoDevice('list')">
            查看更多
        </view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png';
export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({}),
        }
    },
    computed: {
        name() {
            return (
                this.currentDevice.customName ||
                this.currentDevice.deviceInfo?.displayName ||
                this.currentDevice.displayName ||
                ''
            );
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage;
        },
    },
    methods: {
        // 进入设备详情，插件
        gotoDevice(type) {
          if (type === 'detail') {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
          } else {
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
          }
            // Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    },
};
</script>

<style lang="scss" scoped>
.clean-card {
    width: 250rpx;
    height: 201rpx;
    background-color: rgb(218, 228, 240);
    padding: 20rpx;
    border-radius: 40rpx;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    .info {
        // width: 250rpx;
        width: 100%;
        height: 70%;
        display: flex;
        justify-content: space-between;
        .info-title {
            font-size: 24rpx;
            font-weight: bold;
            color: #121212;
            margin-bottom: 6px;
            width: 70%;
            .title{
               white-space: nowrap;    /* 不换行 */
              overflow: hidden;      /* 超出部分隐藏 */
              text-overflow: ellipsis; /* 显示省略号 */
              width: 100%;
              max-width: 80rpx;         /* 需要指定宽度才会生效 */
            }
        }

        .info-image {
            width: 30%;
            min-width: 100rpx;
            background-size: contain;
            // height: 110rpx;
            height: 90%;

        }
    }

    .more {
        width: 208rpx;
        height: 38rpx;
        color: #AAC4E4;
        font-size: 24rpx;
        text-align: center;
        line-height: 38rpx;
        border-radius: 40rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 20rpx;
    }
}
</style>
