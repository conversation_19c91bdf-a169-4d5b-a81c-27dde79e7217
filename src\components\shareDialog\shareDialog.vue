<template>
    <u-popup :show="show && sdkVersion < 15" :closeOnClickOverlay="true" v-prevent-scroll="true" mode="center">
        <view class="share-dialog" v-if="shareType === 'active'" @click="close">
            <view class="share-dialog-content">
                <view class="share-dialog_header">
                    <img
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890b78e838c75390020035.png"
                        alt=""
                        class="share-dialog_header_img"
                    />
                </view>
                <view class="share-dialog_content" :style="{ backgroundImage: `url(${productInfo.imageBg})` }">
                    <!-- 商品卡片 -->
                    <view class="product_card">
                        <slot name="active_tag"></slot>
                        <view class="product_card_img">
                            <img :src="productInfo.image" alt="" class="product_card_img_img" />
                        </view>
                        <view class="product_card_info">
                            <view class="product_card_info_name">{{ productInfo.name }}</view>
                            <view class="product_card_info_desc" :style="{ color: productInfo.descColor }">{{
                                productInfo.desc
                            }}</view>
                            <view class="product_card_info_price" :style="{ color: productInfo.priceColor }">
                                ￥
                                <span class="product_card_info_price_big"
                                    >{{ productInfo.price.split('.')[0] }} </span
                                >.{{ productInfo.price.split('.')[1] }}
                            </view>
                        </view>
                    </view>
                </view>
                <view class="share_btn_group">
                    <view class="web_share_btn" @click="handleShareImage">分享图片</view>
                    <view class="image_share_btn" @click="handleShareWeb">分享链接</view>
                </view>
            </view>
        </view>
        <view class="share-active_dialog" v-if="shareType === 'default'" @click="close">
            <view class="share-active_dialog_content">
                <view class="share-active_dialog_content_header">
                    <img
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890c2797b84c5060012451.png"
                        alt=""
                        class="share-active_dialog_content_header_img"
                    />
                </view>
                <view class="share-active_dialog_content_content">
                    <view class="share-active_dialog_content_img">
                        <img :src="productInfo.image" alt="" class="share-active_dialog_content_img_img" />
                    </view>
                    <!-- 价钱 -->
                    <view class="share-active_dialog_content_price">
                        <view class="share-active_dialog_content_price_text">
                            ￥
                            <span class="share-active_dialog_content_price_text_big"
                                >{{ productInfo.price.split('.')[0] }} </span
                            >.{{ productInfo.price.split('.')[1] }}
                        </view>
                    </view>
                    <!-- 标题 -->
                    <view class="share-active_dialog_content_title">
                        {{ productInfo.name }}
                    </view>
                    <!-- 二维码 -->
                    <view class="share-active_dialog_content_qrcode">
                        <img :src="qrcodeUrl" alt="" class="share-active_dialog_content_qrcode_img" />
                    </view>
                    <!-- 按钮 -->
                    <view class="share_btn_group" :style="{ bottom: '-250rpx', right: '14rpx', left: '14rpx' }">
                        <view class="web_share_btn" @click="handleShareImage">分享图片</view>
                        <view class="image_share_btn" @click="handleShareWeb">分享链接</view>
                    </view>
                </view>
            </view>
        </view>
    </u-popup>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';

interface ProductInfo {
    name: string;
    desc: string;
    image: string;
    imageBg: string;
    price: string;
    priceColor: string;
    descColor: string;
}

@Component({})
export default class ShareDialog extends Vue {
    @Prop()
    readonly show: boolean = false;

    @Prop({
        type: String,
        default: 'active',
        validator: (value: string) => {
            return ['default', 'active'].includes(value);
        },
    })
    readonly shareType: string;

    @Prop()
    readonly productInfo: ProductInfo;

    public qrcodeUrl =
        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6890c8736f8814570010876.png';

    get sdkVersion() {
        return Number(UserModule.sdkVersion);
    }

    @Watch('show')
    onShowChange(newVal: boolean) {
        if (newVal && this.sdkVersion >= 15) {
            // 延迟执行，确保组件已完全渲染
            this.$nextTick(() => {
                this.$emit('share', 'web');
            });
        }
    }

    close() {
        this.$emit('update:show', false);
    }

    handleShareWeb() {
        this.$emit('share', 'web');
    }

    handleShareImage() {
        this.$emit('share', 'image');
    }
}
</script>
<style lang="scss" scoped>
.share-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    .share-dialog-content {
        width: 645rpx;
        height: 936rpx;
        background-color: #fff;
        border-radius: 24rpx;
        padding: 30rpx;
        box-sizing: border-box;
        position: relative;
        display: flex;
        flex-direction: column;

        .share-dialog_header {
            height: 64rpx;
            display: flex;
            align-items: center;

            .share-dialog_header_img {
                height: 64rpx;
            }
        }

        .share-dialog_content {
            width: 574rpx;
            height: 602rpx;
            background-color: #fff;
            margin: 0 auto;
            margin-top: 40rpx;
            border-radius: 24rpx;
            background-position: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            position: relative;

            .product_card {
                width: 510rpx;
                height: 160rpx;
                position: absolute;
                bottom: 32rpx;
                left: 50%;
                transform: translateX(-50%);
                border-radius: 20rpx;
                display: flex;
                align-items: center;
                padding: 16rpx;
                background-color: #fff;

                .product_card_img {
                    width: 120rpx;
                    height: 120rpx;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .product_card_info {
                    display: flex;
                    height: 100%;
                    padding: 10rpx 0;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-left: 28rpx;

                    .product_card_info_name {
                        font-size: 26rpx;
                        font-weight: 500;
                        width: 280rpx;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        // line-height: 36rpx;
                        color: #404040;
                    }

                    .product_card_info_desc {
                        font-size: 20rpx;
                        color: #c59245;
                        width: 320rpx;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 36rpx;
                    }

                    .product_card_info_price {
                        font-size: 28rpx;
                        font-weight: 500;
                        line-height: 36rpx;
                        margin-top: 5rpx;
                    }
                }
                .product_card_info_price_big{
                    font-size: 44rpx;
                }
            }
        }
    }
}

.share-active_dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;

    .share-active_dialog_content {
        width: 602rpx;
        height: 996rpx;
        background: linear-gradient(180deg, #ffffff 58%, #fff9e3 100%);
        border-radius: 24rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .share-active_dialog_content_header {
            width: 365rpx;
            height: 44rpx;
            margin: 0 auto;
            margin-top: 24rpx;

            .share-active_dialog_content_header_img {
                width: 100%;
                height: 100%;
            }
        }

        .share-active_dialog_content_content {
            margin: 20rpx auto 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 32rpx;
            position: relative;

            .share-active_dialog_content_img {
                height: 496rpx;
                width: 496rpx;
                display: flex;
                justify-content: center;
                align-items: center;

                .share-active_dialog_content_img_img {
                    width: 100%;
                    height: 100%;
                }
            }

            .share-active_dialog_content_price {
                margin-top: 20rpx;
                align-self: flex-start;
                font-size: 24rpx;
                font-weight: 500;
                color: #8c6533;

                .share-active_dialog_content_price_text_big {
                    font-size: 44rpx;
                }
            }

            .share-active_dialog_content_title {
                margin-top: 20rpx;
                align-self: flex-start;
                // 2行换行切割
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                font-size: 28rpx;
                text-overflow: ellipsis;
                white-space: normal;
                word-break: break-all;
            }

            .share-active_dialog_content_qrcode {
                margin-top: 20rpx;
                align-self: center;

                .share-active_dialog_content_qrcode_img {
                    width: 160rpx;
                    height: 160rpx;
                }
            }
        }
    }
}

.share_btn_group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    font-size: 40rpx;
    font-weight: 500;

    .web_share_btn {
        width: 276rpx;
        height: 107rpx;
        background-color: #cd8746;
        color: #fff;
        border-radius: 24rpx;
        text-align: center;
        line-height: 107rpx;
    }

    .image_share_btn {
        width: 276rpx;
        height: 107rpx;
        color: #616161;
        background-color: #fff;
        box-sizing: content-box;
        border-radius: 24rpx;
        border: 1rpx solid #7e7e7e !important;
        text-align: center;
        line-height: 107rpx;
    }
}
</style>
