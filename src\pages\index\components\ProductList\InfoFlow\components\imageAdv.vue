<template>
     <view class="class-goodsAct-item">
        <LazyImage :src="Item.img_detail[0].img_url" class="lazyImg"> </LazyImage>
        <view class="class-item-bottom">
           <!-- <view class="buy-item-btn"> {{ Item.img_detail[1].button_text }}</view> -->
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Object, default: { oneGoodsList: [{ }, { }, { }] }})
    readonly Item!: any;

    @Prop({ type: String, default: 'vertical' })
    readonly direction!: string;

    @Prop({ type: String, default: 'text' })
    readonly type!: string;
}
</script>
<style lang="scss" scoped>
.class-goodsAct-item {
    width: calc(calc(100vw - 48rpx)/2);
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    height: 100%;
    margin-bottom: 16rpx;
    position: relative;
    // min-height: 540rpx;
    // min-height: 600rpx;
    .lazyImg{
        width: 100%;
        height: 100%;
        min-height: 536rpx;
        max-height: 536rpx;
        object-fit: fill;
    }
     .class-item-top {
                width: 100%;
                margin: 0 auto 28rpx;
                position: relative;
                img {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 8rpx 8rpx 0 0;
                }

            }
            .class-item-bottom {
                padding: 0 20rpx 12rpx;
                width: 100%;
                position: absolute;
                bottom: 10rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                .buy-item-btn{
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;
                    padding: 12rpx 20rpx;
                    background: #FFFFFF;
                    border-radius: 36rpx;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                }
                .buy-goods-item{
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 12rpx 20rpx;
                    background: #FFFFFF;
                    margin-bottom: 8rpx;
                    border-radius: 16rpx;

                }
                .name {
                    position: relative;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #121212;
                    line-height: 32rpx;
                    text-align: left;
                    .titleW{
                        line-height: 40rpx;
                    }
                    .tag {
                        position: absolute;
                        left: 0;
                        top: 4rpx;
                    }

                    .ecology{
                            width: 52rpx;
                        height: 26rpx;
                        border-radius: 4px;
                        background-color: #FFF4EF;
                        color: #FB3019;
                        text-align: center;
                            padding: 6rpx 8rpx;
                    }
                }

                .postWrap {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-family: MiSans;
                    width: 100%;
                    margin-top: 24rpx;
                     .lazyImgIcon{
                        width: 36rpx;
                        height: 36rpx;
                        object-fit: cover;
                        margin-right: 16rpx;
                    }
                    .content_title_text_grab_text{
                        font-family: MiSans;
                        font-weight: 500;

                        text-align: left;
                         white-space: nowrap;
                        /* 可选：超出容器时显示省略号 */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 22rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: 0px;
                        /* NeutralColor中性色/Gray 4 */
                        color: #777777;

                    }
                    .coupon-text {
                        margin-left: 4rpx;
                        font-family: MiSans, MiSans;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #ee3c2f;
                        line-height: 24rpx;
                    }
                }
            }
}
</style>
