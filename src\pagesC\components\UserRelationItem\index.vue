<template>
    <view>
        <view class="user-relation">
            <view class="user-relation__avatar" @click="jumpHome(r.user_id)">
                <image :src="r.avatar"></image>
                <!-- <image v-if="r.user_avatar_icon" :src="r.user_avatar_icon" class="user-relation__avatar-mark" /> -->
            </view>
            <view class="user-relation__info" @click="jumpHome(r.user_id)">
                <!-- #ifdef MP-WEIXIN -->
                <view class="user-relation__info-name">
                    <rich-text class="u-line-1" :nodes="r.titleContent"></rich-text>
                </view>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <view style="display: flex; align-items: center">
                    <view class="user-relation__info-name" v-html="r.titleContent"></view>
                    <image
                        v-if="r.user_avatar_icon"
                        :src="r.user_avatar_icon"
                        style="width: 32rpx; height: 32rpx; margin-left: 10rpx; flex-shrink: 0"
                    />
                </view>
                <!-- #endif -->
                <view class="user-relation__info-follow">粉丝：{{ r.fans_num }}</view>
                <view class="user-relation__info-id" v-if="r.user_type === 0">追觅ID：{{ r.uid }}</view>
                <view class="user-relation__info-id" v-else>{{ userMap[r.user_type] }}</view>
            </view>
            <!-- v-if="this.creator && this.creator !== -1 && userStatusText && !abnormalStatus" -->
            <view
                v-if="r.user_id != myUserId"
                class="user-relation__btn"
                :class="{
                    each_follow: r.follow_status === 2,
                    followed: r.follow_status === 1 || r.block_status === 1,
                    follow: r.follow_status === 0,
                }"
                @click="handleUserOper(r)"
            >
                <img
                    style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                    v-if="userStatusText(r) === '关注'"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                />
                <img
                    style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                    v-if="userStatusText(r) === '已关注'"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png"
                />

                <text style="margin-left: 4rpx">{{ userStatusText(r) }}</text>
            </view>
        </view>
        <!-- 用户取消关注拉黑 -->
        <CustomModal
            :show="operContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            :title="operContent.titie"
            :content="operContent.tip"
            :confirmText="operContent.confirmText"
            :cancelText="operContent.cancelText"
            showCancelButton
            @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })"
        ></CustomModal>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { postBlock, postCancelBlock, userFollow, userFollowDelete } from '@/http/requestGo/community';
import { OperContent } from '@/pagesC/selfCommunity/selfCommunity.vue';
import { UserModule } from '@/store/modules/user';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class UserRelationItem extends Vue {
    @Prop({
        type: Object,
        default: {},
    })
    readonly r;

    public userData: any = {};
    public myUserId = UserModule.user_id;
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: OperContent = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    // #ifdef MP-WEIXIN

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    userStatusText(r) {
        if (r.block_status === 1) {
            return '解除拉黑';
        } else if (r.follow_status === 1) {
            return '已关注';
        } else if (r.follow_status === 2) {
            return '互相关注';
        } else if (r.block_status === 0 || r.follow_status === 0) {
            return '关注';
        }
        return '';
    }

    public userMap = {
        1: '主播账号',
        2: '官方账号',
        3: '高管账号',
        4: '品牌账号',
        5: '明星账号',
    };

    // 用户操作
    async handleUserOper(r) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                // #endif
                let res;
                if (r.block_status === 1) {
                    // console.log('取消屏蔽');
                    res = await postCancelBlock({ blocked_user_id: +r.user_id });
                } else if (r.follow_status === 1 || r.follow_status === 2) {
                    this.userData = { ...r };
                    // console.log('取消关注');
                    // res = await userFollowDelete({ followed_user_id: +r.user_id });
                    // console.log('取消关注');
                    this.operContentOpen = true;
                    this.operContent = {
                        type: 'follow',
                        titie: '',
                        tip: '确定不再关注该用户',
                        confirmText: '不再关注',
                        cancelText: '取消',
                    };
                    // operContent
                } else if (r.follow_status === 0) {
                    res = await userFollow({ followed_user_id: +r.user_id });
                }
                // console.log('关注');
                if (res.success) {
                    Utils.Toast(
                        r.block_status === 1 ? '已移除黑名单' : r.follow_status === 0 ? '关注成功' : '操作成功',
                    );
                    this.$emit('load');
                }
                // #ifdef MP-WEIXIN
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
    }

    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.userData.user_id });
        } else if (this.operContent.type === 'block') {
            res = await postBlock({ blocked_user_id: +this.userData.user_id });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            this.$emit('load');
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    jumpHome(user_id) {
        Utils.jumpPersonHome(user_id);
    }
}
</script>

<style lang="scss" scoped>
.user-relation {
    padding: 20rpx 30rpx 24rpx 30rpx;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

    &__avatar {
        position: relative;
        width: 96rpx;
        height: 96rpx;
        margin: 12rpx 30rpx 0 0;

        image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }

        image.user-relation__avatar-mark {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30rpx;
            height: 30rpx;
        }
    }

    &__info {
        flex: 1;
        margin-right: auto;

        &-name {
            font-weight: 700;
            font-size: 32rpx;
            color: #121212;
            line-height: 44rpx;
            overflow: hidden;
            max-width: 250rpx;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &-follow,
        &-id {
            font-size: 24rpx;
            color: #777777;
            line-height: 32rpx;
        }

        &-follow {
            margin: 4rpx 0;
        }
    }

    &__btn {
        width: 168rpx;
        // background: #e8dec1;
        border-radius: 192rpx 192rpx 192rpx 192rpx;
        padding: 0 20rpx;
        font-weight: 500;
        font-size: 28rpx;
        // color: #8c6533;
        line-height: 62rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-top: 28rpx;
        margin-left: 36rpx;
        border: 2rpx solid #e2e2e2;
        color: #a6a6a6;

        &--follow {
            border-radius: 154rpx 154rpx 154rpx 154rpx;
        }
        &.each_follow,
        &.followed {
        }
    }

    .follow {
        border: 2rpx solid #dbc49a;
        color: #c2a271;
    }
}
</style>
