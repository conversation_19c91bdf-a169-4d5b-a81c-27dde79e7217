import http from './index';
import req from './requestGo/index.js';

/**
 * 获取心愿列表列表
 */
export function getWishList() {
    return http.post('/main/goods/wish-list', {});
}

/**
 * 取消，加入心愿单
 */
export function updateShopWish(goods_id: string, opt: string) {
    return http.post('/main/goods/wish', { goods_id, opt });
}

// 理由
export function postReason(params: any) {
    return req.post('/api/v1/goods/recommendation-reason/create', params);
}

// 查询
export function postComment(params: any) {
    return req.post('/api/v1/goods/recommendation-reason/list-by-goods', params);
}

// 理由列表
export function postReasonList(params: any) {
    return req.post('/api/v1/tag/list-by-recommendation-reason', params);
}
