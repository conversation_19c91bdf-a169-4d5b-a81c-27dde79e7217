import http from './index';

/**
 * 获取秒杀活动列表
 */
export function getOneYuanFlashSaleList(params: any) {
    return http.post('/main/one-yuan-seckill/start', params);
}

/**
 * 获取秒杀活动详情
 */
export function getOneYuanFlashSaleDetail(params: any) {
    return http.post('/main/one-yuan-seckill/detail', params);
}

/**
 * 获取秒杀活动id
 */
export function getOneYuanFlashSaleId(params: any) {
    return http.post('/main/one-yuan-seckill/get-or-create', params);
}

// 切换商品绑定
export function getOneYuanFlashSelectGoods(params: any) {
    return http.post('/main/one-yuan-seckill/select-gid', params);
}

// 检查用户是否可以被邀请
export function checkUserCanBeInvited(params: any) {
    return http.post('/main/invite/check', params);
}

// 通用加入邀请接口
export function joinInvite(params: any) {
    return http.post('/main/invite/join', params);
}

// 获取邀直接获取0.5元助力金
export function likeInvite(params: any) {
    return http.post('/main/invite/like', params);
}

export function inviteList(params: any) {
    return http.post('/main/invite/list', params);
}

export function consumeMoneyRecord(params) {
    return http.post('/main/invite/consume-money-record', params);
}

// 获取积分购
export function getPointPurchase(params: any) {
    return http.post('/main/wares-order/buy-info', params);
}

// 获取seckill_id
export function getSeckillId(params: any) {
    return http.post('/main/one-yuan-seckill/get-or-create', params);
}
