<template>
    <view class="notice-box u-flex u-flex-col u-row-center">
        <view class="title" v-if="title">
            {{ title }}
        </view>
        <image v-if="showCloseBtn" class="btn-close"
            src="https://wpm-cdn.dreame.tech/images/202206/336117-1655951836495.png" @tap="cancel" />

        <view class="notice-content">
            <slot></slot>
        </view>
        <view class="notice-btn-row u-flex u-row-center">
            <view class="btn-cancel" v-if="cancelFunKey" @tap="otherCancel" hover-class="btn-hover">
                {{ cancelText }}
            </view>
            <view class="btn-cancel" v-else-if="showCancel" @tap="cancel" hover-class="btn-hover">
                {{ cancelText }}
            </view>
            <view class="btn-submit" :class="{ 'btn-submit-b': !showCancel }" @tap="submit" hover-class="btn-hover">
                {{ submitText }}
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop, Emit } from 'vue-property-decorator';

@Component
export default class Notice extends Vue {
    @Prop({ type: Boolean, default: true })
    readonly showCancel;

    @Prop({ type: Boolean, default: true })
    readonly showCloseBtn;

    @Prop({ type: String, default: '' })
    readonly title;

    @Prop({ type: String, default: '取消' })
    readonly cancelText;

    @Prop({ type: String, default: '确定' })
    readonly submitText;

    @Prop({ type: Boolean, default: false })
    readonly cancelFunKey;

    @Emit('close')
    cancel() { }

    @Emit('otherClose')
    otherCancel() { }

    @Emit('submit')
    submit() { }
}
</script>

<style lang="scss">
.notice-box {
    width: 600rpx;
    height: auto;
    background: #ffffff;
    border-radius: 12rpx;
    padding: 54rpx 38rpx 70rpx;
    position: relative;

    .title {
        height: 48rpx;
        font-size: 36rpx;
        color: $theme-text-color;
        line-height: 50rpx;
    }

    .btn-close {
        width: 48rpx;
        height: 48rpx;
        position: absolute;
        right: 24rpx;
        top: 24rpx;
    }

    .notice-content {
        width: 100%;
        font-size: 28rpx;
        color: #121212;
        line-height: 48rpx;
        margin: 44rpx 0 64rpx;
        text-align: left;
        text-align: center;
    }

    .notice-btn-row {
        width: 100%;
        height: 92rpx;

        &.notice-btn-row_center {
            justify-content: center;
        }

        .btn-cancel,
        .btn-submit {
            width: 250rpx;
            height: 80rpx;
            background: #ffffff;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: $theme-color;
            border: 2rpx solid $theme-color;
            line-height: 76rpx;
            text-align: center;
        }

        .btn-cancel {
            margin-right: 22rpx;
        }

        .btn-submit {
            background: $brand-color-normal;
            color: $brand-color-btn-text;
            font-weight: 500;
            border: none;

            &.btn-submit-b {
                width: 520rpx;
                line-height: 92rpx;
                height: 92rpx;
            }
        }
    }
}
</style>
