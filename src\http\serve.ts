/* eslint-disable object-curly-spacing */
import http from './index';
import req from './requestGo';

/*
 * 注册产品
 * @parma {string} product_id  产品id
 * @parma {string} sn  sn编码
 * @parma {string} phone  手机号码
 * @parma {number} buy_time  购买时间
 * @returns
 */
interface productRegParams {
    product_id: string;
    sn: string;
    buy_time: number;
}
export const productReg = (params: productRegParams) =>
    http.post('main/service/product-reg', params, { custom: { loading: true, title: '注册中' } });

/*
 * 已注册产品列表
 * @returns
 */
export const productList = (params) => http.post('main/service/product-list', params);

/*
 * 产品匹配
 * @parma {string} sn  sn编码
 * @returns
 */
interface productMatchParams {
    sn: string;
}
export const productMatch = (params: productMatchParams) =>
    http.post('main/service/product-match', params, { custom: { loading: true, title: '校验中' } });

/*
 * 获取手机号
 * @parma {*} encryptedData  包括敏感数据在内的完整用户信息的加密数据【必要】
 * @parma {*} iv  加密算法的初始向量【必要】
 * @parma {*} openudid  jsCode 用于获取微信会话密钥【必要】
 * @returns
 */
export const phoneDecode = (params) => http.post('main/service/auth-phone', params);

/*
 * 获取快速注册产品列表
 * @returns
 */
export const quickRegisterList = (params) => req.post('api/v1/registration-alert/unregistration-list', params);

/**
 * 更新产品注册信息
 */
export const editRegisterProduct = (params) => req.post('api/v1/registration-alert/update', params);
