.pop_content {
    width: 658rpx;
    height: 880rpx;
    position: relative;
    border-radius: 28rpx;
    box-sizing: border-box;
    padding: 34rpx 0rpx;
    background: linear-gradient(180deg, rgba(242, 225, 177, 0.31) 0%, rgba(242, 225, 177, 0) 100%);
    background-size: 100% 404rpx;
    background-repeat: no-repeat;
    background-color: #f6f6f6;

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 404rpx;
        border-radius: 28rpx;
        z-index: 0;
    }

    .title {
        padding-left: 52rpx;
        font-size: 58rpx;
        font-family: MiSans-Bold, MiSans;
        font-weight: 700;
        color: rgba(47, 47, 47, 0.9);
        line-height: 74rpx;
        z-index: 1;
    }

    .sub-title {
        padding-left: 52rpx;
        font-size: 26rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(47, 47, 47, 0.7);
        line-height: 34Rpx;
        z-index: 1;
    }

    .btn-close {
        width: 44rpx;
        height: 44rpx;
        position: absolute;
        right: 20rpx;
        top: 20rpx;
    }

    .conpon-list {
        width: 100%;
        height: 564rpx;
        padding: 0 38rpx;
        margin-top: 25rpx;
    }

    .btn-row {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 148rpx;
        border-radius: 0 0 28rpx 28rpx;
        padding: 0rpx 54rpx;
        justify-content: space-between;
        background: #f6f6f6;

        .btn-receive {
            width: 550rpx;
            height: 82rpx;
            background: #e8dec1;
            border-radius: 192rpx;
            line-height: 82rpx;
            text-align: center;
            font-size: 26rpx;
            color: #8c6533;
            font-weight: 500;
        }

        .btn {
            height: 82rpx;
            width: 260rpx;
            font-size: 26rpx;
            text-align: center;
            border-radius: 192rpx;
            line-height: 82rpx;
        }

        .btn-cancel {
            background: #eeeeee;
            color: #121212;
            border: 2rpx solid #eeeeee;
        }

        .btn-view {
            background: #e8dec1;
            color: #8c6533;
        }
    }
}

.coupon-item-layout {
    position: relative;
    width: 100%;
    margin-bottom: 32rpx;
}

.pop_content_bind {
    position: relative;
    width: 600rpx;
    height: 922rpx;
    background: #ffffff;
    border-radius: 8rpx;
    overflow: hidden;

    .btn-close {
        position: absolute;
        top: 26rpx;
        right: 26rpx;
        width: 48rpx;
        height: 48rpx;
        z-index: 2;
    }

    .bg {
        position: absolute;
        top: 0;
        left: -2rpx;
        width: 602rpx;
        height: 398rpx;
        z-index: 0;
    }

    .content-box {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 52rpx 40rpx 70rpx;
        z-index: 1;

        .title {
            font-size: 36rpx;
            color: $theme-text-color;
            line-height: 50rpx;
            margin-bottom: 48rpx;
        }

        .tip {
            font-size: 36rpx;
            color: #000000;
            line-height: 50rpx;
            margin-bottom: 22rpx;
        }

        .tip_2 {
            font-size: 28rpx;
            color: #7473c5;
            line-height: 38rpx;
            margin-bottom: -28rpx;
        }

        .image {
            width: 484rpx;
            height: 484rpx;
            margin-bottom: 26rpx;
        }

        .btn {
            width: 520rpx;
            height: 92rpx;
            background: #7473c5;
            border-radius: 12rpx;
            line-height: 92rpx;
            text-align: center;
            font-size: 24rpx;
            color: #ffffff;
        }
    }
}

.pop_content_other_bind {
    position: relative;
    width: 600rpx;
    height: 494rpx;
    background: #ffffff;
    border-radius: 8rpx;
    overflow: hidden;

    .btn-close {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        width: 50rpx;
        height: 50rpx;
        z-index: 2;
    }

    .content-box {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 54rpx 40rpx 70rpx;
        z-index: 1;

        .title {
            font-size: 34rpx;
            color: $theme-text-color;
            line-height: 48rpx;
            margin-bottom: 72rpx;
        }

        .tip {
            width: 350rpx;
            font-size: 28rpx;
            color: #444444;
            line-height: 36rpx;
            text-align: center;
            margin-bottom: 86rpx;
        }

        .btn {
            width: 520rpx;
            height: 92rpx;
            background: #7473c5;
            border-radius: 12rpx;
            line-height: 92rpx;
            text-align: center;
            font-size: 24rpx;
            color: #ffffff;
        }
    }
}

.privacy_content {
    width: 658rpx;
    height: 880rpx;
    position: relative;
    border-radius: 28rpx;
    box-sizing: border-box;
    padding: 34rpx 0rpx;
    // background: linear-gradient(180deg, rgba(242, 225, 177, 0.31) 0%, rgba(242, 225, 177, 0) 100%);
    // background-size: 100% 404rpx;
    // background-repeat: no-repeat;
    background-color: #f6f6f6;
    .privacy_title {
        /* 样式描述：次要标题 */
        font-family: PingFang SC;
        font-size: 36rpx;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: 0px;
        /* Text 文字/Primary */
        /* 样式描述：强调/标题文字 */
        color: #121212;
        z-index: 0;
    }
    .privacy_text {
        width: 100%;
        height: 620rpx;
        padding: 0 38rpx;
        margin-top: 25rpx;
        ::v-deep ::-webkit-scrollbar {
            // 滚动条整体样式
            display: block;
            width: 8rpx !important;
            height: 142rpx !important;
            -webkit-appearance: auto !important;
            background: transparent;
            overflow: auto !important;
        }
        ::v-deep ::-webkit-scrollbar-thumb {
            // 滚动条里面小方块
            border-radius: 58rpx !important;
            box-shadow: inset 0 0 5rpx rgba(0, 0, 0, 0.2) !important;
            background-color: rgba(29, 30, 32, 0.1) !important;
        }
        rich-text {
            padding-right: 10rpx;
        }
    }

    .btn-row {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 148rpx;
        border-radius: 0 0 28rpx 28rpx;
        padding: 0rpx 54rpx;
        justify-content: space-between;
        background: #f6f6f6;

        .btn-receive {
            width: 550rpx;
            height: 82rpx;
            background: #e8dec1;
            border-radius: 192rpx;
            line-height: 82rpx;
            text-align: center;
            font-size: 26rpx;
            color: #8c6533;
            font-weight: 500;
        }

        .btn {
            position: absolute;
            top: 0;
            left: 50%;
            height: 82rpx;
            width: 260rpx;
            font-size: 26rpx;
            text-align: center;
            border-radius: 192rpx;
            line-height: 82rpx;
        }

        .btn-cancel {
            transform: translateX(-285rpx);
            background: #eeeeee;
            color: #121212;
            border: 2rpx solid #eeeeee;
        }

        .btn-view {
            transform: translateX(20rpx);
            background: #e8dec1;
            color: #8c6533;
        }
    }
}
