<template>
    <view class="hot-recommend-container ">
        <view class="hot-recommend-header" @click="handleMoreProduct">

                <text class="hot-title">爆款推荐</text>
                <text class="goods-count">10000+好物等你来</text>
                <text class="more-goods">更多好物</text>
                <image class="arrow-icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68998b146ef304540015186.png" mode="aspectFit"></image>
            </view>
         <u-scroll-list :indicator="false" :indicatorActiveColor="'#DBC49A'" :indicatorInactiveColor="'#EEEEEE'"
        :indicatorWidth="46.15" :indicatorHeight="7.69" :indicatorBarRadius="98.08"
        :indicatorBarStyle="{ margin: '16rpx auto 0' }" :showScrollbar="false">
        <view class="hot-recommend-wrap">
            <view class="goods-list">
                <view class="goods-item" v-for="(item, index) in goodsList" :key="index" @click="handleGoodsClick(item)">

                    <image class="goods-img" :src="item.cover_image" mode="aspectFit"></image>
                    <text class="goods-name">{{ item.name }}</text>
                    <view class="price-cart">
                        <view class="price-wrap">
                            <text class="goods-price-unit">¥</text>
                            <text class="goods-price">{{ item.price }}</text>
                        </view>

                        <image @click.stop="handleAddCart(item)" class="cart-icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68998af3987556240010967.png" mode="aspectFit"></image>
                    </view>
                </view>
            </view>
        </view>
    </u-scroll-list>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { goodsList } from '@/http/goods';
// import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { batchAddShopCart } from '@/http/cart';

@Component({
    components: {
    },
})
export default class ActivitySwiper extends Vue {
    @Prop({ type: Array, default: () => [] })

    readonly oneGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly fiveHalfGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly eightDisGoodsList!: Array<any>;

    @Prop({ type: Array, default: () => [] })
    readonly points!: Array<any>;

    @Prop()
    readonly deduction_rate!: number;

    public name: string = ''

    get fullName() {
        return this.name
    }

    public goodsList: any[] = [];

    async created() {
        await this.fetchHOTProductList();
    }

    /**
     * 获取服务列表
     */
    public async fetchHOTProductList() {
        try {
            const params: Object = {
                tid: 2,
                page: 1,
                page_size: 30,
                single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
            };
            const { list }: any = await goodsList(params);
            this.goodsList = [...list];
        } catch (error) {
            /* eslint-disable no-console */
            console.error('获取服务列表失败', error);
        }
    }

    jumpHandler(item) {
        console.log(item)
         Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .HOME_BANNER_CLICK,
                id: Number(item.id),
                name: item.jumpUrl
            },
            true,
        );
        Utils.navigateTo(item.jumpUrl);
    }

    handleGoodsClick(item) {
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_GOODS_CLICK,
            id: Number(item.gid),
            name: item.name
        }, true,);
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.gid}`);
    }

    handleAddCart(item) {
        this.getBatchAddShopCart(item)
    }

    handleMoreProduct() {
        Utils.navigateTo('/pagesA/brandType/brandType?tid=2&partId=11');
    }

    // 组合购买加入购物车
    async getBatchAddShopCart(item) {
        try {
            const { gid, sid, gini_id } = item;
            const gcombines: Array<any> = [{ gid, sid, num: 1, gini_id, type: 'main' }];
            if (gcombines.length > 0) {
                await batchAddShopCart({ gcombines: JSON.stringify(gcombines) });
                uni.showToast({
                    title: '加入购物车成功',
                    icon: 'success',
                    duration: 2000
                });
            }
        } catch (e) {
            console.error('handleAddCart e=', e);
        }
    }
}
</script>
<style lang="scss" scoped>
.hot-recommend-container {
    background-color: #ff4d6d;
    padding: 20rpx;
    border-radius: 24rpx;
    margin: 16rpx;

    .hot-recommend-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }

    .hot-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
        margin-right: 16rpx;
    }

    .goods-count {
        font-size: 26rpx;
        color: #fff;
        margin-right: auto;
    }

    .more-goods {
        font-size: 24rpx;
        color: #fff;
        margin-right: 8rpx;
    }

    .arrow-icon {
        width: 24rpx;
        height: 24rpx;
    }

    .goods-list {
        display: flex;
        overflow-x: auto;
    }

    .goods-item {
        width: 200rpx;
        margin-right: 20rpx;
        background-color: #fff;
        padding: 16rpx;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .goods-img {
        width: 128rpx;
        height: 128rpx;
        margin-bottom: 12rpx;
        background-color: #f5f5f5;
    }
    .goods-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
        text-align: start;
        -webkit-box-orient: vertical;
        width: 100%;
        white-space: nowrap; /* 强制文字在一行显示，不换行 */
        overflow: hidden;    /* 超出容器部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号（可选） */
    }
    .price-cart {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .price-wrap{
            display: flex;
            align-items: baseline;
            justify-content: flex-start;
            .goods-price-unit{
              font-size: 20rpx;
              color: #ff4d6d;
            }
        }
    }
    .goods-price {
        font-size: 28rpx;
        color: #ff4d6d;
        margin-right: 8rpx;
        font-weight: 500;
    }

    .cart-icon {
        width: 40rpx;
        height: 40rpx;
    }
}
</style>
