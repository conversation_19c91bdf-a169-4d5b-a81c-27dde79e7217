<template>
    <div class="stack-container" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
        <transition-group :name="deltaY < 0 ? 'slide' : 'slide-down'" tag="div" class="stack-wrapper">
            <div
                v-for="(card, index) in visibleCards"
                :key="card.id"
                class="card"
                :style="{ top: `${index * 10}px`, background: card.color, zIndex: 3 - index }"
            >
                {{ card.text }}
            </div>
        </transition-group>
    </div>
</template>

<script>
export default {
    data() {
        return {
            cards: [
                { id: 1, text: 'Card 1', color: '#ff6b6b' },
                { id: 2, text: 'Card 2', color: '#feca57' },
                { id: 3, text: 'Card 3', color: '#48dbfb' },
                { id: 4, text: 'Card 4', color: '#1dd1a1' },
                { id: 5, text: 'Card 5', color: '#5f27cd' },
            ],
            currentIndex: 0,
            startY: 0,
            deltaY: 0,
        };
    },
    computed: {
        visibleCards() {
            // 循环取 3 张卡片
            return [
                this.cards[this.currentIndex % this.cards.length],
                this.cards[(this.currentIndex + 1) % this.cards.length],
                this.cards[(this.currentIndex + 2) % this.cards.length],
            ];
        },
    },
    methods: {
        onTouchStart(e) {
            this.startY = e.touches[0].clientY;
            this.deltaY = 0;
        },
        onTouchMove(e) {
            this.deltaY = e.touches[0].clientY - this.startY;
        },
        onTouchEnd() {
            if (this.deltaY < -50) {
                // 上滑
                this.currentIndex = (this.currentIndex + 1) % this.cards.length;
            } else if (this.deltaY > 50) {
                // 下滑
                this.currentIndex = (this.currentIndex - 1 + this.cards.length) % this.cards.length;
            }
        },
    },
};
</script>

<style scoped>
.stack-container {
    position: relative;
    width: 300px;
    height: 420px;
    overflow: hidden;
    margin: 20px auto;
    border: 1px solid #ccc;
    border-radius: 12px;
}
.stack-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}
.card {
    position: absolute;
    width: 100%;
    height: 400px;
    color: white;
    font-size: 24px;
    text-align: center;
    line-height: 400px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    transition: transform 0.4s ease, top 0.4s ease;
}

/* 上滑（默认） */
.slide-enter-active,
.slide-leave-active {
    transition: transform 0.4s ease;
}

.slide-enter {
    transform: translateY(100%); /* 从下方进入 */
}
.slide-leave-to {
    transform: translateY(-100%); /* 向上离开 */
}

/* 下滑 */
.slide-down-enter {
    transform: translateY(-100%); /* 从上方进入 */
}

.slide-down-leave-to {
    transform: translateY(100%); /* 向下离开 */
}

</style>
