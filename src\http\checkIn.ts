import http from './index';
/*
 * 获取月签到明细
 */
export function getMonthCheckDetail(params) {
    return http.post('main/member-center/sign-in-month-detail', { ...params });
}

/**
 * 每日签到
 */
export function signInDaliy(params?: any) {
    return http.post('main/member-center/sign-in', { ...params });
}

/**
 * 获取签到首页状态
 */
export function signInHomeStatus() {
    return http.post('main/member-center/sign-in-home');
}

/**
 * 查询签到状态
 */
export function getSignInStatus(params?: any) {
    return http.post('main/member-center/sign-in-status', { ...params });
}

/**
 * 查询连续签到状态
 */
export function getContinueDays() {
    return http.post('main/member-center/continue-sign-info');
}

/**
 * 获取签到积分
 */
export function getdreamestoreinfo() {
    return http.post('/main/dreame-store/info');
}

/**
 * 主按钮十分钟一次领取
 */
export function presentedGoldCoins(params?: any) {
    return http.post('/main/member-center/presented-gold-coins', { ...params });
}

/**
 * 查询十分钟领取时间状态
 */
export function getTaskInfo(params) {
    return http.post('/main/member-center/task-info', { ...params });
}

/**
 * 抽金币加金币值
 */
export function drawGoldCoins() {
    return http.post('/main/member-center/draw-gold-coins');
}

/**
 * 单独获取随机金币
 */
export function getRandCoins() {
    return http.post('/main/member-center/rand-coins');
}

/**
 * 检查是否是首次进入
 */
export function checkIsFirstComeIn() {
    return http.post('/main/member-center/is-first');
}

/**
 * 获取未领取金币明细
 */
export function getScoreUncollect(params?: any) {
    return http.post('/main/member-center/score-uncollect', { ...params });
}

/**
 * 根据事件类型领取金币
 */
export function collectTaskGold(params?: any) {
    return http.post('/main/member-center/collect-task-gold', { ...params });
}

/**
 * 查询最新的抽奖记录（全用户）
 */
export function getLimitGold(params?: any) {
    return http.post('/main/member-center/limit-gold', { ...params });
}
