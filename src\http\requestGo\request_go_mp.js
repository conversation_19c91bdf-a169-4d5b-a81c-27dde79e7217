import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import _ from 'lodash';

const BASE_URL = process.env.VUE_APP_GO_BASE_URL;

export default {
    post(url, data, custom = {}) {
        return request(url, 'POST', data, custom);
    },

    get(url, data, custom = {}) {
        return request(url, 'GET', data, custom);
    },
};

function request(url, method, data, custom = {}) {
    return new Promise((resolve, reject) => {
        const timestamp = new Date().getTime();
        const BASE_DATA = {
            user_id: +UserModule.user_id || 0,
            sessid: UserModule.sessid || '',
            api: AppModule.platform == 'ios' ? 'i_1643178026' : 'a_1643178000',
            version: AppModule.version,
            union: UserModule.union || uni.getStorageSync('union') || '',
            euid: UserModule.euid || uni.getStorageSync('euid') || 0,
            is_internal_purchase: data?.is_internal_purchase || UserModule.isInternalPurchase,
            cps: UserModule.cps,
            referer: uni.getStorageSync('referer') || '',
        };
        const security_key = AppModule.platform == 'ios' ? 'EfCj*YDcb5anqOs' : 'EfCj*YDcb5anqOs';
        const data2 = {
            ...data,
            ...BASE_DATA,
        };

        const sign = getSign(data2, security_key, timestamp);
        // data2.sign = sign
        uni.request({
            url: BASE_URL + url,
            method: method,
            data: {
                ...data2,
            },
            header: {
                'Content-Type': 'application/json',
                'wx-version': 'v1.0.0',
                'dreame-api': AppModule.platform == 'ios' ? 'i_1643178026' : 'a_1643178000',
                'dreame-api-sign': sign,
                'dreame-api-timestamp': timestamp,
                'Access-Token': UserModule.sessid || '',
            },
            success: (res) => {
                const response = res.data;
                if (response.success && response.code === Constants.GO_RET_OK) {
                    resolve(response);
                } else if (response.code === Constants.RET_SESSION_EXP) {
                    UserModule.setSessId('');
                    UserModule.setUserId('');
                    UserModule.setIsLogin(false);
                    UserModule.asyncWxLogin();
                    reject(response);
                } else {
                    // 处理错误消息提示
                    const shouldShowToast =
                        // 有错误消息且未配置toastDeal时显示
                        (response.msg && typeof response.msg === 'string' && !custom?.errorCodes) ||
                        // 配置了toastDeal且不是特定错误码时显示
                        // 晒单有礼 55620013  您已经通过审核，心享官会联系您发放奖品
                        // 晒单有礼 55620014  您已经成功领取过一次奖品，等待下次参与其他活动
                        (custom?.errorCodes && !custom?.errorCodes.includes(response.code));

                    if (shouldShowToast && response.msg) {
                        Utils.Toast(response.msg);
                    }

                    reject(response);
                }
            },
            fail: (err) => {
                const response = err.data;
                if (response.sMsg) {
                    Utils.Toast(response.sMsg);
                }
                reject(response);
            },
            complete: () => {
                if (custom.loading) {
                    uni.hideLoading();
                }
            },
        });

        if (custom.loading) {
            uni.showLoading({ title: custom.title || '正在加载' });
        }
    });
}

function getSign(obj, security_key, sign_time = 0) {
    const cleanedObj = _.omitBy(
        obj,
        (value) => _.isUndefined(value) || _.isNull(value) || (_.isNumber(value) && _.isNaN(value)),
    );
    const sortArr = recursiveSerialization(cleanedObj).join('&') + sign_time + security_key;
    // console.log(sortArr)
    return md5Libs.md5(sortArr);
}

function recursiveSerialization(obj) {
    const sortArr = [];
    if (_.isArray(obj)) {
        const itemArr = [];
        obj.forEach((item, index) => {
            if (_.isObject(item)) {
                if (index == obj.length - 1) {
                    itemArr.push(`${recursiveSerialization(item).join('&')}`);
                } else {
                    itemArr.push(`${recursiveSerialization(item).join('&')}`);
                }
            } else {
                if (index == obj.length - 1) {
                    itemArr.push(item);
                } else {
                    itemArr.push(item + ',');
                }
            }
        });
        sortArr.push(`${itemArr.join('')}`);
    } else if (_.isObject(obj)) {
        const keysArr = [];
        const keysSorted = Object.keys(obj).sort(); // 排序名

        for (let i = 0; i < keysSorted.length; i++) {
            const key = keysSorted[i];
            // @ts-ignore
            const value = obj[key];
            if (_.isArray(value)) {
                keysArr.push(`${key}=[${recursiveSerialization(value).join('&')}]`);
            } else if (_.isObject(value)) {
                keysArr.push(`${key}=[${recursiveSerialization(value).join('&')}]`);
            } else {
                keysArr.push(`${key}=${value}`);
            }
        }

        sortArr.push(`${keysArr.join('&')}`);
    }

    return sortArr;
}
