<template>
    <scroll-view :scroll-y="isProductListScrollEnabled" class="right-box" :refresher-enabled="enableRefresher"
        :upper-threshold="0" :lower-threshold="300" @refresherrefresh="refresh" @scrolltolower="onreachBottom">
        <view class="page-view">
            <block v-for="itm in listData.list" :key="itm.gid">
                <block v-if="[ 'text', 'goods', 'image', 'post' ].includes(itm.type)">
                    <InfoFlow :Item="itm" :type="itm.type"></InfoFlow>
                </block>
                <block v-else>
                    <view class="class-item" @click="gotoGdetailPage(itm.gid, itm.name)">
                        <view class="class-item-top" :class="{ extend: !!itm.atmosphere_img }">
                            <view v-if="itm.atmosphere_img" class="atmosphereImg" :style="{
                                backgroundImage: `url(${itm.atmosphere_img})`,
                            }"></view>
                            <!-- #ifdef H5 -->
                            <LazyImage :src="itm.cover_image" class="marketImg"> </LazyImage>
                            <!-- #endif -->
                            <!-- #ifdef MP-WEIXIN -->
                            <LazyImage :src="itm.cover_image" mode="scaleToFill"> </LazyImage>
                            <!-- #endif -->
                            <view v-if="itm.custom_tag == '一元秒杀'" class="custom_tag"></view>
                        </view>
                        <view class="class-item-bottom">
                            <view class="name u-line-2">
                                <!-- <block v-if="itm.is_presale === 1">
                                    <text class="uni-presale-tag tag" style="line-height: 1">预售</text>
                                </block>
                                <block v-else>
                                    <block v-if="itm.tids && itm.tids.includes('2')">
                                        <text class="uni-presale-tag tag" style="line-height: 1">爆款</text>
                                    </block>
                                    <block v-else-if="itm.tids && itm.tids.includes('1')">
                                        <text class="uni-presale-tag tag" style="line-height: 1">新品</text>
                                    </block>
                                </block> -->
                                <block v-if="itm.is_presale === 1">
                                    <text class=" ysIcon tag" style="line-height: 1"></text>
                                </block>
                                <block v-else>
                                    <block v-if="itm.tids && itm.tids.includes('2')">
                                        <text class=" htIcon tag" style="line-height: 1"></text>
                                    </block>
                                    <block v-else-if="itm.tids && itm.tids.includes('1')">
                                        <text class=" newIcon tag" style="line-height: 1"></text>
                                    </block>
                                </block>
                                <view class="text titleW" :style="{
                                        textIndent: itm.is_presale === 1 || itm.tids.includes('2') || itm.tids.includes('1') ? '72rpx' : '0rpx',
                                    }">{{ itm.name }}</view>
                            </view>
                            <!-- <view class="item_desc">{{ itm.introduce }}</view> -->
                            <view class="price">
                                <view style="
                                    color: #a6a6a6;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    width: 100%;
                                    height: 66rpx;
                                ">
                                    <text class="unit">￥</text>
                                    <text
                                        class="optimizePrice"
                                        v-if="String((Number(itm.price || 0) - Number(itm.subsidy_price ||
                                        0)).toFixed(2)).indexOf('.') !== -1"
                                        style="font-size: 42rpx; line-height: 1; align-self: flex-end"
                                    >
                                        {{ String((Number(itm.price || 0) - Number(itm.subsidy_price ||
                                        0)).toFixed(2)).split('.')[0] }}
                                    </text>
                                    <text
                                       class="optimizePrice"
                                        v-if="String((Number(itm.price || 0) - Number(itm.subsidy_price ||
                                        0)).toFixed(2)).indexOf('.') !== -1"
                                        style="font-size: 22rpx; line-height: 1; align-self: flex-end"
                                    >
                                        .{{ String((Number(itm.price || 0) - Number(itm.subsidy_price ||
                                        0)).toFixed(2)).split('.')[1] }}
                                    </text>

                                    <!-- <text class="optimizePrice">{{ (Number(itm.price || 0) - Number(itm.subsidy_price ||
                                        0)).toFixed(2) }}</text> -->
                                    <!-- <text class="originPrice">￥{{ itm.price }}</text> -->
                                </view>
                                <view class="content_title_text_grab_text">抢</view>
                            </view>
                        </view>
                    </view>
                </block>

            </block>
            <block v-if="!listData.list.length && !listData.isLoading">
                <view class="flex-container" style="width: 100%">
                    <view class="column right-column" v-for="(column, columnIndex) in 2" :key="`column-${columnIndex}`">
                        <view class="skeleton-container" v-for="(skeleton, skeletonIndex) in 5"
                            :key="`skeleton-${columnIndex}-${skeletonIndex}`">
                            <u-skeleton :animate="true" :title="false" :rowsHeight="[120]" :loading="true" rows="1"
                                :rowsWidth="['100%']"></u-skeleton>
                        </view>
                    </view>
                </view>
            </block>
            <block v-if="!listData.list.length">
                <view class="epmty">
                    <LazyImage src="https://wpm-cdn.dreame.tech/images/202412/674ef25ad58d98751106040.png"> </LazyImage>
                    <text class="text">暂无商品</text>
                </view>
            </block>
            <!-- 加载中提示 -->
            <view v-if="listData.isLoading && listData.list.length" class="loading-bottom">
                <text class="uni-loading"></text>
                <text style="margin-left: 12rpx; color: #999;font-size: 20rpx;">正在加载中...</text>
            </view>
            <!-- 已加载全部数据提示 -->
            <view v-if="!listData.isLoading && listData.list.length && page >= totalPages" class="loading-bottom">
                <text style="color: #999;font-size: 20rpx;">已加载全部数据</text>
            </view>
        </view>
    </scroll-view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { IGoods } from '@/store/modules/goods';
import { goodsList, wishGoods, adGoodsList } from '@/http/goods';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import InfoFlow from './InfoFlow/InfoFlow.vue';
// const DataConst = require('./mock') as any;
// const { adlists } = DataConst;

@Component({
    components: {
        InfoFlow,
    },
})
export default class More extends Vue {
    constructor() {
        super();
    }

    public enableRefresher: Boolean = false;

    public isRefreshing: Boolean = false;

    public isProductListScrollEnabled: Boolean = false;

    public current: number = 0;

    public isPartZone: any = null;

    public timer: any = null;

    public isLoaded: Boolean = true;

    public adsLists: Array<any> = [];

    public listData: { list: Array<IGoods>; isLoading: Boolean } = {
        list: [],
        isLoading: false,
    };

    public page: number = 1;

    public totalPages: number = 1;

    public insertedAdIds: any = new Set();

    // 商品是否已加入心愿单
    get isGoodsWish(): boolean {
        return true;
    }

    get rowWidths_skeleton_coupon() {
        return [uni.upx2px(100), uni.upx2px(56)];
    }

    get rowHeight_skeleton_coupon() {
        return uni.upx2px(50);
    }

    // 增加折疊屏 - 每頁請求數
    get isFoldableDevice() {
        return Utils.isFoldableDevice();
    }

    moreBtn() {
        this.$emit('moreShow');
    }

    // 预售/自定义 不展示券后价
    showCouponPrice(item: any) {
        return item.is_presale === 0 && item.is_ini === 0;
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        if (this.page > this.totalPages) {
            this.listData.isLoading = false;
            return;
        }
        await this.getGoodsList();
    }

    async init(reLoad = true) {
        if (reLoad) {
            await this.getADSGoodsLists();
            console.log('调用了吗')
            await this._getListFactory();
        }
    }

    handleWishList(isWish: number, gid: string) {
        // opt: add / remove
        const opt = isWish ? 'remove' : 'add';
        wishGoods({ goods_id: gid, opt })
            .then(() => {
                // 本地更新状态：直接修改当前列表中目标商品的 is_wish 状态，避免重新拉取列表
                const target = this.listData.list.find((item) => item.gid === gid);
                if (target) {
                    this.$set(target, 'is_wish', opt === 'add' ? 1 : 0);
                }
            })
            .catch((err) => {
                console.error(err);
            });
    }

    // 获取广告信息
    async getADSGoodsLists() {
        const params: Object = {
            page: 1,
            page_size: 300,
            page_type: 1, // 首页
        };
        console.log(await adGoodsList(params))
        const { code, data } = await adGoodsList(params);
        if (code === 0) {
            this.adsLists = data?.list || [];
        }
    }

    // 根据adsLists中每个对象的index值插入广告数据到产品列表
    insertADSDataToProductLists() {
        // 检查adsLists是否存在且有数据
        if (!this.adsLists || this.adsLists.length === 0) {
            console.log('adsLists为空，跳过广告插入');
            return;
        }

        // 初始化已插入广告ID集合（假设广告有唯一id标识）
        this.insertedAdIds = this.insertedAdIds || new Set();

        // 筛选出未插入过的广告，并按index排序
        const newAds = [...this.adsLists]
            .filter(ad => ad && ad.id && !this.insertedAdIds.has(ad.id))
            .sort((a, b) => a.index - b.index);

        if (newAds.length === 0) {
            console.log('没有新的广告需要插入');
            return;
        }
        // 从后往前插入，避免索引变化影响后续插入
        for (let i = newAds.length - 1; i >= 0; i--) {
            const ad = newAds[i];
            // 检查广告数据是否完整
            if (typeof ad.index !== 'number') {
                console.warn('广告数据不完整，缺少index，跳过:', ad);
                continue;
            }
            // 计算实际插入位置（考虑已插入的广告对索引的影响）
            // 因为从后往前插，后面插入的广告不影响前面的索引
            const actualIndex = ad.index;
            // 检查插入位置是否有效
            if (actualIndex >= 0 && actualIndex <= this.listData.list.length) {
                // 在指定位置插入广告
                this.listData.list.splice(actualIndex, 0, ad);
                // 标记为已插入
                this.insertedAdIds.add(ad.id);
                console.log(`广告已插入到位置 ${actualIndex}:`, ad.title);
            } else {
                console.warn(`广告插入位置无效 ${actualIndex}，产品列表长度: ${this.listData.list.length}`);
            }
        }
    }

    async getGoodsList() {
        // 如果已经加载完所有数据，直接返回
        if (this.page > this.totalPages) {
            this.listData.isLoading = false;
            return;
        }

        try {
            let product_id = '';
            const params: Object = {
                tid: -1,
                type: -2,
                page: this.page,
                page_size: this.isFoldableDevice ? 12 : 6,
                single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
            };
            // 防止重复请求
            if (this.listData.isLoading) return;

            this.listData.isLoading = true;
            const { list = [], pages = 1 } = await goodsList(params);
            this.totalPages = pages;
            if (this.page === 1) {
                this.listData.list = list;
                // // this.listData.list = [...list];
                // const mergeLists = Utils.randomInsertObjects(list, adlists, 'id');
                // this.listData.list = mergeLists
            } else {
                this.listData.list = this.listData.list.concat(list);
                // const mergeLists = Utils.randomInsertObjects(this.listData.list.concat(list), adlists, 'id');
                // this.listData.list = mergeLists
            }
            this.listData.list.map((item) => {
                product_id = product_id ? `${product_id}、${item.gid}` : product_id;
            });
             this.insertADSDataToProductLists();
            Utils.reportEvent('product_show', { product_id });
            this.listData.isLoading = false;
        } catch (e) {
            console.error('getGoodsList e=', e);
            this.listData.isLoading = false;
        }
    }

    gotoGdetailPage(gid: string, name: string) {
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
            id: Number(gid),
            name: name,
        }, true);
        Utils.reportEvent('product_click', { product_id: gid });
        Utils.navigateTo(
            `/pagesB/goodsDetail/goodsDetail?gid=${gid}`,
        );
    }

    // 触底加载更多
    onreachBottom() {
        // 已经加载完所有数据，不再请求
        if (this.page >= this.totalPages) {
            return;
        }
        this.loadMore();
    }

    loadMore() {
        // 如果正在加载中或已加载全部数据，不执行操作
        if (this.listData.isLoading || this.page >= this.totalPages) return;

        this.page++;
        this._getListFactory();
        this.$emit('loadMore');
    }

    // 下拉刷新
    refresh() {
        // 重置分页信息，重新加载第一页
        this.page = 1;
        this.totalPages = 1;
        this.insertedAdIds.clear(); // 清空已插入广告ID记录
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
        }, 0);
        this.$emit('refresh');
    }

    // 控制ProductList滚动状态
    setScrollEnabled(enabled: boolean) {
        this.isProductListScrollEnabled = enabled;
    }

    mounted() {
        // #ifdef MP-WEIXIN
        this.init();
        // #endif
        this.init();

        // 初始时禁用ProductList的滚动
        this.isProductListScrollEnabled = false;

        // 监听来自商品详情页的心愿单状态更新事件
        uni.$on('wishGoodsChange', this.onWishGoodsChange);

        Utils.reportEvent('shop_click', {});
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SHOP_BUTTON,
        });
    }

    beforeDestroy() {
        // 注销全局事件监听，防止内存泄漏
        uni.$off('wishGoodsChange', this.onWishGoodsChange);
        // 清除定时器，防止内存泄漏
        if (this.timer) {
            clearTimeout(this.timer);
        }
    }

    onWishGoodsChange({ gid, is_wish }) {
        const target = this.listData.list.find((item) => item.gid === gid);
        if (target) {
            this.$set(target, 'is_wish', is_wish);
        }
    }
}
</script>
<style lang="scss" scoped>
@import "./ProductList.scss";
.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}
</style>
