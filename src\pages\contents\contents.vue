<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="u-flex u-row-between" style="width: 100%; padding-right: 30rpx">
                    <view class="userInfo u-flex u-col-center u-row-center" @click="goUserInfo(userInfo.user_id)">
                        <image v-if="userInfo.avatar" class="image" :src="userInfo.avatar"></image>
                        <image v-else class="image"
                            src="https://wpm-cdn.dreame.tech/images/202301/185295-1673921873152.png" mode="aspectFill">
                        </image>
                        <image v-if="userInfo.user_avatar_icon"
                            style="width: 20rpx; height: 20rpx; position: absolute; bottom: 0; right: 0; z-index: 1"
                            :src="userInfo.user_avatar_icon"
                            mode="aspectFill"></image>
                    </view>
                    <view style="width: 128rpx; height: 100%"></view>
                    <view class="u-flex u-row-center u-title"> 觅友分享 </view>
                    <view v-if="platform != 'ohos'" class="community-publish" @click="goMessage">
                        <img class="community-publish-img"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/745327-1751428225191.png" />
                    </view>
                    <view class="community-search" @click="goSearch">
                        <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a1623a0de76590410838.png"
                            alt="" />
                    </view>
                </view>
                <view class="swiper-box">
                    <view class="swiper-item">
                        <block>
                            <scroll-view class="scroll-view" scroll-y using-sticky :refresher-enabled="canPullDown"
                                :upper-threshold="0" :lower-threshold="300" :refresher-triggered="isRefreshing"
                                @scrolltolower="onScrollToLower" @refresherrefresh="refresherrefresh"
                                @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd"
                                :scroll-top="scrollTop">
                                <view class="u-flex-col friend-padding">
                                    <view class="u-flex u-row-between u-col-center" style="height: 48rpx;" @click="goMyFriend">
                                        <view style="color: #272727; font-size: 32rpx; font-weight: 500">
                                            我的好友
                                        </view>
                                        <view style="color: #a6a6a6; font-size: 24rpx;">查看全部</view>
                                    </view>
                                    <scroll-view class="scroll-friend" scroll-x>
                                        <view class="u-flex u-friend-list">
                                            <view v-for="(item, index) in personalList" :key="index"
                                                class="u-flex u-row-center friend-avatar" :style="{
                                                    position: 'absolute',
                                                    left: `${index * 40}px`,
                                                    zIndex: `${index}`,
                                                }">
                                                <view @click="goUserInfo(item.user_id)"
                                                    v-if="index < personalList.length - 1">
                                                    <img :src="item.avatar" class="avatar-size" />
                                                    <img v-if="item.user_avatar_icon" style="
                                                            width: 35rpx;
                                                            height: 35rpx;
                                                            position: absolute;
                                                            bottom: 0;
                                                            right: 0;
                                                            z-index: 99999;
                                                        "
                                                        :src="item.user_avatar_icon"
                                                        mode="aspectFill" />
                                                </view>
                                                <view v-else @click="goFriend">
                                                    <img style="width: 100rpx; height: 100rpx"
                                                        src="@/static/friend/icon_add_friend.png" mode="aspectFill" />
                                                </view>
                                            </view>
                                        </view>
                                    </scroll-view>
                                </view>

                                <view class="u-flex friend-tab-view">
                                    <view class="u-flex">
                                        <view v-for="(item, index) in tabsList" :key="index" class="friend-tab"
                                            @click="clickTabIndex(index)">
                                            <img class="friend-tab-img" v-if="tabsIndex == index"
                                                src="@/static/friend/icon_tab_checked.png" alt="aspectFit" />
                                            <view class="friend-tab-title-select" v-if="tabsIndex == index">{{
                                                item.name
                                            }}</view>
                                            <view class="friend-tab-title" v-else>{{ item.name }}</view>
                                        </view>
                                    </view>
                                    <view @click="changeView" style="margin-right: 12px">
                                        <img class="friend-cut-view-img" v-if="isListProduct"
                                            src="@/static/friend/icon_view_double.png" />
                                        <img class="friend-cut-view-img" v-else
                                            src="@/static/friend/icon_view_single.png" />
                                    </view>
                                </view>

                                <!-- <view class="u-friend-banner" @click="halfPriceClick">
                                    <image
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d2bb7cba05110012147.png"
                                        alt="aspectFill"></image>
                                </view> -->
                                <ActivityBanner :type="40"></ActivityBanner>
                                <view class="u-flex u-row-between v-invite-box" v-if="personalList.length == 1 && tabsIndex === 0">
                                    <view class="u-flex" style="margin: 0 24rpx;">
                                        <img src="@/static/friend/icon_invite_img.png"
                                            style="width: 40rpx; height: 40rpx;">
                                        <view style="margin-left: 16rpx;font-size: 11px;">您的好友尚未发布动态，向您推荐广场内容</view>
                                    </view>
                                    <view class="u-invite" @click="goFriend">邀请好友发布动态</view>
                                </view>
                                <view class="u-flex u-row-between v-invite-box" v-if="!activityList.length && tabsIndex === 1">
                                    <view class="u-flex" style="margin: 0 24rpx;">
                                        <img src="@/static/friend/icon_invite_img.png"
                                            style="width: 40rpx; height: 40rpx;">
                                        <view style="margin-left: 16rpx;font-size: 11px;">您关注的用户尚未发布动态，向您推荐广场内容</view>
                                    </view>
                                    <view class="u-invite" @click="goFriend">邀请好友发布动态</view>
                                </view>
                                <block v-if="isListProduct">
                                    <view>
                                        <view v-if="pageLoading" class="page-skeleton-container">
                                            <view class="skeleton-list">
                                                <view class="skeleton-item" v-for="item in 2" :key="item">
                                                    <!-- 头部骨架屏 -->
                                                    <view class="skeleton-header">
                                                        <view class="skeleton-avatar">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[39]" :loading="true" rows="1"
                                                                :rowsWidth="[39]"
                                                                style="border-radius: 50%"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-user-info">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[22]" :loading="true" rows="1"
                                                                :rowsWidth="[60]"
                                                                style="margin-bottom: 8rpx"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-follow-btn">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[28]" :loading="true" rows="1"
                                                                :rowsWidth="[80]"
                                                                style="border-radius: 192rpx"></u-skeleton>
                                                        </view>
                                                    </view>

                                                    <!-- 内容骨架屏 -->
                                                    <view class="skeleton-content">
                                                        <u-skeleton :animate="true" :title="false" :rowsHeight="[320]"
                                                            :loading="true" rows="1" :rowsWidth="['100%']"></u-skeleton>
                                                    </view>

                                                    <!-- 图片骨架屏 -->
                                                    <view class="skeleton-images">
                                                        <view class="skeleton-image-grid">
                                                            <view class="skeleton-image-item" v-for="imgIndex in 3"
                                                                :key="imgIndex">
                                                                <u-skeleton :animate="true" :title="false"
                                                                    :rowsHeight="[117]" :loading="true" rows="1"
                                                                    :rowsWidth="['100%']"
                                                                    style="border-radius: 8rpx"></u-skeleton>
                                                            </view>
                                                        </view>
                                                    </view>

                                                    <!-- 底部操作骨架屏 -->
                                                    <view class="skeleton-actions">
                                                        <view class="skeleton-share">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[20]" :loading="true" rows="1"
                                                                :rowsWidth="[48]"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-operations">
                                                            <view class="skeleton-op-item" v-for="opIndex in 3"
                                                                :key="opIndex">
                                                                <u-skeleton :animate="true" :title="false"
                                                                    :rowsHeight="[20]" :loading="true" rows="1"
                                                                    :rowsWidth="[48]"></u-skeleton>
                                                            </view>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                        <FriendList1 v-if="tabsIndex == 0" :tabsIndex="tabsIndex" :ListData="ListData1"
                                            @refreshFinish="refreshFinish" @goSquare="goSquare" />
                                        <FriendList2 v-if="tabsIndex == 1" :tabsIndex="tabsIndex" :ListData="ListData2"
                                            @refreshFinish="refreshFinish" @goSquare="goSquare" />
                                        <FriendList3 v-if="tabsIndex == 2" :tabsIndex="tabsIndex" :ListData="ListData3"
                                            @refreshFinish="refreshFinish" @goSquare="goSquare" />
                                    </view>
                                </block>
                                <block v-else>
                                    <view>
                                        <view v-if="pageLoading" class="page-skeleton-container">
                                            <view class="skeleton-list">
                                                <view class="skeleton-item" v-for="item in 2" :key="item">
                                                    <!-- 头部骨架屏 -->
                                                    <view class="skeleton-header">
                                                        <view class="skeleton-avatar">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[39]" :loading="true" rows="1"
                                                                :rowsWidth="[39]"
                                                                style="border-radius: 50%"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-user-info">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[22]" :loading="true" rows="1"
                                                                :rowsWidth="[60]"
                                                                style="margin-bottom: 8rpx"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-follow-btn">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[28]" :loading="true" rows="1"
                                                                :rowsWidth="[80]"
                                                                style="border-radius: 192rpx"></u-skeleton>
                                                        </view>
                                                    </view>

                                                    <!-- 内容骨架屏 -->
                                                    <view class="skeleton-content">
                                                        <u-skeleton :animate="true" :title="false" :rowsHeight="[120]"
                                                            :loading="true" rows="1" :rowsWidth="['100%']"></u-skeleton>
                                                    </view>

                                                    <!-- 图片骨架屏 -->
                                                    <view class="skeleton-images">
                                                        <view class="skeleton-image-grid">
                                                            <view class="skeleton-image-item" v-for="imgIndex in 3"
                                                                :key="imgIndex">
                                                                <u-skeleton :animate="true" :title="false"
                                                                    :rowsHeight="[117]" :loading="true" rows="1"
                                                                    :rowsWidth="['100%']"
                                                                    style="border-radius: 8rpx"></u-skeleton>
                                                            </view>
                                                        </view>
                                                    </view>

                                                    <!-- 底部操作骨架屏 -->
                                                    <view class="skeleton-actions">
                                                        <view class="skeleton-share">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[20]" :loading="true" rows="1"
                                                                :rowsWidth="[48]"></u-skeleton>
                                                        </view>
                                                        <view class="skeleton-operations">
                                                            <view class="skeleton-op-item" v-for="opIndex in 3"
                                                                :key="opIndex">
                                                                <u-skeleton :animate="true" :title="false"
                                                                    :rowsHeight="[20]" :loading="true" rows="1"
                                                                    :rowsWidth="[48]"></u-skeleton>
                                                            </view>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                        <Community1 v-if="tabsIndex == 0" :tabsIndex="tabsIndex" :ListData="ListData1" @goSquare="goSquare"
                                            @refreshFinish="refreshFinish" @showGoodsMask="showGoodsMask"
                                            @refreshProductFinish="refreshProductFinish"
                                            @updateContentStatus="updateContentStatus"
                                            :updatedContentResult="updatedContentResult"></Community1>
                                        <Community2 v-if="tabsIndex == 1" :tabsIndex="tabsIndex" :ListData="ListData2" @goSquare="goSquare"
                                            @refreshFinish="refreshFinish" @showGoodsMask="showGoodsMask"
                                            @refreshProductFinish="refreshProductFinish"
                                            @updateContentStatus="updateContentStatus"
                                            :updatedContentResult="updatedContentResult"></Community2>
                                        <Community3 v-if="tabsIndex == 2" :tabsIndex="tabsIndex" :ListData="ListData3" @goSquare="goSquare"
                                            @refreshFinish="refreshFinish" @showGoodsMask="showGoodsMask"
                                            @refreshProductFinish="refreshProductFinish"
                                            @updateContentStatus="updateContentStatus"
                                            :updatedContentResult="updatedContentResult"></Community3>
                                    </view>

                                </block>
                            </scroll-view>
                        </block>
                    </view>
                </view>
                <view class="add-publish" @click="handlePublish">
                    <image class="add-publish-img" src="@/static/friend/icon_add_publish.png" mode="aspectFill" />
                </view>

                <Publish :open="publishOpen" @close="handlepublishClose" />

                <!-- #ifdef H5 -->
                <H5Publish :open="h5PublishOpen" @close="handleH5PublishClose" :rect="h5PublishRect" />
                <!-- #endif -->
            </view>
            <GoodsCard ref="GoodsCard" />
            <GoodsList ref="GoodsList" @buyGoods="buyGoods" />
        </view>
        <NoNetwork v-else :isNetworkConnect="isNetworkConnect" @onRefresh="onRefresh" />
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <WxLogin></WxLogin>
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import Comment from './components/community/components/comment.vue';
import Consultation from './components/community/components/consultation.vue';
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from './components/CustomSwiper.vue';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import ExploreLife from './components/exploreLife/exploreLife.vue';
// eslint-disable-next-line import/no-duplicates
import Community1 from './components/community/community.vue';
// eslint-disable-next-line import/no-duplicates
import Community2 from './components/community/community.vue';
// eslint-disable-next-line import/no-duplicates
import Community3 from './components/community/community.vue';
// eslint-disable-next-line import/no-duplicates
import FriendList1 from './components/FriendList/FriendList.vue';
// eslint-disable-next-line import/no-duplicates
import FriendList2 from './components/FriendList/FriendList.vue';
// eslint-disable-next-line import/no-duplicates
import FriendList3 from './components/FriendList/FriendList.vue';
import GoodsCard from './components/community/components/CardLink/GoodsCard.vue';
import GoodsList from './components/community/components/CardLink/GoodsList.vue';
import {
    searchFollowList,
    getExVideoList,
    getMessageList,
    getActivetyList,
    userOtherDetail,
} from '@/http/requestGo/community';
import Publish from './components/publish/Publish.vue';
import H5Publish from './components/publish/H5Publish.vue';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import { CheckAppJump } from '@/common/decorators';
import ActivityBanner from '../index/components/ActivityBanner/ActivityBanner.vue';
@Component({
    components: {
        CustomSwiper,
        NoNetwork,
        TabsSwiper,
        ExploreLife,
        Community1,
        Community2,
        Community3,
        Publish,
        H5Publish,
        WxLogin,
        Comment,
        Consultation,
        FriendList1,
        FriendList2,
        FriendList3,
        GoodsCard,
        GoodsList,
        ActivityBanner
    },
    filters: {
        clicks(click) {
            if (Number(click) < 1000) {
                const item = click;
                return item;
            } else {
                const item = (Number(click) / 1000).toFixed(1);
                return item + 'k';
            }
        },
    },
})
export default class Contents extends Vue {
    $refs: {
        GoodsCard,
        GoodsList,
    };

    public tabKey: any = 0;

    public pageLoading: boolean = true;
    $u: any;

    get platform(): string {
        return AppModule.platform;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    // 网络状态
    get isNetworkConnect(): Boolean {
        return AppModule.isNetworkConnect;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    public personalList: any = [];

    public scrollTop: number = 0; // 控制scroll-view滚动位置

    public timer: any = null;

    public isRefreshing: Boolean = false;
    public isLoading: Boolean = false;
    public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成

    public page1: number = 1;
    public page_size1: number = 10;
    public page2: number = 1;
    public page_size2: number = 10;
    public page3: number = 1;
    public page_size3: number = 10;

    public content_id: String = '';

    public productList: any = [];

    public tabsList: any = [
        { id: Number(process.env.VUE_APP_TOPIC_ID), level: 1, name: '消息', parent_id: 0 },
        { id: 10, level: 1, name: '动态', parent_id: 0 },
        { id: 10, level: 1, name: '广场', parent_id: 0 },
    ];

    public tabsIndex: Number = 2;
    public list1: any = [];
    public creator: any = -1;

    public userInfo: any = {};
    public isListProduct: boolean = true; // 是否列表产品
    public isDoubleClick: boolean = false; // 是否双击
    public goods_ids: Array<number> = []
    public refreshProduct: boolean = false;
    public ListData1: Array<any> = [];
    public ListData2: Array<any> = [];
    public ListData3: Array<any> = [];
    public currActiveId: any = process.env.VUE_APP_TOPIC_ID;
    public last_content_id1: string = '';
    public last_content_id2: string = '';
    public last_content_id3: string = '';
    public isLoadGrandData1: boolean = false; // 消息加载广场数据
    public isLoadGrandData2: boolean = false; // 动态加载广场数据
    public activityList: Array<any> = [];
    clicks(val, index) {
        if (Number(val) === 0) {
            return ['点赞', '收藏', '评论'][index];
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    async changeView() {
        this.isListProduct = !this.isListProduct;
    }

    formatDate(date) {
        return Utils.timeAgoFormat(date, 'comment');
    }

    public isTimeout: any = null;

    async clickTabIndex(index) {
        if (this.isLoading) return;
        this.isLoading = true;
        this.tabsIndex = index;
        if (this.tabsIndex == 0) {
            if (this.ListData1.length > 0) {
                this.pageLoading = false;
            } else {
                this.pageLoading = true;
                await this.getListData1()
            }
        } else if (this.tabsIndex == 1) {
            if (this.ListData2.length > 0) {
                this.pageLoading = false;
            } else {
                this.pageLoading = true;
                await this.getListData2()
            }
        } else if (this.tabsIndex == 2) {
            if (this.ListData3.length > 0) {
                this.pageLoading = false;
            } else {
                this.pageLoading = true;
                await this.getListData3()
            }
        }
        this.isLoading = false;
    }

    goSquare() {
        this.clickTabIndex(2)
    }

    goUserInfo(creator) {
        console.log('goUserInfo', creator);
        Utils.jumpPersonHome(creator);
    }

    goFriend() {
        Utils.navigateTo('/pages/contents/components/myFriendList/myFriendList')
    }

    buyGoods(options) {
        if (options) {
            (this.$refs.GoodsCard as any).showGoodsMask(options.gid, this.goodMaskContentId);
            (this.$refs.GoodsList as any).toggleVisible();
        }
    }

    refreshFinish() {
        this.pageLoading = false;
    }

    public goodMaskContentId = ''
    showGoodsMask(ids, contentId) {
        this.goodMaskContentId = contentId
        this.goods_ids = ids;
        if (ids.length === 1) {
            console.log(ids[0]);
            (this.$refs.GoodsCard as any).showGoodsMask(ids[0], contentId);
        } else {
            (this.$refs.GoodsList as any).toggleVisible(ids);
        }
    }

    refresherrefresh() {
        if (this.tabsIndex === 0) {
            this.pageLoading = true;
        }
        this.isRefreshing = true;
        this.tabLoading = true;
        if (!this.isRefreshing) {
            return;
        }
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
            this.tabLoading = false;
        }, 200);
    }

    halfPriceClick() {
        Utils.navigateTo('/pagesB/groupGoods/groupGoods');
    }

    async onScrollToLower() {
        if (this.isLoading) return;
        this.isLoading = true;
        if (this.tabsIndex == 0) {
            if (this.isLoadGrandData1) {
                await this.getExVideoList();
                this.isLoading = false;
            } else {
                await this.getListData1();
                this.isLoading = false;
            }
        } else if (this.tabsIndex == 1) {
            if (this.isLoadGrandData2) {
                await this.getExVideoList();
                this.isLoading = false;
            } else {
                await this.getListData2();
                this.isLoading = false;
            }
        } else if (this.tabsIndex == 2) {
            await this.getListData3();
                this.isLoading = false;
        }
    }

    refreshProductFinish() {
        this.refreshProduct = false;
    }

    async onLoad() {
        // #ifdef H5
        await this.init();
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER_EXPOSURE,
        });
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_NAVBAR_LEISURE_CLICK,
        });
        // #endif
    }

    async onShow() {
        const win: any = window;
        win.onAppPageShow = async () => {
            clearTimeout(this.isTimeout);
            this.isTimeout = setTimeout(async () => {
                Utils.sharedStorage('remove', 'del_content_id');
            }, 200);
        };
        if (this.publishOpen || this.h5PublishOpen) {
            uni.hideTabBar();
        } else {
            uni.showTabBar();
        }
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BUTTON,
        });
    }

    async onRefresh() {
        await this.init();
    }

    async init() {
        if (this.tabsIndex == 0) {
            this.ListData1 = [];
            this.page1 = 1;
            this.last_content_id1 = '';
            this.isLoadGrandData1 = false;
            await this.getListData1();
            this.pageLoading = false;
        } else if (this.tabsIndex == 1) {
            this.page2 = 1;
            this.ListData2 = [];
            this.isLoadGrandData2 = false;
            this.last_content_id2 = '';
            await this.getListData2();
            this.pageLoading = false;
        } else if (this.tabsIndex == 2) {
            this.ListData3 = [];
            this.page3 = 1;
            this.last_content_id3 = '';
            await this.getListData3();
            this.pageLoading = false;
        }
        UserModule.getUserInfo().then(async (res) => {
          const fn = userOtherDetail
          const res2: any = await fn({
            other_user_id: +res.user_id,
          });
            this.userInfo = res2.data;
        });
        const data = await searchFollowList({ keyword: '', page: 1, page_size: 20, type: [3] });
        this.personalList = data.data.list || [];
        this.personalList.push({});
        setTimeout(() => {
            this.pageLoading = false;
        }, 300);
    }

    getRandomUniqueItems(array, count) {
        if (array.length < count) {
            return array;
        }

        const list = [...array]; // 复制原数组
        const randomItems = [];

        for (let i = 0; i < count; i++) {
            const randomIndex = Math.floor(Math.random() * list.length);
            randomItems.push(list.splice(randomIndex, 1)[0]);
        }

        return randomItems;
    }

    async getExVideoList() {
        let p = this.page1;
        let l = this.last_content_id1;
        if (this.tabsIndex == 1) {
            p = this.page2;
            l = this.last_content_id2;
        } else if (this.tabsIndex == 2) {
            p = this.page3;
            l = this.last_content_id3;
        }
        const topic_id: number = +this.currActiveId;
        const page: number = p;
        const page_size: number = 10;
        const params = {
            page,
            page_size: page_size,
            topic_id,
            direction: 0,
            last_content_id: l,
        };
        const res = await getExVideoList(params, false);
        if (res.data) {
            this.$emit('refreshFinish')
            const { list = [] } = res.data;
             if (list.length > 0) {
                if (this.tabsIndex == 0) {
                    this.ListData1 = this.ListData1.concat(list);
                    this.last_content_id1 = res.data.last_content_id;
                    this.pageLoading = false;
                } else if (this.tabsIndex == 1) {
                    this.ListData2 = this.ListData2.concat(list);
                    this.last_content_id2 = res.data.last_content_id;
                    this.pageLoading = false;
                } else if (this.tabsIndex == 2) {
                    this.ListData3 = this.ListData3.concat(list);
                    this.last_content_id3 = res.data.last_content_id;
                }
            }
        }
    }

    async getListData3 () {
        try {
            const topic_id: number = +this.currActiveId;
            const page: number = this.page3;
            const page_size: number = 10;
            const params = {
                page,
                page_size: page_size,
                topic_id,
                direction: 0,
                last_content_id: this.last_content_id3,
            };
            const res = await getExVideoList(params, false);
            if (res.success) {
                if (res.data) {
                    const { list = [], last_content_id = '' } = res.data;
                    if (list === null || last_content_id == '') {
                        this.page3 = 1;
                        this.last_content_id3 = '';
                        this.ListData3.push(null)
                        await this.getExVideoList()
                    } else {
                         if (list.length > 0) {
                            this.ListData3 = this.ListData3.concat(list);
                            this.last_content_id3 = res.data.last_content_id;
                        }
                        if (list.length < 4) {
                            this.last_content_id3 = '';
                            this.ListData3.push(null)
                            await this.getExVideoList()
                        }
                    }
                    this.page3++;
                } else {
                    this.last_content_id3 = '';
                    this.ListData3.push(null)
                    await this.getExVideoList()
                }
            }
        } catch (e) {

        }
    }

    async getListData2 () {
        try {
            const topic_id: number = +this.currActiveId;
            const page: number = this.page2;
            const page_size: number = 10;
            const params = {
                page,
                page_size: page_size,
                topic_id,
                direction: 0,
                last_content_id: this.last_content_id2,
            };
            const res = await getActivetyList(params, false);
            if (res.success) {
                if (res.data) {
                    this.$emit('refreshFinish')
                    const { list = [], last_content_id = '' } = res.data;
                    if (list === null || last_content_id == '') {
                        this.page2 = 1;
                        this.isLoadGrandData2 = true;
                        this.last_content_id2 = '';
                        this.ListData2.push(null)
                        await this.getExVideoList()
                    } else {
                         if (list.length > 0) {
                            this.pageLoading = false;
                            this.ListData2 = this.ListData2.concat(list);
                            this.activityList = list;
                            this.last_content_id2 = res.data.last_content_id;
                        }
                        if (list.length < 4) {
                            this.isLoadGrandData2 = true;
                            this.last_content_id2 = '';
                            this.ListData2.push(null)
                            await this.getExVideoList()
                        }
                    }
                    this.page2++;
                } else {
                    this.isLoadGrandData2 = true;
                    this.last_content_id2 = '';
                    this.ListData2.push(null)
                    await this.getExVideoList()
                }
            }
        } catch (e) {

        }
    }

    async getListData1 () {
        try {
            const topic_id: number = +this.currActiveId;
            const page: number = this.page1;
            const page_size: number = 10;
            const params = {
                page,
                page_size: page_size,
                topic_id,
                direction: 0,
                last_content_id: this.last_content_id1,
            };
            const res = await getMessageList(params, false);
            if (res.success) {
                if (res.data) {
                    this.$emit('refreshFinish')
                    const { list = [], last_content_id = '' } = res.data;
                    if (list === null || last_content_id == '') {
                        this.page1 = 1;
                        this.isLoadGrandData1 = true;
                        this.last_content_id1 = '';
                        this.ListData1.push(null)
                        await this.getExVideoList()
                    } else {
                         if (list.length > 0) {
                            this.pageLoading = false;
                            this.ListData1 = this.ListData1.concat(list);
                            this.last_content_id1 = res.data.last_content_id;
                        }
                        if (list.length < 4) {
                            this.isLoadGrandData1 = true;
                            this.last_content_id1 = '';
                            this.ListData1.push(null)
                            await this.getExVideoList()
                        }
                    }
                    this.page1++;
                } else {
                    this.isLoadGrandData1 = true;
                    this.last_content_id1 = '';
                    this.ListData1.push(null)
                    await this.getExVideoList()
                }
            }
        } catch (e) {

        }
    }

    goSearch() {
      Utils.navigateTo(`/pagesC/search/search`);
    }

    goMyFriend() {
        console.log('goMyFriend');
        Utils.navigateTo(`/pages/contents/friendsList`);
    }

    // 跳转到消息页面
    goMessage() {
        Utils.newMessageChannel('PAGE', 'push', { path: '/message_main' });
    }

    public startX: any = 0;
    public startY: any = 0;
    public canPullDown: boolean = true;
    public isLeftMove: boolean = true;

    touchStart(e) {
        this.startX = e.touches[0].pageX;
        this.startY = e.touches[0].pageY;
    }

    touchMove(e) {
        if (!this.isLeftMove) return;
        const moveX = e.touches[0].pageX;
        const moveY = e.touches[0].pageY;
        const diffX = moveX - this.startX;
        const diffY = moveY - this.startY;

        // 如果是左右滑动，禁用下拉刷新
        if (Math.abs(diffX) > Math.abs(diffY)) {
            this.canPullDown = false;
        } else {
            this.isLeftMove = false;
        }
    }

    touchEnd() {
        // 在滑动结束后，启用下拉刷新
        this.canPullDown = true;
        this.isLeftMove = true;
    }

    public publishOpen: boolean = false;
    public h5PublishOpen: boolean = false;

    handlePublish() {
        if (!this.isPhone) {
            UserModule.authFlow({ target: 'handlePublish' });
        } else {
            uni.hideTabBar();
            this.$nextTick(() => {
                this.publishOpen = true;
            });
        }
    }

    handlepublishClose() {
        this.publishOpen = false;
        this.$nextTick(() => {
            uni.showTabBar();
        });
    }

    // #ifdef H5
    public h5PublishRect = {};
    handleH5Publish() {
        uni.createSelectorQuery()
            .select('.community-publish')
            .boundingClientRect((rect) => {
                this.h5PublishRect = rect;
            })
            .exec();
        this.h5PublishOpen = true;
    }

    handleH5PublishClose() {
        this.h5PublishOpen = false;
    }
    // #endif

    backPage() {
        Utils.goBack();
    }

    // 当在觅友圈点击摸个作品时，记录下当前作品的id和创建人id，用于后续更新当前作品状态
    public updatedContentId = '';
    public updatedCreator = '';
    public updatedContentResult = {};
    @CheckAppJump()
    updateContentStatus(params) {
        this.updatedContentId = params.content_id;
        this.updatedCreator = params.creator;
    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view-refresher {
    background-color: #ffffff !important;
}

.swiper-box {
    height: 100vh;
    background-color: #ffffff;

    // #ifdef H5
    ::v-deep .uni-swiper-wrapper {
        overflow: hidden !important;
    }

    // #endif
    .swiper-item {
        width: 100%;
        height: 100%;

        .scroll-view {
            height: 100%;
            background-color: #f6f6f6;

            // #ifdef H5
            ::v-deep .uni-scroll-view {
                .uni-scroll-view-content {
                    height: fit-content;
                }
            }

            // #endif
            .friend-padding {
                padding: 20rpx 30rpx 0;
                width: 100%;
                // height: 210rpx;
                background-color: #ffffff;
            }

            .scroll-friend {
                width: 100%;
                height: 110rpx;
            }

            .u-friend-list {
                position: relative;
                margin-top: 60rpx;
            }

            .friend-tab-view {
                position: sticky;
                top: -1rpx;
                z-index: 9999;
                background-color: #ffffff;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 114rpx;

                .friend-cut-view-img {
                    width: 172rpx;
                    height: 64rpx;
                }

                .friend-tab {
                    position: relative;
                    width: 150rpx;
                    height: 112rpx;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;

                    .friend-tab-box-img {
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 30rpx;
                    }

                    .friend-tab-img {
                        position: absolute;
                        bottom: 30rpx;
                        width: 90rpx;
                        height: 18rpx;
                    }

                    .friend-tab-title {
                        text-align: center;
                        font-size: 32rpx;
                        color: #aaaaaa;
                    }

                    .friend-tab-title-select {
                        text-align: center;
                        font-size: 36rpx;
                        color: #111111;
                        font-weight: 600;
                        z-index: 1;
                    }

                    .active {
                        font-size: 36rpx;
                        font-weight: 600;
                        color: #111111;
                    }
                }
            }

            .u-friend-banner {
                width: 100%;
                height: 160rpx;
                padding: 0 16rpx;
                border-radius: 20px;

                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .v-invite-box {
                height: 96rpx;
                background-color: #ffffff;
                // margin-bottom: 8rpx;
                color: #121212;
                font-size: 28rpx;
            }

            .u-invite {
                background-color: #F5EDD6;
                color: #8C6533;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                border-radius: 28rpx;
                margin-right: 24rpx;
                height: 56rpx;
                width: 220rpx;
            }

            .friend-avatar {
                width: 100rpx;
                height: 100rpx;
                border-radius: 50%;
                border: 1rpx solid #ffffff;
                background-color: #d8d8d8;

                .avatar-size {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                }
            }

            // 页面骨架屏样式
            .page-skeleton-container {
                padding: 0 32rpx;
            }

            .skeleton-list {
                .skeleton-item {
                    background-color: #fff;
                    margin-bottom: 8rpx;
                    padding: 30rpx 32rpx 40rpx;
                    border-radius: 8rpx;

                    .skeleton-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .skeleton-avatar {
                            flex-shrink: 0;
                        }

                        .skeleton-user-info {
                            flex: 1;
                            margin-left: 15rpx;
                        }

                        .skeleton-follow-btn {
                            flex-shrink: 0;
                        }
                    }

                    .skeleton-content {
                        margin-top: 38rpx;
                        width: 100%;
                    }

                    .skeleton-images {
                        margin-top: 16rpx;
                        margin-bottom: 32rpx;

                        .skeleton-image-grid {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 8rpx;

                            .skeleton-image-item {
                                flex: 1;
                                height: 200rpx;
                                margin-right: 8rpx;
                                margin-bottom: 8rpx;

                                &:nth-child(3n) {
                                    margin-right: 0;
                                }
                            }
                        }
                    }

                    .skeleton-actions {
                        padding-top: 30rpx;
                        display: flex;
                        justify-content: space-between;

                        .skeleton-operations {
                            display: flex;

                            .skeleton-op-item {
                                margin-right: 32rpx;

                                &:last-child {
                                    margin-right: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.contents-container {
    position: relative;
    background: #f6f6f6;

    ::v-deep .uni-swiper-wrapper {
        overflow: auto;
    }

    .title {
        display: flex;
        flex-direction: column;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        z-index: 3;
        color: $color-2A2A2A;
        display: flex;
        background: #ffffff;
        align-items: unset;
        // #ifdef H5
        justify-content: center;
        // #endif
        // #ifdef MP-WEIXIN
        justify-content: flex-start;
        // #endif

        .u-title {
            font-size: 36rpx;
            color: #121212;
            width: 100%;
            height: 96rpx;
            font-weight: 600;
        }

        img {
            display: block;
            height: 46rpx;
            width: 46rpx;
        }

        .userInfo {
            position: relative;
            width: 64rpx;
            height: 64rpx;
            min-width: 64rpx;
            margin-left: 24rpx;
            overflow: hidden;

            .image {
                border-radius: 32rpx;
                width: 100%;
                height: 100%;
            }
        }

        .status-bar-left {
            flex-direction: column;
            margin-right: auto;
            justify-content: start;
            align-items: start;

            .text {
                margin-top: 10rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 27rpx;
                color: #121212;
                line-height: 38rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }

    .swiper-image {
        background-color: #f6f6f6;
        width: 100%;
        height: 417rpx;
        padding: 8rpx 8rpx;

        .carousel-map {
            height: 100%;
            border-radius: 8rpx;
            overflow: hidden;
        }
    }

    .scroll-view-look {
        white-space: nowrap;

        ::v-deep .uni-scroll-view-content {
            display: flex;
            align-items: center;
        }

        .item {
            display: inline-block;
            padding-right: 75rpx;

            &:last-child {
                padding-right: 30rpx;
            }
        }
    }

    .active {
        color: #121212;
        border-color: #121212;
    }
}

.community-search,
.community-publish {
    margin-left: 30rpx;

    .community-publish-img {
        display: block;
        height: 100px;
        width: 100px;
    }
}

.add-publish {
    position: fixed;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    right: 16rpx;
    bottom: 30rpx;
    width: 60px;
    height: 60px;

    .add-publish-img {
        width: 100%;
        height: 100%;
    }
}
</style>
