<template>
    <view class="search" @click="navigateTo">
        <image style="width: 48rpx; height: 48rpx;display: block;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/662359-1751427523289.png"></image>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import Utils from '@/common/Utils'
import { BuType } from '@/components/CustomerServicePicker/customer-butype';

@Component
export default class Search extends Vue {
    constructor() {
        super();
    }

    navigateTo() {
        Utils.decryptContact({}, BuType.BuType_NONE);
    }
}
//  @click="navigateTo(item.type, item.url)"
</script>
<style lang="scss" scoped>
</style>
