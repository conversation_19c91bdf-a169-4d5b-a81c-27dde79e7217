<template>
    <div class="good-card" @click="onclick">
        <img :src="image" :alt="title" class="card-image" />
        <div class="card-title">{{ title }}</div>
        <div class="card-status">{{ status }}</div>
    </div>
</template>

<script lang="ts">
export default {
    name: 'GoodCard',
    props: {
        image: {
            type: String,
            default: 'https://wpm-cdn.dreame.tech/images/202306/535513-1686106493238.png'
        },
        title: {
            type: String,
            required: true
        },
        status: {
            type: String,
            default: ''
        },
        clickHandler: {
            type: Function,
            default: () => {}
        }
    },
    methods: {
        onclick() {
            this.$emit('clickHandler');
        }
    }
}
</script>

<style scoped>
.good-card {
    padding: 12rpx;
    background-color: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 8rpx;
}
.card-title {
    font-size: 14rpx;
    font-weight: 500;
    line-height: 20rpx;
    color: #404040;
}
.card-status {
    font-size: 12rpx;
    line-height: 20rpx;
    color: #A6A6A6;
    margin-top: 4rpx;
}
</style>
