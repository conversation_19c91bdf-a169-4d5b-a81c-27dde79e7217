import http from './index';
import req from './requestGo/index.js';

/*
 * 登录
 * @parma {String} provider 开发服调试开关, 为webtest为开
 * @parma {String} openudid  微信jscode
 * @parma {String} user_type  1=>微信登录 ,10=>企业微信登录，99=> 游客登录
 * @parma {String} source  微信场景值来源
 * @parma {String} code  导购信息code
 * @returns
 */
interface ILoginParams {
    user_type: string | number;
    openudid: string; // 微信code
    source?: string | number; // 微信场景值来源
    code?: string; // 导购信息code
    r_code?: string; // 推荐人code
}
export const login = (params: ILoginParams) => http.post('main/login/index', params);

/*
 * jwt登录
 * @parma {Number} jwt
 * @returns
 */
export const jwtLogin = (params) => http.post('main/login/app-login', params);

/*
 * 登录校验
 * @parma {Number} jwt
 * @returns
 */
export const getLoginType = () => http.post('main/login/get-login-type');

/*
 * 重新登录
 * @parma {Number} reset_sess 是否刷新session 0否1是
 * @returns
 */
export const autoLogin = (params) => http.post('main/login/auto', params);

/*
 * 获取个人信息
 * @returns
 */
export const getUserInfo = (params) => http.post('main/my/info', params);
/*
 * 关闭新人大礼包弹窗
 * @returns
 */
export const lockAppRegister = (params) => http.post('main/my/lock-app-register', params);

/*
 * 修改个人信息
 * @parma {String} nick 要修改的昵称
 * @parma {Number} sex  性别 1男2女
 * @parma {String} avatar  头像
 * @returns
 */
export const modify = (params) => http.post('main/data/modify', params);

/*
 * 退出登录
 * @returns
 */
export const logout = () => http.post('main/login/logout');

/*
 * 授权手机号并注册会员
 * @parma {*} encryptedData  包括敏感数据在内的完整用户信息的加密数据【必要】
 * @parma {*} iv  加密算法的初始向量【必要】
 * @parma {*} openudid  jsCode 用于获取微信会话密钥【必要】
 * @parma {*} r_code  推荐人code【必要】
 * @returns
 */
export const phoneAuth = (params) => http.post('main/data/phone-auth', params);

/*
 * 获取新人礼包信息
 * @returns
 */
export const regAwardInfo = (grant_type: string) => http.post('main/activity/list', { grant_type });

/*
 * 领取新人礼包
 * @parma {*} grant_type  1:新人礼包
 * @returns
 */
export const receiveAward = (params) => http.post('main/activity/receive', params);

/*
 * 绑定导购
 * @parma {*} code  导购信息加密code
 * @returns
 */
export const boundGuide = (code: string) => http.post('main/data/bound-guide', { code });

/*
 * 物流公司列表
 * @returns
 */
export const expCom = () => http.post('main/data/exp-com', {});

/*
 * 统一数据上报
 * @returns
 */
interface ICallbackParams {
    id: string;
    type: string;
}
export const dataCallback = (params: ICallbackParams) => http.post('main/data/callback', params);

/*
 * 数据埋点
 * @returns
 */
// export const logTrace = (params) => http.post('main/mall-log/trace', params);
export const logTrace = (params) => req.post('api/v1/mall-log/trace', { ...params });
/*
 * 用户绑定
 * @returns
 */
export const userBind = (params) => http.post('main/user-bind/bind', params);

/*
 * 用户绑定
 * @returns
 */
export const browse = (params) => http.post('main/member-center/browse', params);

