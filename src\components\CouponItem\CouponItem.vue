<template>
    <view @click="$emit('onTap')">
        <view
            class="coupon-item-layout"
            :style="{ width: width }"
            :class="{
                'coupon-disbale': isDisbale,
                'coupon-consumer': !isDisbale && isConsumerCoupon,
                'qixi-coupon': isQixiCoupon,
            }"
        >
            <image v-if="statusImage" class="image" :src="statusImage"></image>
            <!-- 消费券图片，如果有选中的消费券则不显示 -->
            <img
                v-if="consumeListUrl && isSelect"
                class="superposition-image"
                src="https://wpm-cdn.dreame.tech/images/202407/66836584106c70675239058.png"
                alt="消费券提示"
            />
            <!-- 优惠券图片，如果有选中的优惠券则不显示 优惠券不可叠加 -->
            <!-- <img
                v-if="couponListUrl && isSelect"
                class="superposition-image"
                src="https://wpm-cdn.dreame.tech/images/202407/668365f8716634647070653.png"
                alt="优惠券提示"
            /> -->
            <view
                class="layout-top u-flex u-row-between"
                :style="{ width: statusImage ? 'calc(100% - 176rpx)' : '100%' }"
            >
                <view class="top-left">
                    <view class="u-flex">
                        <text class="name" :style="{ maxWidth: isSelect ? '520rpx' : '100%' }"
                            >{{ itemData.web_name || itemData.type_name
                            }}{{
                                isCouponType
                                    ? Number(itemData.resource_type) !== 1
                                        ? ` ${itemData.discount}元`
                                        : ` ${itemData.discount}折`
                                    : ''
                            }}</text
                        >
                        <!-- <block v-if="isCouponType">
              <text class="value" v-if="Number(itemData.resource_type) !== 1">
                {{ itemData.discount }}元
              </text>
              <text v-else class="value">{{ itemData.discount }}折</text>
            </block> -->
                    </view>
                    <view class="condition u-flex u-row-left">
                        <text>
                            {{ itemData.condition }}
                        </text>
                        <text class="devide"> | </text>
                        <text style="width: 360rpx; white-space: nowrap; text-overflow: ellipsis; overflow: hidden">
                            {{ itemData.condition_scope }}
                        </text>
                    </view>
                    <view class="notCanUse" v-if="itemData.canUse === false">
                        {{
                            itemData.type == 6 && carList.length > 1
                                ? '兑换券不支持和其他商品一起下单'
                                : '不符合使用条件'
                        }}
                    </view>
                </view>
                <view class="top-right u-flex u-col-center u-row-center">
                    <view class="check-box" v-if="isSelect" :class="selected ? 'car-sel' : 'car-unsel'"></view>
                    <view
                        class="btn"
                        v-if="!isSelect && itemData.status == '0'"
                        :hover-class="isDisbale ? '' : 'btn-hover'"
                        @click="handleUsed"
                        >{{ statusText }}</view
                    >
                </view>
            </view>

            <view class="layout-bottom u-flex u-row-between u-col-center">
                <view class="bottom-left">
                    <view class="time u-flex u-row-left u-col-center">
                        <block v-if="itemData.start_time > 0">
                            <text>有效期：</text>
                            <text>{{ $u.timeFormat(itemData.start_time, 'yyyy.mm.dd') }}</text>
                            <span>-</span>
                            <text>{{ $u.timeFormat(itemData.expire_time, 'yyyy.mm.dd') }}</text>
                        </block>
                        <text v-else>有效期至{{ $u.timeFormat(itemData.expire_time, 'yyyy.mm.dd') }}</text>
                    </view>
                </view>
                <view v-if="isHasRule" @touchstart.stop.prevent="hanldeShowRule" class="bottom-right u-flex">
                    <text class="rule-text">使用规则</text>
                    <view class="rule-icon"></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { GoodsModule } from '@/store/modules/goods';
interface Coupon {
    id: String;
}

const STATUS_USE = '1'; // 已使用
const STATUS_PAST = '2'; // 过期
// const STATUS_NOT_USE = '3'; // 未生效
const TYPE_COUPON = '1'; // 优惠卷
const CONSUMER_COUPON = '10'; // 消费券
@Component({
    components: {},
})
export default class CouponItem extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly isSelect!: Number;

    @Prop({ type: Number, default: 0 })
    readonly isReceive!: Number;

    @Prop({ type: Object, default: null })
    readonly itemData;

    @Prop({ type: Boolean, default: false })
    readonly selected!: Boolean;

    @Prop({ type: String, default: '' })
    readonly width!: String;

    @Prop({ type: Array, default: () => [] })
    public carList: [];

    @Prop({ type: String, default: '' })
    readonly selectCouponId!: String;

    @Prop({ type: String, default: '' })
    readonly selectConsumeId!: String;

    @Prop({ type: Array, default: () => [] })
    public consumeList!: Coupon[];

    @Prop({ type: Array, default: () => [] })
    public couponList!: Coupon[];

    // 是否是七夕活动优惠券
    @Prop({ type: Boolean, default: false })
    readonly isQixiCoupon!: Boolean;

    public showRuleModel: Boolean = false; // 展示规则模块
    public rule: String = '';

    get isDisbale(): Boolean {
        if (
            this.itemData.status == STATUS_USE ||
            this.itemData.status == STATUS_PAST ||
            this.itemData.canUse == false
        ) {
            return true;
        }
        return false;
    }

    get isHasRule(): Boolean {
        if (uni.$u.test.isEmpty(this.itemData.use_rule_note)) return false;
        return true;
    }

    get isCouponType(): Boolean {
        return this.itemData.type === TYPE_COUPON || this.itemData.type === CONSUMER_COUPON;
    }

    get isConsumerCoupon(): Boolean {
        return this.itemData.type === CONSUMER_COUPON;
    }

    get statusText(): string {
        let text = '去使用';

        if (this.itemData.status === STATUS_USE) {
            text = '已使用';
        }
        if (this.itemData.status === STATUS_PAST) {
            text = '已过期';
        }
        return text;
    }

    get statusImage(): String {
        let url = '';
        if (this.itemData.coupon_status === 1) {
            // 已领取
            url = 'https://wpm-cdn.dreame.tech/images/202307/453551-1688524341239.png';
        } else if (this.itemData.coupon_status === 2) {
            // 已过期
            url = 'https://wpm-cdn.dreame.tech/images/202307/555311-1688525669497.png';
        } else if (this.itemData.coupon_status === 4) {
            // 已领光
            url = 'https://wpm-cdn.dreame.tech/images/202307/168822-1688524417501.png';
        } else if (this.itemData.status === STATUS_PAST) {
            // 已过期
            url = 'https://wpm-cdn.dreame.tech/images/202307/555311-1688525669497.png';
        }
        return url;
    }

    get consumeListUrl(): Boolean {
        return (
            !this.consumeList.some((item) => item.id === this.selectConsumeId) &&
            this.itemData.type === CONSUMER_COUPON &&
            !this.isDisbale
        );
    }

    get couponListUrl(): Boolean {
        return (
            !this.couponList.some((item) => item.id === this.selectCouponId) &&
            this.itemData.type === TYPE_COUPON &&
            !this.isDisbale
        );
    }

    handleUsed() {
        if (this.isDisbale) return;
        // if (this.itemData.status === STATUS_NOT_USE) return; // 未到生效时间，不能跳转
        if (Number(this.itemData.jump_goods_id) === 0) {
            // #ifdef H5
            uni.navigateTo({ url: '/pages/shop/shop' });
            // #endif
            // #ifdef MP-WEIXIN
            uni.switchTab({ url: '/pages/shop/shop' });
            // #endif
        } else {
            // 使用指定优惠券ID
            GoodsModule.setUseCouponId('0');
            uni.navigateTo({
                url: `/pagesB/goodsDetail/goodsDetail?gid=${this.itemData.jump_goods_id}`,
            });
        }
    }

    handleReceive() {
        this.$emit('receive');
    }

    hanldeShowRule() {
        this.$emit('showRule', this.itemData.use_rule_note);
    }
}
</script>

<style lang="scss" scoped>
.coupon-item-layout {
    width: 100%;
    height: 231rpx;
    position: relative;
    background-image: url('https://wpm-cdn.dreame.tech/images/202307/64c23b18352882172817834.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 24rpx 32rpx 18rpx;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    margin: 0 auto;

    .image {
        position: absolute;
        right: 0;
        top: 0;
        width: 220rpx;
        height: 182rpx;
    }

    .superposition-image {
        position: absolute;
        right: 0;
        top: 0;
        width: 100%;
        height: 231rpx;
    }

    .layout-top {
        align-items: self-start;

        .top-left {
            overflow: hidden;

            .name,
            .value {
                font-size: 32rpx;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: $brand-color-btn-text;
                line-height: 42rpx;
            }

            .condition {
                margin-top: 8rpx;
                font-size: 24rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: $brand-color-btn-text;
                line-height: 34rpx;
                overflow: hidden;
                max-width: 100%;
                text-wrap: nowrap;
                white-space: nowrap;
                text-overflow: ellipsis;

                .devide {
                    padding: 0 4rpx;
                    color: $brand-color-btn-text;
                }
            }

            .notCanUse {
                position: absolute;
                right: 32rpx;
                bottom: 94rpx;
                font-size: 20rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: $text-color-disable;
                line-height: 28rpx;
            }
        }

        .top-right {
            font-size: 24rpx;

            .check-box {
                position: absolute;
                top: 62rpx;
                right: 32rpx;
                width: 36rpx;
                height: 36rpx;

                &.car-sel {
                    width: 36rpx;
                    height: 36rpx;
                    background-image: url('https://wpm-cdn.dreame.tech/images/202307/64b74bc756c643555594271.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }

                &.car-unsel {
                    width: 36rpx;
                    height: 36rpx;
                    background-image: url('@/static/check.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }

            .btn {
                width: 116rpx;
                height: 48rpx;
                border-radius: 38rpx;
                border: 2rpx solid $fill-color-primary-active;
                line-height: 44rpx;
                text-align: center;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: $brand-color-btn-text;
            }
        }
    }

    .layout-bottom {
        width: calc(100% - 32rpx);
        position: absolute;
        left: 32rpx;
        bottom: 22rpx;
        height: 32rpx;

        .bottom-left {
            .time {
                font-size: 20rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: $text-color-secondary;
                line-height: 32rpx;
            }
        }

        .bottom-right {
            position: absolute;
            right: 32rpx;

            .rule-text {
                font-size: 20rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: $text-color-secondary;
                line-height: 32rpx;
            }

            .rule-icon {
                margin-left: 8rpx;
                width: 24rpx;
                height: 24rpx;
                background-image: url('https://wpm-cdn.dreame.tech/images/202307/64b7966e06ad80277397426.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
        }
    }

    &.coupon-disbale {
        background-image: url('https://wpm-cdn.dreame.tech/images/202307/64c23b18765734842821095.png');

        .layout-top {
            .top-left {
                .u-flex,
                .condition {
                    color: $text-color-disable !important;

                    .name,
                    .value,
                    .devide {
                        color: $text-color-disable !important;
                    }
                }
            }

            .top-right {
                .check-box {
                    &.car-unsel {
                        background-image: url('https://wpm-cdn.dreame.tech/images/202307/64b74cfee4a9e9365594297.png');
                    }
                }
            }
        }

        .layout-bottom {
            .bottom-left,
            .bottom-right {
                .time,
                .rule-text {
                    color: $text-color-disable !important;
                }

                .rule-icon {
                    background-image: url('https://wpm-cdn.dreame.tech/images/202307/64b7966e066590267472666.png');
                }
            }
        }
    }

    &.coupon-consumer {
        background-image: url('https://wpm-cdn.dreame.tech/images/202406/667d1aa47ee1d5202430418.png');

        .layout-top {
            .top-left {
                .u-flex,
                .condition {
                    color: $text-color-white !important;

                    .name,
                    .value,
                    .devide {
                        color: $text-color-white !important;
                    }
                }
            }
        }

        .layout-bottom {
            .bottom-left,
            .bottom-right {
                .time,
                .rule-text {
                    color: $text-color-white !important;
                }

                .rule-icon {
                    background-image: url('https://wpm-cdn.dreame.tech/images/202406/667d1c75532753412403438.png');
                }
            }
        }

        .btn {
            border: 2rpx solid $fill-color-bg-white !important;
            color: $text-color-white !important;
        }
    }
}
.qixi-coupon {
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a41527a1cb46630015946.png');
    .layout-top {
        .top-left {
            .name,
            .value {
                color: #fff;
            }

            .condition {
                color: #fff;

                .devide {
                    color: #fff;
                }
            }
        }

        .top-right {
            font-size: 24rpx;
            .btn {
                border: 2rpx solid #fff;
                color: #fff;
            }
        }
    }
    .layout-bottom {
        .bottom-left {
            .time {
                color: #fff;
            }
        }

        .bottom-right {
            .rule-text {
                color: #fff;
            }

            .rule-icon {
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a41c0f31e212040010425.png');
            }
        }
    }
}
</style>
