.boost-progress {
  position: relative;
  width: calc(100% - 64rpx);
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a95b8eef2af9800028646.png') no-repeat center center;
  height: 774rpx;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 104rpx 0rpx 26rpx 0rpx;
  margin: 16rpx auto 0rpx;
}

.reward-tiers {
  display: flex;
  justify-content: space-between;
  padding: 0rpx 48rpx 0rpx 48rpx;
  margin-bottom: 10rpx;

  .first {
    max-width: 154rpx;
    height: 196rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7d4c662dcd4050018067.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .second {
    max-width: 154rpx;
    height: 196rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7d4eb835825380011790.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .third {
    max-width: 154rpx;
    height: 196rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7d5314891c2970011282.png') no-repeat center center;
    background-size: 100% 100%;
  }

}

.reward-tier {
  flex: 1;
  text-align: center;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.reward-tier.unlocked {
  opacity: 1;
}

.gift-box {
  width: 80px;
  height: 80px;
  margin: 0 auto 10px;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
  border-radius: 15px;
  position: relative;
  box-shadow: 0 8px 20px rgba(255, 107, 157, 0.3);
}

.gift-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.product {
  width: 30px;
  height: 20px;
  margin-bottom: 5px;
}

.vacuum-cleaner {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 3px;
  position: relative;
}

.vacuum-cleaner::after {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 8px;
  background: white;
  border-radius: 2px;
}

.vacuum-cleaner.premium::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 4px;
  background: white;
  border-radius: 2px;
}

.laptop {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 3px;
  position: relative;
}

.laptop::after {
  content: '';
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 6px;
  background: white;
  border-radius: 2px;
}

.coupon {
  font-size: 10px;
  margin-bottom: 2px;
}

.number {
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.tier-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.progress-section {
  margin-bottom: 30px;
}

.progress-bar {
  box-sizing: border-box;
  height: 20rpx;
  width: calc(100% - 112rpx);
  margin: 0 auto;
  background: #FFE3E2;
  border-radius: 10rpx;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 0rpx 8rpx;
}

.progress-fill {
  height: 12rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7e3833db6a2530012112.png') no-repeat center center;
  background-size: 100% 100%;
  transition: width 0.5s ease;
  border-radius: 1246rpx;
  overflow: hidden;
}

.progress-marker {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 42rpx;
  height: 42rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dd8de13369220011579.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.progress-marker.unlocked {
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a95709dbdba9010010335.png') no-repeat center center;
  background-size: 100% 100%;
}

.marker-1 {
  left: 7.965%;
}

.marker-2 {
  left: 46.39%;
}

.marker-3 {
  right: 7.965%;
}

.current-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 58px - 16rpx);
  position: relative;
  margin: 0 auto;
}

.progress-arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a8039ade2b19100010432.png') no-repeat center center;
  background-size: 100% 100%;
  transform: translate(-50%, -50%);
  width: 46rpx;
  height: 44rpx;
}

.progress-text {
  position: absolute;
  top: 18rpx;
  color: #EC225B;
  font-size: 24rpx;
  left: 50%;
  white-space: nowrap;
  transform: translate(-50%, -50%);
}

.invite-button-container {
  max-width: 462rpx;
  min-width: 462rpx;
  height: 114rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a952a5631b04060010376.gif') no-repeat center center;
  background-size: contain;
  position: absolute;
  bottom: 26rpx;
  left: 50%;
  transform: translateX(-50%);
}