<template>
    <view v-show="!isCompleted" class="timeout-tips"
        :class="{ counting: time > 0 && isShow, completed: isCompleted, borderColor: !isShow }"
        :style="time > 0 && isShow ? { '--time-remaining': time, '--total-time': 15000 } : {}">
        <view class="timeout" :style="{ marginTop: isShow ? '15rpx' : '25rpx' }">
            <view v-if="isShow">
                <view>浏览{{ Math.ceil(time / 1000) }}s</view>
                <view>获得奖励</view>
            </view>
            <view v-else>
                任务完成
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Watch, Prop } from 'vue-property-decorator';
import { getTaskInfo, doGoldTask } from '@/http/vip';
import { UserModule } from '@/store/modules/user';

@Component
export default class GoldCoinTimeoutTips extends Vue {
    public isShow: boolean = false;
    public isCompleted: boolean = true;
    public point: number = 0;
    public grow: number = 0;
    public timer: any = null;
    public time: number = 15000;
    public taskInfoMap: any = {
        oneYuanPurchase: 'mall/dreame/view_goods_one_yuan', // 一元购
        richPlan: 'mall/dreame/view_goods_rich_plan', // 暴富计划
        fiveDiscountPurchase: 'mall/dreame/view_goods_five_buy', // 五折购
        halfPriceBuy: 'mall/dreame/view_goods_half_buy', // 半价购
        pointsShopping: 'mall/dreame/view_goods_points_shopping', // 积分购物
        appShareGoods: 'mall/dreame/app_invite_reg_gold', // 分享商品
        appInviteRegGold: 'mall/dreame/app_share_goods', // 邀请注册
        viewGoodsMoney: 'mall/dreame/view_goods_money', // 逛商城15秒
    };

    @Prop({
        type: String,
        default: '',
    })
    readonly taskCode;

    @Watch('time', { immediate: true })
    timeChange(newVal: number, oldVal: number) {
        if (this.time <= 0) {
            this.isShow = false;
            this.clearTimer();
            this.setViewGoodsTaskStatus();
        }
    }

    @Watch('isShow', {})
    isShowChange(newVal: number, oldVal: number) {
        if (this.isShow == false) {
            setTimeout(() => {
                this.timer = null;
            }, 17);
        }
    }

    // 是否授权
    get wxAuth(): boolean {
        let wxAuth = true;
        // #ifdef MP-WEIXIN
        wxAuth = UserModule.wxAuth;
        // #endif
        return wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        let isPhone = true;
        // #ifdef MP-WEIXIN
        isPhone = UserModule.isPhone;
        // #endif
        return isPhone;
    }

    async getViewGoodsTaskStatus() {
        getTaskInfo({ taskCode: this.taskInfoMap[this.taskCode] }).then((res) => {
            console.log('res', typeof res.completed);
            if (!res.completed) {
                this.isShow = true;
                this.isCompleted = false;
                this.timer = setInterval(() => {
                    this.time = this.time - 17;
                }, 17);
            } else {
                this.isShow = false;
                this.isCompleted = true;
            }
        });
    }

    async setViewGoodsTaskStatus() {
        await doGoldTask({ type: this.taskCode });
        this.clearTimer();
    }

    clearTimer() {
        this.timer && clearInterval(this.timer);
        this.timer = null;
        this.isShow = false;
    }
}
</script>
<style lang="scss" scoped>
.timeout-tips {
    width: 96rpx;
    height: 96rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889e04fb7ef27530011544.png') no-repeat center center;
    background-size: 100% 100%;
    position: fixed;
    right: 16rpx;
    top: 220rpx;
    z-index: 4;
    font-family: MiSans;
    font-size: 18rpx;
    font-weight: 600;
    color: #fb495c;
    display: flex;
    justify-content: center;
    text-align: center;
    border-radius: 50%;
}

.borderColor::before {
    border: 4rpx solid #fb495c !important;
}

.timeout-tips::before {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    border-radius: 50%;
    border: 4rpx solid #616161;
    z-index: -2;
}

.timeout-tips::after {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    border-radius: 50%;
    background: conic-gradient(from 0deg,
            #f84145 0deg,
            #f84145 calc(var(--progress, 0) * 360deg),
            transparent calc(var(--progress, 0) * 360deg),
            transparent 360deg);
    -webkit-mask: radial-gradient(circle at center, transparent 42rpx, black 46rpx);
    mask: radial-gradient(circle at center, transparent 42rpx, black 46rpx);
    z-index: -1;
    transition: all 0.3s ease;
    opacity: 0;
}

.timeout-tips.counting::after {
    --progress: calc(1 - var(--time-remaining, 0) / var(--total-time, 15));
    opacity: 1;
}

.timeout-tips.completed::after {
    background: conic-gradient(from 0deg, #f84145 0deg, #f84145 360deg);
    -webkit-mask: radial-gradient(circle at center, transparent 42rpx, black 46rpx);
    mask: radial-gradient(circle at center, transparent 42rpx, black 46rpx);
    opacity: 1;
}
</style>
