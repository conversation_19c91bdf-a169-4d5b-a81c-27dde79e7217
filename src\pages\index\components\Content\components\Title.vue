<template>
    <view class="title-wrap">
        <view class="title_text">{{ title }}</view>
        <view class="title_slot">
            <slot name="contentWrap"></slot>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
        // uScrollList,
        // CoinPoints,
        // MemberLevel,
    }
})
export default class TabList extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: [Number, String], default: '99' })
    readonly title!: any;

    async created() {
        // await this.fetchTagList();
    }
}
</script>

<style lang="scss" scoped>
.title-wrap{
    padding: 20rpx;
    background-color: #fff;
    .title_text{
        color: #3D3D3D;
        font-size: 28rpx;
        font-weight: 500;
    }
}
</style>
