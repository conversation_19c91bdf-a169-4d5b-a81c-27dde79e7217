<template>
    <view>
        <!-- <view class="ceo_welfare"  @click="navigateToPage('/pages/shop/shop', CEO_WELFARE_CLICK_EVENT)">
        <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686fadd91b5d81120011992.png" style="width: 100%;height: 100%;border-radius: 16rpx;"/>
    </view> -->
        <view class="ceo_container">
            <view
                v-for="(item, idx) in ceoActions"
                :key="idx"
                :class="item.class"
                @click="ceoActionClick(item.url, item.event, item.id)"
            >
                <image :src="item.img" :style="imgStyle" />
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { Vue, Component } from 'vue-property-decorator';
import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
// import { getPrizeDrawingInfo } from '@/http/jsfl';

interface CeoActionItem {
    class: string;
    url: string;
    event: number;
    img: string;
    id: number;
}

@Component
export default class CeoAction extends Vue {
    public CEO_WELFARE_CLICK_EVENT =
        Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CEO_WELFARE_CLICK;

        get imgStyle() {
        return {
            // width: '352rpx',
            // height: '258rpx',
            width: '100%',
            height: '100%',
            borderRadius: '15rpx',
        };
    }

    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    // 配置抽离，易于维护和扩展
    ceoActions: CeoActionItem[] = [
        // {
        //     class: 'ceo_container_left',
        //     url: '/pages/shop/shop',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CEO_ONLINE_CLICK,
        //     img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686aa148c23577950010567.png',
        // },
        // {
        //     class: 'ceo_container_right',
        //     url: '/pagesC/serve/serve',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CEO_CASH_RED_ENVELOPE_CLICK,
        //     img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686aa15d7e62a5180014345.png',
        // },
        // {
        //     class: 'ceo_container_left',
        //     url: '/pages/shop/shop',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CEO_GIFT_BAG_CLICK,
        //     img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870dd6c6a6164360012235.png',
        // },
        //  {
        //     class: 'ceo_container_left',
        //     url: 'jsfl',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_SUZHOU_CLICK,
        //     img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68750208ae43f7140022045.jpg',
        //     id: 25072001
        // },
        {
            class: 'ceo_container_left',
            url: 'jsfl',
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SUCHAO_NANTONG_CLICK,
            img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6877887fb047a7220011263.jpg',
            id: 25071901
        },
        {
            class: 'ceo_container_right',
            url: '/pagesB/nationalSubsidy/index',
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_GUOBU_CLICK,
            img: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687ce2203f3322590011869.png',
            id: 0
        },
    ];

    ceoActionClick(url: string, event: number, id: number) {
        if (url !== 'jsfl') {
            Utils.navigateTo(url);
        } else {
            // getPrizeDrawingInfo({ uid: this.user_id, action_id: id }).then((res) => {
            //     if (
            //         res.data.user_status === 1 &&
            //         res.data.status === 4 &&
            //         res.data.is_winner === 1 && res.data.is_tip_shown === 0
            //     ) {
            //         Utils.navigateTo('/pagesC/jsfl/suchao_share?actionId=' + id + '&uid=' + this.user_id);
            //     } else {
                    Utils.navigateTo(`/pagesC/jsfl/jsfl?actionId=${id}`);
                // }
            // })
        }

        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event,
        });
    }
}
</script>

<style lang="scss" scoped>
.ceo_container {
    display: flex;
    align-items: center;
    background: #f6f6f6;
    padding: 15rpx;
    margin-top: 15rpx;
    justify-content: space-between;
    &_left {
        width: 352rpx;
        height: 177rpx;
    }
    &_right {
        width: 352rpx;
        height: 177rpx;
    }
}
.ceo_welfare {
    width: 100%;
    height: 230rpx;
    border-radius: 16rpx;
    margin-top: 15rpx;
    padding: 0 15rpx;
    background-size: 100% 100%;
}
</style>
