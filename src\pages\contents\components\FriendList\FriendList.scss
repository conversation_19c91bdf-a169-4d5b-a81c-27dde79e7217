$fill-color-bglg: linear-gradient(90deg, rgba(0, 0, 0, 0.09) 0%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg2: linear-gradient(133deg, #fff2cb 0%, #eedec1 98%);
$fill-color-bglg3: linear-gradient(270deg, #ffffff 55%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg4: linear-gradient(243deg, #ee6b00 -9%, #f6a253 97%);
// 文字颜色
$text-color-more: #7d7d7d;
$top: 58rpx;
$bottm: 60rpx;
$total: $bottm + $top;

.right-box {
  // width: 100%;

  .page-view {
    display: flex;
    flex-direction: column;
    // width: 100%;

    .u-good-content {
      @include flex(row, space-between, flex-start, wrap);
      padding: 0 8rpx 0;
      margin-top: 8rpx;
    }

    .no_more {
      width: 100vw;
      background-color: #ffffff;
      text-align: center;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(29, 30, 32, 0.4);
      line-height: 80rpx;
      height: 80rpx;
      padding-left: 24rpx;
      margin: 8rpx 0 16rpx 0;
    }

    .class-item {
      width: calc(calc(100vw - 24rpx)/2);
      background: #ffffff;
      border-radius: 8rpx;
      margin-bottom: 8rpx;
      overflow: hidden;

      .class-item-top {
        width: 100%;
        padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
        margin: 0 auto 15rpx;
        position: relative;
        background-color: #f5f5f5; // 添加默认背景色
        overflow: hidden; // 防止内容溢出

        .yushouImg {
          position: absolute;
          top: 0;
          left: 0;
          width: 136rpx;
          height: 40rpx;
          z-index: 3;
          background-image: url("@/static/friend/icon_friend_anchor_double.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .hotProImg {
          position: absolute;
          top: 0;
          left: 0;
          width: 48px;
          height: 40rpx;
          z-index: 3;
          background-image: url("@/static/friend/icon_friend_hot_double.png");

          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        img {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        ::v-deep image {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 8rpx 8rpx 0 0;
        }
      }

      .class-item-bottom {
        padding: 0 15rpx 15rpx;
        width: 100%;

        .name {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 26rpx;
          color: #121212;
          line-height: 32rpx;
          height: 80rpx;
          text-align: left;

          .titleW {
            width: 100%;
            // height: 40rpx;
            line-height: 40rpx;
            // 2行
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .price {
          margin-top: 12rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;

          .u-flex {
            width: calc(100% - 58rpx);
          }

          .img {
            width: 18px;
            height: 18px;
            border-radius: 9px;
          }

          .text {
            margin-left: 5px;
            font-size: 10px;
            color: #777777;
            font-weight: 400;
          }

          .oper_item {
            display: flex;
            flex-direction: row;
            align-items: center;

            &:not(:last-child) {
              padding-right: 32rpx;
            }

            img,
            image {
              width: 16px;
              height: 16px;
              margin-right: 8rpx;
              object-fit: contain;
            }

            .oper_num {
              font-family: MiSans, MiSans;
              font-weight: 400;
              font-size: 24rpx;
              color: #777777;
              line-height: 40rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }

    /* 媒体查询适应折叠屏 */
    @media screen and (min-width: 450px) {
      .class-item {
        width: calc(calc(100vw - 32rpx - 36rpx)/4);

        &:nth-child(even) {
          transform: none;
        }

        &:not(:nth-child(4n+1)) {
          margin-left: 16rpx;
        }
      }
    }

    .list-title {
      position: fixed;
      top: 34rpx;
      right: 0;
      left: 0;
      z-index: 100;
      background: #ffffff00;

      .title-img {
        position: absolute;
        top: 0rpx;
        right: 26rpx;
        z-index: 100;
        // width: 100rpx;
        height: 38rpx;
        display: flex;
        align-items: center;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 28rpx;
        color: #404040;
        line-height: 38rpx;
        text-align: left;

        .arrowTransform {
          transition: 0.2s;
          transform-origin: center;
        }

        .arrowTransformReturn {
          transition: 0.2s;
          transform-origin: center;
          transform: rotate(180deg);
        }
      }

      .drop-down {
        position: relative;
        top: -38rpx;
        max-height: calc(100% - 94rpx);
        overflow: hidden;
        overflow-y: auto;

        // border-radius: 0px 0px 23rpx 23rpx;
        background: #ffffff;
        transition: all 0.3s ease-in-out;

        .down-box {
          height: 88rpx;
          padding: 0 16rpx;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 32rpx;
          line-height: 88rpx;
          color: $text-color-regular;

          &.active {
            position: relative;
            font-weight: 400;
            color: #8c6533;
            text-transform: none;
            background: rgba(241, 223, 200, 0.27);
            border-radius: 4rpx 4rpx 4rpx 4rpx;

            &::after {
              content: '';
              position: absolute;
              right: 26rpx;
              top: 24rpx;
              width: 40rpx;
              height: 40rpx;
              background-image: url('https://wpm-cdn.dreame.tech/images/202411/672c6a81546803460056226.png');
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }
          }
        }
      }
    }

    .group-list {
      position: relative;
      top: 0;
      z-index: 10;
      overflow-y: auto;
      height: 100%;
      // #ifdef MP-WEIXIN
      padding-bottom: 86rpx;
      // #endif
      // #ifdef H5
      padding-bottom: calc(86rpx);
      // #endif
      -webkit-overflow-scrolling: touch;

      .item {
        .item-title {
          position: sticky;
          top: -1rpx;
          z-index: 10;
          height: 92rpx;
          padding: 0 24rpx;
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 32rpx;
          color: #404040;
          line-height: 40rpx;
          background: #f5f5f5;
          display: flex;
          align-items: center;
        }

        .item-content {
          @include flex(row, flex-start, flex-start, wrap);
          position: relative;
          -webkit-overflow-scrolling: touch;
          padding: 0rpx 8rpx 0;

          .item-content-block {
            width: calc(50% - 4rpx);
            overflow: hidden;
            margin-bottom: 8rpx;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
            background: #ffffff;

            .class-content-top {
              width: 100%;
              padding-top: 113%; // 图片尺寸 1170*1320 高宽比 1.13
              overflow: hidden;
              margin: 0 auto 36rpx;
              position: relative;

              img {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
              }

              ::v-deep image {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
              }
            }

            .class-content-bottom {
              padding: 0rpx 38rpx 46rpx;

              .name {
                height: 64rpx;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 26rpx;
                line-height: 32rpx;
                text-align: left;
                color: #20252b;
              }
            }

            &:nth-child(even) {
              transform: translateX(8rpx);
            }
          }

          @media screen and (min-width: 450px) {
            .item-content-block {
              width: calc(25% - 10rpx);

              &:nth-child(even) {
                transform: none;
              }

              &:not(:nth-child(4n+1)) {
                margin-left: 12rpx;
              }
            }
          }
        }

        .more {
          @include flex(row, center, center, none);
          margin-top: 32rpx;

          .text {
            font-size: 24rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: $text-color-more;
            line-height: 46rpx;
          }

          .downArrow {
            width: 32rpx;
            height: 32rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }

          .upArrow {
            width: 32rpx;
            height: 32rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            transform: rotate(180deg);
          }
        }
      }
    }

    .epmty {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-bottom: 8rpx;
      margin-top: 258rpx;

      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #777777;
        line-height: 38rpx;
      }
    }

    // .no_more {
    //     width: 100%;
    //     text-align: center;
    //     font-size: 24rpx;
    //     font-weight: 400;
    //     color: rgba(29, 30, 32, 0.4);
    //     line-height: 33px;
    //     margin-top: 50rpx;
    // }
  }

  .no-more {
    // margin: 46rpx auto;
    font-size: 28rpx;
    color: $text-color-secondary;
    line-height: 100rpx;
    text-align: center;
  }
}

.box-type {
  height: 30rpx;
  font-size: 22rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: $brand-color-btn-text;
  line-height: 30rpx;
}