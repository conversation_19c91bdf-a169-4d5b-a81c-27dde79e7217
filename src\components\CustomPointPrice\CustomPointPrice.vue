<template>
    <view class="price-point" :style="{color}">
        <view class="point" v-if="curCoin">
            <text class="point-number">{{ curCoin }}</text
            ><text class="point-unit">积分</text>
        </view>
        <view class="plus-sign" v-if="curCoin && curPrice">+</view>
        <view class="pay-price" v-if="curPrice">￥{{ curPrice }}</view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
@Component({})
export default class CustomPointPrice extends Vue {
    @Prop({ type: String, default: '' })
    readonly coin;

    @Prop({ type: String, default: '' })
    readonly price;

    @Prop({ type: String, default: '#121212' })
    readonly color;

    get curCoin(): any {
        let coin = 0;
        if (this.coin) {
            coin = Number(this.coin);
        }
        return coin;
    }

    get curPrice(): any {
        let price = 0;
        if (this.price) {
            price = Number(this.price);
        }
        return price;
    }

    created() {}
}
</script>
<style lang="scss" scoped>
.price-point {
    display: flex;
    align-items: center;
    font-weight: 500;
    height: 40rpx;
    .point {
        display: flex;
        align-items: center;
        padding: 0px;
        .point-number {
            font-size: 26rpx;
            font-weight: 500;
        }
        .point-unit {
            font-size: 26rpx;
            font-weight: 500;
            margin-left: 4rpx;
        }
    }
    .plus-sign {
        font-size: 26rpx;
        font-weight: 500;
    }

    .pay-price {
        font-size: 26rpx;
        font-weight: 500;
    }
}
</style>
