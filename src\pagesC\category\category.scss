::v-deep .uni-scroll-view-refresher {
  background-color: #ffffff !important;
}


.contents-container {
  position: relative;
  background: #f6f6f6;

  ::v-deep .uni-swiper-wrapper {
      overflow: auto;
  }

  .title {
      display: flex;
      flex-direction: column;
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 3;
      color: $color-2A2A2A;
      display: flex;
      background: #ffffff;
      align-items: unset;
      // #ifdef H5
      justify-content: center;
      // #endif
      // #ifdef MP-WEIXIN
      justify-content: flex-start;
      // #endif

      .u-title {
          font-size: 36rpx;
          color: #121212;
          width: 100%;
          height: 96rpx;
          font-weight: 600;
      }

      img {
          display: block;
          height: 46rpx;
          width: 46rpx;
      }
  }

}
