<template>
    <view class="comment_container">
        <view class="comment_list">
            <view class="comment_item" v-for="(item, index) in flowList" :key="index" >
                <view v-if="item">
                    <view style="width: 100%; height: 60rpx" v-if="item.is_hot == 1">
                        <img
                            src="@/static/friend/icon_friend_hot.png"
                            mode="aspectFill"
                            style="width: 100%; height: 100%"
                        />
                    </view>
                    <view style="width: 100%; height: 60rpx" v-if="item.is_hot == 2">
                        <img
                            src="@/static/friend/icon_friend_anchor.png"
                            mode="aspectFill"
                            style="width: 100%; height: 100%"
                        />
                    </view>
                    <view style="padding: 20rpx 24rpx" @click="viewDetail(item)">
                        <view class="comment_item_top">
                            <view class="comment_item_top_left" @click.stop="jumpHome(item)">
                                <view style="position: relative">
                                    <img :src="item.author_avatar" mode="aspectFill" class="comment_item_top_left_img" />
                                    <image
                                        v-if="item.user_avatar_icon"
                                        style="width: 30rpx; height: 30rpx; position: absolute; bottom: 0; right: 0"
                                        :src="item.user_avatar_icon"
                                        mode="aspectFill"
                                    ></image>
                                </view>
                                <view class="comment_item_top_left_name u-line-1" style="margin-left: 30rpx">{{
                                    item.author
                                }}</view>
                                <img
                                    v-if="item.creator == '3587481'"
                                    class="custom_imag"
                                    style="margin-left: 10rpx; width: 128rpx; height: 36rpx; border-radius: 0rpx !important"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png"
                                    alt=""
                                />
                            </view>
                            <view
                                v-if="item.creator && item.creator !== -1 && relationText(item) && item.creator !== user_id"
                                :class="{ active: item.follow_status !== 0 }"
                                class="user-header-relation"
                                @click.stop="handleUserFollow(item, index)"
                            >
                                <img
                                    style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                    v-if="item.follow_status === 0"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                                />
                                {{ relationText(item) }}
                            </view>
                        </view>
                    </view>
                    <view class="comment_item_imgs" @click="viewDetail(item)">
                        <block v-if="item.type === 1">
                            <swiper class="swiper" style="width: 100%; height: 600rpx">
                                <swiper-item v-for="(element, index) in item.image_media_list" :key="index">
                                    <image :src="getImgUrl(element.url)" mode="aspectFill" style="width: 100%; height: 100%"></image>
                                </swiper-item>
                            </swiper>
                        </block>
                        <block v-else-if="item.type === 2">
                            <view style="width: 100%; height: 600rpx">
                                <image :src="item.cover_image" mode="aspectFill" style="width: 100%; height: 100%"></image>
                                <img
                                    src="@/static/friend/icon_video_play.png"
                                    style="
                                        width: 112rpx;
                                        height: 112rpx;
                                        position: absolute;
                                        top: 50%;
                                        left: 50%;
                                        transform: translate(-50%, -50%);
                                    "
                                />
                            </view>
                        </block>
                    </view>
                    <template v-if="item.goods_ids && item.goods_ids.length > 0">
                        <CardLink :goodsIds="item.goods_ids" @showGoodsMask="(ids) => showGoodsMask(ids, item.content_id)"/>
                    </template>
                    <view>
                        <view class="comment_item_middle">
                            <view v-if="item.title" class="comment_item_middle_text u-line-2">{{ item.title }}</view>
                        </view>
                    </view>
                    <view class="comment_item_bottom">
                        <view class="comment_item_bottom_forward">
                            <button
                                v-show="!item.jump_url"
                                open-type="share"
                                plain
                                class="share-btn"
                                :data-id="item.content_id"
                                @click.stop="handerShare(item)"
                            >
                                <img
                                    src="@/static/friend/icon_friend_share.png" style="width: 40rpx; height: 40rpx;"
                                />
                                <text style="color: #777777; font-size: 23rpx; margin-left: 8rpx">分享</text>
                            </button>
                        </view>
                        <view class="comment_item_bottom_oper">
                            <view class="oper_item" style="margin-right: 32rpx" @click.stop="handleOperFlow(item, 'praise')">
                                <img v-if="!item.is_praise" src="@/static/friend/icon_love_noclick.png" />
                                <img v-else src="@/static/friend/icon_love_click.png" />
                                <view class="oper_num" style="margin-left: 8rpx">{{ clicks(item.praise, 0) }}</view></view
                            >
                            <view class="oper_item" style="margin-right: 32rpx" @click.stop="handleOperFlow(item, 'fav')"
                                >
                                <img v-if="!item.is_favorite" src="@/static/friend/icon_friend_nostar.png" style="width: 40rpx; height: 40rpx;"/>
                                <img v-else src="@/static/friend/icon_friend_star.png" style="width: 40rpx; height: 40rpx;"/>
                                <view class="oper_num" style="margin-left: 8rpx">
                                    {{ clicks(item.favorite, 1) }}</view
                                ></view
                            >
                            <view class="oper_item" @click.stop="viewDetail(item)" v-show="!item.jump_url"
                                ><img
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68674bbc4e8c63220012250.png"
                                /><view class="oper_num" style="margin-left: 8rpx">{{ clicks(item.comment, 2) }}</view></view
                            >
                        </view>
                    </view>
                </view>
                <view v-else>
                    <view class="u-flex no_more">
                        <img src="@/static/friend/icon_message_finish.png" style="width: 36rpx; height: 36rpx;" />
                        <view v-if="currentIndex==0" style="font-size: 28rpx; color: #777777; margin-left: 16rpx;">消息内容到底，继续浏览广场</view>
                        <view v-if="currentIndex==1" style="font-size: 28rpx; color: #777777; margin-left: 16rpx;">动态内容到底，继续浏览广场</view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 用户取消关注拉黑 -->
        <CustomModal
            :show="operContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            :title="operContent.titie"
            :content="operContent.tip"
            :confirmText="operContent.confirmText"
            :cancelText="operContent.cancelText"
            showCancelButton
            @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })"
        >
        </CustomModal>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import {
    postCancelFavorite,
    postCancelPraise,
    postFavorite,
    postPraise,
    userFollowDelete,
    userRelation,
    userFollow,
} from '@/http/requestGo/community';
import { UserModule } from '@/store/modules/user';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { compressImageUrl } from '@/utils/imageCompression';
import ImageCompression from '@/components/LazyImage/ImageCompression.vue';
import { CheckAppJump } from '@/common/decorators';
import CardLink from './CardLink/CardLink.vue';

const FOLLOW_BTN_OPTION = [
    { value: 0, label: '关注' },
    { value: 1, label: '已关注' },
    { value: 2, label: '互相关注' },
];
@Component({ components: { ImageCompression, CardLink } })
export default class Contents extends Vue {
    @Prop({ type: Array })
    public flowList: Array<any>;

    @Prop({ type: Boolean })
    public finished: Boolean;

    @Prop({ type: Number })
    public relationStatus: number;

    @Prop({})
    public topicId: any;

    @Prop({ type: Boolean, default: false })
    public isJump: boolean;

    @Prop({ type: Number })

    public currentIndex: number;

    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    public index: number = 0;

    public creator: any = -1;

    public c_list: Array<any> = [];
    public did: string = '';
    public page: number = 1;
    public isShow: boolean = true;
    public currentTab: string = 'tab-0'; // tab定位锚点
    public showIsImage: any = false;
    public currentNum: number = 0;

    onShow() {
        console.log('currentIndex', this.currentIndex)
    }

    // #ifdef MP-WEIXIN
    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    get user_id(): any {
        let user_id: any = 0;
        // #ifdef MP-WEIXIN
        user_id = UserModule.userInfo.user_id;
        // #endif

        // #ifdef H5
        user_id = UserModule.user_id;
        // #endif
        return user_id;
    }

    clicks(val, index) {
        if (Number(val) === 0) {
            return '';
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    relationText(item) {
        return FOLLOW_BTN_OPTION.find((v) => v.value === item.follow_status)?.label || '关注';
    }

    async handleContenConfirm() {
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.creator });
        }
        if (res.success) {
            Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
            userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                this.c_list[this.index].follow_status = res.data.follow_status;
            });
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    @CheckAppJump()
    handleUserFollow(item, index) {
        this.index = index;
        this.creator = item.creator;
        // #ifdef MP-WEIXIN
        if (!this.wxAuth || !this.isPhone) {
            UserModule.authFlow({ target: 'handleUserFollow' });
            return;
        }
        // #endif

        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.creator);
            // const api = this.relationStatus === 0 ? userFollow : userFollowDelete;
            if (item.follow_status === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        item.follow_status = res.data.follow_status;
                        this.$emit('changeFollowStatus', item, res.data.follow_status);
                    });
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +this.creator });
                if (res.success) {
                    Utils.Toast('取消关注');
                    userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                        this.c_list[this.index].follow_status = res.data.follow_status;
                        this.$emit('changeFollowStatus', item, res.data.follow_status);
                    });
                }
                this.operContentOpen = false;
                setTimeout(() => {
                    this.operContent = { type: '' };
                }, 500);
            }
        }, 500);
    }

    @CheckAppJump()
    async handleOperFlow(item, type) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                await this.handleOper(item, type);
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
        Utils.reportEvent('give_like', { title: item.title });
        // #ifdef H5
        await this.handleOper(item, type);
        // #endif
    }

    async handleOper(item, type) {
        let flag, res;
        // const defaultPraise = item.is_praise;
        if (item[type === 'praise' ? 'is_praise' : 'is_favorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = flag;
            item[type === 'praise' ? 'praise' : 'favorite'] += +flag ? 1 : -1;
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = res.data.id;
        }
        // this.$emit('praise', id);
    }

    viewDetail(item, isJump = false) {
        // #ifdef H5
        if (item.jump_url && isJump) {
            Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
            return;
        }
        // #endif
        const { content_id = '', type, creator } = item;
        console.log('🚀 ~ Contents ~ viewDetail ~ item:', item);
        this.$emit('updateContentStatus', { content_id, creator });
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${this.topicId}`,
        );
    }

    jumpHome(item) {
        console.log('jumpHome', item);
        Utils.jumpPersonHome(item.creator);
    }

    getImgUrl(url) {
        return compressImageUrl(url, {
            quality: 100,
            maxWidth: 1800,
            maxHeight: 1800,
            format: 'webp',
        });
    }

    showGoodsMask(ids, contentId) {
        // if (options) {
        //     (this.$refs.GoodsCard as any).showGoodsMask(options.gid);
        //     (this.$refs.GoodsList as any).toggleVisible();
        //     return;
        // }
        // console.log('showGoodsMask ==== ', ids)
        // const goods_ids = ids;
        // if (goods_ids.length === 1) {
        //     console.log(goods_ids[0]);
        //     (this.$refs.GoodsCard as any).showGoodsMask(goods_ids[0]);
        // } else {
        //     // this.$refs.GoodsList.toggleVisible();
        //     (this.$refs.GoodsList as any).toggleVisible();
        // }
        this.$emit('showGoodsMask', ids, contentId);
    }

    onLoad() {}

    async handerShare(item) {
        console.log('%c item: ', 'font-size:16px;background-color: #93C0A4;color:#fff;', item)
        this.$emit('share', item);
    }
}
</script>

<style lang="scss">
.comment_container {
    .comment_list {
        background-color: #f6f6f6;
        .comment_item {
            background-color: #fff;
            margin-bottom: 8px;
            &_top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .user-header-relation {
                    padding: 12rpx 24rpx;
                    border-radius: 192rpx;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 36rpx;
                    margin-left: 16rpx;
                    border: 2rpx solid #dbc49a;
                    color: #c2a271;
                    display: flex;
                    align-items: center;

                    &.active {
                        border: 2rpx solid #e2e2e2;
                        color: #a6a6a6;
                    }
                }
                &_left {
                    display: flex;
                    align-items: center;
                    position: relative;
                    img,
                    image {
                        object-fit: cover;
                        width: 69rpx;
                        height: 69rpx;
                        border-radius: 50%;
                    }
                    .comment_item_top_left_tag {
                        position: absolute;
                        width: 31rpx;
                        height: 31rpx;
                        left: 50rpx;
                        bottom: 0;
                    }

                    ::v-deep .custom_imag {
                        object-fit: contain;
                        border-radius: 0rpx !important;
                    }

                    &_name {
                        margin-left: 15rpx;
                        font-family: MiSans, MiSans;
                        font-weight: 400;
                        font-size: 31rpx;
                        color: #121212;
                        line-height: 40rpx;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        max-width: 380rpx;
                    }
                }
            }
            &_middle {
                position: relative;
                margin-top: 38rpx;
                &_text {
                    word-wrap: break-word;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 27rpx;
                    color: #404040;
                    line-height: 42rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    display: -webkit-box;
                    overflow: hidden;
                    -webkit-box-orient: vertical;
                    text-overflow: ellipsis;
                }
                &_more {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 27rpx;
                    color: #ab8c5e;
                    line-height: 42rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }
            &_imgs {
                position: relative;
                margin-top: 16rpx;
                display: flex;
                flex-wrap: wrap;

                &_icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    right: 12rpx;
                    top: 12rpx;
                    padding: 6rpx 12rpx;
                    height: 35rpx;
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 208rpx 208rpx 208rpx 208rpx;

                    img {
                        object-fit: cover;
                        width: 23rpx;
                        height: 23rpx;
                        margin-right: 8rpx;
                    }

                    text {
                        font-family: MiSans, MiSans;
                        font-weight: 400;
                        font-size: 19rpx;
                        color: #ffffff;
                        line-height: 25rpx;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                    }
                }
                &_item {
                    &:not(:nth-child(3n)) {
                        margin-right: 8rpx;
                    }
                    object-fit: cover;
                    margin-bottom: 8rpx;
                    width: calc((100vw - 80rpx) / 3);
                    height: calc((100vw - 80rpx) / 3);
                    border-radius: 8rpx;

                    &:last-child {
                        margin-right: 0rpx;
                    }
                }

                .comment_item_video_icon {
                    height: 34rpx;
                    width: 34rpx;
                    position: absolute;
                    top: 12rpx;
                    left: 180rpx;
                    display: flex;
                    img,
                    image {
                        height: 34rpx;
                        width: 34rpx;
                    }
                }
            }
            &_bottom {
                margin-top: 20rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                    width: 38rpx;
                    height: 38rpx;
                }

                &_oper {
                    display: flex;
                    .oper_item {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        img,
                        image {
                            // margin-right: 8rpx;
                            object-fit: contain;
                        }
                        .oper_num {
                            // min-width: 25px;
                            font-family: MiSans, MiSans;
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #777777;
                            // line-height: 40rpx;
                            text-align: center;
                            font-style: normal;
                            text-transform: none;
                        }
                        &:last-child {
                            margin-right: 12px;
                        }
                    }
                }
            }
            .comment_item_middle {
                padding: 5px 12px 0px;
            }
            .comment_item_bottom {
                padding: 0px 0px 10px 12px;

                .comment_item_bottom_oper {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                }
            }
        }
    }
    .no_more {
        background-color: #ffffff;
        text-align: center;
        font-size: 24rpx;
        font-weight: 400;
        color: rgba(29, 30, 32, 0.4);
        line-height: 80rpx;
        height: 80rpx;
        padding-left: 24rpx;
        margin-top: 16rpx;
    }
}
.share-btn {
    padding: 0;
    border: unset !important;
    @include flex($justify: center, $align: center);
}

// 骨架屏样式
.skeleton-images {
    display: flex;
    flex-wrap: wrap;

    .skeleton-image-item {
        &:not(:nth-child(3n)) {
            margin-right: 8rpx;
        }
        margin-bottom: 8rpx;
        width: calc((100vw - 80rpx) / 3);
        height: calc((100vw - 80rpx) / 3);
        border-radius: 8rpx;
        overflow: hidden;

        &:last-child {
            margin-right: 0rpx;
        }

        .skeleton-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
</style>
