export interface IBookingSnConfigInfo {
    p_sn: string;
    sn_alias: string;
}
export interface IBookingModel {
    name: string;
    phone: string;
    verify_code: string;
    area?: string;
    pid: string;
    province: string
    cid: string;
    city: string
    aid: string;
    county: string
    detail: string;
    type: string;
    context_type: string;
    context_type_name: string;
    expect_time: string;
    expect_time_text?: string;
    is_buy: string;
    is_buy_name?: string;
    sn: string;
    price: string | number;
    is_explore: string;
    is_explore_name?: string;
    images: string;
    sn_config: any;
    sn_config_str?: string;
}

export interface IPriceParams {
    id: string;
    oprice: string;
    price: string;
    note: string;
    ctime: string;
}
