<template>
    <div class="my-device-card">
        <!-- <div class="device-header">
            <div class="device-title">我的设备</div>
            <div class="go-device" @click="gotoDevice">
                查看全部
                <u-icon name="arrow-right" size="12" color="#A6A6A6"></u-icon>
            </div>
        </div> -->
        <u-scroll-list
            :indicator="false"
            :indicatorActiveColor="'#DBC49A'"
            :indicatorInactiveColor="'#EEEEEE'"
            :indicatorWidth="46.15"
            :indicatorHeight="7.69"
            :indicatorBarRadius="98.08"
            :showScrollbar="false"
        >
            <NoMachine  v-if="deviceList.length == 0"/>
            <view class="swiper-group-item-content" style="display: flex;flex-direction: row; gap: 16rpx;" v-else>
            <view v-for="(item, index) in deviceList" :key="index">
                <view class="swiper-item-content">
                    <Vacuum
                        v-if="item.deviceInfo.categoryPath === '/lifeapps/vacuum'"
                        :currentDevice="item"
                        @edit-device="editDeviceInfo"
                    />
                    <WashMachine v-else-if="item.deviceInfo.categoryPath === '/lifeapps/hold'"  :class="{'only-one': deviceList.length === 1}" :currentDevice="item"/>
                    <OtherMachine v-else :currentDevice="item"  :class="{'only-one': deviceList.length === 1}"/>
                </view>
            </view>
            <AddDevice v-if="deviceList.length !== 0"/>
            <view class="view_all" @click="gotoDevice">
                <text>查看全部</text>
                <image class="view_arrow" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b3974916015950011243.png" />
            </view>
           </view>
        </u-scroll-list>
        <!-- <div class="device-list">
            <swiper class="u-swiper" style="height: 330rpx" @change="changeDevice">
                <swiper-item class="u-swiper__wrapper__item" v-for="(group, index) in deviceListGroup" :key="index">
                    <view class="swiper-group-item">
                        <view class="swiper-group-item-content" v-for="(item, index) in group" :key="index">
                            <view class="swiper-item-content">
                                <NoMachine v-if="item.isNoMachine" />
                                <Vacuum
                                    v-else-if="item.deviceInfo.categoryPath === '/lifeapps/vacuum'"
                                    :currentDevice="item"
                                    @edit-device="editDeviceInfo"
                                />
                                <WashMachine
                                    v-else-if="item.deviceInfo.categoryPath === '/lifeapps/hold'"
                                    :currentDevice="item"
                                />
                                <OtherMachine v-else :currentDevice="item" />
                            </view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
            <div class="swiper-pagination">
                <div
                    v-for="(item, index) in deviceListGroup"
                    :key="index"
                    class="pagination-item"
                    :class="{ activepoint: index === currentDeviceIndex, pointmargin: index !== 0 }"
                />
            </div>
        </div> -->
        <EditDeviceDialog
            v-if="editDialogVisible"
            :currentDevice="currentDevice"
            @refresh-list="refreshList"
            @close="editDialogVisible = false"
        />
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import WashMachine from './components/WashingMachine.vue';
import Vacuum from './components/Vacuum.vue';
import OtherMachine from './components/OtherMachine.vue';
import NoMachine from './components/NoMachine.vue';
import EditDeviceDialog from './components/EditDeviceDialog.vue';
import AddDevice from './components/AddDevice.vue';

export default {
    name: 'MyDevice',
    components: {
        WashMachine,
        Vacuum,
        OtherMachine,
        NoMachine,
        EditDeviceDialog,
        AddDevice,
    },
    props: {
        deviceList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            currentDeviceIndex: 0, // 当前设备索引
            editDialogVisible: false, // 编辑对话框是否可见
            currentDeviceStatus: {},
        };
    },
    computed: {
        currentDevice() {
            return this.deviceList[this.currentDeviceIndex] || {};
        },
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        supportFastCommand() {
            const { device = {}} = this.currentDevice;
            const { deviceInfo = {}} = device;
            const { feature = '' } = deviceInfo;
            // 支持快捷指令的设备型号
            return feature === 'fastCommand';
        },
        supportVideo() {
            const { device = {}} = this.currentDevice;
            const { deviceInfo = {}} = device;
            const { videoDynamicVendor = false } = deviceInfo;
            return videoDynamicVendor;
        },
        // deviceListGroup() {
        //     // 把deviceList 按 每2个一组 分组
        //     const group = [];
        //     for (let i = 0; i < this.deviceList.length; i += 2) {
        //         group.push(this.deviceList.slice(i, i + 2));
        //     }
        //     // 如果长度为奇数，则最后一组补一个空对象
        //     if (this.deviceList.length % 2 !== 0) {
        //         group[group.length - 1].push({});
        //     }
        //     return group;
        // },
    },
    methods: {
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        },
        // 设备切换
        changeDevice(e: any) {
            const { detail = {}} = e;
            const { current = 0 } = detail;
            this.currentDeviceIndex = current;
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.editDialogVisible = true;
        },
        // 刷新设备列表
        refreshList() {
            this.$emit('refresh-list');
        },
    },
};
</script>

<style lang="scss" scoped>
.my-device-card {
     margin-bottom: 16rpx;
}
.device-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    align-items: center;
}
.device-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: MiSans;
     font-size: 24rpx;
    line-height: 48rpx;
    color: #333333;
    font-weight: 800;
}
.only-one {
    width: calc((100vw - 30px) / 2);
}
.go-device {
    font-size: 22rpx;
    color: #A6A6A6;
    display: flex;
    font-weight: normal;
    align-items: center;
    gap: 4rpx;
}
.goto-device-icon {
    width: 32rpx;
    height: 32rpx;
}
.device-list {
    margin-top: 24rpx;
}

.swiper-pagination {
    display: flex;
    justify-content: center;
    margin-top: 32rpx;
}
.swiper-group-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
}
.pagination-item {
    width: 12rpx;
    height: 6rpx;
    border-radius: 6rpx;
    background-color: #e2e2e2;
}
.activepoint {
    background-color: #404040;
}
.pointmargin {
    margin-left: 8rpx;
}

.view_all {
    width: 320rpx;;
    border:2rpx dashed #E2E2E2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 156rpx;
    border-radius: 16rpx;
    background-repeat: no-repeat;
    background-position:left top, right bottom;
    font-size: 28rpx;
    border-radius: 20rpx;
    color: #404040;

    .view_arrow {
        width: 36rpx;
        height: 36rpx;
    }
}
</style>
