<template>
    <div class="device-item" @click="gotoDevice">
        <div @click.stop="editDeviceInfo"><img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6866360d757f94810015349.png" class="device-item-more" alt="more" /></div>
        <div class="device-info-card">
            <div class="device-info-area">
                <div class="device-info-name">{{ name }}</div>
            </div>
            <div class="device-info-image-area">
                <img :src="image" alt="设备图片" class="device-info-image" />
            </div>
        </div>
        <div
            class="other-device"
        >
            请进入设备进行操作
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage = 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png'
export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        name() {
            return this.currentDevice.customName || this.currentDevice.deviceInfo?.displayName || this.currentDevice.displayName || '';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage
        },
    },
    methods: {
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    }
}

</script>

<style scoped>
.device-item {
    border-radius: 32rpx;
    overflow: hidden;
    position: relative;
    background: linear-gradient(116deg, #FDFAF6 0%, #F1E5CA 68%, #F6F0E2 104%);
}
.device-item-more {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    width: 52rpx;
    height: 52rpx;
}
.device-info-card {
    display: flex;
    justify-content: space-between;
}
.device-info-area {
    padding: 32rpx;
}
.device-info-name {
    color: #121212;
    font-size: 32rpx;
    font-weight: 600;
}
.device-info-status {
    color: #777777;
    font-size: 24rpx;
    line-height: 32rpx;
    display: flex;
    align-items: center;
    margin-top: 12rpx;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.col-line {
    width: 1rpx;
    height: 16rpx;
    background-color: #777777;
    margin: 0 16rpx;
}
.device-info-image-area {
    width: 160rpx;
    height: 180rpx;
    margin-right: 64rpx;
}
.device-info-image {
    width: auto;
    height: 200rpx;
    object-fit: contain;
}
.other-device {
    color: #3D3D3D;
    font-size: 24rpx;
    line-height: 32rpx;
    margin: 44rpx 0 32rpx 32rpx;
    margin-left: 32rpx;
}
</style>
