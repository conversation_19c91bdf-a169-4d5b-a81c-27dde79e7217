<template>
    <view class="featured-category">
        <view class="category-title">{{ title }}</view>
        <view class="category-grid" :style="gridStyle">
            <view
                class="category-item"
                v-for="(item, index) in categoryList"
                :key="item.id"
                v-show="item.visible !== false"
                @tap="handleCategoryClick(item, index)"
            >
                <view class="item-image-wrapper">
                    <image class="item-image" :src="item.image" mode="aspectFit" :lazy-load="true" />
                </view>
                <view class="item-name">{{ item.name }}</view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { CategoryItem, CategoryClickEvent, DEFAULT_CATEGORIES } from './types';

@Component({
    name: 'FeaturedCategory',
})
export default class FeaturedCategory extends Vue {
    // Props定义
    @Prop({
        type: Array,
        default: () => DEFAULT_CATEGORIES,
    })
    readonly categoryList!: CategoryItem[];

    @Prop({ type: String, default: '精选分类' })
    readonly title!: string;

    @Prop({ type: Number, default: 3 })
    readonly columns!: number;

    // 计算属性：动态网格样式
    get gridStyle(): Record<string, string> {
        return {
            'grid-template-columns': `repeat(${this.columns}, 1fr)`,
        };
    }

    // 处理分类点击事件
    handleCategoryClick(item: CategoryItem, index: number): void {
        // 检查分类是否可见
        if (item.visible === false) {
            return;
        }

        // 构建点击事件数据
        const clickEvent: CategoryClickEvent = { item, index };

        // 发送点击事件给父组件
        this.$emit('category-click', clickEvent);

        // 根据类型进行不同的跳转处理
        this.navigateToCategory(item);

        // 埋点统计
        this.trackCategoryClick(item, index);
    }

    // 导航到分类页面
    private navigateToCategory(item: CategoryItem): void {
        if (item.url) {
            Utils.navigateTo(item.url);
        } else if (item.tid) {
            // 跳转到商品分类页面
            Utils.navigateTo(`/pagesA/brandType/brandType?tid=${item.tid}`);
        }
    }

    // 埋点统计
    private trackCategoryClick(item: CategoryItem, index: number): void {
        Utils.reportEvent('category_click', {
            category_id: item.id,
            category_name: item.name,
            category_type: item.type || 'product',
            position: index,
        });
    }
}
</script>

<style lang="scss" scoped>
.featured-category {
    padding: 32rpx;
    background: $uni-bg-color;
    border-radius: 24rpx;
    margin-bottom: 16rpx;

    .category-title {
        font-size: 36rpx;
        font-weight: 500;
        color: $uni-text-color;
        margin-bottom: 32rpx;
        line-height: 44rpx;
    }

    .category-grid {
        /* #ifndef MP-WEIXIN */
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32rpx 24rpx;
        /* #endif */

        /* #ifdef MP-WEIXIN */
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        /* #endif */

        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 24rpx 16rpx;
            border-radius: 16rpx;
            transition: all 0.3s ease;
            position: relative;

            /* #ifdef MP-WEIXIN */
            width: 30%;
            margin-bottom: 32rpx;
            box-sizing: border-box;
            /* #endif */

            &:active {
                background: $uni-bg-color-hover;
                transform: scale(0.98);
            }

            .item-image-wrapper {
                width: 120rpx;
                height: 120rpx;
                border-radius: 16rpx;
                background: $uni-bg-color-grey;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 16rpx;
                overflow: hidden;
                border: 2rpx solid $uni-border-color;

                .item-image {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 12rpx;
                }
            }

            .item-name {
                font-size: 28rpx;
                color: $uni-text-color;
                text-align: center;
                line-height: 36rpx;
                font-weight: 400;
            }
        }
    }
}

// 响应式适配 - 小屏设备优化
@media screen and (max-width: 750rpx) {
    .featured-category {
        padding: 24rpx;

        .category-grid {
            /* #ifndef MP-WEIXIN */
            gap: 24rpx 16rpx;
            /* #endif */

            .category-item {
                padding: 20rpx 12rpx;

                /* #ifdef MP-WEIXIN */
                width: 30%;
                margin-bottom: 24rpx;
                /* #endif */

                .item-image-wrapper {
                    width: 100rpx;
                    height: 100rpx;

                    .item-image {
                        width: 80rpx;
                        height: 80rpx;
                    }
                }

                .item-name {
                    font-size: 26rpx;
                }
            }
        }
    }
}
</style>
