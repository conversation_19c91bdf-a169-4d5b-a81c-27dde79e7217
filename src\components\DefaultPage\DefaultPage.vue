<template>
    <view v-if="show" class="empty-box u-flex u-flex-col u-row-center u-col-center"
        :class="{ 'empty-page': mode === 'page' }">
        <view class="none u-flex u-col-center">
            <image class="empty-icon" :style="{
                width: imgWidth + 'rpx',
                height: imgHeight + 'rpx',
                marginBottom: imgM<PERSON><PERSON> + 'rpx',
            }" :src="icon" />
        </view>
        <text class="title" :style="tipStyle">{{ tip }}</text>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component
export default class Notice extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly show;

    @Prop({ type: String, default: 'box' })
    readonly mode; // box: 自适应高度; page: 整页高度

    @Prop({ type: String, default: 'https://wpm-cdn.dreame.tech/images/202203/621df39ba806a6881392764.png' })
    readonly icon;

    @Prop({ type: String, default: '您还没有已绑定的产品' })
    readonly tip;

    @Prop({ type: String, default: '200' })
    readonly imgWidth;

    @Prop({ type: String, default: '200' })
    readonly imgHeight;

    @Prop({ type: String, default: '60' })
    readonly imgMbottom;

    @Prop({ type: String, default: '' })
    readonly tipStyle;
}
</script>

<style lang="scss">
.empty-box {
    width: 100%;
    height: 100%;
    font-size: 28rpx;
    color: $text-color-primary;
    line-height: 40rpx;

    &.empty-page {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
    }

    .empty-icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 64rpx;
    }

    .title {
        font-size: 28rpx;
        font-weight: 400;
        color: $text-color-secondary;
        line-height: 42rpx;
    }
}
</style>
