import http from './index';

// 会员权益
export const getLevelBenefit = (params) => http.post('main/member-center/level-benefit', params);

export const getmodify = (params) => http.post('main/data/modify', params);

// export const getOperate = (params) => http.post('main/member-center/operate-config ', params);

// 会员任务
export const getTaskList = (params) => http.post('main/member-center/task-list', params);

// 会员任务
export const getEmployeeTaskList = (params) => http.post('main/user-employee/task-list', params);

// 领取新人礼包

export const getReceive = (params) => http.post('main/activity/receive', params);

// 获取/修改商品浏览任务状态
export const viewGoodsTask = (params) => http.post('main/event-center/view-goods', params);

// 会员中心接口
export const getGrowthCenter = (params) => http.post('main/member-center/growth-center', params);

// 查询金币配置
export const getCoinConfig = (params) => http.post('main/draw/gold-config', params);

// 获取会员积分和觅享分
export const getScoreGet = () => http.post('main/member-center/score-get');

// 领取全部积分
export const getScoreCollectAll = (params) => http.post('main/member-center/score-collect-all', params);

// 领取积分
export const getScoreCollect = (params) => http.post('main/member-center/score-collect', params);

// 觅享分明细
export const getScoreGetGrows = (params) => http.post('main/member-center/score-get-grows', params);

// 积分明细
export const getScoreGetPoints = (params) => http.post('main/member-center/score-get-points', params);

// 获取未领取积分明细
export const getScoreUncollect = (params) => http.post('main/member-center/score-uncollect', params);

// 获取未领取金币明细
export const getCoinUncollect = (params) => http.post('main/member-center/score-uncollect', params);

// 获取运营说明配置
export const getOperateConfig = (params) => http.post('main/member-center/operate-config', params);

// 会员基本信息接口
export const getBasicInfo = (params) => http.post('main/member-center/basic-info', params);

// 获取任务信息
export const getTaskInfo = (params) => http.post('main/member-center/task-info', params);

// 会员中心-重置等级变更提示
export const resetLevelChangePrompt = (params) => http.post('main/member-center/reset-level-change-prompt', params);

// 会员中心-重置生日祝福弹窗提示
export const hasShowBirthCard = (params) => http.post('main/member-center/has-show-birth-card', params);

// 直播观看6
export const watchLive = (params) => http.post('main/event-center/watch-live', params);

// 微笑大使详情
export const smileInfo = () => http.post('main/user-employee/info');

// 微笑分列表
export const smileScoreDetail = (params) => http.post('main/user-employee/score-list', params);

// 微笑分邀请新用户列表
export const smileRegList = (params) => http.post('main/user-employee/register-list', params);

// 微笑分邀请下单列表
export const smileOrderList = (params) => http.post('main/user-employee/order-list', params);

// 分享太阳码
export const bindShare = () => http.post('main/user-bind/share');

/*
分享
 */
export const shareSmile = () => http.post('main/user-bind/app-share');

/*
分享确认
 */
export const confirmSmile = () => http.post('main/user-employee/received-tips');

// 申请成为追觅大使
export const applyEmployee = () => http.post('main/user-employee/apply');

// 追觅大使推荐用户绑定
export const bindEmployee = () => http.post('main/user-bind/bind');

// 完成金币任务
export const doGoldTask = (params) => http.post('main/member-center/browse', params);

// 分享落地页任务列表
export const getNoUserIdTaskList = (params) => http.post('main/member-center/no-auth-task-list', params);

// 获取搜商品金币
export const getCoin = (params) => http.post('main/member-center/detail-info', params);
