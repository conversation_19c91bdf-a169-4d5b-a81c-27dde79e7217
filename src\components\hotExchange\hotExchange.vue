<template>
    <view class="hotExchange">
        <view class="title">
            <view class="title-left">{{ title }}</view>
            <view class="title-right" @click="navToPointsPage">
                <text>进入积分商城</text>
                <image src="https://wpm-cdn.dreame.tech/images/202309/64f694de656ac4151723914.png"></image>
            </view>
        </view>
        <view class="content u-flex">
            <view v-for="item in waresList" :key="item.id">
                <view style="margin-right: 16rpx">
                    <pointProducts pointProductStyle="width: 286rpx; height: 490rpx; borderRadius: 14rpx"
                        :imgHeight="288" :pricePointFontSize="26" :addFontSize="22"
                        levelIllustrateStyle="height: 38rpx; lineHeight: 38rpx"
                        :signStyle="{ width: '58rpx', height: '36rpx' }" :pointProductsInfo="item" />
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import pointProducts from '@/components/pointProducts/pointProducts.vue';
import Utils from '@/common/Utils';

@Component({
    components: {
        pointProducts
    },
})

export default class hotExchange extends Vue {
    @Prop({
        type: Array,
        default: () => ({
            cover_image: '', // 图片链接
            levels_tag: '', // 等级说明
            label_tag: '', // 标签
            online_time_tag: '', // 上架时间
            name: '', // 标题文本
            introduce: '', // 卖点文本
            point: 0, // 积分
            price: 0, // 优惠价格
            mprice: 0 // 原始价格
        }),
    })
    readonly waresList;

    // 推荐位名字
    @Prop({ type: String, default: '' })
    readonly title;

    navToPointsPage() {
        Utils.navigateTo('/pagesA/point/new_point');
    }
}
</script>

<style lang="scss" scoped>
$text-color-comeIn: #7D7D7D;

.hotExchange {
    margin-top: 46rpx;
    -webkit-overflow-scrolling: touch;

    .title {
        @include flex(row, space-between, center, none);

        .title-left {
            margin-left: 10rpx;
            font-size: 32rpx;
            font-weight: 500;
            line-height: 42rpx;
            color: $text-color-primary;
        }

        .title-right {
            font-size: 28rpx;
            line-height: 38rpx;
            color: $text-color-comeIn;

            image {
                width: 38rpx;
                height: 38rpx;
            }
        }
    }

    .content {
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
    }
}
</style>
