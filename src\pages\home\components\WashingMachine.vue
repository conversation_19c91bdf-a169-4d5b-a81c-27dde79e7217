<template>
    <div class="device-item" @click="gotoDevice">
        <div @click.stop="editDeviceInfo"><img src="./icon-3-points.png" class="device-item-more" alt="more" /></div>
        <div class="device-info-card">
            <div class="device-info-area">
                <div class="device-info-name">{{ currentDevice.customName || currentDevice.displayName }}</div>
                <div
                    v-if="battery || status"
                    class="device-info-status"
                >
                    <div>
                        <img src="./icon-battery.png" alt="电量" class="device-info-battery" />
                        {{ battery }}%
                    </div>
                    <div class="col-line"/>
                    <div>{{ status }}</div>
                </div>
            </div>
            <div class="device-info-image-area">
                <img :src="image" alt="设备图片" class="device-info-image" />
            </div>
        </div>
        <div
            class="other-device"
        >
            请进入设备进行操作
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const defaultDeviceImage = require('./img_robot_placholder.png')

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage
        },
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true
        }
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber()
            const bind_id = bindDomain.split('.')[0]
            const data = { did, id, from: 'mapp', method: 'get_properties', params: [{ did, siid: 2, piid: 1 }, { did, siid: 3, piid: 1 }, { did, siid: 4, piid: 38 }, { did, siid: 4, piid: 83 }, { did, siid: 4, piid: 48 }] }
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data)
            }
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter(r => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter(r => r.siid === 3 && r.piid == 1);
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel('DEVICE', 'getStatusStr', {
                                model: model,
                                latestStatus: onlineStatus[0].value
                            }, (res) => {
                                newStatus.status = res.data;
                                this.currentDeviceStatus = { ...newStatus };
                            });
                        }
                    }
                }
            };
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: body
            }, commandResultCB);
        },
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
    }
}

</script>

<style scoped>
.device-item {
    border-radius: 32rpx;
    overflow: hidden;
    position: relative;
    background: linear-gradient(116deg, #FDFAF6 0%, #F1E5CA 68%, #F6F0E2 104%);
}
.device-item-more {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    width: 52rpx;
    height: 52rpx;
}
.device-info-card {
    display: flex;
    justify-content: space-between;
}
.device-info-area {
    padding: 32rpx;
}
.device-info-name {
    color: #121212;
    font-size: 32rpx;
    font-weight: 600;
}
.device-info-status {
    color: #777777;
    font-size: 24rpx;
    line-height: 32rpx;
    display: flex;
    align-items: center;
    margin-top: 12rpx;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.col-line {
    width: 1rpx;
    height: 16rpx;
    background-color: #777777;
    margin: 0 16rpx;
}
.device-info-image-area {
    width: 160rpx;
    height: 180rpx;
    margin-right: 64rpx;
}
.device-info-image {
    width: auto;
    height: 200rpx;
    object-fit: contain;
}
.other-device {
    color: #3D3D3D;
    font-size: 24rpx;
    line-height: 32rpx;
    margin: 44rpx 0 32rpx 32rpx;
    margin-left: 32rpx;
}
</style>
