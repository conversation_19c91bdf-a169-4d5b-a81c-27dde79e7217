<template>
    <view class="barnd-header-wrap">
        <view class="brand-item search" @click="navigateTo">
            <image style="width: 46rpx; height: 46rpx;display: block;" src="https://wpm-cdn.dreame.tech/images/202306/535513-1686106493238.png"></image>
        </view>
        <view class="brand-item" @click="handleContact">
            <image style="width: 40rpx; height:40rpx;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6895fc51b595e7440012050.png"></image>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
// import { UserModule } from '@/store/modules/user';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import Constants from '@/common/Constants';

@Component
export default class CoinPoints extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    handlePointsClick() {
        Utils.navigateTo('/pagesA/point/shop_point');
        // Utils.navigateTo('/pagesC/purchase/purchase');
        // Utils.navigateTo('/pagesC/goldCoins/goldCoins');
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }

    navigateTo() {
        Utils.navigateTo('/pagesA/search/search?search_type=' + 1)
    }

     handleContact() {
       Utils.decryptContact({}, BuType.BuType_NONE);
    }
}
</script>
<style lang="scss" scoped>
.barnd-header-wrap{
    display: flex;
    align-items: center;
    gap: 30rpx;
    height: 114rpx;
    .brand-item {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #FFFFFF;
        width: 64rpx;
        height: 64rpx;
        border-radius: 32rpx;
        text-align: center;

    }
}
</style>
