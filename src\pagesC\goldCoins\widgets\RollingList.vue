<template>
    <view class="rolling-list-container" :style="{ height: itemHeight + 'rpx', overflow: 'hidden' }">
        <view
            class="rolling-content"
            :style="{
                transform: `translateY(-${currentIndex * itemHeight}rpx)`,
                transition: transitionEnabled ? `transform ${duration}ms ease-out` : 'none',
            }"
        >
            <view
                v-for="(item, index) in items"
                :key="index"
                :style="{ height: itemHeight + 'rpx', display: 'flex', alignItems: 'center' }"
            >
                <slot :item="item" :index="index"></slot>
            </view>
            <!-- 复制第一条数据用于无缝衔接 -->
            <view :style="{ height: itemHeight + 'rpx', display: 'flex', alignItems: 'center' }">
                <slot :item="items[0]" :index="items.length"></slot>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'RollingList',
    props: {
        // 滚动数据列表
        items: {
            type: Array,
            required: true,
            default: () => [],
        },
        // 每条记录的高度(px)
        itemHeight: {
            type: Number,
            default: 50,
        },
        // 滚动动画时长(ms)
        duration: {
            type: Number,
            default: 500,
        },
        // 滚动间隔时间(ms)
        interval: {
            type: Number,
            default: 2000,
        },
    },
    data() {
        return {
            currentIndex: 0,
            timer: null,
            transitionEnabled: true, // 新增：控制过渡动画是否生效
        };
    },
    watch: {
        items() {
            this.resetRolling();
        },
    },
    mounted() {
        this.startRolling();
    },
    beforeDestroy() {
        this.stopRolling();
    },
    methods: {
        startRolling() {
            if (this.items.length <= 1) return;

            this.timer = setInterval(() => {
                // 滚动前确保过渡动画开启
                this.transitionEnabled = true;
                this.currentIndex++;

                // 当滚动到最后一条复制的数据时，无缝重置
                if (this.currentIndex >= this.items.length) {
                    // 1. 等待当前滚动动画结束（duration 时长）
                    setTimeout(() => {
                        // 2. 关闭过渡动画，让重置瞬间完成
                        this.transitionEnabled = false;
                        // 3. 重置索引到 0（此时无动画）
                        this.currentIndex = 0;

                        // 4. 下一帧再开启过渡动画（避免影响后续滚动）
                        setTimeout(() => {
                            this.transitionEnabled = true;
                        }, 100);
                        // requestAnimationFrame(() => {
                        //     this.transitionEnabled = true;
                        // });
                    }, this.duration);
                }
            }, this.interval);
        },

        stopRolling() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },

        resetRolling() {
            this.stopRolling();
            // 重置时关闭动画，避免跳动
            this.transitionEnabled = false;
            this.currentIndex = 0;
            // 下一帧恢复动画
            requestAnimationFrame(() => {
                this.transitionEnabled = true;
                this.startRolling();
            });
        },
    },
};
</script>

<style scoped>
.rolling-list-container {
    position: relative;
    width: 100%;
}

.rolling-content {
    width: 100%;
}
</style>
