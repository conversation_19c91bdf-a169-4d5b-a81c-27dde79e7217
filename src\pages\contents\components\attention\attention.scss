::v-deep .uni-scroll-view-refresher {
  background-color: #ffffff !important;
}

.community-search {
  img {
    width: 44rpx;
    height: 44rpx;
  }
}

.swiper-box {
  height: calc(100vh - 260rpx);

  // #ifdef H5
  ::v-deep .uni-swiper-wrapper {
    overflow: hidden !important;
  }

  // #endif
  .swiper-item {
    width: 100%;
    height: 100%;

    .scroll-view {
      height: 100%;
      background-color: #ffffff;

      // #ifdef H5
      ::v-deep .uni-scroll-view {
        .uni-scroll-view-content {
          height: fit-content;
        }
      }

      // #endif
      .u-search-top {
        background-color: #F6F6F6;
        height: 68rpx;
        border-radius: 16rpx;
        margin: 24rpx;
        padding: 24rpx;

        .u-search {
          margin-left: 10rpx;
          font-size: 28rpx;
        }
      }

      .u-avatur-top {
        width: 100%;

        .u-avatar {
          width: 302rpx;
          height: 302rpx;
          border-radius: 24rpx;
          background-color: #ffffff;
          overflow: hidden;
          box-sizing: content-box;
          border: 4rpx solid #E1CEAB;
        }

        .u-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #121212;
          margin-top: 16rpx;
        }

        .u-id {
          font-size: 26rpx;
          color: #777777;
        }

        .u-weixin {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx;

          .u-weixin-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #121212;
          }

          .u-weixin-name {
            font-size: 26rpx;
            font-weight: 400;
            color: #777777;
          }
        }
      }

      .u-recommend-box {
        width: 100%;
        display: flex;
        flex-direction: column;

        .u-recommend-title {
          font-size: 36rpx;
          color: #111111;
          font-weight: 600;
          padding: 0 32rpx;
        }

        .u-recommend-content {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx 20rpx;

          .u-recommend-left {
            display: flex;
            flex-direction: row;
            align-items: center;
          }

          .u-recomment-right {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            border: 2rpx solid #DBC49A;
            height: 64rpx;
            width: 192rpx;
            border-radius: 32rpx;
            font-size: 28rpx;
            color: #C2A271;
          }

          .u-recomment-right-yes {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            border: 2rpx solid #A6A6A6;
            height: 64rpx;
            width: 192rpx;
            border-radius: 32rpx;
            font-size: 28rpx;
            color: #A6A6A6;
            font-weight: 500;
          }
        }
      }

      .u-good-box {
        display: flex;
        flex-direction: column;

        .u-good-title {
          font-size: 36rpx;
          color: #111111;
          font-weight: 600;
          padding: 0 32rpx;
        }

        .u-good-content {
          @include flex(row, space-between, flex-start, wrap);
          padding: 0 8rpx 0;
        }

        .class-item {
          width: calc(calc(100vw - 48rpx)/2);
          background: #ffffff;
          border-radius: 24rpx;
          margin-bottom: 16rpx;
          overflow: hidden;

          .class-item-top {
            width: 100%;
            padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
            margin: 0 auto 36rpx;
            position: relative;
            // background-color: #f5f5f5; // 添加默认背景色
            overflow: hidden; // 防止内容溢出

            img {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              z-index: 2; // 确保在占位层之上
            }

            ::v-deep image {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border-radius: 8rpx 8rpx 0 0;
              object-fit: cover;
              z-index: 2; // 确保在占位层之上
            }
          }

          .class-item-bottom {
            padding: 0 32rpx 12rpx;

            .u-good-title-img {
              overflow: hidden;

              .text {
                display: inline;
                /* 确保文本环绕 */
                text-align: justify;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                text-overflow: ellipsis;
                // text-indent: 67rpx;
                overflow: hidden;
                position: relative;

                .indent-icon {
                  width: 52rpx;
                  height: 26rpx;
                  margin-top: 10rpx;
                  position: absolute;
                  top: 0;
                  left: 0;
                }
              }
            }
          }
        }

        .u-good-bottom {
          background-color: #F5EDD6;
          color: #8C6533;
          height: 88rpx;
          border-radius: 44rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          font-size: 32rpx;
          font-weight: 500;
          margin: 60rpx 24rpx 208rpx 24rpx;
        }
      }

      .u-good-box {
        margin: 40rpx 24rpx 0 24rpx;
      }

      .u-user-content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx;
      }

      .u-recommend-left {
        display: flex;
        align-items: center;
      }

      .attention {
        width: 192rpx;
        height: 64rpx;
      }
    }
  }

  .class-item-radio {
    top: 16rpx;
    left: unset;
    width: 40rpx;
    height: 40rpx;
  }
}

.check-box {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;

  .icon {
    width: 100%;
    height: 100%;
  }

  &.car-sel {
    border-radius: 50%;
    width: 40rpx;
    height: 40rpx;
  }

  &.car-unsel {
    border-radius: 50%;
    width: 40rpx;
    height: 40rpx;
    background: #ffffff;
    border: 2rpx solid rgba(34, 34, 34, 0.1);
  }
}

.bottom {
  width: 100%;
  padding: 11px 13px;
  background: #ffffff;
  border-top: 0.5px solid #f0f2f3;
  position: fixed;
  bottom: 0;
  height: 124rpx;

  .sel-btn {
    font-size: 24rpx;
    color: $theme-text-color;
  }

  .btn {
    width: 250rpx;
    line-height: 76rpx;
    background: #7473c5;
    border-radius: 8rpx;
    color: #ffffff;
    text-align: center;
    font-size: 28rpx;
  }
}

.allattention_bottom {
  width: 100%;
  height: 88rpx;
}

.empty-box {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  color: $text-color-primary;
  line-height: 40rpx;

  &.empty-page {
    width: 100vw;
    height: calc(100% - 60px);
    position: fixed;
    top: 60px;
    left: 0;
    bottom: 0;
  }

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 64rpx;
  }

  .title {
    font-size: 28rpx;
    font-weight: 400;
    color: $text-color-secondary;
    line-height: 42rpx;
  }
}