<template>
    <view>
        <view class="waterfall-box" v-for="(item, index) in c_list" :key="item.content_id" @click="viewDeatail(item)">
            <block v-if="item.type === 'banner'">
                <!-- {{ item.bannerList }} -->
                <view class="waterfall-box-banner">
                    <view class="carousel-map">
                        <custom-swiper
                            bgColor="#ffffff"
                            height="550rpx"
                            :circular="true"
                            :autoplay="true"
                            :loop="true"
                            :interval="11000"
                            :duration="400"
                            :fullScreenBtn="false"
                            :isAutoPlayVideo="true"
                            :showControls="false"
                            :indicator="true"
                            :indicatorStyle="indicatorStyle"
                            @change="change"
                            @click="jumpDetailPage"
                            :list="getBannerList(item.bannerList)"
                        >
                            <view slot="indicator" class="indicator-row u-flex u-col-center u-row-center">
                                <view
                                    v-for="(_, i) in item.bannerList"
                                    class="indicator__dot"
                                    :key="i"
                                    :class="{ active: i === currIndex }"
                                ></view>
                            </view>
                        </custom-swiper>
                    </view>
                </view>
            </block>
            <block v-else-if="item.type === 'draft'">
                <!-- {{ item.bannerList }} -->
                <view class="waterfall-box-draft">
                    <image
                        class="back_img"
                        src="https://wpm-cdn.dreame.tech/images/202501/794062-1736315746580.png"
                        mode="widthFix"
                    ></image>
                    <view class="box_draft">
                        <image
                            class="tip_img"
                            src="https://wpm-cdn.dreame.tech/images/202501/576273-1736216475022.png"
                        ></image>
                        <text class="draft_intro">本地草稿箱</text>
                        <view class="bottom_box">
                            <text class="tip">有{{ item.num }}篇动态待发布</text>
                            <image
                                class="right_icon"
                                src="https://wpm-cdn.dreame.tech/images/202501/634445-1736216763664.png"
                            >
                            </image>
                        </view>
                    </view>
                </view>
            </block>
            <block v-else>
                <view
                    :style="{
                        // minHeight: `${
                        //     item.cover_height < 132 ? 132 : item.cover_height > 265 ? 265 : item.cover_height
                        // }px`,
                        // maxHeight: `490rpx`,
                        position: 'relative',
                        overflow: 'hidden',
                    }"
                >
                    <image
                        :src="
                            item.cover_image ||
                            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202502/069273-1740192407839.png'
                        "
                        :style="{
                            width: '100%',
                            borderRadius: '16rpx 16rpx 0 0',
                            filter:
                                !creatorSelf &&
                                ![2, 4].includes(getContentStatus(item)) &&
                                item.boxType !== 'drafts_item'
                                    ? 'blur(10px)'
                                    : 'unset',
                        }"
                        mode="widthFix"
                    >
                    </image>
                    <!-- 左下角状态 -->
                    <view
                        v-if="
                            ((creatorSelf && [2, 4].includes(getContentStatus(item))) ||
                                (!creatorSelf && getContentStatus(item) === 4)) &&
                            showView
                        "
                        class="view_status"
                    >
                        <image
                            v-if="item.permission_type === 2"
                            src="https://wpm-cdn.dreame.tech/images/202501/518757-1736217098324.png"
                        ></image>
                        <image v-else src="https://wpm-cdn.dreame.tech/images/202501/854829-1736217496520.png"></image>
                        <text>{{ (item.permission_type === 2 ? '仅自己可见' : item.browse || 0) | viewFormat }}</text>
                    </view>
                </view>
                <!-- 视频icon -->
                <img
                    v-if="item.type === 2 && !(item.goods_ids && item.goods_ids.length)"
                    class="box-video-icon"
                    src="https://wpm-cdn.dreame.tech/images/2024010/586856-1730278888318.png"
                />

                <!-- 个人动态页显示 -->
                <view
                    v-if="getContentStatus(item) !== 4 && item.boxType !== 'drafts_item'"
                    class="content_status"
                    :class="{
                        reviewed: [0].includes(getContentStatus(item)),
                        review: [1].includes(getContentStatus(item)),
                        violation: [2, 3].includes(getContentStatus(item)),
                        another: !creatorSelf,
                    }"
                >
                    <view v-if="creatorSelf" class="status_text u-line-3">{{
                        content_status[getContentStatus(item)]
                    }}</view>
                    <view v-else-if="content_other_status[getContentStatus(item)].length" class="status_text">
                        <block v-for="(i, index) in content_other_status[getContentStatus(item)]" :key="index">
                            <view class="other_text">{{ i }}</view>
                        </block>
                    </view>
                </view>
                <view
                    class="box-item-title"
                    :style="{
                        filter:
                            !creatorSelf && ![2, 4].includes(getContentStatus(item)) && item.boxType !== 'drafts_item'
                                ? 'blur(5px)'
                                : 'unset',
                    }"
                >
                    {{ item.title || '--' }}
                </view>
                <!-- 底部操作 -->
                <view class="box-comment">
                    <!-- 草稿箱操作 -->
                    <template v-if="item.boxType === 'drafts_item'">
                        <div class="left time">{{ publishAt(item.update_at) }}</div>
                        <div class="right" @click.stop="handleOper(item, index, 'del')">
                            <image src="https://wpm-cdn.dreame.tech/images/202501/540114-1736322555340.png"></image>
                        </div>
                    </template>
                    <!-- 正常用户和点赞 -->
                    <template v-else>
                        <view class="person left">
                            <view style="position: relative;">
                              <image
                                v-if="item.author_avatar"
                                :src="item.author_avatar"
                                style="width: 35rpx; height: 35rpx; border-radius: 50%; margin-right: 8rpx"
                                :fade="true"
                                :lazyLoad="true"
                                duration="450"
                              >
                              </image>
                              <image class="author_tag" v-if="item.user_avatar_icon" :src="item.user_avatar_icon"></image>
                            </view>
                            <view class="author">{{ item.author }}</view>
                            <!-- <img v-if="item.creator == '3587481'"  style="margin-left: 10rpx;width: 128rpx;height: 36rpx" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202506/68623deeaf8877190010978.png" alt=""/> -->
                        </view>
                        <view class="sum right" @click.stop="handleOper(item, index)">
                            <image
                                v-show="item.isPraise"
                                class="view-num"
                                src="https://wpm-cdn.dreame.tech/images/2024010/449434-1730107172922.png"
                            />
                            <image
                                v-show="!item.isPraise"
                                class="view-num"
                                src="https://wpm-cdn.dreame.tech/images/2024010/679696-1730106684413.png"
                            />
                            <text>{{ item.praiseNum | praise }}</text>
                        </view>
                    </template>
                </view>

                <!-- 显示是否挂链接 icon 展示 -->
                 <view v-if="item.goods_ids && item.goods_ids.length" class="box-link">
                    <image class="box-link-icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888c4ada30686680012026.png" />
                 </view>
            </block>
        </view>
    </view>
</template>

<script>
import { postCancelPraise, postPraise } from '@/http/requestGo/community.ts';
import CustomSwiper from './CustomSwiper.vue';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
import _ from 'lodash';
// import Utils from '@/common/Utils';
// import Constants from '@/common/Constants';
export default {
    name: 'MyGoodsDemo',
    components: {
        CustomSwiper,
    },
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        showView: {
            type: Boolean,
            default: true,
        },
        // 显示异常状态
        creatorSelf: {
            type: Boolean,
            default: false,
        },
    },

    filters: {
        praise(val) {
            if (Number(val) === 0) {
                return '点赞';
            } else if (Number(val) < 10000) {
                return val;
            } else {
                const item = (Number(val) / 10000).toFixed(1);
                return item + 'w';
            }
        },
        viewFormat(val) {
            if (typeof val === 'number') {
                if (Number(val) < 10000) {
                    return val;
                } else {
                    const item = (Number(val) / 10000).toFixed(1);
                    return item + 'w';
                }
            } else return val;
        },
    },
    watch: {
        list: {
            deep: true,
            handler() {
                if (this.list.length) {
                    this.c_list = this.list.map((i, index) => {
                        if (i.type === 'banner') {
                            this.bannerList = i.bannerList;
                        }
                        if (
                            this.c_list[index] &&
                            this.c_list[index].content_id &&
                            _.isEqual(this.c_list[index], this.list[index])
                        ) {
                            return { ...this.c_list[index] };
                        }
                        return { ...i, isPraise: i.isPraise || i.is_praise, praiseNum: i.praiseNum || i.praise };
                    });
                }
            },
        },
    },
    data() {
        return {
            c_list: [],
            currIndex: 0,
            indicatorStyle: { bottom: '34rpx' },
            bannerList: [],
            content_status: Constants.COMMUNITY_CONTENT_STATUS,
            content_other_status: Constants.COMMUNITY_CONTENT_OTHER_STATUS,
            lastClickTime: 0
        };
    },
    computed: {
        // #ifdef MP-WEIXIN
        wxAuth() {
            return UserModule.wxAuth;
        },
        isPhone() {
            return UserModule.isPhone;
        },
        // #endif
    },
    methods: {
        publishAt(time) {
            return Utils.timeAgoFormat(time, 'publish');
        },
        viewDeatail(item) {
            // #ifdef H5
            if (item.jump_url) {
                Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
                return;
            };
            // #endif
            if (item.type === 'draft') {
                this.$emit('change', item);
                Utils.navigateTo('/pagesC/selfCommunity/drafts');
            } else if (item.type !== 'banner') {
                this.$emit('change', item);
            }
        },
        getContentStatus(item) {
            return item.report_status === 2
                ? 3
                : item.audit_status === 3
                ? 1
                : item.report_status === 1
                ? 2
                : item.audit_status === 1
                ? 0
                : 4;
        },
        async handleOper(item, index, type = 'praise') {
            // #ifdef MP-WEIXIN
            try {
                if (this.wxAuth && this.isPhone) {
                    await this.handlePraise(item, index, type);
                } else {
                    const target = 'service';
                    // 进行授权
                    UserModule.authFlow({ target });
                }
            } catch (e) {
                console.error('getDetailData err=', e);
            }
            // #endif
            Utils.reportEvent('give_like', { title: item.title });
            // #ifdef H5
            await this.handlePraise(item, index, type);
            // #endif
        },
        async handlePraise(item, index, type) {
            const now = +Date.now();
            if (now - this.lastClickTime < 500) {
                Utils.Toast('请勿频繁操作'); // 提示
                return;
            }
            this.lastClickTime = now;
            uni.$u.throttle(async () => {
                if (type === 'praise') {
                    let flag, res;
                    // const defaultPraise = item.is_praise;
                    if (item.isPraise) {
                        res = await postCancelPraise({ id: item.is_praise });
                        flag = false;
                    } else {
                        res = await postPraise({
                            entity_id: item.content_id,
                            type: 1,
                        });
                        flag = true;
                    }
                    if (res.success) {
                        const item = this.c_list[index];
                        item.isPraise = flag;
                        item.praiseNum = item.praiseNum + (+flag ? 1 : -1);
                        item.isPraise && (item.is_praise = res.data.id);
                        this.$set(this.c_list, index, item);
                        this.$nextTick(() => {
                            this.$emit('praise', {
                                item,
                                isPraise: item.isPraise,
                                is_praise: item.is_praise,
                                praiseNum: item.praiseNum,
                            });
                        })
                    }
                } else {
                    this.$emit('change', { ...item, type: 'del' });
                }
            }, 500);
        },
        change(e) {
            this.currIndex = e.current;
        },
        // banner跳转
        jumpDetailPage() {
            const item = this.bannerList[this.currIndex];
            const path = item.jump_url.split('?')[0];
            const jump_type = item.jump_type + '';
            // #ifdef MP-WEIXIN
            switch (jump_type) {
                case '1':
                    uni.navigateTo({
                        url: `/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`,
                    });
                    break;
                case '2':
                    uni.navigateTo({
                        url: `/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`,
                    });
                    break;
                case '3':
                    if (Constants.TAB_BAR_LIST.includes(path)) {
                        uni.reLaunch({
                            url: `${item.jump_url}`,
                        });
                    } else {
                        uni.navigateTo({ url: `${item.jump_url}` });
                    }
                    break;
                case '4':
                    uni.navigateToMiniProgram({
                        appId: item.appid,
                        path: item.jump_url,
                    });
            }
            // #endif
            // #ifdef H5
            switch (jump_type) {
                case '1':
                    Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}`);
                    break;
                case '2':
                    Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.jump_url}`);
                    break;
                case '3':
                    if (Constants.TAB_BAR_LIST.includes(path)) {
                        if (path === '/pages/index/index') {
                            Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                        } else if (path === '/pages/vipCenter/vipCenter') {
                            Utils.messageChannel('navigation', { type: 'mall', path: 'home/vip' });
                        } else if (path === '/pages/contents/contents') {
                            Utils.messageChannel('navigation', { type: 'mall', path: 'home/explore' });
                        } else if (path === '/pages/shop/shop') {
                            Utils.navigateTo(`${item.jump_url}`);
                        } else {
                            Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
                        }
                    } else {
                        Utils.navigateTo(`${item.jump_url}`);
                    }
                    break;
                case '4':
                    Utils.openMiniProgram({
                        id: item.appid,
                        path: item.jump_url,
                    });
                    break;
            }
            // #endif
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CONTENTS_BANNER,
            });
        },
        getBannerList(data) {
            if (data && data.length > 0) {
                const newList = [];
                data.forEach((itm) => {
                    if (itm.image) {
                        const item = { url: '', title: '', type: 'image' };
                        item.url = itm.image;
                        item.title = itm.title;
                        newList.push(item);
                    }
                    if (itm.video) {
                        const item = { url: '', title: '', type: 'image', poster: '' };
                        item.url = itm.video;
                        item.title = itm.title;
                        item.poster = `${itm.video}?x-oss-process=video/snapshot,t_1000,m_fast`;
                        item.type = 'video';
                        newList.push(item);
                    }
                });
                return newList;
            }
            return [];
        },
    },
};
</script>

<style lang="scss">
.waterfall-box {
    border-radius: 16rpx;
    margin-bottom: 8rpx;
    background-color: #ffffff;
    position: relative;
    overflow: hidden;

    .box-item-title {
        padding-right: 20rpx;
        padding-left: 20rpx;
        font-size: 28rpx;
        margin-top: 20rpx;
        color: #1d1e20;
        font-weight: 500;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all !important;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .box-comment {
        font-size: 20rpx;
        font-weight: 400;
        color: rgba(29, 30, 32, 0.4);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            padding: 20rpx 20rpx 24rpx;
            display: flex;
            align-items: center;

            &.time {
                color: #777777;
            }
            .author {
                /* display: flex;
                align-items: center; */
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: keep-all;
                margin-right: 4rpx;
                max-width: 120rpx;
            }
            .author_tag {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 18rpx;
                height: 18rpx;
                margin-left: 4rpx;
                border-radius: 50%;
            }
        }

        .right {
            padding: 20rpx 20rpx 24rpx;
            display: flex;
            align-items: center;
            .view-num,
            img,
            image {
                width: 28rpx;
                height: 28rpx;
                margin-right: 8rpx;
            }
            text {
                min-width: 50rpx;
            }
        }
    }

    .box-link {
        position: absolute;
        top: 12rpx;
        right: 12rpx;
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(18, 18, 18, 0.3);

        .box-link-icon {
            width: 22px;
            height: 22px;
        }
    }

    .box-video-icon {
        position: absolute;
        top: 22rpx;
        right: 22rpx;
        width: 46rpx;
        height: 46rpx;
    }

    .view_status {
        position: absolute;
        left: 8rpx;
        bottom: 8rpx;
        padding: 8rpx 16rpx;
        background: rgba(32, 37, 43, 0.4);
        border-radius: 96rpx 96rpx 96rpx 96rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
            width: 12px;
            height: 12px;
        }

        text {
            /* 10 补充文字/Regular */
            font-family: PingFang SC;
            font-size: 10px;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            letter-spacing: 0px;
            /* Text 文字/text_5 */
            color: #ffffff;
            margin-left: 10rpx;
        }
    }

    .content_status {
        position: absolute;
        top: 8rpx;
        left: 8rpx;
        width: calc(100% - 16rpx);
        background: #e8dec1;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 19rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        text-align: center;
        padding: 13rpx 28rpx;
        .status_text {
            line-height: 27rpx;
            color: #ab8c5e;

            .other_text {
                text-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.8);
            }
        }
        // &.review {
        // }
        &.reviewed {
            background: #e8dec1;
            .status_text {
                color: #ab8c5e;
            }
        }
        &.review {
            background: #fff3e5;
            .status_text {
                color: #bf6e12;
            }
        }
        &.violation {
            background: #ffeeea;
            .status_text {
                color: #ee3c2f;
            }
        }

        &.another {
            background-color: unset;
            top: 32%;
            left: 50%;
            transform: translate(-50%, -50%);
            .status_text {
                color: #fff;
                view {
                    line-height: 38rpx;
                    font-size: 26rpx;
                }
            }
        }
    }

    .waterfall-box-banner {
        width: 100%;
        height: 550rpx;
        background: #ffffff;
        border-radius: 8rpx 8rpx 8rpx 8rpx;

        .indicator-row {
            width: 100%;
            height: 4rpx;
            z-index: 2;

            .indicator__dot {
                width: 38rpx;
                height: 4rpx;
                background: rgba(255, 255, 255, 0.1);

                &.active {
                    height: 4rpx;
                    background: rgba(255, 255, 255, 0.9);
                }
            }
        }
    }

    .waterfall-box-draft {
        position: relative;
        height: 212rpx;
        overflow: hidden;
        .back_img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            img {
                object-fit: cover;
            }
        }

        .box_draft {
            position: absolute;
            top: 0;
            left: 0;
            background: rgba(24, 24, 24, 0.8);
            height: 100%;
            width: 100%;
            padding-top: 40rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: PingFang SC, PingFang SC;
            font-style: normal;
            font-weight: 400;
            text-transform: none;
            .tip_img {
                width: 46rpx;
                height: 46rpx;
                margin-bottom: 8rpx;
            }
            .right_icon {
                width: 31rpx;
                height: 31rpx;
            }
            .draft_intro {
                font-size: 23rpx;
                color: #ffffff;
                line-height: 33rpx;
            }
            .bottom_box {
                display: flex;
                margin-top: 12rpx;
            }
            .tip {
                font-size: 19rpx;
                color: #ffffff;
                line-height: 27rpx;
            }
        }
    }

    ::v-deep .video-poster {
        .play {
            position: absolute;
            top: 22rpx;
            right: 22rpx;
            width: 46rpx;
            height: 46rpx;
        }
    }
}
</style>
