<template>
    <div class="boost-progress">
        <!-- 奖励等级 -->
        <div class="reward-tiers">
            <div
                class="reward-tier first"
                :style="{ backgroundImage: `url(${info.prize[0].total_image})` }"
                :class="{ unlocked: info.total_petal_number >= info.prize[0].petal_count }"
                @click="clickPrrize(info.prize[0].total_prize_image)"
            ></div>

            <div
                class="reward-tier second"
                :style="{ backgroundImage: `url(${info.prize[1].total_image})` }"
                :class="{ unlocked: info.total_petal_number >= info.prize[1].petal_count }"
                @click="clickPrrize(info.prize[1].total_prize_image)"
            ></div>

            <div
                class="reward-tier third"
                :style="{ backgroundImage: `url(${info.prize[2].total_image})` }"
                :class="{ unlocked: info.total_petal_number >= info.prize[2].petal_count }"
                @click="clickPrrize(info.prize[2].total_prize_image)"
            ></div>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
            <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                <div
                    class="progress-marker marker-1"
                    :class="{ unlocked: info.total_petal_number >= info.prize[0].petal_count }"
                >
                    <i
                        class="icon"
                        :class="info.total_petal_number >= info.prize[0].petal_count ? 'check' : 'lock'"
                    ></i>
                </div>
                <div
                    class="progress-marker marker-2"
                    :class="{ unlocked: info.total_petal_number >= info.prize[1].petal_count }"
                >
                    <i
                        class="icon"
                        :class="info.total_petal_number >= info.prize[1].petal_count ? 'check' : 'lock'"
                    ></i>
                </div>
                <div
                    class="progress-marker marker-3"
                    :class="{ unlocked: info.total_petal_number >= info.prize[2].petal_count }"
                >
                    <i
                        class="icon"
                        :class="info.total_petal_number >= info.prize[2].petal_count ? 'check' : 'lock'"
                    ></i>
                </div>
            </div>
            <div class="current-progress">
                <div class="progress-arrow" :style="{ left: progressPercentage + '%' }"></div>
                <div class="progress-text" :style="{ left: progressPercentage + '%' }">
                    {{ info.total_people_number }}人参与
                </div>
            </div>
        </div>

        <!-- 邀请按钮 -->
        <div class="invite-button-container" @click="handleInvite"></div>
        <prize-model :show="showPrizeModel" @close="closePrizeModel" :imageUrl="imageUrl" />
    </div>
</template>

<script lang="ts">
import prizeModel from './prizeModel.vue';
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
        prizeModel,
    },
})
export default class BoostProgress extends Vue {
    @Prop({ type: Object, default: () => {} }) info!: any;
    public showPrizeModel: boolean = false;
    public imageUrl: string = '';

    get progressPercentage() {
        // 计算进度百分比
        // 7.965% 对应 10000，50% 对应 30000，92.035% 对应 100000
        // 10000-30000: 7.965% ~ 50%  (区间长度: 20000)
        // 30000-100000: 50% ~ 92.035% (区间长度: 70000)
        if (this.info.total_petal_number <= this.info.prize[0].petal_count) {
            // 0 ~ 10000
            return (this.info.total_petal_number / this.info.prize[0].petal_count) * 7.965;
        } else if (this.info.total_petal_number <= this.info.prize[1].petal_count) {
            // 10000 ~ 30000
            // 7.965% + (当前-10000)/(20000) * (50-7.965)
            return (
                7.965 +
                ((this.info.total_petal_number - this.info.prize[0].petal_count) /
                    (this.info.prize[1].petal_count - this.info.prize[0].petal_count)) *
                    (50 - 7.965)
            );
        } else if (this.info.total_petal_number <= this.info.prize[2].petal_count) {
            // 30000 ~ 100000
            // 50% + (当前-30000)/(70000) * (92.035-50)
            return (
                50 +
                ((this.info.total_petal_number - this.info.prize[1].petal_count) /
                    (this.info.prize[2].petal_count - this.info.prize[1].petal_count)) *
                    (92.035 - 50)
            );
        } else {
            // 超过100000片，最大92.035%
            return (
                92.035 +
                ((this.info.total_petal_number - this.info.prize[2].petal_count > 50000
                    ? 50000
                    : this.info.total_petal_number - this.info.prize[2].petal_count) /
                    50000) *
                    (100 - 97.035)
            );
        }
    }

    closePrizeModel() {
        this.showPrizeModel = false;
    }

    clickPrrize(url: string) {
        this.imageUrl = url;
        this.showPrizeModel = true;
    }

    handleInvite() {
        this.$emit('invite');
    }
}
</script>

<style lang="scss" scoped>
@import './boostProgress.scss';
</style>
