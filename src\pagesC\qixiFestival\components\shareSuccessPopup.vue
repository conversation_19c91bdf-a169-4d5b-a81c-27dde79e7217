<template>
    <u-popup
        :show="isShow"
        class="shareSuccessPopup"
        mode="center"
        :round="24"
        @close="closePopup"
        :bgColor="'transparent'"
        :closeOnClickOverlay="false"
    >
        <view class="shareSuccessContent">
            <view class="shareSuccessContent-title">
                <text class="title">花瓣+{{ count }}</text>
                <text class="subTitle">助力好友成功</text>
            </view>
            <view class="btn" @click="closePopup"> </view>
            <view class="closeBtn" @click="closePopup">
                <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82181277841620011623.png"
                    mode="widthFix"
                ></image>
            </view>
        </view>
    </u-popup>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
    name: 'shareSuccessPopup',
})
export default class ShareSuccessPopup extends Vue {
    @Prop({ default: false }) isShow: boolean;
    @Prop({ default: 0 }) count: number;
    closePopup() {
        this.$emit('closePopup');
    }
}
</script>

<style lang="scss" scoped>
.shareSuccessPopup {
    .shareSuccessContent {
        width: 612rpx;
        height: 652rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a83df04bc993100011824.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        position: relative;
        .shareSuccessContent-title {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            gap: 20rpx;
            .title {
                color: #e53a5a;
                font-size: 60rpx;
                font-weight: 500;
            }
            .subTitle {
                color: #333;
                font-size: 30rpx;
                font-weight: 400;
            }
        }
        .btn {
            position: absolute;
            bottom: 34px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 234px;
            height: 45px;
        }
        .closeBtn {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -98rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            image {
                width: 56rpx;
                height: 56rpx;
            }
        }
    }
}
</style>
