<template>
    <view
        class="community_container"
        :style="{ 'padding-top': pagePaddingContent + 'rpx', 'padding-bottom': `${pagePaddingBottom}rpx` }"
    >
        <view class="header">
            <custom-bar2 :title="barName[pageType]"></custom-bar2>
        </view>
        <div class="box_user_list">
            <scroll-view
                scroll-y
                class="swiper-item-view"
                :scroll-top="scrollTop"
                @scroll="handleScroll"
                refresher-background="#fff"
                :refresher-enabled="true"
                :upper-threshold="0"
                :refresher-triggered="isRefreshing"
                @refresherrefresh="refresherrefresh"
                @scrolltolower="onreachBottom"
            >
                <view class="page-box" v-if="listData.list && listData.list.length > 0">
                    <view class="user_item" v-for="item in listData.list" :key="item.id">
                        <view class="user_info" @click="handleSelf(item)">
                            <view style="position: relative;">
                              <image
                                class="user_avator"
                                :src="
                                    item.avatar || 'https://wpm-cdn.dreame.tech/images/202301/185295-1673921873152.png'
                                "
                            ></image>
                            <image
                                class="user_avator"
                                v-if="item.user_avatar_icon"
                                :src="
                                    item.user_avatar_icon
                                "
                                style="width: 32rpx;height: 32rpx;position: absolute;bottom: 0;right: 0;"
                            ></image>
                            </view>
                            <view class="user_text u-line-1">{{ item.name || '暂无昵称' }}</view>
                        </view>
                        <view
                            class="user_oper"
                            :class="{ follow: item.follow_status === 0 }"
                            @click="handleUserOper(item)"
                        >
                            <img
                                style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                v-if="userStatusText(item) === '关注'"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                            />
                            <img
                                style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                                v-if="userStatusText(item) === '已关注'"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png"
                            />

                            <view class="oper_text">{{ userStatusText(item) }}</view>
                        </view>
                    </view>
                    <view class="no_more" v-if="finished">已显示全部内容</view>
                </view>
                <view class="empty_node u-flex-col u-col-center" v-if="listData.list && listData.list.length === 0">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202502/982778-1740215230212.png"
                    ></image>
                    <view class="empty_text">你还没有{{ barTip[pageType] }}</view>
                </view>
            </scroll-view>
        </div>
        <!-- 用户取消关注拉黑 -->
        <CustomModal
            :show="operContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            :title="operContent.titie"
            :content="operContent.tip"
            :confirmText="operContent.confirmText"
            :cancelText="operContent.cancelText"
            showCancelButton
            @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })"
        >
        </CustomModal>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import {
    myBlockList,
    myFansList,
    myFollowList,
    postCancelBlock,
    userFollow,
    userFollowDelete,
} from '@/http/requestGo/community';
import Utils from '@/common/Utils';
import { OperContent } from './selfCommunity.vue';
import { UserModule } from '@/store/modules/user';
@Component({
    components: {},
    filters: {},
})
export default class order extends Vue {
    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get pagePaddingBottom(): number {
        return AppModule.pagePaddingBottom;
    }

    get pagePaddingContent(): number {
        return Number(AppModule.pagePaddingTop);
    }

    // #ifdef MP-WEIXIN

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }
    // #endif

    private orderData: {
        list: Array<any>;
        pages: number;
    };

    public pageType: number = -1;
    public barName: any = ['我的关注', '我的粉丝', '黑名单用户'];
    public barTip: any = ['关注任何人', '粉丝~', '拉黑任何人'];
    public scrollTop: number = 0;
    public old: any = { scrollTop: 0 };
    public isRefreshing: boolean = false;
    public finished: Boolean = false; // 是否加载完成
    public page: number = 0; // 由于分页一般页面要+1，所以为了方便分页查询，这里初始设置页码为0
    public listData: any = { list: [] };
    public isLoading: boolean = false;
    public page_size: number = 6;
    private showLoading: boolean = false; // 加载toast
    public data: any = {};
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: OperContent = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    onLoad(option) {
        if (option.type) {
            this.pageType = +option.type;
        }
        // #ifdef H5
        this.init();
        // #endif
    }

    onUnLoad() {}

    onShow() {
        // #ifdef MP-WEIXIN
        this.init();
        // #endif
        // #ifdef H5
        const win: any = window;
        win.onAppPageShow = () => {
            this.init();
        };
        // #endif
    }

    handleScroll(e) {
        this.old.scrollTop = e.detail.scrollTop;
    }

    async init() {
        this._initPageData();
        await this._getListFactory();
    }

    async _getList() {
        this.isLoading = true;
        try {
            const page: number = this.page;
            const page_size: number = this.page_size;
            const params: any = { page, page_size };
            const res: any = await [myFollowList, myFansList, myBlockList][this.pageType](params);
            const { list = [], total = 1 } = res.data;
            const mapList = list.map((item) => ({
                ...item,
                avatar: item.avatar || item.follow_user_avatar || item.blocked_user_avatar,
                name: item.user_name || item.follow_user_name || item.blocked_user_name,
                user_id: item.user_id || item.follow_user_id || item.blocked_user_id,
            }));
            this.orderData = { list: mapList || [], pages: Math.ceil(total / page_size) };
            if (this.page >= this.orderData.pages) {
                this.finished = true;
                // Utils.Toast('没有更多了');
            }
            if (page > 1) {
                this.listData.list = this.listData.list.concat(mapList);
            } else {
                this.$set(this.listData, 'list', mapList || []);
            }
            this.isLoading = false;
        } catch (e) {
            console.error('_getList e=', e);
            this.isLoading = false;
        }
    }

    // 初始化页面的数据
    _initPageData() {
        this.finished = false;
        this.page = 0;
        if (this.listData.list) {
            this.$set(this.listData, 'list', []);
        }
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        if (this.finished) return;
        this.page++;
        await this._getList();
    }

    onreachBottom() {
        this._getListFactory();
    }

    async refresherrefresh() {
        this.isRefreshing = true;
        await this.init();
        this.isRefreshing = false;
    }

    userStatusText(item) {
        if (this.pageType === 0) {
            return ['关注', '已关注', '互相关注'][item.follow_status];
        } else if (this.pageType === 1) {
            return ['关注', '已关注', '互相关注'][item.follow_status];
        } else if (this.pageType === 2) {
            return '解除拉黑';
        }
        return '';
    }

    // 用户操作
    handleUserOper(item) {
        // #ifdef MP-WEIXIN
        try {
            if (this.wxAuth && this.isPhone) {
                // #endif
                uni.$u.throttle(async () => {
                    let res;
                    this.data = { ...item };
                    if (this.pageType === 0 || this.pageType === 1) {
                        // console.log('取消关注');
                        if (item.follow_status == 0) {
                            res = await userFollow({ followed_user_id: item.user_id });
                        } else {
                            this.operContentOpen = true;
                            this.operContent = {
                                type: 'follow',
                                titie: '',
                                tip: '确定不再关注该用户',
                                confirmText: '不再关注',
                                cancelText: '取消',
                            };
                            // res = await userFollowDelete({ followed_user_id: item.user_id });
                        }
                    } else if (this.pageType === 2) {
                        // console.log('取消拉黑');
                        this.operContentOpen = true;
                        this.operContent = {
                            type: 'block',
                            titie: `确定解除拉黑该用户`,
                            confirmText: '确定',
                            cancelText: '取消',
                        };
                        // res = await postCancelBlock({ blocked_user_id: item.user_id });
                    }
                    if (res && res.success) {
                        Utils.Toast(
                            item.block_status === 1
                                ? '已移除黑名单'
                                : item.follow_status === 0
                                ? '关注成功'
                                : '操作成功',
                        );
                        this.init();
                    }
                }, 500);
                // #ifdef MP-WEIXIN
            } else {
                const target = 'service';
                // 进行授权
                UserModule.authFlow({ target });
            }
        } catch (e) {
            console.error('getDetailData err=', e);
        }
        // #endif
    }

    async handleContenConfirm() {
        console.log(this.data, 'this.data');
        let res;
        // 二次确认弹框确认
        if (this.operContent.type === 'follow') {
            res = await userFollowDelete({ followed_user_id: +this.data.user_id });
        } else if (this.operContent.type === 'block') {
            res = await postCancelBlock({ blocked_user_id: +this.data.user_id });
        }
        if (res.success) {
            Utils.Toast(
                this.operContent.type === 'block'
                    ? '已移除黑名单'
                    : this.operContent.type === 'follow'
                    ? '操作成功'
                    : '关注成功',
            );
            this.init();
        }
        this.operContentOpen = false;
        setTimeout(() => {
            this.operContent = { type: '' };
        }, 500);
    }

    handleSelf(item) {
        Utils.jumpPersonHome(item.user_id);
        // uni.navigateTo({
        //     url: `/pagesC/selfCommunity/selfCommunity?creator=${item.user_id}`,
        // });
    }
}
</script>

<style lang="scss" scoped>
.community_container {
    background-color: #fff;
    height: 100vh;

    .box_user_list {
        height: 100%;
        background-color: #fff;
        position: relative;

        .swiper-item-view {
            height: 100%;
            .user_item {
                box-sizing: border-box;
                padding: 30rpx 38rpx 30rpx 30rpx;
                width: 100%;
                height: 162rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .user_info {
                    display: flex;
                    align-items: center;
                    flex: 1;
                    image {
                        width: 96rpx;
                        height: 96rpx;
                        border-radius: 50%;
                        margin-right: 30rpx;
                    }
                    .user_text {
                        padding-right: 30rpx;
                        flex: 1;
                        font-family: Source Han Sans, Source Han Sans;
                        font-weight: 700;
                        font-size: 31rpx;
                        color: #121212;
                        line-height: 44rpx;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                    }
                }
                .user_oper {
                    width: 158rpx;
                    border-radius: 154rpx 154rpx 154rpx 154rpx;
                    border: 2rpx solid #E2E2E2;
                    color: #A6A6A6;
                    padding: 16rpx 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .oper_text {
                        text-align: center;
                        font-family: MiSans, MiSans;
                        font-weight: 500;
                        font-size: 27rpx;
                        line-height: 31rpx;
                        font-style: normal;
                        text-transform: none;
                    }

                    &.follow {
                        border: 2rpx solid #DBC49A;
                        color: #C2A271;
                    }
                }
            }
        }

        .no_more {
            display: flex;
            justify-content: center;
            margin: 18rpx auto 0;
            font-family: MiSans;
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 0px;

            font-variation-settings: 'opsz' auto;
            font-feature-settings: 'kern' on;
            /* Text 文字/text_4 */
            /* 样式描述：置灰/禁用 */
            color: #a6a6a6;

            z-index: 1;
        }
        .empty_node {
            position: absolute;
            top: 39%;
            left: 50%;
            transform: translate(-50%, -50%);
            image {
                width: 508rpx;
                height: 508rpx;
            }
            .empty_text {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 27rpx;
                color: #000;
                line-height: 38rpx;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
