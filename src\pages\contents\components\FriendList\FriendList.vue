<template>
    <scroll-view
        :scroll-y="isProductListScrollEnabled"
        class="right-box"
        :refresher-enabled="enableRefresher"
        :upper-threshold="0"
        :lower-threshold="300"
    >
        <view class="page-view">
            <view class="u-good-content">
                <block v-for="(itm, index) in ListData" :key="index">
                    <view v-if="itm" class="class-item" @click="viewDetail(itm)">
                        <view class="class-item-top">
                            <block v-if="itm.is_hot == 1">
                                <view class="hotProImg"></view>
                            </block>
                            <block v-if="itm.is_hot == 2">
                                <view class="yushouImg"></view>
                            </block>
                            <image :src="getImgUrl(itm.cover_image)" mode="aspectFill" class="lazyImg"/>
                            <img
                                v-if="itm.type === 2"
                                src="@/static/friend/icon_video_play.png"
                                style="
                                    width: 96rpx;
                                    height: 96rpx;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                "
                            />
                        </view>
                        <view class="class-item-bottom">
                            <view class="name">
                                <view class="text titleW">{{ itm.title }}</view>
                            </view>
                            <view class="price">
                                <view class="u-flex">
                                    <view
                                        style="position: relative; display: flex; flex-direction: row; align-items: center"
                                    >
                                        <image class="img" :src="itm.author_avatar" mode="scaleToFit"></image>
                                        <image
                                            v-if="itm.user_avatar_icon"
                                            style="
                                                width: 20rpx;
                                                height: 20rpx;
                                                position: absolute;
                                                bottom: 0;
                                                right: 0;
                                            "
                                            :src="itm.user_avatar_icon"
                                            mode="aspectFill"
                                        ></image>
                                    </view>
                                    <view class="text author-text">
                                        {{ itm.author }}
                                    </view>
                                </view>
                                <view class="">
                                    <view class="oper_item" @click.stop="handleOperFlow(itm, 'praise')"
                                        ><image
                                            :src="
                                                !itm.is_praise
                                                    ? 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68674e1ac77418170019079.png'
                                                    : 'https://wpm-cdn.dreame.tech/images/2024010/449434-1730107172922.png'
                                            "
                                        /><view class="oper_num"> {{ clicks(itm.praise) }}</view></view
                                    >
                                </view>
                            </view>
                        </view>
                    </view>
                    <view v-else>
                        <view class="u-flex no_more">
                            <img src="@/static/friend/icon_message_finish.png" style="width: 36rpx; height: 36rpx;" />
                            <view v-if="tabsIndex == 0" style="font-size: 28rpx; color: #777777; margin-left: 16rpx;">消息内容到底，继续浏览广场</view>
                            <view v-if="tabsIndex == 1" style="font-size: 28rpx; color: #777777; margin-left: 16rpx;">动态内容到底，继续浏览广场</view>
                        </view>
                    </view>
                </block>
            </view>
        </view>
    </scroll-view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import {
        postCancelFavorite,
        postCancelPraise,
        postFavorite,
        postPraise,
    } from '@/http/requestGo/community';
import { UserModule } from '@/store/modules/user';
import { compressImageUrl } from '@/utils/imageCompression';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Array })
    public ListData: Array<any>;

    @Prop({ type: Number })
    public tabsIndex: number;

    public c_list: Array<any> = [];

    public enableRefresher: Boolean = false;

    public isProductListScrollEnabled: Boolean = false;

    public current: number = 0;

    public timer: any = null;

    public listData: { list: Array<any>; isLoading: Boolean } = {
        list: [],
        isLoading: false,
    };

    public page: number = 1;

    public currActiveId: any = process.env.VUE_APP_TOPIC_ID;
    public last_content_id: string = '';
    public isLoadGrandData: boolean = false;

    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    getImgUrl(url) {
        return compressImageUrl(url, {
            quality: 100,
            maxWidth: 600,
            maxHeight: 600,
            format: 'webp',
        });
    }

    viewDetail(item, isJump = false) {
        // #ifdef H5
        if (item.jump_url && isJump) {
            Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
            return;
        }
        // #endif
        const { content_id = '', type, creator } = item;
        this.$emit('updateContentStatus', { content_id, creator });
        Utils.navigateTo(
            type === 1
                ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}&topic_id=${this.currActiveId}`,
        );
    }

    mounted() {
        // 初始时禁用ProductList的滚动
        this.isProductListScrollEnabled = false;
        // 监听来自商品详情页的心愿单状态更新事件
        uni.$on('wishGoodsChange', this.onWishGoodsChange);

        Utils.reportEvent('shop_click', {});
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SHOP_BUTTON,
        });
    }

    beforeDestroy() {
        // 注销全局事件监听，防止内存泄漏
        uni.$off('wishGoodsChange', this.onWishGoodsChange);
        // 清除定时器，防止内存泄漏
        if (this.timer) {
            clearTimeout(this.timer);
        }
    }

    onWishGoodsChange({ gid, is_wish }) {
        const target = this.listData.list.find((item) => item.gid === gid);
        if (target) {
            this.$set(target, 'is_wish', is_wish);
        }
    }

    clicks(val) {
        if (Number(val) === 0) {
            return 0;
        } else if (Number(val) < 10000) {
            return val;
        } else {
            const item = (Number(val) / 10000).toFixed(1);
            return item + 'w';
        }
    }

    async handleOperFlow(item, type) {
        Utils.reportEvent('give_like', { title: item.title });
        await this.handleOper(item, type);
    }

     async handleOper(item, type) {
        let flag, res;
        if (item[type === 'praise' ? 'is_praise' : 'is_favorite']) {
            const fn = type === 'praise' ? postCancelPraise : postCancelFavorite;
            res = await fn({ id: item[type === 'praise' ? 'is_praise' : 'is_favorite'] });
            flag = false;
        } else {
            if (type === 'praise') {
                res = await postPraise({
                    entity_id: item.content_id,
                    type: 1,
                });
            } else {
                res = await postFavorite({
                    content_id: item.content_id,
                });
            }
            flag = true;
        }
        if (res.success) {
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = flag;
            item[type === 'praise' ? 'praise' : 'favorite'] += +flag ? 1 : -1;
            item[type === 'praise' ? 'is_praise' : 'is_favorite'] = res.data.id;
        }
        // this.$emit('praise', id);
    }
}
</script>
<style lang="scss" scoped>
@import './FriendList.scss';
.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    background-color: #f6f6f6;
    font-size: 24rpx;
    font-weight: 400;
    color: rgba(29, 30, 32, 0.4);
    line-height: 33px;
    padding-top: 50rpx;
    margin-bottom: 208rpx;
}
.author-text {
  white-space: nowrap;      /* 禁止换行 */
  overflow: hidden;         /* 隐藏溢出内容 */
  text-overflow: ellipsis;  /* 显示省略号 */
  width: calc(100% - 65rpx);
}
</style>
