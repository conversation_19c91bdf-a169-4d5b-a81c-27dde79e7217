{"eslint.enable": true, "eslint.run": "onType", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.eol": "\n", "editor.trimAutoWhitespace": true, "editor.formatOnSave": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "workbench.activityBar.orientation": "vertical"}