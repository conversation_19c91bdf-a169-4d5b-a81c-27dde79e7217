<template>
    <view class="prize-result-container">
        <image
            @click="getPrizeResult"
            v-if="isShowIcon"
            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a81c720d4960540023232.png"
            mode="widthFix"
            class="prize-result-image"
        ></image>
        <u-popup
            bgColor="transparent"
            :show="show"
            mode="center"
            @close="close"
            @open="open"
            :closeOnClickOverlay="false"
        >
            <view class="prize-model-container">
                <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82556c7b3d8180010743.png"
                    mode="widthFix"
                    class="prize-model-image-title"
                ></image>
                <view class="prize-model-image-content">
                    <view class="big_prize">
                        <view v-for="item in prizeList" :key="item.id">
                            <view class="big_prize_title" v-for="(user, index) in item.users" :key="user.uid">
                                <image :src="item.prize_image" mode="widthFix" class="big_prize_title_image"></image>
                                <view class="big_prize_title_text">
                                    <view
                                        style="
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            font-size: 20rpx;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            white-space: nowrap;
                                        "
                                    >
                                        <image
                                            style="
                                                width: 32rpx;
                                                height: 32rpx;
                                                border-radius: 50%;
                                                background-color: #fff;
                                            "
                                            :src="user.avatar"
                                            mode="widthFix"
                                            class="big_prize_title_text_image"
                                        ></image>
                                        <text
                                            style="
                                                overflow: hidden;
                                                text-overflow: ellipsis;
                                                white-space: nowrap;
                                                max-width: 280rpx;
                                            "
                                            >{{ user.nick_name }}</text
                                        >
                                    </view>
                                    <view>
                                        <view
                                            style="font-size: 28rpx; color: #fff; text-align: center; font-size: 26rpx"
                                        >
                                            恭喜获得！
                                        </view>
                                        <view
                                            style="font-size: 28rpx; color: #fff; text-align: center; font-size: 26rpx"
                                        >
                                            {{ item.prize_name }}
                                        </view>
                                    </view>
                                    <view>
                                        <view
                                            style="
                                                font-size: 26rpx;
                                                color: #fff;
                                                text-align: center;
                                                color: rgba(255, 255, 255, 0.7);
                                            "
                                        >
                                            花瓣码：{{ item.sings[index] }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 奖励金 券 -->
                    <view class="reward_money" v-if="rewardList.length > 0">
                        <view v-for="item in rewardList" :key="item" style="margin-bottom: 24rpx">
                            <view class="reward_money_title">{{ item.prize_name }}</view>
                            <view class="reward_money_content">
                                <view class="coupon_codes">
                                    <view
                                        class="coupon_code_item"
                                        v-for="(code, index) in item.sings || []"
                                        :key="index"
                                    >
                                        {{ code }}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view
                        style="border-top: 2rpx dashed #53100b; margin: 32rpx 0; opacity: 0.2"
                        v-if="myprizeList.length > 0"
                    ></view>
                    <view>
                        <view
                            style="font-size: 24rpx; color: #53100b; margin-bottom: 24rpx"
                            v-if="myprizeList.length > 0"
                        >
                            您的获奖情况如下，奖励将于3日内发出：
                        </view>
                        <view style="height: 188rpx; overflow: auto" v-if="myprizeList.length > 0">
                            <view v-for="item in myprizeList" :key="item.id" class="my_prize_item">
                                <view class="my_prize_item_code"> 花瓣码：{{ item.lucky_sign_code }} </view>
                                <view class="my_prize_item_prize">
                                    <text class="line_clamp" style="flex: 1">{{ item.prize_name }}</text>
                                    <image
                                        v-if="item.prize_type === '2'"
                                        @click="claimPrize(item)"
                                        style="width: 62rpx; height: 28rpx; margin-left: 8rpx"
                                        :style="{ opacity: item.status === 1 ? 0.4 : 1 }"
                                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a840b5a06636570016097.png"
                                        mode="widthFix"
                                    ></image>
                                </view>
                            </view>
                        </view>
                        <view v-if="myprizeList.length === 0" class="no_prize"> </view>
                    </view>
                </view>
                <image
                    @click="close"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a816a5c85aa8210011074.png"
                    mode="widthFix"
                    class="prize-model-close-image"
                ></image>
            </view>
        </u-popup>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
@Component({
    components: {},
})
export default class PrizeResult extends Vue {
    @Prop({ type: Boolean, default: false }) show!: boolean;
    @Prop({ type: Array, default: () => [] }) prizeList!: any[];
    @Prop({ type: Array, default: () => [] }) rewardList!: any[];
    @Prop({ type: Array, default: () => [] }) myprizeList!: any[];
    @Prop({ type: Boolean, default: false }) isShowIcon!: boolean;
    public getPrizeResult() {
        this.$emit('getPrizeResult');
    }

    mounted() {}

    claimPrize(item: any) {
        // 1 已领取 0 未领取
        if (item.status === 1) {
            Utils.Toast('已领取');
        } else {
            this.$emit('claimPrize', item.id);
        }
    }

    close() {
        this.$emit('close');
    }
}
</script>
<style lang="scss" scoped>
.prize-result-image {
    position: absolute;
    top: 360rpx;
    right: 0rpx;
    width: 44rpx;
    height: 140rpx;
}
.prize-model-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 610rpx;
    transform: translateY(96rpx);
    .prize-model-image-title {
        width: 610rpx;
        height: 220rpx;
        top: 0rpx;
        left: 0rpx;
        z-index: 1;
    }
    .prize-model-image-content {
        transform: translateY(-96rpx);
        padding: 24rpx 30rpx 24rpx 30rpx;
        position: relative;
        top: 0rpx;
        left: 0rpx;
        width: 610rpx;
        min-height: 600rpx;
        // max-height: 1130rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a827cc74b284780208253.png')
            no-repeat center center;
        background-size: 100% 100%;
        z-index: 2;
        .big_prize {
            display: flex;
            flex-direction: column;
            max-height: 380rpx;
            overflow: auto;
            .big_prize_title {
                font-size: 32rpx;
                padding-left: 8rpx;
                background: linear-gradient(305deg, #f03258 41%, #ff5177 105%);
                width: 530rpx;
                min-height: 184rpx;
                border-radius: 24rpx;
                margin: 0 auto;
                margin-bottom: 12rpx;
                display: flex;
                align-items: center;
                .big_prize_title_image {
                    width: 164rpx;
                    height: 164rpx;
                }
            }
            .big_prize_title_text {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;
                width: calc(100% - 164rpx);
                overflow: hidden;
                padding: 14rpx 0rpx 14rpx 0rpx;
                width: calc(100% - 164rpx);
                overflow: hidden;
                flex: 1;
                padding-left: 24rpx;
                .big_prize_title_text_image {
                    margin-right: 8rpx;
                }
                text {
                    font-size: 32rpx;
                    color: #fff;
                }
            }
        }
    }
}
.prize-model-image {
}
.reward_money {
    max-height: 372rpx;
    overflow: auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 24rpx 32rpx;
    .reward_money_title {
        font-size: 28rpx;
        background: linear-gradient(272deg, #52100a 3%, #a34354 99%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        margin-bottom: 16rpx;
    }
    .reward_money_content {
        .coupon_codes {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8rpx;
            .coupon_code_item {
                color: rgba(68, 23, 20, 0.6);
                font-size: 20rpx;
                font-weight: 500;
                padding: 8rpx 4rpx;
                border-radius: 8rpx;
                white-space: nowrap;
                text-align: center;
                min-height: 48rpx;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}

.my_prize_item {
    display: flex;
    align-items: center;
    height: 52rpx;
    justify-content: space-between;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a83e1b5f9703920010852.png')
        no-repeat center center;
    background-size: 100% 100%;
    margin-bottom: 16rpx;
    padding: 0rpx 20rpx 0rpx 26rpx;
    .my_prize_item_code {
        font-size: 20rpx;
        color: rgba(68, 23, 20, 0.6);
    }
    .my_prize_item_prize {
        font-size: 28rpx;
        width: 302rpx;
        padding-left: 28rpx;
        font-weight: 600;
        color: #ec225b;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .line_clamp {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
        }
    }
}
.no_prize {
    width: 100%;
    height: 310rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a84269cf06a8480010857.png')
        no-repeat center center;
    background-size: contain;
}
.prize-model-close-image {
    width: 56rpx;
    height: 56rpx;
    margin-top: 60rpx;
    transform: translateY(-96rpx);
}
</style>
