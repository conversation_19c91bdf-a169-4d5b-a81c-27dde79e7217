<template>
  <div class="activity-banner">
    <view class="member-level">
      <MemberLevel />
    </view>
    <!-- <CardStackNew :swiperList="swiperList" /> -->
    <CardStack
      :autoPlay="true"
      :autoPlayInterval="5000"
      :showIndicators="false"
      :visibleCount="3"
      :swipeThreshold="50"
      :enableSwipe="true"
      :swiperList="swiperList"
      @card-change="handleCardChange"
      @card-click="handleCardClick"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import CardStack from '../CardStack/index.vue';
// import CardStackNew from '../CardStack/indexNew.vue';
import { getTagList } from '@/http/requestGo/community';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import MemberLevel from './MemberLevel.vue';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import { mineInfo } from '@/http/mine';

@Component({
  components: {
    CardStack,
    MemberLevel,
    // CardStackNew,
  },
})
export default class ActivityBannerComponent extends Vue {
  public swiperList: any[] = [];

  async created() {
    await this.fetchTagBannerList();
  }

  /**
   * 获取服务列表
   */
  public async fetchTagBannerList() {
    try {
      const version = AppModule.VersionCode;
      const res: any = await getTagList({ type: 23, page_size: 30, is_show: 1, tag_version: `${version}` });
      // 根据接口返回的数据结构取 list
      const tagList = (res?.data?.list || res?.data || []) as any[];
      // 仅取 item.version 不超过当前版本的条目
      const validList = tagList.filter((item) => {
        const v = Number(item.version || 0);
        return v !== 0 && v <= Number(version);
      });

      // 找到 <= version 的最大版本号
      const maxVersion = validList.reduce((max, item) => {
        const v = Number(item.version);
        return v > max ? v : max;
      }, -Infinity);

      const extraList = validList
        .filter((item) => Number(item.version) === maxVersion)
        .map((item) => ({
          ...item,
          image: item.icon || '',
          jumpUrl: item.jump_url || '',
          type: item.jump_type || '',
          version: item.version || '',
          // 接口返回 is_show 值为 "1" 或 "2"，此处统一转成字符串便于后续比较
          isShow: String(item.is_show || ''),
        }));
      // 仅保留 isShow 为 "1" 的数据
      const visibleExtraList = extraList.filter((extra) => extra.isShow === '1');
      this.swiperList = [...visibleExtraList];
    } catch (error) {
      console.error('获取服务列表失败', error);
    }
  }

  handleCardChange(card: any, index: number) {
    // 可以在这里添加更多的业务逻辑
    this.$emit('card-change', card, index);
  }

  handleCardClick(card: any) {
    console.log('🎯 卡片点击:', card);
    const { type, jumpUrl } = card;
         Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .HOME_BANNER_BANNER_EXPOSURE,
            name: card.text,
        });

        if (['manager', 6].includes(type)) { // 在线客服
            Utils.decryptContact('', BuType.BuType_NONE);
        } else if (['device', 10].includes(type)) { // 设备
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        } else if (['chuxing', 9].includes(type)) { // 出行
            Utils.newMessageChannel('PAGE', 'external', { path: `${jumpUrl}` });
        } else if (['content', 8].includes(type)) { // 觅友空间
            Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' });
        } else if (type === 'ambassador') {
            mineInfo().then(response => {
                const { is_employee } = response
                const isEmployee = is_employee === 1
                if (isEmployee) {
                    Utils.navigateTo(`/pagesC/ambassador/detail/index`);
                } else {
                    Utils.navigateTo(`/pagesC/ambassador/login/index`);
                }
            })
        } else {
            Utils.navigateTo(`${jumpUrl}`);
        }
    // 记录点击事件日志
    // Utils.logTrace(
    //   {
    //     module: Constants.LOG_TRACE_MODULE_DREAME,
    //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.HOME_BANNER_CLICK,
    //     id: Number(card.id),
    //     name: card.jumpUrl || card.text
    //   },
    //   true,
    // );

    // // 处理跳转逻辑
    // if (card.jumpUrl && card.jumpUrl !== '#') {
    //   // 如果有有效的跳转链接，执行跳转
    //   Utils.navigateTo(card.jumpUrl);
    // } else {
    //   // 如果没有跳转链接，可以显示提示或执行其他逻辑
    //   console.log('该卡片暂无跳转链接:', card.text);
    //   // 这里可以根据需要添加其他处理逻辑，比如：
    //   // Utils.Toast('功能即将上线，敬请期待', 1500);
    // }

    // // 向上级父组件传递点击事件
    // this.$emit('card-click', card);
  }
}
</script>

<style lang="scss" scoped>
.activity-banner{
    position: relative;
    padding: 0 20px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.member-level{
  position: absolute;
  right: 46rpx;
  top: 10rpx;
  background: #F7F7F7;
  width: 80rpx;
  height: 80rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
</style>
