<template>
    <view class="switch-button-wrap">
        <text class="switch-title">热门商品</text>
        <view class="switch-button" @click="handleSwitchBtn">
            <block v-if="choosed">
                <view class="switch-icon list-Icon"></view>
            </block>
            <block v-else>
                <view class="switch-icon noLists-Icon"></view>
            </block>

            <text class="switch-text">视图切换</text>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
    }
})
export default class SwitchButton extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: [Number, String], default: '99' })
    readonly icon!: any;

    @Prop({ type: [Number, String], default: '99' })
    readonly text!: any;

    @Prop({ type: [Boolean], default: true })
    readonly choosed!: any;

    handleSwitchBtn() {
        this.$emit('update:choosed', !this.choosed);
    }
}
</script>

<style lang="scss" scoped>
.switch-button-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56rpx;
    padding: 0 24rpx 0 38rpx;
    margin-top: 16rpx;
    margin-bottom: 16rpx;
    background-color: #f6f6f6;
    .switch-title{
        font-size: 24rpx;
        font-weight: 800;
        line-height: normal;
        color: #333333;
    }
    .switch-button{
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.04);
        padding: 10rpx;
        border-radius: 16rpx;
        width: 150rpx;
        height: 56rpx;
        .list-Icon{
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68847800d09208540010612.png');
            background-repeat: no-repeat;
            background-size: cover;
        }
        .noLists-Icon{
            background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688477fee55409390010996.png');
            background-repeat: no-repeat;
            background-size: cover;
        }
         .switch-icon{
            width: 36rpx;
            height: 36rpx;
            margin-right: 8rpx;
        }
        .switch-text{
            font-size: 20rpx;
            font-weight: 500;
            color: #404040;
        }
    }

}
</style>
