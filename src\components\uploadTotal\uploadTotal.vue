<template>
    <u-popup :show="show" mode="bottom" :round="19" @close="onClose" :safeAreaInsetBottom="false">
        <view class="total u-flex-col" @touchmove.stop.prevent>
            <scroll-view class="u-flex-1" scroll-y>
                <view class="select">
                    <view v-for="(value, index) in selectList" :key="index" class="select_item">
                        <view class="sel" @click="upload(index)">{{value}}</view>
                    </view>
                </view>
                <view class="blank">
                        <u-button class="btn" @click="onClose">取消</u-button>
                </view>
            </scroll-view>
        </view>
    </u-popup>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component
export default class PopServiceItem extends Vue {
    @Prop({ type: Boolean, default: false })
    readonly show!: Boolean;

    onClose() {
        this.$emit('show', false);
    }

    public selectList:any = ['拍照', '从相册选择'];

    public selectImage:any = ['camera', 'album'];

    upload(index) {
        const _this = this
        uni.chooseImage({
            count: 9,
            sizeType: ['original', 'compressed'],
            sourceType: [`${this.selectImage[index]}`],
            success: function (res) {
            _this.setImageList(res.tempFilePaths[0])
            }
        })
    }

    setImageList(data) {
        this.$emit('imageList', data)
        this.$emit('show', false);
    }
}
</script>

<style lang="scss" scoped>
.total {
    height: 486rpx;
    width: 100%;
    box-sizing: border-box;
    overflow-y: auto;

    .u-flex-1 {
        overflow: auto;
        background-color: #F4F4F4;
        border-radius: 192rpx 192rpx 0rpx 0rpx;
        .select {
            height: 270rpx;
            background-color: #ffffff;
            .select_item:nth-child(1) {
                margin-bottom: -16rpx;
            }
            .select_item {
                padding: 56rpx 80rpx;
                text-align: center;
                color: #121212;
                font-size: 32rpx;
                font-weight: 500;
                &+.select_item {
                    border-top: 2rpx solid #D8D8D8;
                    .sel {
                        margin-top: -16rpx;
                    }
                }
            }
        }

        .blank {
            height: 192rpx;
            background-color: #ffffff;
            margin-top: 24rpx;
            padding: 38rpx 38rpx 70rpx;
            .btn {
                height: 86rpx;
                background-color: #E8DEC1;
                border-radius: 192rpx;
                color: #6C4314;
                font-size: 32rpx;
                font-weight: 500;
            }
        }
    }
}
</style>
