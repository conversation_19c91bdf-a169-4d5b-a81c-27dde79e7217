<template>
    <view v-if="liveImage && isShow && liveText" class="live" @click="gotoLiveRoom">
        <image class="image" :src="liveImage"></image>
        <view class="text">{{ liveText }}</view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { getLiveList } from '@/http/live';
import { watchLive } from '@/http/vip';
import Constants from '@/common/Constants'
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';

@Component
export default class Notice extends Vue {
    @Prop({
        type: Boolean,
        default: true
    })
    readonly isShow;

    public LIVE_STATUS_LIVE: String = 'https://wpm-cdn.dreame.tech/images/202309/64f19645c62e38111397084.png'
    public LIVE_STATUS_TO_BEGIN: String = 'https://wpm-cdn.dreame.tech/images/202309/64f1965fdc4699021398160.png'
    public LIVE_STATUS_END: String = 'https://wpm-cdn.dreame.tech/images/202309/64f19672ec9899691398134.png'
    public liveImage: String = ''
    public LIVE_STATUS_LIVE_TEXT: String = '直播中'
    public LIVE_STATUS_TO_BEGIN_TEXT: String = '直播预告'
    public LIVE_STATUS_END_TEXT: String = '直播结束'
    public liveText: String = ''
    public liveRoomId: number = 0

    gotoLiveRoom() {
        const roomId = this.liveRoomId
        const customParams = encodeURIComponent(JSON.stringify({ path: 'pages/index/index', pid: 1 })) // 开发者在直播间页面路径上携带自定义参数（如示例中的 path 和pid参数），后续可以在分享卡片链接和跳转至商详页时获取，详见【获取自定义参数】、【直播间到商详页面携带参数】章节（上限600个字符，超过部分会被截断）
        if (this.liveImage === this.LIVE_STATUS_LIVE && this.liveText === this.LIVE_STATUS_LIVE_TEXT) {
            watchLive({})
        }
        Utils.reportEvent('live', {
            live_status: this.liveText
        })
        uni.navigateTo({
            url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${roomId}&custom_params=${customParams}`,
            success() {
                AppModule.setLiveMark('wx_live')
            }
        })
    }

    async getLiveList(isJumpLive) {
        try {
            const list = await getLiveList();
            const liveList = list.filter(l => l.live_status == Constants.LIVE_STATUS_LIVE)
            const toLiveList = list.filter(l => l.live_status == Constants.LIVE_STATUS_TO_BEGIN)
            const endList = list.filter(l => l.live_status == Constants.LIVE_STATUS_END)
            // 当有正在进行的直播时，入口状态呈现为【直播中】
            // 当尚未有开播的直播时，入口状态呈现【直播预告】
            // 当最后一场直播结束时，入口状态呈现为【直播结束】
            if (liveList.length > 0) {
                this.liveImage = this.LIVE_STATUS_LIVE
                this.liveText = this.LIVE_STATUS_LIVE_TEXT
                this.liveRoomId = liveList[0].roomid
            } else if (toLiveList.length > 0) {
                this.liveImage = this.LIVE_STATUS_TO_BEGIN
                this.liveText = this.LIVE_STATUS_TO_BEGIN_TEXT
                this.liveRoomId = toLiveList[toLiveList.length - 1].roomid
            } else if (endList.length > 0) {
                this.liveImage = this.LIVE_STATUS_END
                this.liveText = this.LIVE_STATUS_END_TEXT
                this.liveRoomId = endList[0].roomid
            } else {
                this.liveImage = ''
                this.liveText = ''
            }
            isJumpLive && this.liveImage && this.liveText && this.gotoLiveRoom()
        } catch (e) {
            this.liveImage = ''
            this.liveText = ''
            console.error('getLiveList e=', e);
        }
    }
}
</script>

<style lang="scss" scoped>
.live {
    width: 96rpx;
    height: 96rpx;
    @include flex(column, center, center, none);
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 8rpx 32rpx 0px rgba(0, 0, 0, 0.11999999731779099);
    border: 2rpx solid #E2E2E2;
    border-radius: 50%;
    z-index: 100;

    .image {
        width: 42rpx;
        height: 42rpx;
    }

    .text {
        font-size: 20rpx;
        color: $text-color-primary;
        line-height: 28rpx;
    }
}
</style>
