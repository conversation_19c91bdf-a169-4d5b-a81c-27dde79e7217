<template>
    <view class="container">
        <!-- 头部导航栏 -->
        <custom-bar title="金币兑换" background="transparent" position="relative">
            <view slot="customTitle">金币兑换</view>
        </custom-bar>
        <!-- 头部分区 -->
        <view class="header-section">
            <view class="coins-points-info">
                <view class="info-item">
                    <view class="info-item-top">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a618cb03547220012265.png"
                            class="icon"
                        />
                        <text class="label">我的金币</text>
                    </view>
                    <view class="info-content">
                        <text class="value">{{ totalCoins }}</text>
                    </view>
                </view>
                <view class="info-line"></view>
                <view class="info-item">
                    <view class="info-item-top">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874d48142b172730019223.png"
                            class="icon"
                        />
                        <text class="label">我的积分</text>
                    </view>
                    <view class="info-content">
                        <text class="value">{{ totalPoints }}</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 兑换列表 -->
        <view class="exchange_container">
            <view class="exchange_container_desc">
                <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874d1c0eefc19790011163.png"
                    class="exchange_container_desc_img"
                />
                <view class="exchange_container_desc_text">
                    <view class="exchange_container_desc_text_title">恭喜您</view>
                    <view class="exchange_container_desc_text_desc">获得金币兑换积分资格 </view>
                    <view class="exchange_container_color_line"> 1000金币兑换1积分 </view>
                    <view class="exchange_container_desc_text_gray"> 积分可在消费时抵扣现金 </view>
                </view>
                <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874d1be1f6b91290023836.png"
                    class="exchange_container_desc_img"
                />
            </view>
            <view class="exchange-list">
                <view class="exchange-item" v-for="(item, index) in exchangeItems" :key="index">
                    <view class="item-left">
                        <image
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6874d48142b172730019223.png"
                            class="points-icon"
                        />
                        <view class="points-info">
                            <view class="points-value">{{ item.pointsValue }} 积分</view>
                        </view>
                    </view>
                    <view class="item-right">
                        <view class="coins-required">
                            <image
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a618cb03547220012265.png"
                                mode="scaleToFill"
                                class="coins-icon"
                            />
                            <text class="coins-text">{{ item.coinsRequired }}</text>
                        </view>
                        <view
                            class="exchange-btn"
                            @click="handleExchange(item)"
                        >
                        立即兑换
                        </view>
                    </view>
                </view>
                <view class="exchange-list-desc">
                    说明：金币和积分属于虚拟商品，一经兑换概不退还
                </view>
            </view>
            <u-modal :show="show" :content='content' width="638rpx">
            <template #default>
                <view class="modal-content">
                    {{ content }}
                </view>
            </template>
            <template #confirmButton>
               <view class="confirm-button-container">
                <view class="cancel-button" @click="handleCancel"> 取消兑换 </view>
                <view class="confirm-button" @click="handleConfirm"> 确认兑换 </view>
               </view>
            </template>
        </u-modal>
        </view>
        <custom-toast ref="customToast" />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import { exchangeCoinToPoints, getCoinInfo, getCoinConfig } from '@/http/coin';
import Utils from '@/common/Utils';

@Component({
    components: {},
})
export default class CoinExchange extends Vue {
    public totalCoins: number = 0;

    public totalPoints: number = 0;

    public exchangeItems: Array<any> = [];

    public show: boolean = false;

    public title: string = '';

    public content: string = '';

    public currentExchangeItem: any = null;

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    async onLoad() {
        this.getCoinConfig();
        this.getUserGoldAndPoints();
    }

    // 判断是否可以兑换
    canExchange(coinsRequired: number): boolean {
        return this.totalCoins >= coinsRequired;
    }

    async getUserGoldAndPoints() {
        const res = await getCoinInfo();
        this.totalCoins = res.totalGold;
        this.totalPoints = res.totalPoints;
    }

    async getCoinConfig() {
        const res = await getCoinConfig();
        this.exchangeItems = res.exchange_gold_list.map((item: any) => ({
            id: item.id,
            pointsValue: item / res.exchange_gold_rate,
            coinsRequired: item,
        }));
    }

    // 处理兑换逻辑
    async handleExchange(item: any) {
        if (!this.canExchange(item.coinsRequired)) {
            Utils.Toast('金币不足，快去做任务得金币', 2000, null, 'width:400rpx');
            return;
        }
        // 保存当前要兑换的item
        this.currentExchangeItem = item;
        // 弹出确认框
        this.show = true;
        this.title = '提示';
        this.content = `请确认兑换${item.pointsValue}积分`;
    }

    // 处理取消兑换
    handleCancel() {
        this.show = false;
        this.currentExchangeItem = null;
    }

    // 处理确认兑换
    async handleConfirm() {
        if (!this.currentExchangeItem) {
            return;
        }

        this.show = false;

        try {
            // 调用兑换接口
            const params = {
                use_gold: this.currentExchangeItem.coinsRequired,
            };

            await exchangeCoinToPoints(params);
            Utils.Toast(`兑换${this.currentExchangeItem.pointsValue}积分成功！`, 2000);
            // 更新本地数据
            this.totalCoins -= this.currentExchangeItem.coinsRequired;
            this.totalPoints += this.currentExchangeItem.pointsValue;
            // 清空当前兑换item
            this.currentExchangeItem = null;
        } catch (error) {
            console.error('兑换失败:', error);
            Utils.Toast('兑换失败，请稍后重试', 2000);
        }
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }
}
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687662dd007b10020011339.png') no-repeat;
    background-size: 100% 100%;
}

.header-section {
    padding: 0 32rpx 0;
    .coins-points-info {
        background: #ffffff;
        border-radius: 24rpx 24rpx 0 0;
        padding: 36rpx 79rpx 15rpx;
        display: flex;
        align-items: center;
        position: relative;
        justify-content: space-between;
        .info-item {
            display: flex;
            flex-direction: column;
            width: 50%;
            flex: 1;
            &:first-child {
                margin-right: 40rpx;
            }
            .info-item-top {
                display: flex;
                align-items: center;
                justify-content: center;
                .icon {
                    width: 47rpx;
                    height: 47rpx;
                    margin-right: 20rpx;
                }
                .label {
                    font-size: 31rpx;
                    color: #121212;
                    font-weight: 500;
                    margin-bottom: 8rpx;
                }
            }
            .info-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                .value {
                    font-size: 64rpx;
                    font-weight: 600;
                    color: #fe7f3c;
                }
            }
        }
        .info-line {
            width: 1rpx;
            height: 72rpx;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 50%;
            transform: translateY(-50%);
            background: #f5f5f5;
        }
    }
}

.exchange_container {
    background: linear-gradient(180deg, #ffe7d9 0%, #fffcf6 27%, #ffffff 100%);
    padding: 80rpx 39rpx 40rpx;
    border-radius: 30rpx 30rpx 0 0;
    flex: 1;
    .exchange_container_desc {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .exchange_container_desc_img {
            width: 63rpx;
            height: 127rpx;
        }
        .exchange_container_desc_text {
            font-size: 48rpx;
            color: #000000;
            font-weight: bold;
            margin-bottom: 16rpx;
            text-align: center;
            .exchange_container_color_line {
                background: linear-gradient(99deg, #ff8d50 7%, #f9580d 51%, #ff2e20 94%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-top: 10rpx;
                text-fill-color: transparent;
            }
            .exchange_container_desc_text_gray {
                font-size: 28rpx;
                color: #888888;
                font-weight: 500;
            }
        }
    }
    .exchange-list {
        margin-top: 71rpx;
        .exchange-list-desc{
            font-size: 24rpx;
            color: #888888;
            font-weight: normal;
            margin-top: 180rpx;
            text-align: center;
        }
        .exchange-item {
            background: #ffffff;
            border-radius: 22rpx;
            padding: 32rpx;
            margin-bottom: 24rpx;
            display: flex;
            background: linear-gradient(90deg, #FFF8E4 0%, #FFF7DF 100%);
            justify-content: space-between;
            align-items: center;
            .item-left {
                display: flex;
                align-items: center;
                justify-content: center;
                .points-icon {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 16rpx;
                }
                .points-info {
                    .points-value {
                        font-size: 32rpx;
                        font-weight: 600;
                        color: #121212;
                        margin-right: 8rpx;
                    }
                }
            }
            .item-right {
                display: flex;
                flex-direction: column;
                .coins-required {
                    text-align: right;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .coins-icon {
                        width: 30rpx;
                        height: 30rpx;
                        margin-right: 8rpx;
                    }
                    .coins-text {
                        font-size: 23rpx;
                        font-weight: normal;
                        color: #777777;
                    }
                }
                .exchange-btn {
                    width: 163rpx;
                    height: 63rpx;
                    border-radius: 49rpx;
                    padding: 11rpx 23rpx;
                    background: linear-gradient(111deg, #FF8D50 7%, #F9580D 52%, #FF2E20 95%);
                    color: #ffffff;
                    font-size: 28rpx;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;
                    margin-top: 10rpx;
                    color: #FFFFFF;
                }
            }
        }
    }

}
.confirm-button-container {
        display: flex;
        justify-content: space-between;
        .confirm-button {
            width: 242rpx;
            height: 96rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50rpx;
            background: #E8DEC1;
            color: #8C6533;
            font-size: 28rpx;
            font-weight: 500;
        }
        .cancel-button {
            width: 242rpx;
            height: 96rpx;
            display: flex;
            font-size: 28rpx;
            align-items: center;
            justify-content: center;
            border-radius: 50rpx;
            background: #EEEEEE;
            color: #121212;
        }
    }

.modal-content{
    font-size: 36rpx;
    color: #121212;
    font-weight: 500;
    text-align: center;
    margin-top: 20rpx;
}
:deep(.u-modal__content){
    padding: 0;
}
</style>
