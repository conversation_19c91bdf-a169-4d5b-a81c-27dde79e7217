<template>
    <view>
        <view class="checkIn_box">
            <!-- 顶部 -->
            <view class="header">
                <view class="myPetal" @click="showMyPetal">
                    <text>我的花瓣：{{ myPetal.count || 0 }}</text>
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7d8691cd911180011372.png"
                        mode="widthFix"
                    ></image>
                </view>
                <view class="checkInTip">
                    <u-switch
                        v-model="isCheckInTip"
                        @change="CheckInTipChange"
                        inactiveColor="#D1C9C6"
                        activeColor="#EC225B"
                        size="10"
                        :disabled="!$isInApp()"
                    ></u-switch>
                </view>
            </view>
            <!-- 花瓣列表 -->
            <scroll-view class="checkInListContainer" scroll-x>
                <view class="checkInList">
                    <view
                        class="checkInItem"
                        v-for="(item, index) in checkInList"
                        :key="index"
                        :class="[
                            { drawItem: index === checkInList.length - 1 },
                            { qixiItem: getDate(item.day) === '8.29' },
                        ]"
                    >
                        <view class="petalCount">
                            <text>+{{ item.numValue }}</text>
                        </view>
                        <view class="petalImage">
                            <image :src="getPetalImage(item)" mode="widthFix"></image>
                        </view>
                        <view class="petalStatus">
                            <view v-if="getDate(item.day) === '8.31'" class="drawIcon">
                                <text>开奖日</text>
                                <image
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a81ce616e560940011557.png"
                                    mode="widthFix"
                                ></image>
                            </view>
                            <view :class="item.classes" @click="checkIn(item)">{{
                                getPetalStatus(item.day, item.status)
                            }}</view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
        <u-popup
            :show="showMyPetalPopup"
            @close="showMyPetalPopup = false"
            mode="center"
            bgColor="transparent"
            :closeOnClickOverlay="false"
        >
            <!-- 有花瓣码 -->
            <view v-if="myPetal.count > 0" class="popupContainer myPetalPopup hasPetalCode">
                <scroll-view
                    class="myPetalPopupContent"
                    scroll-y
                    :enable-flex="true"
                    style="display: flex; flex-direction: column; justify-content: center"
                >
                    <view class="petalCodeItem" v-for="(item, index) in myPetal.list" :key="index">
                        <view class="petalCodeItemContent">
                            <text>花瓣码 -</text><text>{{ item.lucky_sign_code }}</text>
                        </view>
                        <view class="petalCodeTime">
                            <text>获得时间：</text>
                            <text>{{ getTime(item.get_time) }}</text>
                        </view>
                    </view>
                </scroll-view>
                <view class="closeBtn myPetalPopupBtn" @click="showMyPetalPopup = false">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82181277841620011623.png"
                        mode="widthFix"
                    ></image>
                </view>
            </view>
            <!-- 无花瓣码 -->
            <view v-else class="popupContainer myPetalPopup noPetalCode">
                <view class="myPetalPopupContent">
                    <view class="over doBtn" @click="showMyPetalPopup = false"> </view>
                </view>
                <view class="closeBtn myPetalPopupBtn" @click="showMyPetalPopup = false">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82181277841620011623.png"
                        mode="widthFix"
                    ></image>
                </view>
            </view>
        </u-popup>
        <u-popup
            :show="overPopup"
            @close="overPopup = false"
            mode="center"
            bgColor="transparent"
            :closeOnClickOverlay="false"
        >
            <view class="popupContainer overPopup">
                <view class="overBtn" @click="overPopup = false"> </view>
                <view class="closeBtn" @click="overPopup = false">
                    <image
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82181277841620011623.png"
                        mode="widthFix"
                    ></image>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { UserModule } from '@/store/modules/user';
import { CheckAppJump } from '@/common/decorators';
import {
    activityCheckInIn,
    activityMyCouponCode,
    activitySubscribeInfo,
    activitySubscribe,
} from '@/http/activity/shopFestival';
@Component
export default class CheckIn extends Vue {
    @Prop({ type: Object, required: true }) info: any;
    @Prop({ type: Number, required: true }) overTime: number;
    @Prop({ type: String, default: '' }) link!: string;
    // public curDate = '2025-08-26';
    public secondsData = 5;
    public timerId: any = null;
    public isCheckInTip: Boolean = false;
    public showMyPetalPopup: Boolean = false;
    public myPetal: any = {};
    public overPopup: Boolean = false;
    public petalImage = [
        {
            claimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada23146640010973.png',
            notClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada14f06610011486.png',
            unClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a81b8e583483610208245.png',
        },
        {
            claimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada203c6640011266.png',
            notClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada15166610012050.png',
            unClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a81b8e574483570011791.png',
        },
        {
            claimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada32036680011382.png',
            notClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a7dfada405d6720010562.png',
            unClaimed:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a81b8e584503620011826.png',
        },
    ];

    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    get checkInData() {
        return this.info.extra;
    }

    get checkInList() {
        const list = this.checkInData.list || [];
        list.forEach((item: any) => (item.classes = this.getStatusClass(item.day, item.status, item.canCheck)));
        return list;
    }

    async created() {
        this.initData();
    }

    async initData() {
        activitySubscribeInfo({
            user_id: this.user_id,
            ac_id: this.info.activity_id,
        }).then((data: any) => {
            this.isCheckInTip = data.subscribe === '1';
        });
        this.myPetal = await activityMyCouponCode({
            activity_id: this.info.activity_id,
        });
    }

    @CheckAppJump()
    async showMyPetal() {
        this.showMyPetalPopup = true;
    }

    @CheckAppJump()
    async CheckInTipChange(value: boolean) {
        this.isCheckInTip = value;
        try {
            await activitySubscribe({
                user_id: this.user_id,
                ac_id: this.info.activity_id,
                subscribe: value ? 1 : 0,
            });
        } catch (error) {
            this.isCheckInTip = !value;
        }
    }

    // 打卡（防抖处理）
    private checkInDebounce = false;
    // 打卡
    @CheckAppJump()
    async checkIn(item: any) {
        if (this.checkInDebounce) return;
        this.checkInDebounce = true;
        try {
            // 打卡活动已结束
            if (Number(this.overTime) * 1000 - new Date().getTime() < 0) {
                this.overPopup = true;
                return;
            }
            if (this.getDate(item.day) !== this.getDate()) {
                Utils.Toast('只能打卡今日');
                return;
            }
            if (item.status === 0 && item.canCheck === 1) {
                // 打卡
                await this.checkInIn({
                    activity_id: this.info.activity_id,
                    member_center_save_id: this.checkInData.member_center_save_id,
                });
                item.status = 1;
                Utils.Toast('打卡成功');
                this.initData();
                this.$emit('refresh');
                return;
            }
            if (item.status === 1) {
                Utils.Toast('已打卡');
                return;
            }
            if (item.canCheck === 0) {
                Utils.Toast('不可打卡');
            }
        } finally {
            setTimeout(() => {
                this.checkInDebounce = false;
            }, 800);
        }
    }

    // 打卡接口
    async checkInIn(data: any) {
        await activityCheckInIn(data);
    }

    // 获取花瓣图片
    getPetalImage(item: { day: string; status: number; canCheck: number; numValue: number }) {
        // const formatDay = this.getDate(item.day);
        // 定义花瓣图片索引映射
        const dayIndexMap: { [key: string]: number } = {
            1: 0,
            2: 1,
        };
        // 修复：使用 item.numValue 而不是未定义的 numValue
        const idx = dayIndexMap[item.numValue] ?? 2;
        const petal = this.petalImage[idx];

        if (item.status === 1) {
            return petal.claimed;
        } else if (item.canCheck === 0) {
            return petal.unClaimed;
        } else {
            return petal.notClaimed;
        }
    }

    // 获取时间
    getTime(time: string) {
        const date = new Date(Number(time) * 1000);
        console.log(date, 'date');
        const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
        const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
        const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
        return `${month}/${day} ${hours}:${minutes}`;
    }

    // 获取花瓣状态
    getPetalStatus(day: string, status: number) {
        const formatDay = this.getDate(day);
        if (formatDay === this.getDate() && status === 0) {
            return '打卡';
        } else if (formatDay === this.getDate() && status === 1) {
            return '已打';
        } else if (formatDay === '8.29') {
            return '七夕';
        } else {
            return formatDay;
        }
    }

    // status: 0: 未打卡,1:已打卡 canCheck: 0: 不可打卡,1: 可打卡
    getStatusClass(day: string, status: number, canCheck: number) {
        const formatDay = this.getDate(day);
        const statusClass = ['normal'];
        if (formatDay === '8.29') {
            statusClass.push('qixi');
        }
        if (formatDay === '8.31') {
            statusClass.push('draw');
        }
        if (canCheck === 0) {
            statusClass.push('unClaimed');
        }
        if (status === 1) {
            statusClass.push('checked');
        }
        if (formatDay === this.getDate() && status === 0) {
            statusClass.push('curCheckIn');
        } else if (formatDay === this.getDate() && status === 1) {
            statusClass.push('curChecked');
        }
        // ['normal', 'qixi', 'draw', 'unClaimed', 'checked', 'curCheckIn', 'curChecked']
        return statusClass;
    }

    getDate(dates?: string) {
        const date = dates ? new Date(dates) : new Date();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}.${day}`;
    }

    close() {
        this.$emit('close');
    }
}
</script>
<style lang="scss" scoped>
::v-deep .u-switch {
    overflow: visible !important;
}
::v-deep .u-switch__node {
    width: 30rpx !important;
    height: 30rpx !important;
    transform: translate(-18rpx, -2rpx) !important;
}
::v-deep .u-switch__node--on {
    transform: translate(8rpx, -2rpx) !important;
}
.checkIn_box {
    position: relative;
    width: 686rpx;
    height: 330rpx;
    padding-top: 88rpx;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68abc3fd3c34e2470014679.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .header {
        width: 100%;
        .myPetal {
            position: absolute;
            top: 3%;
            left: 16rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rpx;
            font-size: 28rpx;
            color: #54120c;
            // background: #fff;
            padding: 10rpx;
            z-index: 10;
            image {
                width: 13.7rpx;
                height: 24rpx;
            }
        }
        .checkInTip {
            position: absolute;
            top: 26rpx;
            left: 398rpx;
            // 通过background-image 渐变属性改样式
        }
    }
    .checkInListContainer {
        width: 100%;
        height: 100%;
        padding: 0rpx 22rpx 0rpx;
        overflow: hidden;
        .checkInList {
            width: auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 14rpx;
            .checkInItem {
                flex: 0 0 80rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border-radius: 16rpx;
                background: linear-gradient(180deg, #faf2f6 1%, rgba(250, 250, 250, 0) 100%);
                .petalCount {
                    text-align: center;
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #ec225b;
                    padding: 20rpx 8rpx 14rpx;
                }
                .petalImage {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding-bottom: 34rpx;
                    image {
                        width: 66rpx;
                        height: 66rpx;
                    }
                }
                .petalStatus {
                    font-size: 24rpx;
                    font-weight: 500;
                    width: 100%;
                    color: #3d3d3d;
                    text-align: center;
                    position: relative;
                    .drawIcon {
                        position: absolute;
                        top: -44rpx;
                        left: 0;
                        width: 100%;
                        flex-direction: column;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #c58257;
                        font-size: 20rpx;
                        image {
                            width: 18rpx;
                            height: 10rpx;
                        }
                    }
                    .normal {
                        font-size: 24rpx;
                        font-weight: 600;
                        line-height: 40rpx;
                        padding: 2rpx 0rpx;
                        width: 100%;
                        border-radius: 194rpx;
                    }
                    .unClaimed {
                        opacity: 0.5;
                    }
                    .checked {
                        opacity: 0.5;
                    }
                    .curCheckIn {
                        color: #ffffff;
                        background: linear-gradient(180deg, #e33456 7%, #ef586d 56%, #fe888a 100%);
                    }
                    .qixi {
                        color: #ec225b;
                    }
                    .draw {
                        color: #c58257;
                    }
                    .curChecked {
                        color: #ffffff;
                        background: #d1c9c6;
                    }
                }
            }
            .qixiItem {
                background: linear-gradient(180deg, #ffd4df 1%, rgba(250, 250, 250, 0) 100%);
            }
            .drawItem {
                background: linear-gradient(180deg, #ffdfcb 1%, rgba(250, 250, 250, 0) 100%);
            }
        }
    }

    .popupContainer {
        width: 610rpx;
        height: 650rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .myPetalPopupContent {
            width: 100%;
            height: 430rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 180rpx;
            gap: 20rpx;
            .petalCodeItem {
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82481576a33580011579.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: center center;
                width: 530rpx;
                height: 124rpx;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
                padding-left: 160rpx;
                gap: 12rpx;
                margin: 0 auto 20rpx;
                .petalCodeItemContent {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 38rpx;
                    font-weight: 500;
                    color: #671d1d;

                    text {
                        margin-right: 10rpx;
                    }
                }
                .petalCodeTime {
                    font-size: 24rpx;
                    color: rgba(68, 23, 20, 0.6);
                }
            }
            .over {
                position: absolute;
                left: 50%;
                bottom: 33px;
                width: 231px;
                height: 44px;
                transform: translateX(-50%);
            }
        }
        .closeBtn {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -98rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            image {
                width: 56rpx;
                height: 56rpx;
            }
        }
    }
    .hasPetalCode {
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a821812e4011890010781.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
    }
    .noPetalCode {
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a82e948bcb95730011567.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
    }

    .overPopup {
        width: 612rpx;
        height: 522rpx;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a8607507ffb0330012430.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        .overBtn {
            position: absolute;
            left: 50%;
            bottom: 70rpx;
            width: 472rpx;
            height: 88rpx;
            transform: translateX(-50%);
        }
    }
}
</style>
