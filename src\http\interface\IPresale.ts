export interface IPresale {
    can_pay_deposit: number // 是否可付定金
    can_pay_tail: number // 是否可付尾款
    coefficient: number //
    deposit: string // 定金
    deposit_status: number // 定金状态
    end_payment: number // 结束尾款时间
    expand_price: string // 膨胀金额
    presale_time: number // 预售开始时间
    scheduled_number_now: number //
    start_payment: number // 开始尾款时间
    surplus_time: number //
    tail_order_status: number // 尾款订单状态
    tail_price: string // 尾款
}
