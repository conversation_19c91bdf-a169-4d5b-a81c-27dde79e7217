<template>
  <view class="coin-record u-flex">
    <view class="coin-record-left">
      <view class="action-info" @click="maskShow(itemData)">
        <view class="action-text">{{ itemData.eventName }}</view>
        <image
          src="@/static/cart_arrow.png"
          class="right_arrow"
          v-if="itemData.eventName && itemData.eventName.length >= 15">
        </image>
      </view>
      <view class="time">{{ formatTime(itemData.updateTime) }}</view>
    </view>
    <view class="coin-record-right">
      <view class="coin-amount" :style="colorStyle">
        <text class="amount-text">{{ itemData.numValue > 0 ? '+' : '' }} {{ itemData.numValue }}金币</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator';

@Component({
  components: {},
})

export default class CoinRecord extends Vue {
  @Prop({ type: Object, default: null })
  readonly itemData;

  @Prop({ type: String, default: null })
  readonly colorStyle;

  maskShow(item) {
    if (item.eventName && item.eventName.length >= 15) {
      this.$emit('showMask', item);
    }
  }

  formatTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
}
</script>

<style lang="scss" scoped>
.coin-record {
  width: 100%;
  padding: 18rpx 0;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;

  .coin-record-left {
    width: 70%;

    .action-info {
      display: flex;
      align-items: center;

      .action-text {
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        font-weight: normal;
        color: $text-color-primary;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 0;
        max-width: 400rpx;
      }

      .right_arrow {
        width: 25rpx;
        height: 25rpx;
        margin-left: 8rpx;
      }
    }

    .time {
      font-family: MiSans;
      font-size: 28rpx;
      font-weight: normal;
      color: #777777;
    }
  }

  .coin-record-right {
    position: absolute;
    right: 0rpx;
    top: 50%;
    transform: translateY(-50%);

    .coin-amount {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .coin-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }

      .amount-text {
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}

.coin-record:last-child {
  border-bottom: none;
}
</style>
