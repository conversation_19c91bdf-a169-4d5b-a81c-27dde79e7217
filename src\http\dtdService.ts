/* eslint-disable object-curly-spacing */
import { IBookingModel } from './interface/IBookingModel';
import { ICommentSaveModel } from './interface/ICommentModel';
import http from './index';
/*
 * 预约订单列表
 * @parma {string} type         类型
 * @parma {string} status       状态（0：待支付 1：待服务 2:服务中 3：已取消 4：已完成）
 * @parma {string} source_page  来源（1：全部订单 2：上下水页面）
 * @returns
 */
interface pOrderListParams {
    type: string;
    status?: string;
    source_page: string;
}
export const pOrderList = (params: pOrderListParams, loading: boolean = true) =>
    http.post('main/plumbing/order-list', params, { custom: { loading } });

/*
 * 预约订单详情
 * @parma {string} order_no  预约订单order_no
 * @returns
 */
export const pOrderInfo = (order_no: string) => http.post('main/plumbing/order-info', { order_no });

/*
 * 勘测下单
 * @parma {string} name         联系人
 * @parma {string} phone        联系人电话
 * @parma {string} verify_code  验证码
 * @parma {string} pid          省id
 * @parma {string} cid          市id
 * @parma {string} aid          区id
 * @parma {string} detail       详细地址
 * @parma {string} type         类型（1：勘测预约）
 * @parma {string} context_type 环境类型（1：毛坯房 2：装修中 3：已入住）
 * @parma {string} expect_time  期望时间
 * @parma {string} is_buy       是否购买追觅产品
 * @parma {string} sn           sn编码
 * @parma {string} price        价格
 * @returns
 */
export const pSaveOrder = (params: IBookingModel) =>
    http.post('main/plumbing/order-save', params, { custom: { loading: true, title: '正在提交' } });

/*
 * 发送验证码
 * @parma {string} phone  手机号
 * @returns
 */
export const pSendCode = (phone: string) =>
    http.post('main/plumbing/send-code', { phone }, { custom: { loading: true, title: '正在获取验证码' } });

/*
 * 查询区域记录
 * @parma {string} phone  手机号
 * @returns
 */
interface pSearchParams {
    pid: string;
    cid: string;
    aid: string;
}
export const pSaveSearch = (params: pSearchParams) => http.post('main/plumbing/search-save', params);

/*
 * 价格配置信息
 * @returns
 */
export const priceConfig = () => http.post('main/plumbing/price-config', {});

/*
 * 退款列表
 * @returns
 */
export const refundList = () => http.post('main/plumbing/refund-list', {});

/*
 * 申请退款
 * @parma {String} order_no 订单号【必要】
 * @parma {String} og_ids 订单商品-id【必要】
 * @parma {String} m_type 退款类型 1：退款 2：退货退款
 * @parma {String} r_type 退款原因
 * @returns
 */
interface OrderRefunParams {
    type: string;
    order_no: string;
    sn: string;
    images: string;
    describe: string;
    buy_order_no: string;
    channel: string;
}
export const orderApplyRefund = (params: OrderRefunParams) =>
    http.post('main/plumbing/apply-refund', params, { custom: { loading: true, title: '正在提交' } });

/*
 * 退款详情
 * @parma {String} refund_no 退款订单号【必要】
 * @returns
 */
export const refundInfo = (refund_no: string) => http.post('main/plumbing/refund-info', { refund_no });

/*
 * sn配置列表
 * @param {String} type 类型
 * @returns
 */
export const snConfig = (params) => http.post('main/plumbing/sn-config', params);

/*
 * 评价记录
 * @param {String} type 类型
 * @param {String} order_no 订单ID
 * @param {String} overall_grade 总体评价等级
 * @param {String} speed_grade 上门速度等级
 * @param {String} manner_grade 服务态度等级
 * @param {String} specialty_grade 服务态度等级
 * @param {String} brand_grade 专业能力等级
 * @param {String} content 评论内容
 * @param {String} tags 标签
 * @param {String} images 图片
 * @returns
 */
export const commentSave = (params: ICommentSaveModel) =>
    http.post('main/plumbing/comment-save', params, { custom: { loading: true, title: '正在提交' } });

/*
 * 标签列表
 * @returns
 */
export const tagConfig = () => http.post('main/plumbing/tag-config', {});

/*
 * 产品类型选择列表
 * @returns
 */
export const productType = () => http.post('main/plumbing/product-type', {});

// /*
//  * 产品信息——上门指导
//  * @returns
//  */
// export const productInfo = () => http.post('main/plumbing/sn-config', {});

/*
 * 勘测下单
 * @parma {string} name         联系人
 * @parma {string} phone        联系人电话
 * @parma {string} verify_code  验证码
 * @parma {string} pid          省id
 * @parma {string} cid          市id
 * @parma {string} aid          区id
 * @parma {string} detail       详细地址
 * @parma {string} type         类型（1：勘测预约）
 * @parma {string} context_type 环境类型（1：毛坯房 2：装修中 3：已入住）
 * @parma {string} expect_time  期望时间
 * @parma {string} sn           sn编码
 * @parma {string} product_type 产品类型
 * @returns
 */
export const sSaveOrder = (params: IBookingModel) =>
    http.post('main/plumbing/service-order-save', params, { custom: { loading: true, title: '正在提交' } });

/*
 * 滑块验证
 * @returns
 */
export const plumbingSendCode = (params) => http.post('main/plumbing-activity/send-code', params);

/*
 * 提交-上门服务
 * @returns
 */
export const plumbingSaveOrder = (params) => http.post('main/plumbing-activity/service-order-save', params);
