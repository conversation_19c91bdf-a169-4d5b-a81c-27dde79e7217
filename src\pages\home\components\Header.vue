<template>
    <div class="header-container">
        <div class="logo-box">
            <img
                class="logo-image"
                alt=""
                src="https://wpm-cdn.dreame.tech/images/202308/64e3034aac1717041228215.png"
            />
            <div class="logo-text">追觅官方商城</div>
        </div>
        <div class="icon-box">
            <img
                alt=""
                class="icon-image"
                src="https://wpm-cdn.dreame.tech/images/202306/517617-1686122160024.png"
            />
            <div @click="goMessages">
                <img
                    alt=""
                    class="icon-image message-icon"
                    src="./icon-notice.png"
                />
            </div>
            <img
                alt=""
                class="icon-image"
                src="https://wpm-cdn.dreame.tech/images/202306/225448-1686107296428.png"
            />
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

export default {
    name: 'Header',
    methods: {
        // 跳转到消息页面
        goMessages() {
            Utils.newMessageChannel('PAGE', 'push', { path: '/message_main' });
        }
    }
}
</script>

<style scoped>
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 100rpx 32rpx 0 32rpx;
}
.logo-box {
}
.logo-image {
    width: 220rpx;
    height: 30rpx;
}
.message-icon {
    margin: 0 36rpx;
}
.logo-text {
    font-size: 28rpx;
    color: #121212;
    margin-top: 10rpx;
}
.icon-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.icon-image {
    width: 48rpx;
    height: 48rpx;
}
</style>
