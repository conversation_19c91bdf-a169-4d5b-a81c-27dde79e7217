<template>
    <view>
        <view class="container">
            <view class="header-container" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="notice-bar">
                    <u-notice-bar
                        text="10000金币兑换1元, 用户连续30天未访问APP，金币清零"
                        bgColor="transparent"
                        color="#fff"
                        fontSize="24rpx"
                        :speed="60"
                    ></u-notice-bar>
                </view>
                <view class="header">
                    <view class="left">
                        <image
                            class="search_back"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686e528986f1d5530025252.png"
                            @click="goBack"
                        ></image>
                    </view>
                    <view class="header_title_img">
                        <text>我的金币</text>
                    </view>
                    <view class="right">
                        <view class="rule" @click="openRulePopup"></view>
                    </view>
                </view>
                <view class="amount">
                    <view class="amount-label">金币余额</view>
                    <view>
                        <text class="amount-number">{{ coinCount }}</text>
                    </view>
                    <view class="amount-s">
                        <!-- 保留两位小数 -->
                        折合现金 {{ Number(coinCount) > 0 && Number(goldConversion) > 0 ? truncateTwoDecimals(coinCount / goldConversion) : 0 }}元
                    </view>
                </view>
                <view class="amount-btn" @click="goToMall"> 去购物 </view>
            </view>
            <view class="content" :style="{ height: `calc(100vh - 450rpx)`}">
                <view class="tabs-header">
                    <view
                        class="tab-item"
                        v-for="(item, index) in tabList"
                        :key="item.id"
                        :class="{ active: current === index }"
                        @click="tabClick(index)"
                    >
                        <text class="tab-text">{{ item.name }}</text>
                        <view class="tab-line" v-if="current === index"></view>
                    </view>
                </view>
                <swiper
                    class="swiper-box"
                    :current="swiperCurrent"
                    @change="swiperChange"
                    @animationfinish="animationfinish"
                    :style="{ height: 'calc(100% - 100rpx)' }"
                >
                    <!-- 获取明细 -->
                    <swiper-item class="swiper-item">
                        <scroll-view @scrolltolower="earnLowerBottom" scroll-y style="height: 100%; width: 100%">
                            <view class="swiper-div" style="height: 100%">
                                <view
                                    class="main u-flex u-flex-col u-col-center u-row-left"
                                    v-if="earnList && earnList.length > 0"
                                >
                                    <view class="main-item" v-for="item in earnList" :key="item.id">
                                        <CoinRecord
                                            style="width: 100%"
                                            :itemData="item"
                                            colorStyle="color:#FE7F3C"
                                            @showMask="showMaskclick"
                                        />
                                    </view>
                                </view>
                                <view
                                    v-if="earnList.length === 0"
                                    class="empty-box u-flex u-col-center u-row-center"
                                    :style="{ height: `100%` }"
                                >
                                    <view class="empty-text"> 暂无记录 </view>
                                </view>
                            </view>
                        </scroll-view>
                    </swiper-item>

                    <!-- 消耗明细 -->
                    <swiper-item class="swiper-item">
                        <scroll-view @scrolltolower="consumeLowerBottom" scroll-y style="height: 100%; width: 100%">
                            <view class="swiper-div" style="height: 100%">
                                <view
                                    class="main u-flex u-flex-col u-col-center u-row-left"
                                    v-if="consumeList && consumeList.length > 0"
                                >
                                    <view class="main-item" v-for="item in consumeList" :key="item.id">
                                        <CoinRecord
                                            style="width: 100%; margin-top: 16rpx"
                                            :itemData="item"
                                            colorStyle="color:#FF6200"
                                            @showMask="showMaskclick"
                                        />
                                    </view>
                                </view>
                                <view
                                    v-if="consumeList.length === 0"
                                    class="empty-box u-flex u-col-center u-row-center"
                                    :style="{ height: `calc(100% - 98rpx)` }"
                                >
                                    <view class="empty-text"> 暂无记录 </view>
                                </view>
                            </view>
                        </scroll-view>
                    </swiper-item>
                </swiper>
            </view>
            <!-- 活动规则弹窗 -->
            <u-popup
                :show="showRulePopup"
                v-prevent-scroll="showRulePopup"
                mode="center"
                :round="18"
                :safe-area-inset-bottom="false"
                catchtouchmove
                :customStyle="{ backgroundColor: 'transparent' }"
            >
                <view class="rule-popup-content">
                    <view class="rule-popup-header">
                        <view class="rule-title">
                            <view class="rule-title-decoration left"></view>
                            <text class="rule-title-text">活动规则</text>
                            <view class="rule-title-decoration right"></view>
                        </view>
                        <view class="rule-popup-close" @click="closeRulePopup">
                            <view class="close-icon"></view>
                        </view>
                    </view>
                    <scroll-view scroll-y class="rule-container">
                        <view class="rule-content">
                            <view class="rule-content-text">
                                <img
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889d064b830b7540017252.png"
                                    class="rule-img"
                                />
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </u-popup>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import CoinRecord from '@/pages/coinDetail/components/CoinRecord.vue';
import { AppModule } from '@/store/modules/app';
import { getCoinConfig, getCoinDetail } from '@/http/coin';
import Utils from '@/common/Utils';
import { VIPModule } from '@/store/modules/vip';
import { getScoreGet } from '@/http/vip';
import truncateTwoDecimals from './helpers/truncateTwoDecimals';

@Component({
    components: {
        DefaultPage,
        CoinRecord,
    },
})
export default class inviteRecord extends Vue {
    public tabList: Array<any> = [
        {
            name: '获取明细',
            checked: true,
            id: 1,
        },
        {
            name: '消耗明细',
            checked: false,
            id: 2,
        },
    ];

    public current: Number = 0;
    public swiperCurrent: number = 0;
    public isShowMask: boolean = false; // 遮罩层是否显示
    public earnList: Array<any> = []; // 获取明细列表
    public consumeList: Array<any> = []; // 消耗明细列表
    public earnPage: number = 1; // 获取明细页码
    public consumePage: number = 1; // 消耗明细页码
    public size: number = 15; // 每页条数
    public isEarnMore: Boolean = true; // 判断获取明细是否触底加载
    public isConsumeMore: Boolean = true; // 判断消耗明细是否触底加载
    public numValue: String = '';
    public eventName: String = '';
    public showRulePopup: boolean = false;
    public amount: number = 0;
    public baseInfo: any = {};
    public goldConversion: number = 0;
    public coinCount: number = 0;

    get statusBarHeight() {
        return AppModule.statusBarHeight;
    }

    onLoad() {
        this.init();
    }

    async init() {
        this.loadEarnList();
        this.loadConsumeList();
        VIPModule.getCoinTaskList();
        this.getCoinConfigHandler();
        this.getScoreGet();
    }

    truncateTwoDecimals = truncateTwoDecimals;

    openRulePopup() {
        this.showRulePopup = true;
    }

    async getCoinConfigHandler() {
        const res = await getCoinConfig({ ac_id: 30 });
        this.goldConversion = Number(res.gold_conversion);
    }

    async getScoreGet() {
        const res = await getScoreGet();
        this.coinCount = Number(res.totalGold);
    }

    closeRulePopup() {
        this.showRulePopup = false;
    }

    goBack() {
        Utils.goBack();
    }

    goToMall() {
        uni.navigateTo({
            url: '/pages/shop/shop',
        });
    }

    // 加载获取明细列表
    async loadEarnList() {
        const params = { asset_type: 3, type: 1, page: this.earnPage, size: this.size };
        const result = await getCoinDetail(params);
        if (result && result.records) {
            this.earnList = [...this.earnList, ...result.records];
            if (result.records.length < this.size) {
                this.isEarnMore = false;
            }
        }
    }

    // 加载消耗明细列表
    async loadConsumeList() {
        const params = { asset_type: 3, type: 2, page: this.consumePage, size: this.size };
        const result = await getCoinDetail(params);
        if (result && result.records) {
            this.consumeList = [...this.consumeList, ...result.records];
            if (result.records.length < this.size) {
                this.isConsumeMore = false;
            }
        }
    }

    // 获取明细触底加载
    earnLowerBottom() {
        if (!this.isEarnMore) return;
        this.earnPage += 1;
        this.loadEarnList();
    }

    // 消耗明细触底加载
    consumeLowerBottom() {
        if (!this.isConsumeMore) return;
        this.consumePage += 1;
        this.loadConsumeList();
    }

    // 标签点击切换
    tabClick(index: number) {
        this.current = index;
        this.swiperCurrent = index;
    }

    swiperChange(e) {
        const current = e.detail.current;
        this.swiperCurrent = current;
        this.current = current;
    }

    animationfinish(e) {
        const current: number = e.detail.current;
        this.swiperCurrent = current;
        this.current = current;
    }

    showMaskclick(item) {
        this.isShowMask = true;
        this.eventName = item.eventName;
        this.numValue = (item.numValue > 0 ? '+' : '') + item.numValue;
    }
}
</script>

<style lang="scss" scoped>
.notice-bar {
    width: 100%;
    height: 40rpx;
    position: absolute;
    top: 486rpx;
    left: 0;
    z-index: 1000;
}

.rule-img {
    width: 100%;
}

.rule-popup-content {
    width: 664rpx;
    height: 824rpx;
    max-height: 824rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f035eb9e49650010648.png')
        no-repeat center center;
    background-size: 100% 100%;
    border-radius: 32rpx;
    overflow: hidden;

    .rule-popup-header {
        padding: 94rpx 36rpx 30rpx;
        position: relative;
        text-align: center;

        .rule-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;

            .rule-title-decoration {
                width: 20rpx;
                height: 20rpx;

                &.left {
                    margin-right: 16rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                        no-repeat center center;
                    background-size: 100% 100%;
                    width: 92rpx;
                    height: 20rpx;
                }

                &.right {
                    margin-left: 16rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885f04fa02e06560010515.png')
                        no-repeat center center;
                    background-size: 100% 100%;
                    width: 92rpx;
                    height: 20rpx;
                    transform: rotate(180deg);
                }
            }

            .rule-title-text {
                font-size: 36rpx;
                font-weight: 600;
                color: #ff7d01;
                position: relative;
            }
        }

        .rule-popup-close {
            position: absolute;
            right: 36rpx;
            top: 24rpx;
            width: 46rpx;
            height: 46rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;

            .close-icon {
                width: 46rpx;
                height: 46rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
                    no-repeat center center;
                background-size: 100% 100%;
            }
        }
    }

    .rule-container {
        padding: 0 36rpx 32rpx;

        .rule-content {
            background: #ffffff;
            border-radius: 32rpx;
            padding: 24rpx 14rpx 24rpx 0rpx;

            .rule-content-text {
                padding: 0rpx 24rpx;
                max-height: 572rpx;
                height: 620rpx;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    display: block;
                    width: 8rpx !important;
                    height: 0rpx !important;
                    opacity: 0; // 不生效
                    transition: height 2s;
                }

                &::-webkit-scrollbar-track {
                    border-radius: 10rpx !important;
                }

                &::-webkit-scrollbar-thumb {
                    background: #d8d8d8 !important;
                    border-radius: 10rpx !important;
                }
            }

            .rule-section {
                margin-bottom: 32rpx;
                font-size: 22rpx;
                color: #3d3d3d;
                line-height: 42rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .rule-section-title {
                    font-size: 32rpx;
                    font-weight: 600;
                    color: #333333;
                    margin-bottom: 16rpx;
                    line-height: 1.4;
                }

                .rule-section-content {
                    .rule-text {
                        font-size: 28rpx;
                        color: #666666;
                        line-height: 1.6;
                        margin-bottom: 12rpx;
                        text-align: justify;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}

::v-deep .is_empty_popup {
    height: 732rpx;
    width: 660rpx;
    border-radius: 40rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885fdd90f9560640011767.png')
        no-repeat center center;
    background-size: 100% 100%;
    position: relative;

    .is_empty_popup_title {
        position: absolute;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885fd8d2b2e51770017971.png')
            no-repeat center center;
        background-size: 100% 100%;
        width: 596rpx;
        height: 128rpx;
        bottom: 56rpx;
        left: 50%;
        transform: translateX(-50%);
        right: 0;
    }
}

::v-deep .is_activity_not_start {
    height: 732rpx;
    width: 660rpx;
    border-radius: 40rpx;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885fdaf7ec575190010669.png')
        no-repeat center center;
    background-size: 100% 100%;

    .is_empty_popup_title {
        position: absolute;
        background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6885fd8d2b2e51770017971.png')
            no-repeat center center;
        background-size: 100% 100%;
        width: 596rpx;
        height: 128rpx;
        bottom: 56rpx;
        left: 50%;
        transform: translateX(-50%);
        right: 0;
    }
}

.container {
    overflow: hidden;
    background: #fff5e9;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ce9789f155650010412.png');
    background-size: 750rpx 1650rpx;
}

.header-container {
    position: relative;
    height: 572rpx;
    flex-shrink: 0;

    .amount {
        position: absolute;
        left: 40rpx;
        bottom: 160rpx;

        .amount-label {
            font-size: 30rpx;
            line-height: 41rpx;
            color: #fff;
        }

        .amount-number {
            font-size: 96rpx;
            line-height: 127rpx;
            font-weight: 600;
            color: #fff;
        }

        .amount-s {
            font-size: 22rpx;
            line-height: 22rpx;
            color: #fff;
        }
    }
}

.amount-btn {
    position: absolute;
    right: 20rpx;
    bottom: 192rpx;
    width: 200rpx;
    height: 84rpx;
    font-size: 26rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #df5765;
    background: #fff;
    border-radius: 84rpx;
    margin-top: 24rpx;
}

.header {
    height: 108rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
        position: fixed;
        padding-left: 20rpx;
        color: #ffffff;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        z-index: 1000;

        .search_back {
            width: 46rpx;
            height: 46rpx;
            margin-right: 26rpx;
        }
    }

    .header_title_img {
        height: 46rpx;
        font-size: 35rpx;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        text-align: center;
        font-weight: 500;
    }

    .right {
        height: 100%;
        display: flex;
        align-items: center;

        .rule {
            position: absolute;
            right: 0;
            display: flex;
            width: 46rpx;
            height: 46rpx;
            align-items: center;
            justify-content: center;
            line-height: 51rpx;
            font-size: 24rpx;
            color: #ffffff;
            margin-right: 22rpx;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888daa650b4f3310012642.png');
        }
    }
}

.content {
    padding: 0 38rpx;
    margin: 0 32rpx;
    margin-top: 20rpx;
    background: #fff;
    border-radius: 48rpx 48rpx 0 0;

    .tab {
        display: flex;
        justify-content: space-between;
        height: 112rpx;

        .tab-item {
            position: relative;
            width: 302rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 32rpx;
            color: #777777;
            text-align: center;
            z-index: 1;

            &.active {
                color: #121212;

                .line {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    border-bottom: 2rpx solid #121212;
                }
            }
        }
    }

    .record {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 150rpx;

        .record-name {
            max-width: 340rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 16px;
            line-height: 22px;
            color: #121212;
        }

        .record-time {
            font-size: 14px;
            line-height: 20px;
            color: #777777;
        }

        .record-amount {
            font-size: 16px;
            line-height: 22px;
            color: #a6a6a6;
        }
    }
}

.empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #7f7f7f;
    font-size: 28rpx;

    .empty_icon {
        width: 300rpx;
        height: 300rpx;
    }
}

.container {
    .coinDetails {
        border-radius: 24rpx 24rpx 0 0;
        padding: 0 32rpx;
        -webkit-transform: rotate(0deg);
        background: $fill-color-bg-white;
        overflow: hidden;
        margin: 24rpx 32rpx 0;
    }
}

.empty-text {
    font-size: 32rpx;
    color: #777777;
    font-weight: normal;
}

.tabs-header {
    display: flex;
    height: 96rpx;
    background: #ffffff;

    .tab-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;

        .tab-text {
            font-size: 32rpx;
            color: #777777;
            font-weight: 500;
            transition: color 0.3s;
        }

        .tab-line {
            position: absolute;
            bottom: 0rpx;
            width: 100%;
            left: 50%;
            transform: translateX(-50%);
            height: 5.5rpx;
            background: #121212;
            border-radius: 3rpx;
        }

        &.active {
            .tab-text {
                color: #121212;
                font-weight: 600;
            }
        }
    }
}

.swiper-box {
    .main {
        height: 100%;
        background-color: $fill-color-bg-white;
        border-radius: 24rpx;
        display: -webkit-box;
        padding-top: 16rpx;

        .main-item {
            width: 100%;
            display: flex;
            align-items: center;
        }
    }
}
</style>
