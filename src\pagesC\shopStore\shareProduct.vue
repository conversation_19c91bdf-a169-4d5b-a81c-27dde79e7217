<template>
  <view v-if="!loading">
    <APPSHARE :link="link"></APPSHARE>
    <view class="container" :style="{ 'padding-top': pagePaddingContent + 'rpx' }">
      <view class="header">
        <custom-bar2 :showBack="true" :isBack="true" :zIndex="10" background="#fff"
          :BackIcon="'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b0a21545503450472365.png'">
          <template #customTitle>
            <view class="header_search_box">
              <u--input v-model="searchText" class="header_search" placeholder="X60新品首发，上下水热销扫地机！" prefixIcon="search"
                @input="searchKeydown" prefixIconStyle="font-size: 52rpx;color: #A6A6A6;"></u--input>
              <view class="search-cancel" v-if="searchText" @click="cancelSearch">取消</view>
              <view class="header_right_more">
                <view class="header_right_share" @click="handleShareClick(null, 'active')"></view>
              </view>
            </view>
          </template>
        </custom-bar2>
      </view>
      <!-- 店铺信息头部 -->
      <view class="shop-header">
        <view class="header_right_item">
          <view class="header_right_item_image_box">
            <image class="header_right_item_image" :src="storeDetail.store_photo" />
            <view class="header_right_item_image_cover"></view>
          </view>
          <text class="header_right_item_text">{{ storeDetail.store_name || '我的小店' }}</text>
        </view>
        <view v-if="!isStoreCreator && $isInApp() && user_id != storekeeper" class="operate-header">
                    <view
                        class="user-header-relation"
                        :class="{ active: relationStatus !== 0 }"
                        @click="handleUserFollow"
                    >
                        <img
                            style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                            v-if="relationText === '关注'"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e6b9cd5b6420010495.png"
                        />
                        <img
                            style="width: 32rpx; height: 32rpx; margin-right: 4rpx"
                            v-if="relationText === '已关注'"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68676e932e5d31900010863.png"
                        />
            {{ relationText }}
          </view>
        </view>
        <!-- <view class="follow-btn" @click="handleFollow">
                    <text class="follow-icon">+</text>
                    <text class="follow-text">关注</text>
                </view> -->
      </view>

      <!-- 分类标签页 -->
      <view class="category-tabs">
        <view style="flex: 1; display: flex; align-items: center">
                    <view
                        v-for="(tab, index) in categoryTabs"
                        :key="index"
                        class="tab-item"
                        :class="{ active: currentTab === index }"
                        @click="switchTab(tab, index)"
                    >
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </view>
        <view class="view-toggle" @click="handleToggleView">
          <view class="toggle-icon" :class="{ active: isListProduct }"></view>
          <text class="toggle-text">视图切换</text>
        </view>
      </view>

            <scroll-view
                scroll-y
                class="swiper-item-view"
                :scroll-top="scrollTop"
                @scroll="handleScroll"
                refresher-background="#f4f4f4"
                :refresher-enabled="canPullDown"
                :upper-threshold="0"
                :refresher-triggered="isRefreshing"
                @refresherrefresh="refresherrefresh"
                @scrolltolower="onreachBottom"
                @touchstart="touchStart"
                @touchmove="touchMove"
                @touchend="touchEnd"
            >
        <view class="page-box" v-if="listData && listData.list && listData.list.length > 0 && !isListProduct">
          <block v-for="item in listData.list" :key="item.order_no">
                        <GoodsLists
                            :Item="item"
                            @navToDetail="navToDetail"
                            @shareGoods="handleShareClick(item, 'default')"
                        />
          </block>
        </view>
                <view  class="page-box" v-if="listData && listData.list && listData.list.length > 0 && isListProduct">
          <block v-for="item in listData.list" :key="item.gid">
            <ProductListOne :itm="item" @navToDetail="navToDetail"></ProductListOne>
          </block>
        </view>
                <view
                    class="empty_node u-flex-col u-col-center"
                    v-if="(listData && listData.list && listData.list.length == 0) || (listData && !listData.list)"
                >
                    <image style="width: 508rpx;min-height: 508rpx;" class="image" src="https://wpm-cdn.dreame.tech/images/202308/64f045b481d935316549678.png">
          </image>
          <view class="empty_text">暂无商品, 快去选购吧~</view>
        </view>
      </scroll-view>
      <view style="height: 0">
        <PopReceipt v-if="showReceiptPop" :isShow.sync="showReceiptPop" :itemData="receiptData" />
      </view>
      <custom-toast ref="customToast" />
      <!-- #ifdef MP-WEIXIN -->
      <privacy />
      <!-- #endif -->
    </view>
    <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="shareType" />
    <!-- 用户取消关注拉黑 -->
        <CustomModal
            :show="operContentOpen"
            width="616rpx"
            contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;"
            :title="operContent.titie"
            :content="operContent.tip"
            :confirmText="operContent.confirmText"
            :cancelText="operContent.cancelText"
            showCancelButton
            @confirm="handleContenConfirm"
            @cancel="(operContentOpen = false), (operContent = { type: '' })"
        >
    </CustomModal>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { FOLLOW_BTN_OPTION } from './constant';
import { IOrderList, IUnionSource } from '@/store/interface/order';
import { storeGoodsList, salesCommission, salesStoreDetail, storeFilterLabelList } from '@/http/order';
import { userRelation, userFollow, userFollowDelete } from '@/http/requestGo/community';
import Utils from '@/common/Utils';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import OrderItem from './components/OrderItem/OrderItem.vue';
import PopReceipt from './components/PopReceipt/PopReceipt.vue';
// import PopOrderCancelItem from '../orderDetail/components/PopOrderCancelItem.vue';
import Constants from '@/common/Constants';
import CardList from './components/CardList.vue';
import GoodsLists from './components/ProductList/ProductList.vue';
import ProductListOne from './components/ProductListOne/ProductList.vue';
import { AppModule } from '@/store/modules/app';
import { UserModule } from '@/store/modules/user';
import shareDialog from '@/components/shareDialog/shareDialog.vue';
import { CheckAppJump } from '@/common/decorators';
import {
    getShareContent,
} from '@/http/goods'
export interface OperContent {
  type: 'follow' | 'block' | '';
  titie?: string;
  tip?: '确定不再关注该用户' | '';
  confirmText?: '确定' | '不再关注' | '';
  cancelText?: '取消' | '不保存' | '';
}
@Component({
  components: {
    TabsSwiper,
    OrderItem,
    PopReceipt,
    CardList,
    GoodsLists,
    shareDialog,
    ProductListOne,
  },
})
export default class order extends Vue {
  $refs!: {
    tabs;
  };

  public searchText: string = '';
  public isListProduct: Boolean = true;

  public isRefreshing: Boolean = false;
  public timer: any = null;
  public pagesTitle: string = '我的店铺';
  public link = '';
  public shareType: string = 'default';

  private orderData: {
    list: Array<IOrderList>;
    pages: number;
  };

  public show: Boolean = false;
  public isLoaded: boolean = false;
  public listData: any = [];
  public current: Number = 0;
  public startX: any = 0;
  public startY: any = 0;
  public canPullDown: boolean = true;
  public isLeftMove: boolean = true;
  public storeDetail: any = {};
  public storeStatistics: any = {};
  public swiperCurrent: number = 0;
  public storekeeper: any = '0';
  public scrollTop: Number = 0;
  public tid: any = [];
  public productInfo: any = {
    name: '',
    desc: '',
    image: '',
    imageBg: '',
    price: '',
  };

  public options1 = [
    {
      text: '删除',
      style: {
        backgroundColor: '#f56c6c',
      },
    },
  ];

  public shareContentList: Array<any> = [];
  public old: any = { scrollTop: 0 };
  public goodsData: any = {};
  public finished: Boolean = false; // 是否加载完成
  public page: number = 0; // 由于分页一般页面要+1，所以为了方便分页查询，这里初始设置页码为0
  public showReceiptPop: Boolean = false; // 展示确认收货弹窗
  public showOcancelPop: Boolean = false; // 展示取消订单弹窗
  public orderCdata: Object = { type: 1, order_no: '', deposit_order_no: '' }; // 取消订单信息
  public receiptData: Object = null; // 确认收货数据
  public barStyle: Object = { bottom: '6rpx', backgroundColor: '#7473C5' };
  public name: string = '';
  public isStoreCreator: boolean = true; // 是否是店铺主
  public relationStatus: number = 0; // 关注状态 0:未关注 1:已关注 2:互相关注
  public operContentOpen: boolean = false; // 操作内容弹窗
  public loading: boolean = false;
  public timerShop: any = null;
  public operContent: OperContent = {
    type: '',
    titie: '',
    tip: '',
    confirmText: '',
    cancelText: '',
  };

  // 新增属性
  public currentTab: number = 0;
  public categoryTabs: Array<{ name: string }> = [];

  get pagePaddingTop(): number {
    return AppModule.pagePaddingTop;
  }

  get pagePaddingContent(): number {
    return Number(AppModule.pagePaddingTop);
  }

  get pagePaddingHeaderContent(): number {
    return Number(AppModule.pagePaddingTop) + 80;
  }

  get swiperBoxStyle(): Object {
    return { height: `100vh` };
  }

  get userInfo(): any {
    return UserModule.userInfo;
  }

  get user_id(): any {
    return UserModule.user_id || 12507;
  }

  get relationText() {
    return FOLLOW_BTN_OPTION.find((v) => v.value === this.relationStatus)?.label || '关注';
  }

  @CheckAppJump()
  handleToggleView() {
    this.isListProduct = !this.isListProduct;
  }

  refresherrefresh() {
    this.isRefreshing = true;
    clearTimeout(this.timer);
    this.timer = setTimeout(async () => {
      this.init();
      this.isRefreshing = false;
    }, 200);
  }

  onUnLoad() {
    uni.$off('HANDLE_RECEIPT_POP');
    uni.$off('HANDLE_ORDER_CANCEL_POP');
    uni.$off('updateOrderList');
  }

  onBackPress(event) {
    if (event.from === 'backbutton') {
      if (this.showReceiptPop) {
        this.showReceiptPop = false;
      } else {
        Utils.goBack();
      }
      return true;
    }
    return false;
  }

  async onLoad(option) {
    this.loading = true;
    this.storekeeper = option.storekeeper;
    this.isStoreCreator = Number(this.user_id) === Number(this.storekeeper);
    Utils.setPageTitle('追觅小店');
    const res = await storeFilterLabelList({ store_id: this.storekeeper });
    this.categoryTabs = res;
    this.tid = res ? res[0].tid : '-1';
    // uni.$on('updateOrderList', this.handleUpdateOrderList);
    this.handleStoreDetail();
    this.link = option.link;

    // this.handleSalesCommission();
    if (option.Index) {
      return this.changeIndexFormMine(option.Index);
    }
    if (!this.isStoreCreator && Utils.isInApp()) {
      await this.getRelationStatus();
    }
    this.loading = false;
  }

  onShow() {
    setTimeout(() => {
      this.init();
    }, 300);
  }

  beforeDestroy() {
    uni.$off('HANDLE_RECEIPT_POP');
    uni.$off('HANDLE_ORDER_CANCEL_POP');
  }

  async init() {
    this._initPageData();
    await this._getListFactory();
  }

  async getRelationStatus() {
    const res = await userRelation({
      user_id: Number(this.user_id),
      to_user_id: Number(this.storekeeper),
    });
    this.relationStatus = res.data.follow_status;
  }

  @CheckAppJump()
  handleUserFollow() {
    uni.$u.throttle(() => {
      const user_id = Number(this.user_id);
      const other_id = Number(this.storekeeper);
      // const api = this.relationStatus === 0 ? userFollow : userFollowDelete;
      if (this.relationStatus === 0) {
        userFollow({ user_id, followed_user_id: other_id }).then(() => {
          Utils.Toast('关注成功');
          userRelation({ user_id, to_user_id: other_id }).then((res) => {
            this.relationStatus = res.data.follow_status;
          });
        });
      } else {
        this.operContentOpen = true;
        this.operContent = {
          type: 'follow',
          titie: '',
          tip: '确定不再关注该用户',
          confirmText: '不再关注',
          cancelText: '取消',
        };
      }
    }, 500);
  }

  async handleContenConfirm() {
    let res;
    // 二次确认弹框确认
    if (this.operContent.type === 'follow') {
      res = await userFollowDelete({ followed_user_id: +this.storekeeper });
    }
    if (res.success) {
      Utils.Toast(this.operContent.type === 'follow' ? '操作成功' : '已拉黑');
      userRelation({ user_id: +this.user_id, to_user_id: +this.storekeeper }).then((res) => {
        this.relationStatus = res.data.follow_status;
      });
    }
    this.operContentOpen = false;
    setTimeout(() => {
      this.operContent = { type: '' };
    }, 500);
  }

  searchKeydown(e) {
    this.name = e || '';
    this.timerShop && clearTimeout(this.timerShop);
    this.timerShop = setTimeout(() => {
      this.page = 1;
      this.finished = false;
      this._getList();
    }, 500);
  }

  cancelSearch() {
    this.searchText = '';
    this.name = '';
    this.page = 1;
    this.finished = false;
    this._getList();
  }

  handleUpdateOrderList() {
    this.page = 1;
    this.finished = false;
    this._getList();
  }

  changeIndexFormMine(index) {
    this.current = index;
    this.swiperCurrent = index;
  }

  // 初始化页面的数据
  _initPageData() {
    this.finished = false;
    this.page = 0;
    if (this.listData && this.listData.length === 0) {
      this.listData = [];
    }
  }

  // 跳转详情
  @CheckAppJump()
  navToDetail(item) {
    const { gid, name } = item;
    Utils.logTrace(
      {
        module: Constants.LOG_TRACE_MODULE_DREAME,
        event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
        id: Number(gid),
        name: name,
      },
      true,
    );
    Utils.reportEvent('product_click', { product_id: gid });
    Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}&storekeeper=${this.storekeeper}&unionSource=${IUnionSource.DREAME_SMALL_SHOP}`);
  }

  onreachBottom() {
    this._getListFactory();
  }

  @CheckAppJump()
  navToHome() {
    // #ifdef H5
    Utils.messageChannel('navigation', { type: 'home', path: 'home/h5' });
    // #endif

    // #ifdef MP-WEIXIN
    uni.switchTab({ url: '/pages/shop/shop' });
    // #endif
  }

  touchStart(e) {
    this.startX = e.touches[0].pageX;
    this.startY = e.touches[0].pageY;
  }

  @CheckAppJump()
  addGoodsInfo() {
    uni.navigateTo({ url: '/pagesC/shopProduct/shop' + '?is_selected=1' });
  }

  async handleStoreDetail() {
    try {
      const res = await salesStoreDetail({ store_id: this.storekeeper });
      this.storeDetail = res;
    } catch (e) {
      console.error('handleSalesCommission e=', e);
      this.storeDetail = {};
    }
  }

  async handleSalesCommission() {
    try {
      const res = await salesCommission();
      this.storeStatistics = res;
    } catch (e) {
      console.error('handleSalesCommission e=', e);
      this.storeStatistics = {};
    }
  }

  touchMove(e) {
    if (!this.isLeftMove) return;
    const moveX = e.touches[0].pageX;
    const moveY = e.touches[0].pageY;
    const diffX = moveX - this.startX;
    const diffY = moveY - this.startY;

    // 如果是左右滑动，禁用下拉刷新
    if (Math.abs(diffX) > Math.abs(diffY)) {
      this.canPullDown = false;
    } else {
      this.isLeftMove = false;
    }
  }

  touchEnd() {
    // 在滑动结束后，启用下拉刷新
    this.canPullDown = true;
    this.isLeftMove = true;
  }

  async _getListFactory() {
    // 如果已经全部加载完成直接终止执行
    if (this.finished) return;
    this.page++;
    await this._getList();
  }

  async getStoreGoodsList() {
    try {
      const res = await storeGoodsList({
        page: this.page,
        page_size: 20,
      });
      if (res.code == 0) {
        console.log(res.data.list);
        // this.goodsList = res.data.list;
      }
    } catch (e) {
      console.error('getStoreGoodsList e=', e);
    }
  }

  async _getList() {
    try {
      const page: string | number = this.page;
      const page_size: string | number = 10;
      const tid = this.tid;
      const { list, pages } = await storeGoodsList({
        page,
        store_id: this.storekeeper,
        page_size,
        tid,
        name: this.name,
      });

      if (this.page > pages) {
        this.finished = true;
        Utils.Toast('没有更多了');
        return;
      }
      const _list = list || [];
      if (page > 1) {
        this.listData.list = this.listData.list.concat(_list);
         this.$forceUpdate();
      } else {
        this.listData.list = _list;
        this.$forceUpdate();

        // this.$set(this.listData, 'list', this.orderData || []);
      }
    } catch (e) {
      console.error('_getList e=', e);
    }
  }

  handleScroll(e) {
    this.old.scrollTop = e.detail.scrollTop;
  }

  /* 分享商品 */
  @CheckAppJump()
  handleShareClick(item, type) {
    if (UserModule.sdkVersion < 13) {
      Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
      return;
    }
    this.shareType = type; // active default
    this.goodsData = item || this.listData.list[Math.floor(Math.random() * this.listData.list.length)];
    const shareImage =
      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png';
    console.log(item, type, this.goodsData);
    const { name, introduce, cover_image, price } = this.goodsData;
    this.show = true;
    this.productInfo = {
      name: name,
      desc: introduce,
      image: cover_image,
      price: price,
      imageBg: type === 'active' ? shareImage : '',
    };
  }

  async getShareContentData() {
      const res = await getShareContent({ tid: this.goodsData.tids.length > 0 ? this.goodsData.tids[0] : -1 });
      this.shareContentList = res;
  }

  async handleShare(type: 'web' | 'image') {
    const pagePath =
      this.shareType == 'active'
        ? `/pagesC/shopStore/shareProduct?storekeeper=${this.storekeeper}`
        : `/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&storekeeper=${this.storekeeper}`;
    const path = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0`;
    this.show = false;
    if (this.shareType === 'active') {
      Utils.cardShare(type)(
        {
          target: 'wechat,weixin_circle,qq,sina,image_template,download',
          link: `${path}#${pagePath}`,
          jumpLink: `${path}#${pagePath}`,
        },
        {
          content: {
            url: `${path}#${pagePath}`,
                        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png' + '?x-oss-process=image/resize,w_200',
                        title: `【${this.storeDetail.nickname}】这是我的追觅小店，快来看看吧！！`,
            desc: '这是我精选的追觅商品清单，快进来看看吧！',
          },
          extras: {
            type: 'activity',
            id: this.goodsData.gid,
            goods: this.productInfo,
          },
        },
      );
    } else {
      await this.getShareContentData();
      Utils.cardShare(type, 'goods')(
        {
          target: 'image_template,download,wechat,weixin_circle,qq,sina',
          link: `${path}#${pagePath}`,
          jumpLink: `${path}#${pagePath}`,
        },
        {
          content: {
            url: `${path}#${pagePath}`,
            image: this.goodsData.cover_image + '?x-oss-process=image/resize,w_200',
            title: `【${this.goodsData.price}元】 ${this.goodsData.name}`,
            desc:
              this.goodsData.introduce ||
              this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
            // title: `【${this.productInfo.price}元】${this.goodsData.name}`,
            //         desc:
            //             this.goodsData.introduce ||
            //             this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
          },
          extras: {
            type: 'goods',
            id: this.goodsData.gid,
            goods: {
              name: this.goodsData.name,
              desc: this.goodsData.introduce,
              image: this.goodsData.cover_image,
              price: this.goodsData.price,
              priceColor: '#FF7D01',
              descColor: '#C59245',
            },
          },
        },
      );
    }
  }

  // 新增方法
  handleFollow() {
    // 处理关注逻辑
    Utils.Toast('关注成功');
  }

  @CheckAppJump()
  switchTab(item, index: number) {
    console.log(item, index);
    this.tid = item.tid;
    this.currentTab = index;
    // 这里可以根据不同的tab加载不同的数据
    this.init();
  }
}
</script>

<style lang="scss" scoped>
@import './shareProduct.scss';

.header {
  background-color: #fff;
}

.page-box {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20rpx;
  flex-wrap: wrap;
}

.swiper-item-view {
  flex: 1;
  overflow: auto;
  background: #f4f4f4;
}
</style>
