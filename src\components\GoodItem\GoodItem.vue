<template>
    <view class="item-container">
        <view :class="['item', direction, handleGoodsDisable(good) ? 'disabled' : '']" @click="gotoGdetailPage"
            @touchstart="drawStart" @touchmove="drawMove" @touchend="drawEnd" :style="{
                transform: `translateX(${-right}rpx)`,
                transition: transition,
                borderRadius: `16rpx ${borderRadius}rpx ${borderRadius}rpx 16rpx`,
            }">
            <view class="goodLeft">
                <image v-if="isShowSelect" @click.stop="onSelectItem(index)" class="icon" :src="good.selected
                    ? 'https://wpm-cdn.dreame.tech/images/202306/514215-1687241028296.png'
                    : isNotSold || isSoldOut
                        ? 'https://wpm-cdn.dreame.tech/images/202307/64b74cfee4a9e9365594297.png'
                        : 'https://wpm-cdn.dreame.tech/images/202306/340856-1687241129889.png'
                    " mode="aspectFit"></image>
                <view class="good-cover" :class="bigCover ? 'big' : ''">
                    <image v-if="good.spec.length === 0" :src="good.market_image || good.images"></image>
                    <image v-else :src="good.spec.image" />
                    <image v-if="isNotSold" class="tag-image"
                        src="https://wpm-cdn.dreame.tech/images/202306/857253-1687774910809.png">
                    </image>
                    <!-- 已下架 -->
                    <image v-else-if="isSoldOut" class="tag-image"
                        src="https://wpm-cdn.dreame.tech/images/202306/118629-1687774792279.png"></image>
                    <!-- 无货 -->
                </view>
            </view>
            <view class="good-detail">
                <view>
                    <view class="good-name"
                        :class="{ 'u-line-1': direction === 'vertical', 'u-line-2': direction != 'vertical' }">
                        <text class="isPresale uni-presale-tag" v-if="good.is_presale == 1">预售</text>
                        <text class="isInShop uni-genaral-tag" v-if="good.is_internal_purchase == 1">内购</text>
                        <text class="text">{{ good.name }}</text>
                    </view>
                    <block v-if="good.jd_baitiao_support === '1'">
                        <view class="biaotiao" v-if="good.interest_free === 0">
                            支持京东白条
                        </view>
                        <view class="freeInterest" v-else>
                            <view class="text">分期免息</view>
                            <view class="num">{{ good.interest_free }}期</view>
                        </view>
                    </block>
                </view>
                <view v-if="direction !== 'vertical' && good.spec && good.spec.attr_cnf"
                    class="attr u-flex u-col-center u-row-between" style="margin-top: 16rpx; margin-bottom: 32rpx">
                    <view class="attr">
                        <text class="attr-item" v-for="(item, index) in good.spec.attr_cnf" :key="index">{{
                            item.at_val
                            }}</text>
                    </view>
                </view>
                <view class="num-text" v-if="isShowNum"> x{{ good.num }} </view>
                <!-- 仅购物车页面展示 -->
                <view class="endTime"
                    v-if="good.is_ini == 1 && good.gini_etime > 0 && isShowSelf && !handleGoodsDisable(good)">
                    <text class="timeText">距活动结束剩</text>
                    <view class="originTime">
                        <u-count-down @change="onChange" :time="good.gini_etime * 1000 - currentTime"
                            format="DD天 HH:mm:ss" @finish="$emit('refreshList')">
                            <view class="time" v-if="showTime">
                                {{ timeData.days || '00' }}&nbsp;天&nbsp;{{
                                    timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours
                                }}:{{ timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes }}:{{
                                    timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
                                }}
                            </view>
                        </u-count-down>
                    </view>
                </view>
                <view class="u-flex u-row-between u-col-center" style="align-items: flex-end; margin-top: 22rpx">
                    <view v-if="good.spec && good.spec['price']" class="good-price">
                        <view class="price">
                            <text class="sign" style="margin-right: 10rpx">¥</text>
                            <text class="num">{{ parseFloat(good.spec['price']) }}</text>
                        </view>
                    </view>
                    <view v-else class="good-price">
                        <slot name="inner">
                            <view class="price">
                                <text class="sign" style="margin-right: 10rpx">¥</text>
                                <text class="num">{{
                                    good.is_presale ? parseFloat(good.uprice) : parseFloat(good.price)
                                    }}</text>
                            </view>
                            <view class="mprice" v-if="isShowMprice && good.mprice">
                                <text class="sign" style="margin-right: 10rpx">¥</text>
                                <text class="num">{{ parseFloat(good.mprice) }}</text>
                            </view>
                        </slot>
                    </view>
                    <view v-if="isShowNumBox" @click.stop>
                        <number-box v-if="good.limit_num > 0" :value.sync="good.num" :min="1" :max="good.limit_num"
                            bgColor="#fff" inputWidth="66rpx" buttonWidth="48rpx" buttonSize="48rpx" fontSize="30rpx"
                            color="#333333" :disabled="handleGoodsDisable(good)" @blur="valChange($event, good)"
                            @change="valChange($event, good)" @overlimit="handleOverLimit">
                        </number-box>
                        <number-box v-else :value.sync="good.num" :min="1" bgColor="#fff" inputWidth="66rpx"
                            buttonWidth="48rpx" buttonSize="48rpx" fontSize="30rpx" color="#333333"
                            :disabled="handleGoodsDisable(good)" @blur="valChange($event, good)"
                            @change="valChange($event, good)" @overlimit="handleOverLimit">
                        </number-box>
                    </view>
                </view>
                <view class="deposit" v-if="good.is_presale == 1 && isShowdeduction">
                    <text class="depositStyle"
                        v-if="good.deposit != 0 && good.expand_price != 0 && good.deposit != good.expand_price">定金{{
                            good.deposit }}抵{{ good.expand_price }}</text>
                </view>
            </view>
        </view>
        <view>
            <slot name="footer"></slot>
        </view>
        <view v-if="canDelete && right > 10" class="delete u-flex u-row-center u-col-center"
            @click.stop="delData(good)">
            <image class="iamge" src="https://wpm-cdn.dreame.tech/images/202306/451555-1687085770564.png" />
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit, Watch } from 'vue-property-decorator';
import NumberBox from '@/components/NumberBox/NumberBox.vue';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import { VIPModule } from '@/store/modules/vip';

@Component({
    components: {
        NumberBox,
    },
})
export default class GoodItem extends Vue {
    @Prop({
        type: Object,
        default: () => { },
    })
    readonly good;

    @Prop({ type: String, default: '' })
    readonly curTransformId;

    @Prop({ type: String, default: 'cart' })
    readonly type;

    @Prop({ type: String, default: 'vertical' })
    readonly direction;

    @Prop({ type: Boolean, default: false })
    readonly isShowSelf;

    @Prop({ type: Boolean, default: false })
    readonly isShowMprice;

    @Prop({ type: Boolean, default: false })
    readonly isShowSelect;

    @Prop({ type: Boolean, default: false })
    readonly isShowNumBox;

    @Prop({ type: Boolean, default: false })
    readonly isShowPresale;

    @Prop({ type: Boolean, default: true })
    readonly isShowdeduction;

    @Prop({ type: Number, default: 0 })
    readonly index;

    @Prop({ type: Boolean, default: false })
    readonly canDelete;

    @Prop({ type: Boolean, default: true })
    readonly isShowDetail;

    @Prop({ type: Boolean, default: false })
    readonly bigCover;

    @Prop({ type: Boolean, default: false })
    readonly isShowNum;

    @Prop({ type: Boolean, default: true })
    readonly canSwipe;

    @Watch('curTransformId', { immediate: true }) // watch，此处是监听isShow的变化
    async isShowChange(newVal: Boolean, oldVal: Boolean) {
        this.transition = 'transform 300ms ease 0s';
        this.right = 0;
        this.distance = 0;
        this.borderRadius = 16;
        this.totalDisX = 0;
    }

    get isBlacklist(): Boolean {
        return VIPModule.smileInfo.is_blacklist == 1; // 黑名单 1:是 2:否
    }

    get isEmployee(): Boolean {
        return VIPModule.smileInfo.is_employee == 1; // 1:是员工 0:不是员工
    }

    public goodNoneImageUrl: string = ''; // 蒙层图片
    public timeData: any = {
        days: '',
        hours: '',
        minutes: '',
        second: '',
    };

    public startX: number = 0;
    public isSoldOut: boolean = false;
    public isNotSold: boolean = false;
    public right: number = 0;
    public delBtnWidth: number = 116;
    public transition: string = 'none 0 ease 0s';
    public currentTime: number = +new Date();
    public borderRadius: number = 16;
    public onceDisX: number = 0;
    public totalDisX: number = 0;
    public distance: number = 0;
    get showTime(): Boolean {
        const status = String(this.timeData.days) !== 'undefined' &&
            String(this.timeData.hours) !== 'undefined' &&
            String(this.timeData.minutes) !== 'undefined' &&
            String(this.timeData.seconds) !== 'undefined';
        return status;
    }

    async valChange(event, item) {
        try {
            this.$emit('valChange', { value: event.value });
        } catch (e) {
            console.error('valChange', e);
        }
    }

    handleChange(event, item) {
        if (this.type !== 'cart') {
            this.$emit('valChange', { value: event.value });
        }
    }

    onChange(e) {
        this.timeData = e;
    }

    // 处理无货、已下架 样式状态
    handleGoodsDisable(item) {
        if (String(item.status) === Constants.STATUS_SOLD_OUT || String(item.is_del) === Constants.STATUS_DEL) {
            this.isNotSold = true;
            return true;
        } else {
            this.isNotSold = false;
        }
        if (String(item.prod_status) === Constants.PROD_STATUS_NONE_GOODS) {
            this.isSoldOut = true;
            return true;
        } else {
            this.isSoldOut = false;
        }
        return false;
    }

    @Emit('onSelectItem')
    onSelectItem(index) { }

    gotoGdetailPage() {
        this.$emit('onIsDeleting', '')
        if (this.curTransformId == this.good.id) {
            return;
        }
        if (this.distance > 0 || this.right > 0) {
            this.distance = 0;
            this.right = 0;
            this.transition = 'transform 300ms ease 0s';
            this.$emit('onIsDeleting', '')
            return;
        }
        if (!this.isShowDetail || this.isNotSold || this.isSoldOut) return;
        if (this.good.is_internal_purchase == 1) {
            if (this.isBlacklist) {
                Utils.Toast('微笑大使身份冻结中，不可查看内购商品');
                return;
            }
            if (!this.isEmployee) {
                Utils.Toast('微笑大使身份失效，不可查看内购商品');
                return;
            }
        }
        Utils.reportEvent('cart_good_click', { product_id: this.good.gid })
        // 添加延时，保证键盘收起再跳转，不然返回后页面会卡死
        setTimeout(() => {
            uni.navigateTo({ url: `/pagesB/goodsDetail/goodsDetail?gid=${this.good.gid}` });
        }, 200);
    }

    blurInputs() {
        const inputElements = this.$el.querySelectorAll('input');
        inputElements.forEach((input) => {
            if (document.activeElement === input) {
                input.blur();
            }
        });
    }

    // 开始触摸滑动
    drawStart(e) {
        if (!this.canDelete) return;
        if (!this.canSwipe) return;

        const touch = e.touches[0];
        this.startX = touch.clientX;
        this.transition = 'none 0 ease 0s';
        e.preventDefault();
        e.stopPropagation();
        return false;
    }

    // 触摸滑动
    drawMove(e) {
        if (!this.canDelete) return;
        if (!this.canSwipe) return;
        const touch = e.touches[0];
        const disX = this.startX - touch.clientX;
        this.distance = this.totalDisX + disX;
        this.transition = 'transform 300ms ease 0s';

        if (this.distance > this.delBtnWidth) {
            this.right = this.delBtnWidth;
            this.borderRadius = 16;
            this.distance = this.delBtnWidth;
        } else if (this.distance < 0) {
            this.right = 0;
            this.distance = 0;
            this.borderRadius = 16;
            // this.$emit('onIsDeleting', '')
        } else {
            this.right = this.distance;
            this.borderRadius = 0;
        }
        if (disX < -20) {
            this.distance = 0;
            this.right = 0;
            // this.$emit('onIsDeleting', '')
        }
        if (this.right != 0) {
            this.$emit('onIsDeleting', this.good.id)
        }
        e.preventDefault();
        e.stopPropagation();
        return false;
    }

    // 触摸滑动结束
    drawEnd(e) {
        if (!this.canDelete) return;
        this.transition = 'transform 300ms ease 0s';
        if (this.distance < 30) {
            this.distance = 0;
            this.right = 0;
        } else {
            this.distance = this.delBtnWidth;
            this.right = this.delBtnWidth;
        }
        this.totalDisX = this.distance;
        if (this.right == 0) {
            this.borderRadius = 16;
        }

        this.$emit('onIsNotDeleting')
        e.preventDefault();
        e.stopPropagation();
        return false;
    }

    handleOverLimit(type) {
        this.$emit('limitOver', type);
    }

    @Emit('delData')
    delData(good) { }
}
</script>
<style lang="scss" scoped>
.item-container {
    position: relative;
    background-color: #fff;
    margin-bottom: 16rpx;
    border-radius: 16rpx;
    touch-action: pan-y;

    .item {
        z-index: 2;
    }

    .good-price {
        // margin-top: 16rpx;
        font-size: 32rpx;

        .price {
            display: inline-block;
            height: 54rpx;
            font-family: MiSans-Medium, MiSans;
            font-weight: 500;
            color: $fill-color-primary-active;
            line-height: 54rpx;

            .sign {
                font-size: 32rpx;
                font-weight: 500;
                line-height: 34rpx;
            }
        }

        .mprice {
            margin-left: 8rpx;
            display: inline-block;
            height: 34rpx;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #aaaaaa;
            line-height: 34rpx;
            text-decoration: line-through;
        }
    }

    .item.horizontal {
        width: 100%;
        background: $fill-color-bg-white;
        // margin-bottom: 32rpx;
        border-radius: 16rpx;
        box-sizing: border-box;
        padding: 38rpx 32rpx 38rpx 38rpx;
        display: flex;
        align-items: flex-start;
        position: relative;

        .goodLeft {
            display: flex;
            align-items: center;
        }

        .icon {
            min-width: 36rpx;
            width: 36rpx;
            height: 36rpx;
            margin-right: 36rpx;
            z-index: 1;
        }

        .good-cover {
            width: 154rpx;
            min-width: 154rpx;
            height: 154rpx;
            margin-right: 16rpx;
            position: relative;

            image {
                width: 100%;
                height: 100%;
                border-radius: 14rpx;
            }

            .tag-image {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 92rpx;
                height: 92rpx;
                z-index: 1;
            }

            &.big {
                width: 230rpx;
                min-width: 230rpx;
                height: 230rpx;
            }
        }

        .attr {
            display: flex;
            flex-wrap: wrap;

            .attr-item {
                color: $text-color-secondary;
                background-color: $fill-color-bg-gray;
                padding: 4rpx 12rpx;
                border-radius: 8rpx;
                font-size: 24rpx;
                margin-right: 16rpx;
                margin-top: 10rpx;
            }
        }

        .good-detail {
            flex: 1;

            .good-name {
                line-height: 38rpx;
                font-size: 28rpx;
                -webkit-text-size-adjust: none !important;

                .isPresale,
                .isInShop {
                    margin-right: 8rpx;
                    transform: translateY(-2rpx);
                }

                .text {
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #1d1e20;
                }
            }

            .biaotiao {
                display: block;
                width: 208rpx;
                height: 46rpx;
                margin: 16rpx 0 0rpx;
                padding-left: 48rpx;
                padding-right: 12rpx;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 24rpx;
                color: #DE3030;
                line-height: 46rpx;
                background: rgba(221, 45, 45, 0.04);
                background-image: url('https://wpm-cdn.dreame.tech/images/202411/6735a0eb747e94770007651.png');
                background-repeat: no-repeat;
                background-size: 28rpx;
                background-position: 12rpx center;
                border-radius: 8rpx;
                border: 1rpx solid #DE2F2F;
            }

            .freeInterest {
                display: flex;
                align-items: center;
                width: 196rpx;
                height: 46rpx;
                margin: 16rpx 0 0rpx;
                text-align: center;
                border-radius: 8rpx;
                border: 1rpx solid #DE2F2F;

                .text {
                    width: 65%;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #DE3030;
                    line-height: 46rpx;
                    background: rgba(221, 45, 45, 0.2);
                    border-radius: 8rpx 0 0 8rpx;
                }

                .num {
                    width: 35%;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #DE2F2F;
                    line-height: 46rpx;
                    background: rgba(221, 45, 45, 0.06);
                    border-radius: 0 8rpx 8rpx 0;
                }
            }
        }
    }

    .item.vertical {
        width: 332rpx;
        height: 492rpx;
        background: #ffffff;
        box-sizing: border-box;
        border-radius: 16rpx;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        position: relative;

        .good-cover {
            width: 240rpx;
            height: 240rpx;
            margin: auto;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .good-name {
            line-height: 38rpx;

            .text {
                font-size: 28rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: $text-color-primary;
                line-height: 38rpx;
            }
        }

        .good-price {
            margin-top: 16rpx;

            .price {
                display: inline-block;
                height: 56rpx;
                font-size: 40rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: $theme-color;
                line-height: 56rpx;

                .sign {
                    font-size: 24rpx;
                    font-weight: 400;
                    line-height: 34rpx;
                }
            }

            .mprice {
                margin-left: 8rpx;
                display: inline-block;
                height: 34rpx;
                font-size: 24rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #aaaaaa;
                line-height: 34rpx;
                text-decoration: line-through;
            }
        }
    }

    .delete {
        width: 116rpx;
        height: 100%;
        background-color: $func-color-danger-bg;
        position: absolute;
        top: 0;
        right: 0rpx;
        font-size: 28rpx;
        font-family: MiSans-Medium, MiSans;
        font-weight: 500;
        color: #ffffff;
        border-radius: 0rpx 16rpx 16rpx 0rpx;
        z-index: 1;

        .iamge {
            width: 46rpx;
            height: 46rpx;
        }

        .icon_delete {
            height: 80rpx;
            width: 80rpx;
        }
    }

    .disabled::after {
        display: block;
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 24rpx;
        background-color: rgba($color: #ffffff, $alpha: 0.7);
    }

    .disabled {
        .icon {
            opacity: 0.7;
        }
    }

    .presaleStyle {
        width: 70rpx;
        height: 38rpx;
        background: #f44e4e;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        opacity: 1;
        color: #ffffff;
        margin-right: 18rpx;
        font-size: 19rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 27rpx;
        padding: 8rpx 12rpx;
        text-align: center;
    }

    .postionRelative {
        position: relative;
        top: -6rpx;
    }

    .endTime {
        color: $fill-color-bg-serch;
        background: $fill-color-bg-buyNow-time;
        border-radius: 8rpx 0rpx 0rpx 8rpx;
        white-space: normal;
        height: 49rpx;
        width: 100%;
        padding-left: 12rpx;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        margin-top: 16rpx;
        margin-bottom: 16rpx;

        .timeText {
            font-size: 20rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 28rpx;
            margin-right: 8rpx;
        }

        .originTime {
            font-size: 20rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 28rpx;

            .time {
                @include flex;
                align-items: center;
                height: 28rpx;
                font-size: 20rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: $fill-color-bg-serch !important;
                line-height: 28rpx;
            }
        }
    }

    .num-text {
        text-align: right;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #aaaaaa;
    }

    .deposit {
        .depositStyle {
            background: #ffffff;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
            opacity: 1;
            border: 1rpx solid #e72d2d;
            font-size: 19rpx;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #f44e4e;
            padding: 4rpx 12rpx;
        }
    }
}
</style>
