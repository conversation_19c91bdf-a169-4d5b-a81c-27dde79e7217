const fs = require('fs');
const execSync = require('child_process').execSync;

const versionPath = './src/version_mp.json';

// 读取版本号记录文件
const versionInfo = JSON.parse(fs.readFileSync(versionPath, 'utf-8'));

// 获取当前日期
const currentDate = new Date();
const dateStr = `${currentDate.getFullYear().toString().slice(2)}${(currentDate.getMonth() + 1)
    .toString()
    .padStart(2, '0')}${currentDate.getDate().toString().padStart(2, '0')}`;

// 更新版本号
if (versionInfo.date === dateStr) {
    versionInfo.buildCount += 1;
} else {
    versionInfo.date = dateStr;
    versionInfo.buildCount = 1;
}

// 更新版本号记录文件
fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 4));

execSync('npm run build:mp-weixin');
