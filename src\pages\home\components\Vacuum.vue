<template>
<div class="device-item" @click="gotoDevice">
    <div @click.stop="editDeviceInfo"><img src="./icon-3-points.png" class="device-item-more" alt="more" /></div>
    <div class="device-info-card">
        <div class="device-info-area">
            <div class="device-info-name">{{ name }}</div>
            <div
                v-if="battery || status"
                class="device-info-status"
            >
                <div>
                    <img src="./icon-battery.png" alt="电量" class="device-info-battery" />
                    {{ battery }}%
                </div>
                <div class="col-line"/>
                <div>{{ status }}</div>
            </div>
        </div>
        <div class="device-info-image-area">
            <img :src="image" alt="设备图片" class="device-info-image" />
        </div>
    </div>
    <div
        class="device-control-area"
    >
        <div class="device-control-content">
            <div class="device-control-item" @click.stop="startClean">
                <img :src="workIcon" alt="开始清洁" class="device-control-icon" />
            </div>
            <div class="col-line2"/>
            <div class="device-control-item" @click.stop="goBackHome">
                <img :src="chargeIcon" alt="回充" class="device-control-icon" />
            </div>
            <template v-if="supportVideo">
                <div class="col-line2"/>
                <div class="device-control-item" @click.stop="goVideoPage">
                    <img src="./icon-video.png" alt="视频" class="device-control-icon" />
                </div>
            </template>
            <!-- <template v-if="supportFastCommand">supportFastCommand
                <div class="col-line2"/>
                <div class="device-control-item" @click.stop="goQuickOrderPage">
                    <img src="./icon-quick-order.png" alt="快捷指令" class="device-control-icon" />
                </div>
            </template> -->
        </div>
    </div>
</div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

const iconCharge = require('./icon-charge.png')
const iconPause = require('./icon-pause.png')
const iconStartClean = require('./icon-start-clean.png')
const iconBackCharge = require('./icon-back-charge.png')
const defaultDeviceImage = require('./img_robot_placholder.png')

const deviceCanStopStatus = [1, 7, 9, 10, 11, 12, 15, 16, 17, 18, 20, 22, 23, 25, 26, 27, 28, 37, 38, 97, 98, 101, 107];

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
            supportVideo: false, // 是否支持视频
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        supportFastCommand() {
            const { deviceInfo = {}} = this.currentDevice;
            const { feature = '' } = deviceInfo;
            // 支持快捷指令的设备型号
            return feature === 'fastCommand';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage
        },
        workIcon() {
            const device = this.currentDevice
            if (!device) return ''
            if (device.online) {
                if (deviceCanStopStatus.includes(device.latestStatus)) {
                    return iconPause
                }
                return iconStartClean
            }
            return 'https://wpm-cdn.dreame.tech/images/202309/322149-1694417909403.png'
        },
        chargeIcon(): string {
            const device = this.currentDevice
            if (!device) return ''
            if (device.online) {
                if (device.latestStatus === 5) {
                    return iconCharge
                }
                return iconBackCharge
            }
            return 'https://wpm-cdn.dreame.tech/images/202401/252682-1706594717679.png'
        },
        name() {
            return this.currentDevice.customName || this.currentDevice.deviceInfo?.displayName || this.currentDevice.displayName || '';
        }
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true
        }
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber()
            const bind_id = bindDomain.split('.')[0]
            const data = { did, id, from: 'mapp', method: 'get_properties', params: [{ did, siid: 2, piid: 1 }, { did, siid: 3, piid: 1 }, { did, siid: 4, piid: 38 }, { did, siid: 4, piid: 83 }, { did, siid: 4, piid: 48 }] }
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data)
            }
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter(r => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter(r => r.siid === 3 && r.piid == 1);
                        const supportVideo1 = result.filter(r => r.siid === 4 && r.piid == 38);
                        const supportVideo2 = result.filter(r => r.siid === 4 && r.piid == 83);
                        const supportVideoStatus1 = supportVideo1[0]?.value;
                        const supportVideoStatus2 = supportVideo2[0]?.value;
                        const _supportVideo = supportVideoStatus1 | supportVideoStatus2;
                        this.supportVideo = (_supportVideo & 1) !== 0;
                        console.log((_supportVideo & 1) !== 0, '支持视频状态------------------');
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel('DEVICE', 'getStatusStr', {
                                model: model,
                                latestStatus: onlineStatus[0].value
                            }, (res) => {
                                newStatus.status = res.data;
                                this.currentDeviceStatus = { ...newStatus };
                            });
                        }
                    }
                }
            };
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: body
            }, commandResultCB);
        },
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 开始清洁
        startClean() {
            const { did = '', bindDomain = '', latestStatus, model } = this.currentDevice;
            const id = this.getRandomNumber()
            const bind_id = bindDomain.split('.')[0]
            let data = {}
            if (deviceCanStopStatus.includes(latestStatus)) {
                // eslint-disable-next-line object-curly-spacing
                data = { id, method: 'action', params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 } }
                Utils.reportEvent('device_pause', {
                    device_model: model
                })
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = { id, from: 'mapp', method: 'action', params: { did, siid: 2, in: [{ piid: 100, value: '2,1,{"app_auto_clean":1}' }], aiid: 1 } }
                Utils.reportEvent('device_start', {
                    device_model: model
                })
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data)
            }
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: params
            }, (res) => {
                console.log(' start clean ', res)
            });
        },
        // 返回基站
        goBackHome() {
            const { did = '', bindDomain = '', latestStatus } = this.currentDevice;
            const id = this.getRandomNumber()

            const bind_id = bindDomain.split('.')[0]
            let data = {}
            if (latestStatus === 5) {
                // eslint-disable-next-line object-curly-spacing
                data = { id, method: 'action', params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 } }
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = { id: id, from: 'mapp', method: 'action', params: { did: did, siid: 3, in: [{ piid: 100, value: '{"charge":1}' }], aiid: 1 } }
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data)
            }
            Utils.newMessageChannel('HTTP', 'request', {
                method: 'post',
                path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                body: params
            }, (res) => {
                console.log(' start charge ', res)
            });
        },
        // 进入视频页面
        goVideoPage() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'video' });
        },
        // 进入快捷指令页面
        goQuickOrderPage() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'quickCommand' });
        },
    }
}

</script>

<style scoped>
.device-item {
    position: relative;
    background: linear-gradient(116deg, #FDFAF6 0%, #F1E5CA 68%, #F6F0E2 104%);
}

.device-info-card {
    display: flex;
    justify-content: space-between;
}

.device-info-area {
    padding: 32rpx;
}

.device-info-name {
    color: #121212;
    font-size: 32rpx;
    font-weight: 600;
}
.device-info-status {
    color: #777777;
    font-size: 24rpx;
    line-height: 32rpx;
    display: flex;
    align-items: center;
    margin-top: 12rpx;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.col-line {
    width: 1rpx;
    height: 16rpx;
    background-color: #777777;
    margin: 0 16rpx;
}
.device-info-image-area {
    width: 160rpx;
    height: 180rpx;
    margin-right: 64rpx;
}
.device-info-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.device-control-area {
    padding: 0 32rpx 32rpx 32rpx;
}
.device-control-content {
    height: 84rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 32rpx;
    background-color: #FFFFFF;
}
.device-control-item {
    width: 25%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.device-control-icon {
    width: 48rpx;
    height: 48rpx;
}
</style>
