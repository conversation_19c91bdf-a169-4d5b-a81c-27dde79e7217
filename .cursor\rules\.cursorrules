# Vue.js/uni-app电商项目 Cursor规则

## 项目概述
这是一个基于uni-app框架构建的Vue.js移动端电商应用，特点包括：
- 多平台支持（H5、微信小程序、App）
- TypeScript类型安全
- SCSS样式
- 组件化架构
- 带TypeScript接口的HTTP服务层

## 技术栈
- **框架**: Vue.js 2.7 + uni-app
- **语言**: TypeScript + JavaScript
- **样式**: SCSS
- **状态管理**: Vuex
- **HTTP客户端**: 自定义请求层，带拦截器
- **UI组件**: uView UI（已打补丁版本）
- **装饰器**: vue-property-decorator（类组件风格）

## 项目结构
- `src/pages/` - 主要应用页面
- `src/pagesA/`, `src/pagesB/`, `src/pagesC/` - 功能特定页面组
- `src/components/` - 可复用Vue组件
- `src/http/` - API服务和请求工具
- `src/store/` - Vuex状态管理
- `src/utils/` - 工具函数
- `src/common/` - 常量、权限、验证规则
- `src/static/` - 静态资源（图片、图标）

## 代码风格与约定

### Vue组件
- 优先使用`vue-property-decorator`的类组件风格
- 对于简单组件，也可使用传统的选项式API
- 使用单文件组件(.vue)
- 组件名遵循PascalCase命名
- 类组件使用`@Component`装饰器声明
- 使用装饰器定义props、watch、emit等

### TypeScript
- 使用严格类型检查
- 在`src/http/interface/`中定义接口
- 谨慎使用类型断言
- 对象形状优先使用`interface`而非`type`

### 样式
- 所有样式表使用SCSS
- CSS类名遵循BEM命名规范
- 在组件中使用作用域样式
- 使用rpx单位实现uni-app响应式设计

### API层
- HTTP服务按功能在`src/http/`中组织
- 使用TypeScript接口定义请求/响应类型
- 实现适当的错误处理
- 使用请求拦截器处理通用headers/认证

## 开发指南

### 组件开发
- 保持组件小而专注
- 使用props输入数据，events输出数据
- 实现适当的加载状态
- 优雅处理错误状态
- 对重型组件使用懒加载

### 状态管理
- 使用Vuex管理全局状态
- 在组件中尽可能保持本地状态
- 使用mixins或工具函数共享逻辑
- 实现适当的响应式模式

### 性能优化
- 对长列表实现虚拟滚动
- 对图片使用懒加载
- 通过树摇优化包体积
- 在适当时缓存API响应

### 移动端优先方法
- 为触摸交互设计
- 实现适当的响应式布局
- 考虑不同屏幕尺寸
- 处理网络连接问题

## 常用模式

### 页面结构（推荐使用类组件）
```vue
<template>
  <view class="page-container">
    <!-- 页面内容 -->
  </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
  name: 'PageName'
})
export default class PageName extends Vue {
  // 组件逻辑
}
</script>

<style lang="scss" scoped>
.page-container {
  // 页面样式
}
</style>
```

### vue-property-decorator类组件模式
```vue
<template>
  <view class="page-container">
    <text>{{ title }}</text>
    <button @click="handleClick">{{ buttonText }}</button>
  </view>
</template>

<script lang="ts">
import { Component, Prop, Watch, Emit, Vue } from 'vue-property-decorator'

@Component({
  name: 'MyComponent'
})
export default class MyComponent extends Vue {
  @Prop({ required: true }) title!: string
  @Prop({ default: '点击' }) buttonText!: string
  
  private count = 0
  
  @Watch('count')
  onCountChange(newVal: number, oldVal: number) {
    console.log('count changed:', newVal, oldVal)
  }
  
  @Emit('click')
  handleClick() {
    this.count++
    return this.count
  }
  
  // 生命周期钩子
  mounted() {
    console.log('组件已挂载')
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  // 页面样式
}
</style>
```

## vue-property-decorator使用规范

### 装饰器使用指南
- **@Component**: 声明组件，必须放在类的最前面
- **@Prop**: 声明props，支持类型检查和默认值
- **@Watch**: 监听数据变化，可指定immediate和deep选项
- **@Emit**: 声明事件发射，自动将方法名转换为kebab-case事件名
- **@Provide/@Inject**: 依赖注入，用于跨组件通信
- **@Model**: 双向绑定，用于自定义v-model
- **@Ref**: 获取模板引用

### 类组件最佳实践
```typescript
import { Component, Prop, Watch, Emit, Vue, Provide, Inject } from 'vue-property-decorator'

@Component({
  name: 'ExampleComponent',
  components: {
    // 子组件注册
  }
})
export default class ExampleComponent extends Vue {
  // 1. Props声明
  @Prop({ type: String, required: true }) 
  title!: string
  
  @Prop({ type: Number, default: 0 }) 
  count!: number
  
  // 2. 响应式数据
  private loading = false
  private userInfo: UserInfo | null = null
  
  // 3. 计算属性
  get displayTitle(): string {
    return this.loading ? '加载中...' : this.title
  }
  
  // 4. 侦听器
  @Watch('count', { immediate: true, deep: true })
  onCountChange(newVal: number, oldVal: number) {
    console.log('count changed:', newVal, oldVal)
  }
  
  // 5. 方法
  @Emit('update:count')
  updateCount(value: number) {
    return value
  }
  
  private async fetchData() {
    this.loading = true
    try {
      const response = await api.getUserInfo()
      this.userInfo = response.data
    } catch (error) {
      console.error('获取数据失败:', error)
    } finally {
      this.loading = false
    }
  }
  
  // 6. 生命周期钩子
  mounted() {
    this.fetchData()
  }
}
```

### 装饰器类型定义
```typescript
// 接口定义
interface UserInfo {
  id: number
  name: string
  avatar?: string
}

// Props接口
interface Props {
  title: string
  count: number
  userInfo?: UserInfo
}

// 事件接口
interface Events {
  'update:count': number
  'user-select': UserInfo
}
```

### Vuex与装饰器结合使用
```typescript
import { Component, Vue } from 'vue-property-decorator'
import { namespace } from 'vuex-class'

const UserModule = namespace('user')

@Component({
  name: 'UserComponent'
})
export default class UserComponent extends Vue {
  // 映射state
  @UserModule.State('userInfo') userInfo!: UserInfo
  @UserModule.State('loading') loading!: boolean
  
  // 映射getter
  @UserModule.Getter('isLoggedIn') isLoggedIn!: boolean
  
  // 映射mutation
  @UserModule.Mutation('SET_USER_INFO') setUserInfo!: (userInfo: UserInfo) => void
  
  // 映射action
  @UserModule.Action('fetchUserInfo') fetchUserInfo!: () => Promise<void>
  
  async mounted() {
    if (!this.isLoggedIn) {
      await this.fetchUserInfo()
    }
  }
}
```

## 文件命名约定
- 组件: PascalCase（例如：`CustomButton.vue`）
- 页面: camelCase（例如：`orderDetail.vue`）
- 工具: camelCase（例如：`dateUtil.ts`）
- 类型/接口: PascalCase（例如：`UserInfo.ts`）
- SCSS文件: kebab-case（例如：`order-detail.scss`）

## 最佳实践
1. **错误处理**: 始终为API调用实现适当的错误处理
2. **加载状态**: 为异步操作显示加载指示器
3. **验证**: 对用户输入使用表单验证
4. **可访问性**: 在组件设计中考虑可访问性
5. **测试**: 为关键业务逻辑编写单元测试
6. **文档**: 为复杂组件和工具编写文档
7. **装饰器使用**: 优先使用vue-property-decorator的类组件风格，保持代码清晰和类型安全

## uni-app特定
- 使用uni-app API处理平台特定功能
- 优雅处理平台差异
- 使用条件编译处理平台特定代码
- 遵循uni-app生命周期钩子和约定
- 在类组件中使用uni-app生命周期钩子（如`onLoad`、`onShow`等）

## 添加新功能时
1. 为新数据结构创建TypeScript接口
2. 在适当的`src/http/`子目录中添加API服务
3. 遵循现有组件模式（优先使用vue-property-decorator类组件）
4. 添加适当的错误处理和加载状态
5. 如需全局状态变更，更新store并使用vuex-class装饰器
6. 跨不同平台测试（H5、微信、App）

## 代码质量
- 使用ESLint和Prettier进行代码格式化
- 遵循 目录下的 .prettierrc.js 文件格式化 以及 .eslintrc.js 文件格式化
- 遵循Vue.js风格指南
- 编写自文档化代码
- 使用有意义的变量和函数名
- 保持函数简小而专注
- 避免深度嵌套和复杂逻辑
- 每次修改完代码，都需要把新代码执行一下`npm run lint`和`npm run format`，确保代码符合规范

## 依赖管理
- 保持依赖项更新
- 对关键依赖使用确切版本
- 记录任何自定义补丁（如uview-ui补丁）
- 添加新依赖时最小化包体积影响
- 核心装饰器依赖：`vue-property-decorator`、`vuex-class`

## uni-app页面类组件示例
```vue
<template>
  <view class="page">
    <text>{{ title }}</text>
    <button @click="navigateToDetail">查看详情</button>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { namespace } from 'vuex-class'

const AppModule = namespace('app')

@Component({
  name: 'HomePage'
})
export default class HomePage extends Vue {
  @AppModule.State('title') title!: string
  
  private pageData = {}
  
  // uni-app页面生命周期
  onLoad(options: any) {
    console.log('页面加载', options)
    this.initPage()
  }
  
  onShow() {
    console.log('页面显示')
  }
  
  onReady() {
    console.log('页面初次渲染完成')
  }
  
  // Vue生命周期
  mounted() {
    console.log('组件挂载完成')
  }
  
  // 页面方法
  private initPage() {
    // 初始化页面数据
  }
  
  private navigateToDetail() {
    uni.navigateTo({
      url: '/pages/detail/detail'
    })
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 20rpx;
}
</style>
``` 