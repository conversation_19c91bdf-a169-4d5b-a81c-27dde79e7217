.class-item {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  position: relative;
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  margin: 0 16rpx 16rpx 16rpx;

  .class-item-top {
    // width: 59vw;
    // padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
    // margin: 0 auto 0rpx;
    position: relative;
    padding-left: 10rpx;

    .yushouImg {
      position: absolute;
      top: -21rpx;
      left: -21rpx;
      width: 150rpx;
      z-index: 99;
      height: 70rpx;
      // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887b4ebe9fdc9580010955.png');
      background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6baf24410011380.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .hotProImg {
      position: absolute;
      top: -21rpx;
      left: -21rpx;
      width: 150rpx;
      height: 70rpx;
      // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d36c841d8200010937.png');
      background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6adab4380010532.png');

      background-repeat: no-repeat;
      background-size: 100% 100%;
      z-index: 99;
    }

    .newProImg {
      position: absolute;
      top: -21rpx;
      left: -21rpx;
      z-index: 99;
      width: 150rpx;
      height: 70rpx;
      // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d33c15417920012131.png');
      background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6a7414360010826.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .lazyImg {
      width: 264rpx;
      height: 264rpx;
      border-radius: 38rpx;
    }

    .custom_tag {
      position: absolute;
      top: 0;
      right: 0;
      width: 126rpx;
      height: 58rpx;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687791bc05b370230010836.png') no-repeat;
      background-size: cover;
      border-radius: 0 8rpx 0 0;
      z-index: 99;
    }

    .service-content {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 42rpx;
      height: 36rpx;
      z-index: 99;

      .service1 {
        width: 100%;
        height: 100%;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687780e260d073970010659.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }

      .service1-active {
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687794fe1cfb61190010236.png');
      }
    }

  }

  .class-item-bottom {
    height: 100%;
    padding: 20rpx;
    position: relative;
    min-height: 270rpx;
    width: 100%;

    .name {
      position: relative;
      font-family: MiSans, MiSans;
      font-weight: 500;
      font-size: 28rpx;
      color: #121212;
      line-height: 32rpx;
      text-align: left;

      //  display: flex;
      // flex-direction: column;
      // justify-content: center;
      .titleW {
        line-height: 40rpx;
      }

      .tag {
        position: absolute;
        left: 0;
        top: 4rpx;
      }

      .ysIcon {
        width: 56rpx;
        height: 28rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8c1015740012798.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

      .htIcon {
        width: 56rpx;
        height: 28rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8bf355730017331.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

      .newIcon {
        width: 56rpx;
        height: 28rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d557d8c0005730011893.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

      .brand {
        width: 64rpx;
        height: 32rpx;
        // border-radius: 4px;
        // background-color: #000000;
        color: #B59A6E;
        text-align: center;
        // padding: 6rpx 8rpx;
        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e272faa3206970149982.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

      }

      .strictly-selected {
        width: 52rpx;
        height: 26rpx;
        border-radius: 4rpx;
        background: linear-gradient(121deg, #318AF7 13%, #28B0EE 100%, #2997E3 100%);

        color: #fff;
        text-align: center;
        padding: 6rpx 8rpx;
      }

      .ecology {
        width: 52rpx;
        height: 26rpx;
        border-radius: 4px;
        background-color: #FFF4EF;
        color: #FB3019;
        text-align: center;
        padding: 6rpx 8rpx;
      }
    }

    .couponText {
      box-sizing: content-box;
      width: 100rpx;
      // height: 36rpx;
      margin: 16rpx 0 10rpx;
      padding: 6rpx 0rpx 6rpx 6rpx;
      background: linear-gradient(89deg,
          rgba(252, 46, 46, 0.09) 0%,
          rgba(228, 43, 43, 0) 100%);
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 20rpx;
      color: #ee3c2f;
      line-height: 24rpx;
    }

    .price {
      display: flex;
      align-items: baseline;
      margin-top: 16rpx;
      font-family: MiSans, MiSans;
      position: absolute;
      bottom: 10rpx;
      height: 58rpx;
      line-height: 58rpx;
      width: 90%;
      color: #FF3654;
      font-size: 28rpx;
      // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png');
      // background-color: #fbf0ef;
      border-radius: 32rpx;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 0rpx 6rpx 0rpx 16rpx;

      .content_title_text_grab_text {
        // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 126rpx;
        height: 52rpx;
        line-height: 52rpx;
        // position: absolute;
        // bottom: 0rpx;
        // top: -10rpx;
        // right: 0;
        background: linear-gradient(270deg, #FF1C7C 5%, #FF4242 100%);
        color: #fff;
        text-align: center;

        border-radius: 160rpx;
        font-weight: bold;
      }

      .unit {
        height: 24rpx;
        font-weight: 500;
        font-size: 24rpx;
        color: #FF3654;
        line-height: 58rpx;
      }

      .optimizePrice {
        height: 58rpx;
        font-weight: 600;
        font-size: 22rpx;
        color: #FF3654;
        line-height: 58rpx;
      }

      .originPrice {
        font-family: MiSans;
        font-size: 22rpx;
        font-weight: normal;
        line-height: normal;
        /* NeutralColor中性色/Gray 5 */
        color: #A6A6A6;
        margin-left: 8rpx;
        text-decoration: line-through;
      }

      .dot {
        height: 32rpx;
        font-family: MiSans, MiSans;
        font-weight: 500;
        font-size: 24rpx;
        color: #404040;
        line-height: 32rpx;
      }

      .primaryPrice {
        height: 32rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #a6a6a6;
        line-height: 32rpx;
        text-decoration-line: line-through;
      }

      .coupon-text {
        margin-left: 4rpx;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 20rpx;
        color: #ee3c2f;
        line-height: 24rpx;
      }

      .price_skeleton {
        display: flex;

        ::v-deep .u-skeleton {
          flex: 0 0 auto;

          &:nth-child(1) {
            margin-right: 16rpx;
          }
        }
      }
    }
  }

  // &:nth-child(even) {
  //     transform: translateX(8rpx);
  // }
}