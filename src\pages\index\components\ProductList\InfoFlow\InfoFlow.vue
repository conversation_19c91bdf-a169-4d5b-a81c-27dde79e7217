<template>
     <block v-if="type==='goods'">
        <view @click="handleClick">
             <GoodsAct :Item="Item"></GoodsAct>
        </view>
    </block>
    <block v-else-if="type==='image'">
        <view @click="handleClick">
             <ImageAdv :Item="Item"></ImageAdv>
        </view>
    </block>
    <block v-else-if="type==='post'">
        <view @click="handleClick">
            <POSTAct :Item="Item"></POSTAct>
        </view>
    </block>
    <block v-else>
    </block>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import POSTAct from './components/post.vue';
import GoodsAct from './components/goods.vue';
import ImageAdv from './components/imageAdv.vue';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
@Component({
    components: {
        POSTAct,
        GoodsAct,
        ImageAdv
    },
})
export default class More extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Object, default: { }})
    readonly Item!: any;

    @Prop({ type: String, default: 'vertical' })
    readonly direction!: string;

    @Prop({ type: String, default: 'vertical' })
    readonly type!: string;

     handleClick() {
        const { jump_link, type, id } = this.Item;
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_TO_ADVS,
                id: Number(id),
            },
            true,
        );
        if (type === 'goods') {
            Utils.navigateTo(`${jump_link}`);
            // uni.navigateTo({
            //     url: `${jump_link}`
            // })
        } else if (type === 'post') {
             Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' });
            // uni.navigateTo({
            //     url: `${jump_link}`
            // })
        } else if (type === 'image') {
            Utils.navigateTo(`${jump_link}`);
            // uni.navigateTo({
            //     url: `${jump_link}`,
            // })
        } else {
            Utils.navigateTo(`${jump_link}`);
            // uni.navigateTo({
            //     url: `${jump_link}`
            // })
        }
        // console.log('click', this.Item);
        // this.$emit('click', this.Item);
    }
}
</script>
<style lang="scss" scoped>
@import "./InfoFlow.scss";
.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}
</style>
