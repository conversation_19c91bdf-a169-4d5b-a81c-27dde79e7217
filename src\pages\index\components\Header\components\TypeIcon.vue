<template>
    <view class="icon-type">
        <!-- #ifdef H5 || APP-PLUS -->
        <image style="width: 262rpx; height: 30rpx;"
            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f3586b368e7350011997.png">
        </image>
        <!-- <view class="title">追觅科技</view> -->
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN-->
        <!-- <image style="width: 276rpx; height: 28rpx;" src="https://wpm-cdn.dreame.tech/images/202309/650a9380bca5e7723515090.png"></image> -->
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component({
})
export default class TypeIcon extends Vue {

}
</script>
<style lang="scss" scoped>
.icon-type {
    position: relative;
    font-size: 26rpx;

    .title {
        margin-top: 8rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: $text-color-primary;
        // line-height: 38rpx;
    }
}
</style>
