import http from './index';

/**
 * 获取心愿列表列表
 */
export function getRecommendFriendList(page:number, page_size:number) {
    return http.post('/main/goods-recommend/friend-list', { page, page_size });
}

/**
 * 取消，加入心愿单
 */
export function updateShopRecommend(gid: string, type : number) {
    return http.post('/main/goods-recommend/user-recommend', { gid, type });
}

/**
 * 我的推荐列表
 */
export function myRecommendFriend(page:number, page_size:number) {
    return http.post('/main/goods-recommend/user-list', { page, page_size });
}
