<template>
    <view class="coin-points">
        <!-- <view class="coin-points-item" v-if="coin > 0">
            <image style="width: 48rpx; height: 48rpx;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6870cea3038ed0150012285.png"></image>
            <view class="coin-points-item-text">
                <view class="coin-points-item-text-value">{{ coin }}</view>
                <view class="coin-points-item-text-title">金币</view>
            </view>
        </view> -->
        <view class="coin-points-item" @click="handleContact">
            <image style="width: 40rpx; height:40rpx;" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6895fc51b595e7440012050.png"></image>
            <!-- <view class="coin-points-item-text">
                <view class="coin-points-item-text-value-info">{{ points }}</view>

            </view> -->
        </view>

    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
// import { UserModule } from '@/store/modules/user';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import Constants from '@/common/Constants';

@Component
export default class CoinPoints extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    handlePointsClick() {
        Utils.navigateTo('/pagesA/point/shop_point');
        // Utils.navigateTo('/pagesC/purchase/purchase');
        // Utils.navigateTo('/pagesC/goldCoins/goldCoins');
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }

     handleContact() {
       Utils.decryptContact({}, BuType.BuType_NONE);
    }
}
</script>
<style lang="scss" scoped>
.coin-points {
    display: flex;
    align-items: center;
    gap: 30rpx;
    height: 114rpx;
}
.coin-points-item {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.coin-points-item-text{
    margin-left: 8rpx;
    text-align: center;
    transform: translateY(2rpx);
}
.coin-points-item-text-value-info{
    font-family: MiSans;
    font-size: 28rpx;
    font-weight: 500;
    letter-spacing: normal;
    color: #8C6533;
    line-height: 1;
}
.coin-points-item-text-title{
    font-family: MiSans;
    font-size: 20rpx;
    letter-spacing: normal;
   color: #777777;;
    line-height: 1;
}
</style>
