diff --git a/node_modules/uview-ui/components/u-collapse/u-collapse.vue b/node_modules/uview-ui/components/u-collapse/u-collapse.vue
index fc188a2..7ead8c2 100644
--- a/node_modules/uview-ui/components/u-collapse/u-collapse.vue
+++ b/node_modules/uview-ui/components/u-collapse/u-collapse.vue
@@ -35,17 +35,6 @@
 				return [this.accordion, this.value]
 			}
 		},
-		watch: {
-			// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件
-			parentData() {
-				if (this.children.length) {
-					this.children.map(child => {
-						// 判断子组件(u-checkbox)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)
-						typeof(child.updateParentData) === 'function' && child.updateParentData()
-					})
-				}
-			},
-		},
 		methods: {
 			// 重新初始化一次内部的所有子元素
 			init() {
