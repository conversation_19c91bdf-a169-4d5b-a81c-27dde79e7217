<template>
    <view class="container">
        <CustomBar title="确认订单" background="#F4F4F4" :isFixed="true"></CustomBar>
        <view class="main" v-if="isLoaded">
            <CouponInfo v-if="isCoupon" />
            <Address v-else :address="addressInfo" :ordinaryAddress="true" :addressList="addressList"></Address>
            <!-- 商品列表 -->
            <view class="main-center" v-if="orderInfo.list && orderInfo.list.length > 0" style="margin-top: 24rpx">
                <block v-for="(item, index) in orderInfo.list" :key="index">
                    <GoodItemOrder
                        type="order"
                        :showBottom="false"
                        :is_last_child="orderInfo.list.length - 1 > index"
                        :index="index"
                        :key="item.id"
                        :good="item"
                        :buy_goods_number="buy_goods_number"
                        :total="orderInfo.total"
                        :isShowSelf="false"
                        :isShowdeduction="false"
                        :direction="'horizontal'"
                        :isShowDetail="false"
                        :pointsMall="pointsMall"
                        :isShowNumBox="true"
                        :bigCover="true"
                        :is_trade_in="is_trade_in"
                        @valChange="valChange($event, item, index)"
                        @limitOver="handleOverLimit"
                    >
                        <view slot="footer" v-if="is_presale" style="padding-top: 32rpx">
                            <pre-sale-step1
                                height="92rpx"
                                :step2lineNum="2"
                                stepStatus="1"
                                :presaleInfo="item.presale_info"
                            ></pre-sale-step1>
                        </view>
                    </GoodItemOrder>
                </block>
            </view>

            <!-- 订单信息 -->
            <view class="main-bottom" style="margin-top: 24rpx" v-if="!pointsMall">
                <view class="content">
                    <view class="order-message">订单明细</view>
                    <view v-if="!is_presale" class="main-bottom-item u-flex u-col-center u-row-between">
                        <text class="title"
                            >商品金额<text class="total">(共{{ totality }}件)</text></text
                        >
                        <text class="item-right">¥ {{ orderInfo.total.tprice }}</text>
                    </view>
                    <view
                        v-if="is_presale && orderInfo.pay_style == 1"
                        class="main-bottom-item u-flex u-col-center u-row-between"
                    >
                        <text class="title">定金</text>
                        <text class="item-right">¥ {{ orderInfo.total.deposit }}</text>
                    </view>
                    <view class="main-bottom-item u-flex u-col-center u-row-between">
                        <text class="title">快递费</text>
                        <text class="item-right"
                            >¥ {{ orderInfo.total ? orderInfo.total.fprice || '0.00' : '0.00' }}</text
                        >
                    </view>
                    <!-- <view class="main-bottom-item u-flex u-col-center u-row-between" v-if="orderInfo.total.is_yjhx == 1">
                        <text class="title">以旧焕新</text>
                        <text class="item-right renew">- ¥ {{ orderInfo.total.acdeprice }}</text>
                    </view> -->

                    <!-- 好友分享获得的购物金 -->
                    <view
                        class="main-bottom-item u-flex u-col-center u-row-between"
                        @click="handleShopMoneyPop"
                        v-if="disable_all_discount != '1'"
                    >
                        <text class="title">购物金</text>
                        <view class="item-right u-flex u-row-center u-col-center">
                            <text
                                :class="{
                                    redColor: shopMoneyText.indexOf('暂无可用') < 0,
                                    grayColor: shopMoneyText.indexOf('暂无可用') > -1 || !isChooseShopMoney,
                                }"
                            >
                                {{ shopMoneyText }}
                            </text>
                            <image
                                class="icon-right"
                                src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                            />
                        </view>
                    </view>

                    <!-- 优惠券 -->
                    <view
                        v-if="!is_presale && orderInfo.total.acdeprice == '0.00' && disable_all_discount != '1'"
                        class="main-bottom-item coupon u-flex u-col-center u-row-between"
                        @click="handleCouponPop"
                    >
                        <text class="title">优惠券</text>
                        <view class="item-right u-flex u-row-center u-col-center">
                            <text
                                :class="{
                                    redColor: couponText.indexOf('暂无可用') < 0,
                                    coupon: couponText.indexOf('张可用') > -1,
                                    grayColor: couponText.indexOf('暂无可用') > -1 || isChooseShopMoney,
                                }"
                                >{{ couponText }}</text
                            >
                            <image
                                class="icon-right"
                                src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                            />
                        </view>
                    </view>

                    <!-- 积分 -->
                    <block v-if="pointsMall">
                        <view
                            class="main-bottom-item u-flex u-col-center u-row-between"
                            @click="handleCoinPop"
                            v-if="!is_presale && disable_all_discount != '1'"
                        >
                            <text class="title">积分</text>
                            <view class="item-right u-flex u-row-center u-col-center">
                                <text
                                    :class="{
                                        redColor:
                                            coinData.coin_type == '1' ||
                                            (coinData.coin_type == 0 && coinData.can_coin != 0),
                                        grayColor: coinText.indexOf('暂无可用') > -1 || isChooseShopMoney,
                                    }"
                                    >{{ coinText }}</text
                                >
                                <image
                                    class="icon-right"
                                    src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                                />
                            </view>
                        </view>
                    </block>
                    <!-- 国家补贴 -->
                    <view
                        v-if="!is_presale && !pointsMall && Number(orderInfo.total.subsidy_price) > 0"
                        class="main-bottom-item subsidy u-flex u-col-center u-row-between"
                    >
                        <text class="title">国家补贴</text>
                        <text class="item-right renew">- ¥{{ orderInfo.total.subsidy_price }}</text>
                    </view>

                    <!-- 觅码 -->
                    <view
                        v-if="
                            orderInfo.total.acdeprice == '0.00' &&
                            (!is_presale || (is_presale && orderInfo.pay_style == 2)) &&
                            disable_all_discount != '1'
                        "
                        class="main-bottom-item gift-card u-flex u-col-center u-row-between"
                        @click="handleSerchCodePop"
                    >
                        <text class="title">觅码</text>
                        <view class="item-right u-flex u-row-center u-col-center">
                            <text
                                :class="{
                                    redColor: searchCodeText.indexOf('暂无可用') < 0,
                                    grayColor: searchCodeText.indexOf('暂无可用') > -1 || isChooseShopMoney,
                                }"
                                >{{ searchCodeText }}</text
                            >
                            <image
                                class="icon-right"
                                src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                            />
                        </view>
                    </view>

                    <!-- 消费金 -->
                    <view
                        v-if="disable_all_discount != '1' && !is_presale && !pointsMall"
                        class="main-bottom-item u-flex u-col-center u-row-between"
                        @click="handleConsumeMoneyPop"
                    >
                        <text class="title">消费金</text>
                        <view class="item-right u-flex u-row-center u-col-center">
                            <text
                                :class="{
                                    redColor: consumeMoneyText.indexOf('暂无可用') < 0,
                                    grayColor: consumeMoneyText.indexOf('暂无可用') > -1,
                                }"
                            >
                                {{ consumeMoneyText }}
                            </text>
                            <image
                                class="icon-right"
                                src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                            />
                        </view>
                        <!-- <text class="item-right renew">- ¥{{ orderInfo.total.consume_money.total_consume_money }}</text> -->
                    </view>

                    <view class="tip" v-if="disable_all_discount != '1'"
                        >购物金不支持和优惠券、积分、国补、礼品卡同时使用</view
                    >
                </view>
                <view class="total info-item u-flex u-col-center u-row-between">
                    <text class="actually-paid">应付</text>
                    <text class="actually-paid-real_price">¥ {{ orderInfo.total.real_price }}</text>
                </view>
                <view class="left-groove"></view>
                <view class="right-groove"></view>
            </view>
            <view
                class="main-bottom-item note u-flex u-col-center u-row-between"
                @click="handleNotePop"
                v-if="!pointsMall"
            >
                <text class="title">订单备注</text>
                <view class="item-right u-flex u-row-center u-col-center">
                    <text class="u-line-1" :class="{ grayColor: !note }">{{ note || '无备注' }}</text>
                    <image
                        class="icon-right"
                        src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"
                    />
                </view>
            </view>
            <!-- 订单信息 积分商城-->
            <view class="main-bottom" style="margin-top: 24rpx; padding-bottom: 46rpx" v-if="pointsMall">
                <view class="order-message">订单明细</view>
                <view class="main-bottom-item u-flex u-col-center u-row-between">
                    <text class="title">快递费</text>
                    <text class="item-right">{{
                        orderInfo.total && orderInfo.total.fprice && Number(orderInfo.total.fprice)
                            ? `￥${orderInfo.total.fprice}` || '包邮'
                            : '包邮'
                    }}</text>
                </view>
                <view v-if="!is_presale" class="main-bottom-item u-flex u-col-center u-row-between">
                    <text class="title">金额</text>
                    <text class="item-right">￥{{ orderInfo.total.tprice }}</text>
                </view>
                <view
                    v-if="is_presale && orderInfo.pay_style == 1"
                    class="main-bottom-item u-flex u-col-center u-row-between"
                >
                    <text class="title">定金</text>
                    <text class="item-right">￥{{ orderInfo.total.deposit }}</text>
                </view>
                <view class="main-bottom-item u-flex u-col-center u-row-between">
                    <text class="title">积分</text>
                    <view class="item-right u-flex u-row-center u-col-center">
                        {{ orderInfo.total.coin.can_coin }}积分
                    </view>
                </view>
            </view>
        </view>
        <!-- <view class="no-data" v-else>
            <image class="img" src="https://wpm-cdn.dreame.tech/images/202309/405025-1694584514109.png"></image>
            <text class="tips">去看看其他商品吧~</text>
        </view> -->
        <!-- 底部按钮 -->
        <view class="footer" :style="{ minHeight: is_presale ? '254rpx' : '180rpx' }" v-if="isLoaded">
            <view v-if="is_presale" class="pre-accord u-flex" @click="preAccord = !preAccord">
                <image
                    class="icon"
                    :src="
                        preAccord
                            ? 'https://wpm-cdn.dreame.tech/images/202307/64a625a5116940713744850.png'
                            : require('@/static/check.png')
                    "
                    mode="aspectFit"
                ></image>
                <view class="accord-text"
                    >未付尾款定金不退，付尾款后可全额退款，详见<text class="link" @click.stop="handlePreAccord"
                        >《预售协议》</text
                    >
                </view>
            </view>
            <view class="u-flex u-col-center u-row-between">
                <view class="price u-flex-col">
                    <view class="total-text">共{{ len }}件 </view>
                    <view class="u-flex all" v-if="!pointsMall">
                        合计：
                        <CustomAmount
                            :totalPrice="orderInfo.total.real_price"
                            unitWeight="500"
                            :totalStyle="{
                                'font-size': '32rpx',
                                'line-height': '40rpx',
                                'font-weight': 500,
                            }"
                            unitSize="32"
                        />
                        <view class="total-discount" v-if="Number(coSubtract)"
                            >共减 <text class="title">¥{{ coSubtract }}</text></view
                        >
                    </view>
                    <view class="u-flex all" v-if="pointsMall">
                        合计：
                        <view class="price-point">
                            <view class="point" v-if="Number(orderInfo.total.coin.can_coin)"
                                ><text class="point-number">{{ orderInfo.total.coin.can_coin }}</text
                                ><text class="point-unit">积分</text></view
                            >
                            <view
                                class="plus-sign"
                                v-if="Number(orderInfo.total.coin.can_coin) && Number(orderInfo.total.real_price)"
                                >+
                            </view>
                            <CustomAmount
                                v-if="Number(orderInfo.total.real_price)"
                                :totalPrice="orderInfo.total.real_price"
                                unitWeight="500"
                                :totalStyle="{
                                    'font-size': '28rpx',
                                    'line-height': '40rpx',
                                    'font-weight': 500,
                                }"
                                unitSize="32"
                            />
                        </view>
                    </view>
                </view>
                <view
                    class="btn"
                    hover-class="btn-hover"
                    @click="$u.debounce(is_presale ? handlePreOrderBuy : handleOrderBuy, 300)"
                    >提交订单</view
                >
            </view>
        </view>
        <pop-order-fail :isShow.sync="showOrderFailPop" :tips="orderFailMsg" />
        <!-- 优惠券弹窗 -->
        <pop-coupon
            :carList="orderInfo.list"
            :isShow.sync="showCouponPop"
            :consumeIds="consumeIds"
            :isDisabled="isDisabled"
            :isChooseShopMoney="isChooseShopMoney"
            @onSelectCoupon="onSelectCoupon"
            @comfirm="handlePopComfirm"
        />
        <!-- 礼品卡弹窗 -->
        <pop-search-code
            @comfirm="comfirmSearchCode"
            @onSelect="onSelect"
            :serchCodeList="serchCodeList"
            :isShow.sync="showSearchCodePop"
        />
        <!-- 购物金弹窗 -->
        <pop-shop-money
            @comfirm="comfirmShopMoney"
            @onSelect="onShopMoneySelect"
            :shopMoneyList="shopMoneyList"
            :isShow.sync="showShopMoneyPop"
        />
        <!-- 消费金弹窗 -->
        <pop-consume-money
            @comfirm="comfirmConsumeMoney"
            @onSelect="onConsumeMoneySelect"
            :consumeMoneyList="consumeMoneyList"
            :isShow.sync="showConsumeMoneyPop"
        />
        <!-- 积分弹窗 -->
        <pop-coin
            :isShow.sync="showCoinPop"
            :coinData="coinData"
            @comfirm="handleCoinPopComfirm"
            :rate="coinData.rate"
        />
        <pop-note :isShow.sync="showNotePop" :note="note" @comfirm="handleNotePopComfirm" />
        <u-modal
            :show="showPopTips"
            v-prevent-scroll="showPopTips"
            width="560rpx"
            confirmText="我知道了"
            @confirm="refreshPage"
        >
            <view class="slot-content">
                {{ hasNewMessage }}
            </view>
        </u-modal>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { orderBuyInfo, commonBuy, orderBuy } from '@/http/order';
import { checkGroupLeader, checkGroupOrderNo, checkOrderStatus, checkCoupon } from '@/http/goods';
import { waresOrderBuyInfo, waresOrderBuy } from '@/http/pointsMall';
import { preOrderBuyInfo, depositBuy } from '@/http/deposit';
import { OrderModule } from '@/store/modules/order';
import { IOrderBuyInfo, IOrderBuyParams, ICoinData, IUnionSource } from '@/store/interface/order';
import { getAddressList } from '@/http/address';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import PopOrderFail from './components/PopOrderFail.vue';
import PopCoupon from './components/PopCoupon.vue';
import PopSearchCode from './components/PopSearchCode.vue';
import PopShopMoney from './components/PopShopMoney.vue';
import PopConsumeMoney from './components/PopConsumeMoney.vue';
import GoodItemOrder from '@/components/GoodItem/GoodItemOrder.vue';
import PopCoin from './components/PopCoin.vue';
import PopNote from './components/PopNote.vue';
import Address from '@/components/AddressPicker/Address.vue';
import PreSaleStep1 from '@/components/PreSaleStep1/index.vue';
import CouponInfo from './components/CouponInfo.vue';
import { userCardList } from '@/http/giftCard';
import Utils from '@/common/Utils';
import { GoodsModule } from '@/store/modules/goods';
import { UserModule } from '@/store/modules/user';

const TYPE_COUPON = '1'; // 优惠卷
const EXCHANGE_COUPON = '6'; // 兑换券
const CONSUMER_COUPON = '10'; // 消费券
interface IAddressInfo {
    id: string;
    nick: string;
    phone: string;
    province: string;
    city: string;
    area: string;
    detail: string;
}
@Component({
    components: {
        PopOrderFail,
        PopCoupon,
        PopCoin,
        Address,
        PopNote,
        PreSaleStep1,
        GoodItemOrder,
        PopSearchCode,
        CouponInfo,
        PopShopMoney,
        PopConsumeMoney,
    },
})
export default class Settlement extends Vue {
    public storekeeper: string = ''; // 店铺user_id
    public unionSource: IUnionSource = IUnionSource.EMPTY;
    public is_presale: number = 0; // 是否预售
    private sParams: string = ''; // 结算参数
    private ac_data: string = ''; // 以旧焕新参数
    private cartIds: string = ''; // 购物车id
    private useCid: string = ''; // 指定使用优惠券id
    public in_express_time: string = ''; // 以旧换新取件时间
    public is_trade_in: string = ''; // 是否以旧换新
    public group_activity_id: number = 0; // 活动id
    public group_purchase_id: number = 0; // 拼团id
    public is_leader: Boolean = false; // 是否是团长
    public pollingTimeout: number = 0; // 已经轮询支付状态的时间
    public timeout: number = 300; // 多少毫秒轮询一次
    public pollingCountTime: number = 2000; // 轮询总时长
    public pollResult: Boolean = false; // 轮询结果
    // public isUseShoppingPrice: Boolean = false; // 选择优惠券后是走购物金还是其他

    public isLoaded: Boolean = false;
    public showPopTips: Boolean = false;
    public preAccord: Boolean = false; // 预售协议
    public pointsMall: Boolean = true;
    public pointGoodType: string = ''; // 积分商品类型 1、实体商品 2、虚拟商品
    public pointGoodSubtype: string = ''; // 积分商品子类型 1、优惠券 2、停车券
    public pop_coupon_id: string | number = '';
    public pop_consume_id: string | number = '';
    public seckill_id: number = -1; // 秒杀id
    public disable_all_discount: string = '0'; // 是否禁用所有折扣
    public typeActivity: string = '0'; // 活动类型
    public orderInfo: IOrderBuyInfo = {
        list: [],
        goods: [],
        gcombines: [],
        cart_ids: [],
        coupon: undefined,
        total: {
            tprice: '0',
            fprice: '0',
            coin: undefined,
            coupon: {
                coupon_id: '',
                cprice: '',
            },
            consume: {
                consume_id: '',
                consume_price: '',
            },
            gift_card: {
                can_number: 0,
                use_amount: 0,
                gift_card_ids: [],
            },
            shopping_price: {
                can_use_shopping_price: '0.00',
                is_use_shopping_price: 0,
                total_shopping_price: '0.00',
            },
            consume_money: {
                can_use_consume_money: '0.00',
                is_use_consume_money: 0,
                total_consume_money: '0.00',
            },
            price: '',
            real_price: '',
            acdeprice: '',
            subsidy_price: '0.00',
            is_yjhx: 0,
        },
        usage_coupon_num: 0,
        usage_consume_num: 0,
    };

    public orderAid: string = '0'; // 订单地址ID
    public trade_in_aid: string = ''; // 以旧换新地址ID
    public addressList: Array<any> = []; // 地址列表
    public addressInfo: IAddressInfo = { id: '', nick: '', phone: '', province: '', city: '', area: '', detail: '' };
    public note: string = '';
    public showOrderFailPop: Boolean = false; // 下单失败弹窗
    public showCouponPop: Boolean = false; // 选择优惠券弹窗
    public showSearchCodePop: Boolean = false; // 选择觅码弹窗
    public showShopMoneyPop: Boolean = false; // 选择觅码弹窗
    public showConsumeMoneyPop: Boolean = false; // 选择消费金弹窗
    public showCoinPop: Boolean = false; // 选择积分弹窗
    public showNotePop: Boolean = false; // 输入备注弹窗
    public orderFailMsg: string = ''; // 不可配送提示
    public hasNewMessage: string = ''; // 发生变动提示
    public serchCodeList: Array<any> = []; // 觅码列表
    public shopMoneyList: Array<any> = []; // 购物金列表
    public consumeMoneyList: Array<any> = []; // 消费金列表
    public gift_card_ids: Array<any> = []; // 指定使用的卡id
    public orderType: string = ''; // 订单活动类型
    public consumeIds: Array<any> = [];
    public isDisabled: boolean = false;
    public isChooseShopMoney = true;
    public isChooseConsumeMoney = true;
    public coinData: ICoinData = {
        coin_type: '0',
        rate: 0,
        can_coin: 0,
        coin_price: 0,
        tcoin: 0,
        coin: 0,
    };

    public buy_goods_number: number = 0;
    public activityType: string = '';

    get totality(): any {
        let num = 0;
        this.orderInfo.list.map((item) => {
            num = num + Number(item.num);
        });

        return num;
    }

    // 积分
    get coinText(): String {
        // if (this.isChooseShopMoney) {
        //     return '不可用';
        // }
        const { coin_type = '0', can_coin = '0', coin_price = '0' } = this.coinData;
        switch (String(coin_type)) {
            case '0':
                if (can_coin == 0) {
                    return '暂无可用';
                } else {
                    return `可用${can_coin}积分`;
                }
            case '1':
                return `-¥${coin_price}`;
            case '2':
                return '不使用积分';
        }
    }

    // 优惠券
    get couponText(): String {
        if (this.is_presale) return '';
        // if (this.isChooseShopMoney) {
        //     return '不可用';
        // }
        const {
            total: {
                coupon: { coupon_id, cprice },
                consume: { consume_id, consume_price } = { consume_id: 0, consume_price: 0 },
            },
            usage_coupon_num,
            usage_consume_num,
        } = this.orderInfo;
        if (usage_coupon_num == 0 && usage_consume_num == 0) {
            Utils.reportEvent('settlement_coupon', {
                settlement_coupon_status: '无可用',
            });
            return '暂无可用';
        } else if (
            (coupon_id == 0 || coupon_id == -1) &&
            (consume_id == 0 || consume_id == -1) &&
            (Number(usage_consume_num) > 0 || Number(usage_coupon_num) > 0)
        ) {
            Utils.reportEvent('settlement_coupon', {
                settlement_coupon_status: '不使用',
            });
            return `${usage_coupon_num + usage_consume_num}张可用`;
        } else {
            Utils.reportEvent('settlement_coupon', {
                settlement_coupon_status: '有可用',
            });
            return `-¥${(Number(cprice) + Number(consume_price)).toFixed(2)}`;
        }
    }

    // 觅码
    get searchCodeText(): String {
        if (this.is_presale) return '';
        // if (this.isChooseShopMoney) {
        //     return '不可用';
        // }
        const {
            total: {
                gift_card: { can_number, use_amount },
            },
        } = this.orderInfo;
        if (can_number == 0) {
            return '暂无可用';
        } else if (Number(use_amount) > 0) {
            return `-¥${use_amount}`;
        } else {
            return `${can_number}张可用`;
        }
    }

    // 购物金
    get shopMoneyText(): String {
        if (this.is_presale) return '';
        const {
            total: {
                shopping_price: { can_use_shopping_price, is_use_shopping_price, total_shopping_price },
            },
        } = this.orderInfo;
        if (is_use_shopping_price == 0) {
            return `当前剩余${total_shopping_price}购物金`;
        } else if (Number(can_use_shopping_price) == 0) {
            return `当前暂无可用购物金`;
        } else {
            return `-¥${can_use_shopping_price}`;
        }
    }

    // 消费金
    get consumeMoneyText(): String {
        if (this.is_presale) return '';
        const {
            total: {
                consume_money: { can_use_consume_money, is_use_consume_money, total_consume_money },
            },
        } = this.orderInfo;
        if (is_use_consume_money == 0) {
            return `当前剩余${total_consume_money}消费金`;
        } else if (Number(can_use_consume_money) == 0) {
            return `当前暂无可用消费金`;
        } else {
            return `-¥${can_use_consume_money}`;
        }
    }

    get live_mark(): String {
        return AppModule.liveMark;
    }

    get coSubtract(): Number | String {
        const total =
            Number(this.couponText.indexOf('-') > -1 ? this.orderInfo.total.coupon.cprice : 0) +
            Number(
                this.couponText.indexOf('-') > -1
                    ? this.orderInfo.total.consume
                        ? this.orderInfo.total.consume.consume_price
                        : 0
                    : 0,
            ) +
            Number(this.coinText.indexOf('-') > -1 ? this.coinData.coin_price : 0) +
            Number(this.orderInfo.total.acdeprice) +
            Number(this.searchCodeText.indexOf('-') > -1 ? this.orderInfo.total.gift_card.use_amount : 0) +
            Number(this.orderInfo.total.subsidy_price) +
            Number(this.orderInfo.total.consume_money?.can_use_consume_money) +
            Number(this.orderInfo.total.shopping_price?.can_use_shopping_price);

        return total.toFixed(2);
    }

    get len(): Number {
        const gcombines: Array<any> =
            this.orderInfo.gcombines && this.orderInfo.gcombines.length > 0 ? this.orderInfo.gcombines : [];
        let len = 0;
        gcombines.forEach((g) => {
            len += Number(g.num);
        });
        return len;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    // 是否是虚拟商品
    get isCoupon(): Boolean {
        return this.pointGoodType === '2';
    }

    getRelation_id() {
        const relationIdLists = {
            COMMON: 0, // 普通订单
            GROUP_PURCHASE: Number(process.env.VUE_APP_GROUP_PURCHASE_ACTIVITY_ID), // 拼团
            MONEY_MAKING: Number(process.env.VUE_APP_OFF_SPEED_ACTIVITY_ID), // 赚钱花
            ONE_YUAN_PURCHASE: Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID), // 一元购
            FIFTY_PERCENT: Number(process.env.VUE_APP_OFF_HALF_PRICE_ACTIVITY_ID), // 五折购
            SIX_DISCOUNT: Number(process.env.VUE_APP_SIX_DISCOUNT_ACTIVITY_ID), // 六折购
            EIGHTY_PERCENT: Number(process.env.VUE_APP_EIGHT_DISCOUNT_ACTIVITY_ID), // 八折购
            POINT_SHOPPING: 0, // 积分购物
            HALF_PRICE: Number(process.env.VUE_APP_HALF_PRICE_ACTIVITY_ID), // 半价购
            POINT_ORDER: 0, // 积分商城订单
            DEPOSIT_ORDER: 0, // 预售订单
            THIRTY_PERCENT: Number(process.env.VUE_APP_THREE_DISCOUNT_ACTIVITY_ID), // 三折购
        };
        return relationIdLists[this.orderType];
    }

    onLoad(options: {
        buy_goods_number: any;
        sParams: string;
        cartIds: string;
        useCid: string;
        is_presale: number;
        type: string;
        pointGoodType: string;
        pointGoodSubtype: string;
        ac_data: string;
        in_express_time: string;
        trade_in_aid: string;
        is_trade_in: string;
        group_purchase_id: string;
        group_activity_id: string;
        gift_card_id: string;
        disable_all_discount: string;
        typeActivity: string;
        seckill_id: string;
        orderType: string;
        storekeeper: string;
        unionSource: IUnionSource;
        activityType: string;
    }) {
        this.storekeeper = options.storekeeper || '';
        this.unionSource = options.unionSource || IUnionSource.EMPTY;
        this.sParams = options.sParams || '';
        this.ac_data = options.ac_data || '';
        this.cartIds = options.cartIds || '';
        this.buy_goods_number = options.buy_goods_number || 0;
        this.disable_all_discount = options.disable_all_discount || '0';
        this.typeActivity = options.typeActivity || '';
        this.seckill_id = Number(options.seckill_id) || 0;
        this.trade_in_aid = options.trade_in_aid || '';
        this.useCid = options.useCid || ''; // 指定使用优惠券id
        this.is_trade_in = options.is_trade_in || '';
        this.group_purchase_id = Number(options.group_purchase_id) || 0;
        this.group_activity_id = Number(options.group_activity_id) || 0;
        this.in_express_time = options.in_express_time || '';
        this.pointsMall = !!options.type;
        this.pointGoodType = options.pointGoodType;
        this.pointGoodSubtype = options.pointGoodSubtype;
        this.is_presale = options.is_presale || 0;
        this.gift_card_ids = options.gift_card_id ? [options.gift_card_id] : [];
        this.orderType = options.orderType || 'COMMON';
        this.activityType = options.activityType || '';

        uni.$on('HANDLE_SETTLEMENT_INIT', this.init);
        // this.init();
    }

    onShow() {
        this.init();
        // this.getDefaultAddress();
    }

    onUnload() {
        uni.$off('HANDLE_SETTLEMENT_INIT');
        Utils.reportEvent('settlement_back', {});
    }

    async init() {
        console.log('init');

        const _data: any = {
            gift_card_ids: this.gift_card_ids.join(','),
        };
        await this.getDefaultAddress();
        await this.getOrderBuyInfo(_data, true);
        await this.getAddressList();
    }

    handlePreAccord() {
        uni.navigateTo({
            url: '/pagesA/preSaleRule/index',
        });
    }

    // 超出数量限制
    handleOverLimit(type: string) {
        if (type === 'minus') {
            Utils.Toast('最少购买一件');
        } else {
            Utils.Toast('超出此产品限购数量');
        }
    }

    async getOrderBuyInfo(data = {}, loading: boolean = false) {
        try {
            const gcombines =
                this.orderInfo.gcombines && this.orderInfo.gcombines.length > 0
                    ? JSON.stringify(this.orderInfo.gcombines)
                    : this.sParams;
            let params: any = {
                gcombines,
                aid: this.orderAid,
                cart_ids:
                    this.orderInfo.cart_ids && this.orderInfo.cart_ids.length > 0
                        ? JSON.stringify(this.orderInfo.cart_ids)
                        : this.cartIds,
                ac_data: this.ac_data,
                is_internal_purchase: GoodsModule.isInshop ? 1 : 0,
            };
            if (this.is_trade_in) {
                params.is_trade_in = this.is_trade_in;
            }
            if (!this.is_presale) {
                params.coupon_id = this.orderInfo.total.coupon.coupon_id || this.useCid || 0;
                if (this.orderInfo && this.orderInfo.total && this.orderInfo.total.consume) {
                    params.consume_id = this.orderInfo.total.consume.consume_id || 0;
                } else {
                    params.consume_id = 0;
                }
                params.coin = this.coinData.coin || 0;
                params.coin_type = this.coinData.coin_type || 0;
            }
            params = { ...params, ...data };
            let result: IOrderBuyInfo;
            if (this.is_presale) {
                result = await preOrderBuyInfo(params, loading);
            } else {
                result = await (this.pointsMall
                    ? waresOrderBuyInfo(params, loading)
                    : orderBuyInfo(
                          {
                              ...params,
                              group_activity_id: this.group_activity_id,
                              disable_all_discount: this.disable_all_discount || '',
                              is_use_shopping_price: this.isChooseShopMoney ? 1 : 0,
                              is_use_consume_money: this.isChooseConsumeMoney ? 1 : 0,
                              relation_type: this.orderType || 'COMMON',
                              relation_id: this.getRelation_id(),
                              coin_type: 2,
                          },
                          loading,
                      ));
                this.coinData = { ...result.total.coin };
                if (this.orderInfo.total.gift_card) {
                    this.gift_card_ids = this.orderInfo.total.gift_card.gift_card_ids;
                }
            }
            this.orderInfo = result;
            if (!this.orderInfo.total.gift_card) {
                this.orderInfo.total.gift_card = {
                    can_number: 0,
                    use_amount: 0,
                    gift_card_ids: [],
                };
            }

            this.isChooseShopMoney = Number(this.orderInfo.total.shopping_price?.can_use_shopping_price) > 0;
            if (this.orderInfo.total.shopping_price) {
                this.shopMoneyList = [
                    {
                        ...this.orderInfo.total.shopping_price,
                        selected: this.isChooseShopMoney,
                    },
                ];
            }
            if (this.orderInfo.total.consume_money) {
                this.consumeMoneyList = [
                    {
                        ...this.orderInfo.total.consume_money,
                        selected: this.isChooseConsumeMoney,
                    },
                ];
            }

            this.isLoaded = true;
        } catch (e) {
            uni.navigateBack({});
            console.error('getOrderBuyInfo e', e);
        }
    }

    async getDefaultAddress() {
        try {
            const params = {
                opt: 1,
                id: this.orderAid,
            };
            const result = await getAddressList(params);
            if (result.list.id) {
                this.orderAid = result.list.id || '0';
                this.addressInfo = result.list;
            }
        } catch (e) {
            console.error('getAddressList e', e);
        }
    }

    async getAddressList() {
        const params = {
            opt: 0,
        };
        const res = await getAddressList(params);
        if (!res) return;
        this.addressList = res.list;
    }

    onSelectCoupon(data: any) {
        // 根据 card_type 更新 coupon_id 或 consume_id
        if (data.card_type === TYPE_COUPON || data.card_type === EXCHANGE_COUPON) {
            this.pop_coupon_id = data.coupon_id;
        } else if (data.card_type === CONSUMER_COUPON) {
            this.pop_consume_id = data.consume_id;
        }
        this.getUsableCoupons();
    }

    async getUsableCoupons() {
        try {
            const gcombines: string = this.orderInfo.gcombines ? JSON.stringify(this.orderInfo.gcombines) : '';
            const res: any = await OrderModule.asyncGetUsableCoupons({
                gcombines,
                coupon_id: this.pop_coupon_id,
                consume_id: this.pop_consume_id,
                is_trade_in: this.is_trade_in,
            });
            if (res && res.consume_code && res.consume_code === 100) {
                this.isDisabled = true;
                Utils.Toast('无法使用，该优惠金额已超出剩余金额~');
            } else {
                this.isDisabled = false;
            }
            this.consumeIds = OrderModule.orderCouponInfo.consume.ids ? OrderModule.orderCouponInfo.consume.ids : [];
        } catch (e) {
            console.log('getAddressList e', e);
        }
    }

    async handlePopComfirm(data: { card_type: any }) {
        try {
            const _data: any = { ...data };
            // this.disable_all_discount = _data.card_type ? '0' : '2';
            if (_data.card_type) {
                this.isChooseShopMoney = false;
            }
            // 选择的优惠券类型是兑换券情况下，不能使用积分
            if (data.card_type === Constants.COUPON_TYPE_JYB) {
                _data.coin = 0;
                _data.coin_type = 2;
                delete _data.card_type;
            } else {
                _data.coin = 0;
                _data.coin_type = 0;
                delete _data.card_type;
            }
            this.getOrderBuyInfo(_data, true);
        } catch (e) {
            console.error('handlePopComfirm e', e);
        }
    }

    async comfirmSearchCode() {
        try {
            const gift_card_ids = [];
            this.serchCodeList.map((item) => {
                if (item.selected) {
                    gift_card_ids.push(item.id);
                }
            });
            if (gift_card_ids.length > 0) {
                this.isChooseShopMoney = false;
            }
            const _data: any = {
                gift_card_ids: gift_card_ids.join(','),
            };
            this.getOrderBuyInfo(_data, true);
        } catch (e) {
            console.error('handlePopComfirm e', e);
        }
    }

    async handleCoinPopComfirm(data: { coin: any; coin_type: any }) {
        try {
            this.coinData.coin = data.coin;
            this.coinData.coin_type = data.coin_type;
            if (data.coin_type == 1) {
                this.isChooseShopMoney = false;
            }
            // this.disable_all_discount = data.coin_type == 0 ? '2' : '0';
            this.getOrderBuyInfo();
        } catch (e) {
            console.error('handlePopComfirm e', e);
        }
    }

    handleNotePopComfirm(data: { note: string }) {
        this.note = data.note;
    }

    async valChange(e: { value: string }, item: { num: any; gid: any; spec: { id: any } }, index: number) {
        try {
            item.num = e.value === '0' ? '1' : e.value;
            const gcombines: Array<any> = uni.$u.deepClone(this.orderInfo.gcombines);
            const gcombine: Object = {
                gid: item.gid,
                sid: item.spec ? item.spec.id || 0 : 0,
                num: item.num || 1,
            };
            gcombines[index] = gcombine;
            this.coinData.coin = 0;
            this.coinData.coin_type = '0';
            if (!this.is_presale) {
                this.orderInfo.total.coupon.coupon_id = '';
            }
            this.useCid = '';
            this.getOrderBuyInfo({ gcombines: JSON.stringify(gcombines) });
        } catch (e) {
            console.error('valChange e', e);
        }
    }

    async handlePreOrderBuy() {
        if (!this.addressInfo.id) {
            return Utils.Toast('请添加或选择收货地址');
        }
        if (!this.preAccord) {
            return Utils.Toast('请阅读并勾选协议');
        }
        Utils.reportEvent('settlement_submit');
        if (this.note) {
            Utils.reportEvent('settlement_note');
        }
        try {
            const params: any = {
                gcombines: this.orderInfo.gcombines ? JSON.stringify(this.orderInfo.gcombines) : '',
                cart_ids: this.orderInfo.cart_ids ? JSON.stringify(this.orderInfo.cart_ids) : '',
                aid: this.orderAid,
                note: this.note,
            };
            const result = await depositBuy(params).catch((err: { iRet: any; sMsg: string }) => {
                if (String(err.iRet) === Constants.ERROR_ORDER_FAIL_E_REASON) {
                    this.showOrderFailPop = true;
                    this.orderFailMsg = err.sMsg;
                    // eslint-disable-next-line no-useless-return
                    return;
                }
            });
            const gid = this.getGoodsId();
            uni.navigateTo({
                url: `/pagesA/cashier/cashier?fromPage=settlement&order_no=${result.order_no}&user_order_type=3&need_pay=${result.need_pay}&gid=${gid}&orderType=${this.orderType}&activityType=${this.activityType}`,
            });
        } catch (e) {
            console.error('handlePreOrderBuy e', e);
        }
    }

    async handleOrderBuy() {
        if (this.pointsMall && !this.isCoupon && !this.addressInfo.id) {
            return Utils.Toast('请先选择收货地址');
        }

        if (!this.isCoupon && !this.addressInfo.id) {
            return Utils.Toast('请添加或选择收货地址');
        }
        Utils.reportEvent('settlement_submit');
        if (this.note) {
            Utils.reportEvent('settlement_note');
        }
        try {
            // 普通商城点击确认订单
            if (!this.pointsMall) {
                this.regularOrder();
            } else {
                this.pointOrder();
            }
        } catch (e) {
            console.error('handleOrderBuy e', e);
        }
    }

    async pointOrder() {
        const params: any = {
            gcombines: this.orderInfo.gcombines ? JSON.stringify(this.orderInfo.gcombines) : '',
            pay_source: 4,
        };
        if (!this.isCoupon) {
            params.aid = this.orderAid;
        } else {
            params.type = this.pointGoodType;
            params.subtype = this.pointGoodSubtype;
        }
        const result = await waresOrderBuy(params).catch((err: { iRet: any; sMsg: string }) => {
            if (String(err.iRet) === Constants.ERROR_ORDER_FAIL_E_REASON) {
                this.showOrderFailPop = true;
                this.orderFailMsg = err.sMsg;
                // eslint-disable-next-line no-useless-return
                return;
            }
        });
        if (result.has_new == 1) {
            this.showPopTips = true;
            this.hasNewMessage = result.msg;
            return;
        }
        if (result.need_pay == '0') {
            if (this.isCoupon) {
                const startTime = new Date().getTime();
                const promiseMaker = () =>
                    checkCoupon({ order_no: result.order_no, sub_type: this.pointGoodSubtype }).then((res) => {
                        const endTime = new Date().getTime();
                        console.log('res', res);
                        // 轮询超过2s 或者卡券发放状态为1时跳转订单详情
                        if (res.status === 1 || endTime - startTime > 2 * 1000) {
                            return true;
                        }
                        return false;
                    });
                Utils.promiseSequence(promiseMaker).then((status) => {
                    if (status) {
                        uni.redirectTo({
                            url: '/pagesA/orderDetail/orderDetail?order_no=' + result.order_no,
                        });
                    }
                });
            } else {
                uni.navigateTo({
                    url: '/pagesA/orderDetail/orderDetail?order_no=' + result.order_no,
                });
            }
        } else {
            const gid = this.getGoodsId();
            uni.navigateTo({
                url: `/pagesA/cashier/cashier?fromPage=settlement&order_no=${result.order_no}&user_order_type=4&coin=${result.coin}&need_pay=${result.need_pay}&gid=${gid}&orderType=${this.orderType}&activityType=${this.activityType}`,
            });
        }
    }

    async regularOrder() {
        const params: IOrderBuyParams = {
            is_trade_in: this.is_trade_in,
            cate_code: GoodsModule.trade_data.cate_code || '',
            item_brand: GoodsModule.trade_data.item_brand || '',
            item_cates: GoodsModule.trade_data.item_cates || '',
            item_model: GoodsModule.trade_data.item_model || '',
            in_express_time: this.in_express_time,
            trade_in_remark: GoodsModule.trade_note,
            trade_in_aid: this.trade_in_aid,
            gcombines: this.orderInfo.gcombines ? JSON.stringify(this.orderInfo.gcombines) : '',
            cart_ids: this.orderInfo.cart_ids ? JSON.stringify(this.orderInfo.cart_ids) : '',
            coupon_id: this.orderInfo.total.coupon.coupon_id || 0,
            consume_id: this.orderInfo.total.consume.consume_id || 0,
            coin: this.coinData.coin || 0,
            aid: this.orderAid,
            note: this.note,
            ac_data: this.ac_data,
            gift_card_ids: this.orderInfo.total.gift_card.gift_card_ids.join(','),
            is_internal_purchase: GoodsModule.isInshop ? 1 : 0,
            group_activity_id: this.group_activity_id,
            group_purchase_id: this.group_purchase_id,
            consume_money: this.orderInfo.total.consume_money?.can_use_consume_money,
            relation_type: this.orderType,
            union: this.unionSource,
            relation_id: this.getRelation_id() || 0,
            inviter_id: UserModule.inviter_id || 0,
        };
        if (this.live_mark) {
            params.live_mark = this.live_mark;
        }

        if (this.seckill_id && this.seckill_id !== -1) {
            params.one_yuan_seckill_id = this.seckill_id;
        }

        // 店铺下单
        if (this.storekeeper) {
            params.storekeeper = this.storekeeper;
        }

        let request;
        // #ifdef H5
        request = commonBuy;
        params.shopping_price = this.orderInfo.total.shopping_price.can_use_shopping_price;
        // #endif
        // #ifdef MP-WEIXIN
        params.pay_type = 99;
        request = orderBuy;
        // #endif
        const result = await request(params).catch((err: { iRet: any; sMsg: string }) => {
            if (String(err.iRet) === Constants.ERROR_ORDER_FAIL_E_REASON) {
                this.showOrderFailPop = true;
                this.orderFailMsg = err.sMsg;
                // eslint-disable-next-line no-useless-return
                return;
            }
        });
        console.log('%c result: ', 'font-size:16px;background-color: #2EAFB0;color:#fff;', result);
        if (result.has_new == 1) {
            this.showPopTips = true;
            this.hasNewMessage = result.msg;
            return;
        }
        const { is_leader, group_purchase_id } = await checkGroupLeader({ order_no: result.order_no });
        this.is_leader = !!is_leader;
        this.group_purchase_id = group_purchase_id;
        if (result.need_pay === '0') {
            if (this.is_leader) {
                // 是团长跳转拼团进行中
                this.updateGoodsDetails_groupPurchase();
                // 查询该订单是否插入到拼团中
                const promiseMaker = () =>
                    checkGroupOrderNo({ order_no: result.order_no }).then((res) => {
                        return res.status;
                    });
                Utils.promiseSequence(promiseMaker).then((status) => {
                    if (status) {
                        uni.redirectTo({
                            url: `/pagesB/inGroupPurchase/indexNew?group_purchase_id=${this.group_purchase_id}&backUrl=/pagesB/groupGoods/groupGoods&order_no=${result.order_no}&user_order_type=3`,
                        });
                    }
                });
            } else {
                // 以旧换新 轮询订单状态
                if (this.is_trade_in) {
                    const startTime = new Date().getTime();
                    const promiseMaker = () =>
                        checkOrderStatus({ order_no: result.order_no }).then((res) => {
                            const endTime = new Date().getTime();
                            console.log('res', endTime - startTime);
                            // 轮询超过2s 或者订单状态为1时跳转订单详情
                            if (res.status === 1 || endTime - startTime > 2 * 1000) {
                                return true;
                            }
                            return false;
                        });
                    Utils.promiseSequence(promiseMaker).then((status) => {
                        if (status) {
                            uni.redirectTo({
                                url: '/pagesA/orderDetail/orderDetail?order_no=' + result.order_no,
                            });
                        }
                    });
                    return;
                }
                uni.redirectTo({
                    url: '/pagesA/orderDetail/orderDetail?order_no=' + result.order_no,
                });
            }
        } else {
            console.log('resultsParams', result, this.sParams);
            const gid = this.getGoodsId();

            uni.redirectTo({
                url: `/pagesA/cashier/cashier?fromPage=settlement&order_no=${result.order_no}&coin=${
                    result.coin
                }&need_pay=${result.need_pay}&is_leader=${this.is_leader}&group_purchase_id=${
                    this.group_purchase_id
                }&disable_all_discount=${this.disable_all_discount || 0}&typeActivity=${
                    this.typeActivity || 0
                }&orderType=${this.orderType}&gid=${gid}&activityType=${this.activityType}`,
            });
        }
    }

    getGoodsId() {
        // 处理空值或非字符串输入
        if (!this.sParams || typeof this.sParams !== 'string') {
            return '';
        }

        let parsedParams = null;
        try {
            parsedParams = JSON.parse(this.sParams);
        } catch (error) {
            console.error('JSON 解析错误:', error.message);
            return '';
        }

        // 验证数据结构：确保是数组且首元素含 gid
        const isArrayWithGid =
            Array.isArray(parsedParams) &&
            parsedParams.length > 0 &&
            typeof parsedParams[0] === 'object' &&
            'gid' in parsedParams[0];

        return isArrayWithGid ? parsedParams[0].gid : '';
    }

    refreshPage() {
        this.showPopTips = false;
        this.getOrderBuyInfo();
    }

    // 跟新详情页的拼团id group_purchase_id 避免返回时页面显示错误
    async updateGoodsDetails_groupPurchase() {
        const pages = getCurrentPages();
        const prevPage: any = pages[pages.length - 2];
        const _pre_route: any = prevPage.route;
        if (_pre_route === 'pagesB/goodsDetail/goodsDetail') {
            prevPage.$vm.group_purchase_id = this.group_purchase_id;
        }
    }

    sleep(ms: number) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    async handleCouponPop() {
        // 选择购物金后，不能选择其他折扣
        // if (this.isChooseShopMoney) {
        //     return;
        // }
        this.pop_coupon_id = this.orderInfo.total.coupon.coupon_id || '0';
        this.pop_consume_id = this.orderInfo.total.consume.consume_id || '0';
        await this.getUsableCoupons();
        this.showCouponPop = true;
    }

    async handleSerchCodePop() {
        // 选择购物金后，不能选择其他折扣
        // if (this.isChooseShopMoney) {
        //     return;
        // }
        if (
            this.orderInfo &&
            this.orderInfo.total &&
            this.orderInfo.total.coupon &&
            this.orderInfo.total.coupon.coupon_id
        ) {
            if (this.orderInfo.total.coupon.card_type === Constants.COUPON_TYPE_JYB) {
                Utils.Toast('兑换券不能使用觅码');
                return;
            }
        }
        await this.getSerchCode();
        this.serchCodeList.map((item, index) => {
            if (this.orderInfo.total.gift_card.gift_card_ids.includes(String(item.id))) {
                this.serchCodeList[index].selected = true;
                this.$set(this.serchCodeList, index, this.serchCodeList[index]);
            } else {
                this.serchCodeList[index].selected = false;
                this.$set(this.serchCodeList, index, this.serchCodeList[index]);
            }
        });

        this.showSearchCodePop = true;
    }

    onSelect(it) {
        const { item, index } = it;
        if (item.type == '2') {
            if (!item.selected) {
                this.serchCodeList.map((cur, i) => {
                    this.serchCodeList[i].selected = item.id == cur.id;
                    this.$set(this.serchCodeList, i, this.serchCodeList[i]);
                });
            } else {
                this.serchCodeList[index].selected = false;
                this.$set(this.serchCodeList, index, this.serchCodeList[index]);
            }
        } else {
            if (!item.selected) {
                this.serchCodeList.map((cur, i) => {
                    if (cur.type == '2') {
                        this.serchCodeList[i].selected = false;
                        this.$set(this.serchCodeList, i, this.serchCodeList[i]);
                    }
                });
                const total = this.serchCodeList.reduce((acc, cur) => {
                    return cur.selected ? acc + Number(cur.amount) : acc;
                }, 0);
                if (
                    total >=
                    Number(this.orderInfo.total.real_price) +
                        Number(this.orderInfo.total.gift_card.use_amount || 0) -
                        Number(this.orderInfo.total.fprice)
                ) {
                    return Utils.Toast('当前订单不可超出抵扣金额');
                }
            }
            item.selected = item.selected ? !item.selected : true;
            this.serchCodeList[index].selected = item.selected;
            this.$set(this.serchCodeList, index, this.serchCodeList[index]);
        }
    }

    async getSerchCode() {
        try {
            const goods_id = [];
            this.orderInfo.list.map((item) => {
                goods_id.push(item.gid);
            });
            const params = { status: 1, goods_id: goods_id.join(','), type: '2' };
            const result = await userCardList(params);
            this.serchCodeList = result.list;
        } catch (e) {
            console.error('getAddressList e', e);
        }
    }

    handleCoinPop() {
        // 选择购物金后，不能选择其他折扣
        // if (this.isChooseShopMoney) {
        //     return;
        // }
        if (
            this.orderInfo &&
            this.orderInfo.total &&
            this.orderInfo.total.coupon &&
            this.orderInfo.total.coupon.coupon_id
        ) {
            if (this.orderInfo.total.coupon.card_type === Constants.COUPON_TYPE_JYB) {
                Utils.Toast('兑换券不能使用积分');
                return;
            }
        }
        this.showCoinPop = true;
        Utils.reportEvent('settlement_point', {});
    }

    handleNotePop() {
        console.log('handleNotePop');
        this.showNotePop = true;
    }

    async handleShopMoneyPop() {
        const {
            total: {
                shopping_price: { total_shopping_price },
            },
        } = this.orderInfo;
        if (Number(total_shopping_price) == 0) {
            return;
        }
        // const { total } = this.orderInfo;
        // const { gift_card } = total;
        // const { discount_card_ids = ['290'] } = gift_card;
        // const result = await userCardList();
        // this.shopMoneyList = result.list.map((v) => {
        //     return {
        //         ...v,
        //         // selected: discount_card_ids.includes(v.id),
        //     };
        // });
        this.showShopMoneyPop = true;
    }

    handleConsumeMoneyPop() {
        const {
            total: {
                consume_money: { total_consume_money },
            },
        } = this.orderInfo;
        if (Number(total_consume_money) == 0) {
            return;
        }
        this.showConsumeMoneyPop = true;
    }

    onShopMoneySelect({ index, item }) {
        this.$set(this.shopMoneyList, index, { ...item, selected: !item.selected });
    }

    onConsumeMoneySelect({ index, item }) {
        this.$set(this.consumeMoneyList, index, { ...item, selected: !item.selected });
    }

    comfirmShopMoney() {
        try {
            this.isChooseShopMoney = this.shopMoneyList.some((v) => v.selected);
            let params = {};
            if (this.isChooseShopMoney) {
                params = { coupon_id: -1, consume_id: -1, coin_type: 2, coin: 0 };
            } else {
                params = { coupon_id: 0, consume_id: 0, coin_type: 2 };
            }
            this.getOrderBuyInfo(params);
        } catch (e) {
            console.error('handlePopComfirm e', e);
        }
    }

    comfirmConsumeMoney() {
        try {
            this.isChooseConsumeMoney = this.consumeMoneyList.some((v) => v.selected);

            this.getOrderBuyInfo({ is_use_consume_money: this.isChooseConsumeMoney ? 1 : 0 });
        } catch (e) {
            console.error('handlePopComfirm e', e);
        }
    }
}
</script>

<style lang="scss" scoped>
@import './settlement.scss';
</style>
