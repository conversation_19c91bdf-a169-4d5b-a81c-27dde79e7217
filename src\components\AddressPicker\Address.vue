<template>
    <view class="address u-flex u-col-center" @click="gotoAddressPage">
        <!-- 普通订单地址 -->
        <view class="content" v-if="ordinaryAddress">
            <view :class="{ 'show-bg': address.id && address.is_del == '0' }"></view>
            <view class="picker" v-if="address.id && address.is_del == '0'">
                <view>
                    <view class="detail">
                        <view class="default" v-if="Number(addressInfo.status) == 1 || Number(address.status) == 1"></view>
                        <text class="nick u-line-1">{{ address.nick }}</text>
                        <text class="name">{{ address.phone }}</text>
                    </view>
                    <view class="contact u-flex">
                        <view class="adress">{{
                            address.province + address.city + address.area + address.detail
                        }}</view>
                    </view>
                </view>
                <view class="jump_image" v-if="String(orderDetail.status) == '-1' || !String(orderDetail.status)"></view>
            </view>
            <view class="no-picker" v-else>
                <view class="u-flex u-row-between u-col-center">
                    <view class="u-flex">
                        <lazy-image customStyle="width: 28rpx; height: 36rpx;" lazyLoad="true"
                            src="https://wpm-cdn.dreame.tech/images/202307/64a50c8d51d483353519155.png"></lazy-image>
                        <view class="add-adress">请填写收货地址、信息</view>
                    </view>
                    <lazy-image customStyle="width: 38rpx; height: 38rpx;" lazyLoad="true"
                        src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"></lazy-image>
                </view>
            </view>
        </view>
        <!-- 预售订单地址 -->
        <view class="content" v-if="presaleAddress">
            <view :class="{ 'show-bg': !address.is_del || address.is_del == '0' }"></view>
            <view class="picker" v-if="!address.is_del || address.is_del == '0'">
                <view>
                    <view class="detail">
                        <view class="default"
                            v-if="defaultAdress">
                        </view>
                        <text class="nick u-line-1">{{ address.nick }}</text>
                        <text class="name">{{ address.phone }}</text>
                    </view>
                    <view class="contact u-flex">
                        <view class="adress">{{
                            address.province + address.city + address.area + address.detail
                        }}</view>
                    </view>
                </view>
                <view class="jump_image" v-if="String(orderDetail.status) == '-1' || !String(orderDetail.status)"></view>
            </view>
            <view class="no-picker" v-else>
                <view class="u-flex u-row-between u-col-center">
                    <view class="u-flex">
                        <lazy-image :lazyLoad="true" customStyle="width: 28rpx; height: 36rpx;"
                            src="https://wpm-cdn.dreame.tech/images/202307/64a50c8d51d483353519155.png"></lazy-image>
                        <view class="add-adress">请填写收货地址、信息</view>
                    </view>
                    <lazy-image customStyle="width: 38rpx; height: 38rpx;" lazyLoad="true"
                        src="https://wpm-cdn.dreame.tech/images/202307/64a50de91f23d1273519342.png"></lazy-image>
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit } from 'vue-property-decorator';
import Utils from '@/common/Utils';

@Component({
    components: {
    },
})
export default class Address extends Vue {
    @Prop({
        type: Object,
        default: () => ({
            aid: '320506',
            area: '吴中区',
            cid: '320500',
            city: '苏州市',
            crm_code: 'b544375a-1a3b-ed11-b399-b6ae76b0a77d',
            ctime: '1663921999',
            detail: '尚金湾',
            id: '2864',
            nick: '陶',
            phone: '13333333333',
            pid: '320000',
            province: '江苏省',
            status: '1',
            user_id: '549647',
        }),
    })
    readonly address;

    @Prop({
        type: Object,
        default: () => ({
            id: '',
            nick: '',
            phone: '',
            province: '',
            city: '',
            area: '',
            detail: '',
            status: '',
        }),
    })
    readonly addressInfo;

    @Prop({
        type: Array,
        default: () => [],
    })
    readonly addressList;

    @Prop({
        type: Object,
        default: () => ({
            id: '',
            nick: '',
            phone: '',
            province: '',
            city: '',
            area: '',
            detail: '',
            status: '',
        }),
    })
    readonly orderDetail;

    @Prop({ type: Boolean, default: true })
    readonly gotoAddress!: boolean;

    @Prop({ type: Boolean, default: true })
    readonly iconRight!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly ordinaryAddress!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly presaleAddress!: boolean;

    @Emit('onSelectItem')
    onSelectItem(index) { }

    @Emit('delData')
    delData(good) { }

    get defaultAdress() {
        return (Number(this.addressInfo.status) == 1) || (Number(this.address.status) == 1) || (Number(this.address.is_default_address || 0) == 1)
    }

    gotoAddressPage() {
        if (String(this.orderDetail.status) && String(this.orderDetail.status) != '-1') {
            return true
        }
        Utils.reportEvent('settlement_address', {
        })

        if (this.addressList.length == 0) {
            uni.navigateTo({ url: '/pagesA/address/add-address' });
        } else {
            uni.navigateTo({
                url: '/pagesA/address/address-list',
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.address {
    width: 100%;
    background: #ffffff;
    border-radius: 16rpx;
    margin-top: 24rpx;
    box-sizing: border-box;

    .icon {
        width: 54rpx;
        height: 54rpx;
        margin-right: 20rpx;
    }

    .show-bg {
        background: url('https://wpm-cdn.dreame.tech/images/202307/841902-1689057724785.png');
        position: absolute;
        background-repeat: no-repeat;
        background-size: 170rpx 226rpx;
        background-position: 476rpx -90rpx;
        width: 100%;
        height: 100%;
        z-index: 2;
        top: 0rpx;
        left: 0rpx;
    }

    .content {
        padding: 40rpx 32rpx 38rpx;
        flex: 1;
        position: relative;

        .no-picker {
            font-size: 28rpx;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 500;
            height: 124rpx;
            display: inline;
            color: $brand-color-btn-text;

            .add-adress {
                margin-left: 12rpx;
            }
        }

        .picker {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .detail {
                font-size: 32rpx;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 600;
                color: #1d1e20;
                display: flex;
                align-items: center;
                line-height: 43rpx;
                margin-bottom: 16rpx;
                word-break: break-all;

                .default {
                    background: url('https://wpm-cdn.dreame.tech/images/202306/649257d4145d40832430859.png');
                    background-repeat: no-repeat;
                    background-size: cover;
                    width: 68rpx;
                    height: 36rpx;
                    margin-right: 16rpx;
                }

                .nick {
                    max-width: 300rpx;
                    margin-right: 16rpx;
                    font-size: 32rpx;
                }

                .name {
                    font-size: 32rpx;
                }
            }

            .contact {
                font-size: 28rpx;
                font-family: PingFang SC-Semibold, PingFang SC;
                font-weight: 400;
                color: rgba(29, 30, 32, 0.4);
                line-height: 38rpx;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                word-break: break-all;

                .adress {
                    width: 582rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    font-weight: 400;
                    color: #777777;
                    line-height: 38rpx;
                }
            }

            .jump_image {
                background: url('https://wpm-cdn.dreame.tech/images/202306/649259b4429fc2722430826.png');
                background-size: cover;
                background-repeat: no-repeat;
                width: 38rpx;
                height: 38rpx;
            }
        }
    }

    .delete {
        width: 144rpx;
        height: 100%;
        background-color: #ffffff;
        border-radius: 24rpx;
        position: absolute;
        top: 0;
        right: -176rpx;
        margin-left: 16rpx;

        .icon_delete {
            height: 80rpx;
            width: 80rpx;
        }
    }
}
</style>
