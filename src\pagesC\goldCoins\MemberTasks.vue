<template>
    <view class="tasks">
        <!-- 列表整体盒子 -->
        <view class="task-list">
            <!-- 每块的盒子 -->
            <view
                class="task-item u-flex u-row-between u-col-center"
                v-for="(item, index) in taskList.slice(0, limit)"
                :key="index"
                :style="{ marginBottom: marginBottom + 'rpx' }"
            >
                <view style="display: flex; align-items: center">
                    <image :src="item.picture" style="width: 58rpx; height: 58rpx; margin-right: 24rpx" />
                    <view class="task-left">
                        <view class="name-box u-flex u-row-between u-col-center">
                            <!-- 左侧标题，可换行 -->
                            <text class="title-text">{{ item.name }}</text>

                            <!-- 右侧计数和提示图标 -->
                            <view
                                class="count-box u-flex u-row-center u-col-center"
                                v-if="item.eventLimitNum && $isInApp()"
                            >
                                <text class="week">({{ item.currentPeriodEventNum }}/{{ item.eventLimitNum }})</text>
                                <image
                                    style="height: 28rpx; width: 28rpx; margin-left: 12rpx"
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fddca0e2250580011686.png"
                                    alt=""
                                ></image>
                            </view>
                            <image
                                v-else
                                style="height: 28rpx; width: 28rpx; margin-left: 12rpx"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689fddca0e2250580011686.png"
                                alt=""
                            ></image>
                            <view class="detail">{{
                                highShow(item) ? '最高' + item.gold * item.eventLimitNum : item.gold
                            }}</view>
                        </view>
                        <view class="integra" v-if="item.gold > 0">
                            <view class="integral">
                                <text>{{ item.descri }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view></view>
                <view class="task-btn">
                    <view v-if="item.haveUnCollect" class="unCollect-btn" @click="getcollectTaskGoldHandle(item)">
                        领取
                    </view>
                    <view v-else :class="{ 'task-btn-completed': item.completed }" @click="doTask(item)">
                        {{ item.completed ? '已完成' : '去完成' }}
                    </view>
                </view>
            </view>
        </view>
        <CustomModal
            :show="isShow"
            width="616rpx"
            contentStyle="color:#404040;font-weight:400;font-size: 32rpx;text-align: center;"
            confirmStyle="width: 524rpx; color: #8C6533; margin-bottom: -20rpx;"
            :content="taskDetail"
            confirmText="知道了"
            @confirm="isShow = false"
        >
        </CustomModal>
        <Live :isShow="false" ref="live"></Live>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { VIPModule } from '@/store/modules/vip';
import Live from '@/components/Live/Live.vue';
import Utils from '@/common/Utils';
import { getcollectTaskGold } from '@/http/coin';
import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
import { CheckAppJump } from '@/common/decorators';

@Component({
    components: {
        Live,
    },
})
export default class MemberTasks extends Vue {
    $refs!: {
        live: Live;
    };

    @Prop({ type: Boolean, default: false })
    readonly isMore!: Boolean;

    @Prop({ type: Number, default: 99999999 })
    readonly limit;

    @Prop({ type: Number, default: 0 })
    readonly marginBottom;

    public isShow: Boolean = false; // 规则弹窗
    public taskDetail: String = ''; // 规则内容

    get taskList(): Array<any> {
        // 移除数量限制，全部展示任务
        return JSON.parse(JSON.stringify(VIPModule.taskList));
    }

    get user_id(): number | string {
        return UserModule.user_id;
    }

    highShow(item) {
        return item.code === 'signin_gold' || item.code === 'watch_video_gold' || item.code === 'gold_prize_draw';
    }

    showDetail(task) {
        this.taskDetail = task.descri;
        this.isShow = true;
    }

    async getcollectTaskGoldHandle(item) {
        await getcollectTaskGold({ code: item.code, group_code: item.groupCode, type_code: item.typeCode });
        this.$emit('refresh'); // 通知父组件刷新
        Utils.isInApp() ? VIPModule.getCoinTaskList() : VIPModule.noUserIdGetCoinTaskList();
    }

    @CheckAppJump()
    doTask(item) {
        if (item.completed) return;
        switch (item.code) {
            case 'view_goods':
                this.goToShop();
                break;
            case 'goods_add_cart':
                this.goToShop();
                break;
            case 'buy_new_products':
                this.goToShop();
                break;
            case 'buy_appoint_goods':
                this.goToShop();
                break;
            case 'buy_main_machine':
                this.goToShop();
                break;
            case 'buy_parts':
                this.goToShop(20);
                break;
            case 'invite_buy':
                Utils.navigateTo('/pagesA/recommend/recommend');
                break;
            case 'reg_sn':
                Utils.navigateTo('/pagesC/serve/serve');
                break;
            case 'evaluate_main_machine':
                Utils.navigateTo('/pagesB/evaluate/myEvaluate');
                break;
            case 'evaluate_parts':
                Utils.navigateTo('/pagesB/evaluate/myEvaluate');
                break;
            case 'share_goods':
                this.goToShop();
                break;
            case 'invite_reg':
                Utils.navigateTo('/pagesA/recommend/recommend');
                break;
            case 'follow_official_account':
                Utils.navigateTo(
                    `/pages/webView/webView?web_url=${encodeURIComponent(
                        'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=**********&idx=1&sn=69a3db2d520c0151d8d6e5a9a82f8ef6&chksm=ce51b09ef92639880531af2392c71e94e559b96dfecf1ee98812a9319654e002224ab6ab2060#rd',
                    )}`,
                );
                break;
            case 'add_wechat':
                Utils.navigateTo(`/pagesA/memberShip/memberShip?typeCode=exclusive`);
                break;
            case 'f_pair': // 跳转设备页
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'start_clean': // 跳转设备页
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'login': // app登录
                break;
            case 'clean_area': // 清扫面积
                Utils.messageChannel('navigation', { type: 'device', path: 'home/device' });
                break;
            case 'f_fill': // 完善个人信息
                // #ifdef H5
                Utils.messageChannel('navigation', { type: 'mine', path: 'mine/accountSetting' });
                // #endif
                // #ifdef MP-WEIXIN
                Utils.navigateTo('/pagesA/userInfo/userInfo');
                // #endif
                break;
            case 'signin': // 签到
                Utils.navigateTo('/pagesA/daliyCheckIn/daliyCheckIn');
                break;
            case 'watch_live': // 直播
                this.$refs.live.getLiveList(true);
                break;
            case 'f_video': // 使用视频管家
                Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' });
                break;
            case 'accessory_usage': // 查看耗材计时
                Utils.messageChannel('navigation', { type: 'mall', path: 'home/device' });
                break;
            case 'app_invite_reg_gold': // 邀请好友
                this.shareClick();
                break;
            case 'app_share_goods': // 分享商品
                Utils.navigateTo('/pages/shop/shop');
                break;
            case 'view_goods_one_yuan': // 逛一元购15秒
                Utils.navigateTo('/pagesC/oneYuanFlashSale/oneYuanFlashSale?from=goldCoins');
                break;
            case 'view_goods_rich_plan': // 逛暴富计划15秒
                Utils.navigateTo('/pagesC/ambassador/login/index?from=goldCoins');
                break;
            case 'view_goods_five_buy': // 逛五折购15秒
                Utils.navigateTo('/pagesC/offPurchase/offPurchase');
                break;
            case 'view_goods_half_buy': // 逛半价购15秒
                Utils.navigateTo('/pagesC/billionsOfShoppingGold/billionsOfShoppingGold');
                break;
            case 'view_goods_points_shopping': // 逛积分购物15秒
                Utils.navigateTo('/pagesA/point/shop_point?from=goldCoins');
                break;
            case 'view_goods_three_gold': // 三折购
                Utils.navigateTo('/pagesC/threeDiscount/aThreeDiscount?from=goldCoins');
                break;
            case 'watch_video': // 观看视频15秒
                // Utils.navigateTo('/pages/contents/videoLiveStreaming');
                Utils.newMessageChannel('PAGE', 'tab', { tabType: 'mall_discovery' });
                Utils.messageChannel('closeWebView', '');
                break;
            case 'gold_prize_draw': // 天天抽金币
                console.log('gold_prize_draw');
                this.$emit('DrawGold');
                break;
            case 'view_goods_group_gold': // 逛拼团好物
                Utils.navigateTo('/pagesB/groupGoods/groupGoods?from=goldCoins');
                break;
            case 'view_goods_gold': // 逛商城60秒
            case 'buy_goods_gold': // 购物返大额金币
                Utils.navigateTo('/pages/shop/shop?from=goldCoins');
                break;
            case 'search_view_goods': // 搜商品赚金币
                uni.navigateTo({
                    url: '/pagesA/search/search?search_type=' + 1 + '&from=goldCoins',
                });
                Utils.reportEvent('search_click', {});
                break;
            case 'signin_gold': // 每日签到
                this.$emit('openSignInPopup');
                break;
            case 'watch_video_gold': // 看视频赚金币
                Utils.navigateTo('/pagesC/contentVideoDetails/contentVideoDetails?from=goldCoins');
                break;
            case 'evaluate_main_machine_gold': // 主机商品评价
            case 'evaluate_parts_gold': // 配件商品评价
                Utils.navigateTo('/pagesB/evaluate/myEvaluate');
                break;
            default:
                break;
        }
    }

    goToShop(tid?: any) {
        // #ifdef MP-WEIXIN
        uni.switchTab({ url: `/pages/shop/shop${tid ? `?tid=${tid}` : ''}` });
        // #endif
        // #ifndef MP-WEIXIN
        Utils.navigateTo(`/pages/shop/shop${tid ? `?tid=${tid}` : ''}`);
        // #endif
    }

    shareClick() {
        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoins?sharerUid=${this.user_id}`;
        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };
        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            const params = {
                target: 'wechat',
                type: 'web',
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/goldCoins/goldCoinsShare?link=${res.data}`,
                    share_image:
                        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889be0d0dc8a0560012127.png',
                    share_title: `来追觅和我一起赚钱！`,
                    share_desc: `一起来追觅搞钱，路子稳当，钱包鼓得快`,
                },
            };
            Utils.messageChannel('share', params);
        });
    }

    created() {
        VIPModule.getCoinTaskList();
    }

    // 页面显示时(包括用户返回当前页面或切换 Tab) 重新拉取任务列表，确保状态实时刷新
    onPageShow() {
        VIPModule.getCoinTaskList();
    }
}
</script>

<style lang="scss">
$text-color-more: #7d7d7d;

.tasks {
    width: auto;
    /* 统一与其他白色卡片风格 */
    // background-color: $fill-color-bg-white;
    .title-container {
        width: 100%;
        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: $text-color-primary;
        }

        .more {
            font-size: 28rpx;
            color: $text-color-more;
            line-height: 38rpx;

            .arrow {
                height: 38rpx;
                width: 38rpx;
            }
        }
    }

    .task-list {
        overflow-y: scroll;
        max-height: calc(100vh - 596rpx);
        /* 外层已是白色卡片，这里去掉背景和圆角以避免双层 */
        border-radius: 0;
        background-color: transparent;
        padding-bottom: 38rpx;

        .task-item {
            // height: 118rpx;
            // padding: 38rpx 0 0 0;
            height: 129rpx;
            border-radius: 32rpx;
            padding: 24rpx 32rpx;
            background: #ffffff;

            .task-left {
                /* 名称整体盒子：左右结构 */
                .name-box {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-size: 28rpx;
                    font-weight: 600;
                    color: $text-color-primary;
                    line-height: 38rpx;
                    width: 100%;

                    .title-text {
                        max-width: 9em; /* 一行容纳约8个汉字 */
                        word-break: break-all;
                        /* 避免被拉伸 */
                        flex: none;
                    }

                    .count-box {
                        flex-shrink: 0;
                        white-space: nowrap;

                        .week {
                            margin-left: 0;
                        }
                    }
                }

                .integra {
                    display: flex;
                    align-items: center;
                    margin-top: 8rpx;

                    .integral {
                        @include flex(row, center, center, none);

                        & + .integral {
                            margin-left: 24rpx;
                        }

                        color: $text-color-more;
                        font-size: 24rpx;

                        image {
                            width: 28rpx;
                            height: 28rpx;
                            margin-right: 8rpx;
                        }
                    }

                    .separate {
                        display: inline-block;
                        width: 32rpx;
                        color: #d8d8d8;
                        font-size: 20rpx;
                        text-align: center;
                    }
                }
            }

            .task-btn {
                width: 132rpx;
                height: 60rpx;
                line-height: 60rpx;
                border-radius: 96rpx;
                color: white;
                font-weight: 500;
                font-size: 28rpx;
                text-align: center;
                background: linear-gradient(325deg, #ffb547 9%, #ff8826 45%, #fd5922 79%);
                .task-btn-completed {
                    background: linear-gradient(115deg, #cecece 7%, #acacac 89%);
                    border-radius: 96rpx;
                }
                .unCollect-btn {
                    background: linear-gradient(116deg, #ff8d50 7%, #f9580d 52%, #ff2e20 95%);
                    border-radius: 96rpx;
                }
            }
        }
    }
}
.detail {
    width: 128rpx;
    height: 32rpx;
    margin-left: 4rpx;
    opacity: 1;
    font-family: MiSans;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 32rpx;
    letter-spacing: 0rpx;
    font-variation-settings: 'opsz' auto;
    color: #fd5922;
}
</style>
