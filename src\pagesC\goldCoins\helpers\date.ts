/**
 * 判断时间戳对应的日期是今天、昨天还是其他
 * @param {number} timestamp - 时间戳(毫秒)
 */
function checkDate(timestamp: number) {
    // 创建时间戳对应的Date对象
    const targetDate = new Date(timestamp);

    // 获取目标日期的年、月、日
    const targetYear = targetDate.getFullYear();
    const targetMonth = targetDate.getMonth();
    const targetDay = targetDate.getDate();

    // 获取今天的日期
    const today = new Date();
    const todayYear = today.getFullYear();
    const todayMonth = today.getMonth();
    const todayDay = today.getDate();

    // 检查是否是今天
    if (targetYear === todayYear && targetMonth === todayMonth && targetDay === todayDay) {
        return {
            isToday: true,
            isYesterday: false,
        };
    }

    // 获取昨天的日期
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);
    const yesterdayYear = yesterday.getFullYear();
    const yesterdayMonth = yesterday.getMonth();
    const yesterdayDay = yesterday.getDate();

    // 检查是否是昨天
    if (targetYear === yesterdayYear && targetMonth === yesterdayMonth && targetDay === yesterdayDay) {
        return {
            isToday: false,
            isYesterday: true,
        };
    }

    // 其他日期
    return {
        isToday: false,
        isYesterday: false,
        isOtherDay: true,
    };
}

export default checkDate;
