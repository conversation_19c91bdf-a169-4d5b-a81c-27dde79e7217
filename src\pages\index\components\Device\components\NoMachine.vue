<template>
    <div
        class="no-device"
    >
        <div class="no-device-text-area">
            <div class="no-device-text-title">暂无设备</div>
            <div class="no-device-text-desc">您还未添加绑定追觅产品</div>
        </div>
        <div class="add-device-btns">
            <IconButton
                width="42%"
                leftIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6866602be94eb9560012083.png"
                rightIcon="https://wpm-cdn.dreame.tech/images/202306/243935-1686290915081.png"
                text="手动添加"
                @click-handler="addDevice(0)"
            />
            <IconButton
                width="42%"
                leftIcon="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68666033493563000011785.png"
                rightIcon="https://wpm-cdn.dreame.tech/images/202306/243935-1686290915081.png"
                text="扫码添加"
                @click-handler="addDevice(1)"
            />
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

import IconButton from './IconButton.vue';

export default {
    name: 'WashMachine',
    components: {
        IconButton,
    },
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    methods: {
        // 添加设备
        addDevice(type = 0) {
            // type 0: 手动添加, 1: 扫码添加
            if (type === 0) {
                Utils.newMessageChannel('PAGE', 'push', { path: '/product_list' });
            } else {
                Utils.newMessageChannel('PAGE', 'push', { path: '/qr_scan' });
            }
        },
    }
}

</script>

<style scoped>
.no-device {
    border-radius: 32rpx;
    overflow: hidden;
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6866393955c013510010498.png');
    background-size: cover;
}
.no-device-text-area {
    padding: 32rpx;
}
.no-device-text-title {
    color: #121212;
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: 500;
}
.no-device-text-desc {
    color: #121212;
    font-size: 24rpx;
    line-height: 32rpx;
    margin-top: 12rpx;
}
.add-device-btns {
    display: flex;
    justify-content: space-between;
    margin-top: 14rpx;
    padding: 24rpx;
}
</style>
