<template>
    <div
        class="edit-dialog-mask"
        @click="closeDialog"
    >
        <div class="edit-dialog-content">
            <div class="edit-dialog-item" @click.stop="addDevice">
                添加设备
            </div>
            <template v-if="isMaster">
                <div class="dialog-line-max" />
                <div class="edit-dialog-item" @click.stop="renameDevice">
                    重命名
                </div>
                <div class="dialog-line" />
                <div class="edit-dialog-item" @click.stop="shareDevice">
                    分享设备
                </div>
            </template>
            <div class="dialog-line" />
            <div class="edit-dialog-item delete-device-item" @click.stop="deleteDevice">
                删除设备
            </div>
        </div>
        <div class="dialog-cancel-button" @click.stop="closeDialog">
            取消
        </div>
    </div>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

export default {
    name: 'ImageButton',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        // 是否是主设备
        isMaster() {
            const { master = false } = this.currentDevice;
            return master;
        }
    },
    methods: {
        closeDialog() {
            this.$emit('close');
        },
        // 添加设备
        addDevice() {
            Utils.newMessageChannel('PAGE', 'push', { path: '/product_list' });
        },
        // 分享设备
        shareDevice() {
            const { deviceInfo = {}} = this.currentDevice;
            this.closeDialog();
            Utils.newMessageChannel('PAGE', 'push', {
                path: '/device_share/add_contacts',
                extra: {
                    did: this.currentDevice.did,
                    model: this.currentDevice.model,
                    productId: deviceInfo.productId || '',
                    displayName: deviceInfo.displayName || '',
                    mainImage: deviceInfo.mainImage || {},
                    permit: deviceInfo.permit || '',
                    customName: this.currentDevice.customName || '',
                }
            });
        },
        // 重命名设备
        renameDevice() {
            Utils.newMessageChannel('DEVICE', 'rename', { device: this.currentDevice }, (data) => {
                if (data.code === 0) {
                    this.$emit('refresh-list');
                }
                this.$emit('close');
            });
        },
        // 删除设备
        deleteDevice() {
            Utils.newMessageChannel('DEVICE', 'delete', { device: this.currentDevice }, (data) => {
                if (data.code === 0) {
                    this.$emit('refresh-list');
                }
                this.$emit('close');
            });
        }
    }
}
</script>

<style scoped>
.edit-dialog-mask {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 0 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
}
.edit-dialog-content {
    width: 100%;
    background-color: #FFFFFF;
    border-radius: 24rpx;
}
.dialog-line-max {
    width: 100%;
    height: 12rpx;
    background-color: #F6F6F6;
}
.dialog-line {
    width: 100%;
    height: 1rpx;
    background-color: #F6F6F6;
}
.edit-dialog-item {
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    font-size: 32rpx;
    color: #404040;
}
.delete-device-item {
    color: #F53F3F;
}
.dialog-cancel-button {
    width: 100%;
    text-align: center;
    height: 112rpx;
    line-height: 112rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    margin: 32rpx 40rpx 68rpx 40rpx;
}
</style>
