<template>
    <view class="step-content">
        <view v-if="depositTip" class="deposit-tip">{{ depositTip }}</view>
        <view class="pre-step u-flex" :style="{ 'min-height': height }">
            <view class="step-list u-flex-col u-col-left u-row-between">
                <view class="step step-1" :class="{'step-line': stepStatus == 1 || stepStatus == 2 || stepStatus == 3}">
                    <view
                        :class="{
                            line: stepStatus == -1 || stepStatus == 0 || !stepStatus,
                            line1: stepStatus == 1 || stepStatus == 2,
                            line2: stepStatus == 3,
                        }"
                    >
                        <image mode="heightFix" :src="lineImage"></image>
                    </view>
                    <view class="label">{{ step1.label }} </view>
                    <view class="num">￥{{ step1.num }}</view>
                    <view
                        class="extra"
                        v-show="(!isShowExtra && isStepExtra1Highlight) || isShowExtra"
                        :class="{ highlight: isStepExtra1Highlight }"
                    >
                    {{ stepExtra1 }}
                    </view>
                </view>
                <view class="step step-2" :class="{'step-line': stepStatus == 1 || stepStatus == 2 || stepStatus == 3}" :style="{ marginTop: step2MarginTop }">
                    <view class="label">{{ step2.label }} </view>
                    <view class="num" v-if="Number(step2.num) != 0">￥{{ step2.num }}</view>
                    <view v-if="isShowExtra" class="extra">{{ stepExtra2 }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { IPresale } from '@/http/interface/IPresale';

const lineImageList = {
    '-1': 'https://wpm-cdn.dreame.tech/images/202303/640014735f4d33903389242.png',
    0: 'https://wpm-cdn.dreame.tech/images/202306/649e4b46659084163932450.png',
    1: 'https://wpm-cdn.dreame.tech/images/202306/649e4b727fcf95233895610.png',
    2: 'https://wpm-cdn.dreame.tech/images/202306/649e4b934fcff3263951149.png',
    3: 'https://wpm-cdn.dreame.tech/images/202306/649e4baee25039273925424.png',
};
@Component
export default class PreSaleStep extends Vue {
    @Prop({
        type: Object,
        default: () => ({
            coefficient: 0,
            deposit: '0.00',
            deposit_status: 0,
            end_payment: 0,
            expand_price: '0.00',
            presale_time: 0,
            scheduled_number_now: 0,
            start_payment: 0,
            surplus_time: 0,
            tail_order_status: 0,
            tail_price: '00.00',
        }),
    })
    readonly presaleInfo: IPresale;

    @Prop({ type: String, default: '-1' })
    readonly status!: String;

    @Prop({ type: String, default: '80rpx' })
    readonly height!: string;

    @Prop({ type: Boolean, default: true })
    readonly isShowExtra!: boolean;

    @Prop({ type: Number, default: 1 })
    readonly step2lineNum!: number;

    public stepStatus: any = '-1';
    public isStepExtra1Highlight: Boolean = false;
    public step1: any = {
        label: '定金 ',
        num: 0,
        extra: '',
    };

    public step2: any = {
        label: '尾款',
        num: 0,
        extra: '',
    };

    get lineImage(): String {
        return lineImageList[this.stepStatus];
    }

    get step2MarginTop(): String {
        return (this.step2lineNum - 1) * 20 + 'rpx';
    }

    get stepExtra1(): String {
        let stepExtra1 = '';
        switch (this.stepStatus) {
            case '-1':
                stepExtra1 = '';
                break;
            case '0':
                stepExtra1 = uni.$u.timeFormat(this.presaleInfo.presale_time, 'mm月dd日 hh:MM') + '截止';
                break;
            case '1':
                if (
                    this.presaleInfo.deposit_status &&
                    Number(this.presaleInfo.deposit) < Number(this.presaleInfo.expand_price)
                ) {
                    stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
                    this.isStepExtra1Highlight = true;
                } else {
                    stepExtra1 = uni.$u.timeFormat(this.presaleInfo.presale_time, 'mm月dd日 hh:MM') + '截止';
                }
                break;
            case '2':
                if (
                    this.presaleInfo.deposit_status &&
                    Number(this.presaleInfo.deposit) < Number(this.presaleInfo.expand_price)
                ) {
                    stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
                    this.isStepExtra1Highlight = true;
                } else {
                    stepExtra1 = uni.$u.timeFormat(this.presaleInfo.presale_time, 'mm月dd日 hh:MM') + '截止';
                }
                break;
            case '3':
                if (
                    this.presaleInfo.deposit_status &&
                    Number(this.presaleInfo.deposit) < Number(this.presaleInfo.expand_price)
                ) {
                    stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
                    this.isStepExtra1Highlight = true;
                } else {
                    stepExtra1 = '';
                }
                break;
            default:
                stepExtra1 = '';
                break;
        }
        return stepExtra1;
    }

    get stepExtra2(): String {
        let stepExtra2 = '';
        if (this.status == '100') {
            return ''
        }
        switch (this.stepStatus) {
            case '-1':
                stepExtra2 = '';
                break;
            case '0':
                stepExtra2 = `请在${uni.$u.timeFormat(
                    this.presaleInfo.end_payment,
                    'mm月dd日 hh:MM',
                )}点前完成尾款支付，可享受积分优惠`;
                break;
            case '1':
                stepExtra2 = `请在${uni.$u.timeFormat(
                    this.presaleInfo.end_payment,
                    'mm月dd日 hh:MM',
                )}点前完成尾款支付，可享受积分优惠`;
                break;
            case '2':
                stepExtra2 = `请在${uni.$u.timeFormat(
                    this.presaleInfo.end_payment,
                    'mm月dd日 hh:MM',
                )}点前完成尾款支付，可享受积分优惠`;
                break;
            default:
                stepExtra2 = '';
                break;
        }
        return stepExtra2;
    }

    get depositTip(): String {
        if (this.stepStatus == '-1') {
            return `${uni.$u.timeFormat(this.presaleInfo.presale_time, 'mm月dd日 hh:MM')}点前支付定金`;
        }
        return '';
    }

    @Watch('presaleInfo', { immediate: true, deep: true })
    genPresaleData() {
        this.step1.num = this.presaleInfo.deposit;
        this.step2.num = this.presaleInfo.tail_price;
        // if (Number(this.presaleInfo.tail_price) == 0) {
        //   this.step2.label = '发货';
        //   this.step2.num = 0;
        // }
        const { deposit_status, tail_order_status, can_pay_deposit, can_pay_tail, end_payment } = this.presaleInfo;
        const newDate = String(end_payment || 0).length > 10 ? new Date().getTime() : Math.round(new Date().getTime() / 1000);
        if (can_pay_deposit === undefined) {
            this.stepStatus = '-1';
        } else if (deposit_status === 0) {
            this.stepStatus = '0';
        } else if (deposit_status === 1 && tail_order_status === 0 && can_pay_tail === 0 && newDate < end_payment) {
            this.stepStatus = '1';
        } else if ((deposit_status === 1 && tail_order_status === 0 && can_pay_tail === 0 && newDate > end_payment) ||
        (deposit_status === 1 && tail_order_status === 0 && can_pay_tail === 1)) {
            this.stepStatus = '2';
        } else {
            this.stepStatus = '3';
        }
    }
}
</script>
<style lang="scss" scoped>
.step-content {
    display: flex;
    width: 100%;
}

.deposit-tip {
    margin-bottom: 10rpx;
    font-size: 24rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(29, 30, 32, 0.4);
    line-height: 32rpx;
    -webkit-text-size-adjust: none;
}

.pre-step {

    .step-list {
        height: 100%;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 38rpx;
        color: #1d1e20;
        font-family: PingFang SC-Medium, PingFang SC;

        .step-1 {
            position: relative;
            .line {
                position: absolute;
                width: 28rpx;
                height: calc(100% + 40rpx);
                left: 0rpx;
                top: 12rpx;
                flex: 1;
            }
            .line1 {
                position: absolute;
                width: 28rpx;
                height: calc(100% + 50rpx);
                left: 0rpx;
                top: 2rpx;
                flex: 1;
            }
            .line2 {
                position: absolute;
                width: 28rpx;
                height: calc(100% + 50rpx);
                left: 0rpx;
                top: 2rpx;
                flex: 1;
            }
            image {
                height: 100%;
            }
        }
        .step {
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-start;
            align-items: flex-start;
            padding-left: 28rpx;
        }
        .step-line{
          padding-left: 40rpx;
        }

        .label {
            min-width: 60rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: $text-color-primary;
        }

        .num {
            color: $fill-color-primary-active;
            font-weight: 500;
            font-size: 24rpx;
            margin-right: 16rpx;
        }

        .extra {
            flex: 1;
            // width: 306rpx;
            font-size: 24rpx;
            font-weight: 400;
            font-family: PingFang SC-Regular, PingFang SC;
            color: $text-color-disable;

            &.highlight {
                font-size: 24rpx;
                font-family: PingFang SC-Medium, PingFang SC;
                white-space: nowrap;
                font-weight: 400;
                color: $func-color-danger-text;
            }
        }

        .twoline {
            margin-top: 38rpx;
        }
    }
}
</style>
