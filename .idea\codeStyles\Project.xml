<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <JSCodeStyleSettings version="0">
      <option name="SPACE_BEFORE_GENERATOR_MULT" value="true" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="OBJECT_LITERAL_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="SPACE_BEFORE_GENERATOR_MULT" value="true" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="OBJECT_LITERAL_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </TypeScriptCodeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="BLOCK_COMMENT_ADD_SPACE" value="true" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="IF_BRACE_FORCE" value="1" />
      <option name="DOWHILE_BRACE_FORCE" value="1" />
      <option name="WHILE_BRACE_FORCE" value="1" />
      <option name="FOR_BRACE_FORCE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="BLOCK_COMMENT_ADD_SPACE" value="true" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="IF_BRACE_FORCE" value="1" />
      <option name="DOWHILE_BRACE_FORCE" value="1" />
      <option name="WHILE_BRACE_FORCE" value="1" />
      <option name="FOR_BRACE_FORCE" value="1" />
    </codeStyleSettings>
  </code_scheme>
</component>