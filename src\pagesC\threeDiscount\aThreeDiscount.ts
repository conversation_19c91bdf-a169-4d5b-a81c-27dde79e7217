import { Vue, Component } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import { getThemeActivity } from '@/http/memberActivity';

import { getOneYuanFlashSelectGoods, joinInvite, getOneYuanFlashSaleDetail, getOneYuanFlashSaleId } from '@/http/oneYuanFlashSalePage';
import { UserModule } from '@/store/modules/user';
import { salesStoreDetail, orderInfo } from '@/http/order';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { CheckAppJump } from '@/common/decorators';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import GoodsGallery from './component/GoodsGallery.vue';
import { GoodsModule, IGoodsInfo } from '@/store/modules/goods';
import { goodsInfo } from '@/http/goods';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

@Component({
    name: 'MemberActivity',
    components: {
        ShareDialog,
        GoodsGallery,
        EarnMoneySpendTimeoutTips
    },
    filters: {},
})
export default class OneYuanFlashSale extends Vue {
    $refs!: {
        TimeoutTips;
        EarnMoneySpendTimeoutTips;
    };

    // public arr: any[] = [];
    public isEmptyPopup: boolean = false;
    public link: string = '';
    public isActivityNotStartPopup: boolean = false;
    public order_no: string = '';
    public unPayPopup = false;
    public goToSelectPopup = false;
    public isEndAcitivity: boolean = false; // 活动是否结束
    public inviter_info: any = {}; // 邀请人信息
    public baseInfo: any = {
        required_invite_number: 10,
    }; // 活动基础信息

    public isInit: boolean = false;
    public loading = false;
    public isOrderShow: boolean = false;
    public orderInfoGoods: any = {};
    public orderInfo: any = {};
    public showRulePopup = false;
    public showAssistPopup = false;
    public relate_id: number = 0; // 秒杀id
    public inviter_id: number = 0; // 老用户id
    public countdownTime: number = 0; // 倒计时时间
    public show: boolean = false;
    public productInfo: any = {};
    public productInfoPopData: any = {};
    public isLock: boolean = false; // 是否锁定
    public showPopNew: boolean = false;
    public isOpenStore: boolean = false;
    public showProduct: boolean = false;
    public inviteCount: number = 0;
    public isBackShow: boolean = false; // 您已选择购买其他商品
    public isConfirmPopup: boolean = false; // 是否确认购买此商品？
    public shareContent: any = {
        url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/threeDiscount?inviter_id=${this.user_id}`,
        image:
            'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6894b1788eb975850033223.png' +
            '?x-oss-process=image/resize,h_300,w_300',
        title: '追觅好物，限时3折购',
        desc: '邀请新用户加入，新款扫地机器人三折拿走！',
    }

    public shareConfig: any = {
        target: 'wechat,weixin_circle,qq,sina,image_template,download',
        link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/threeDiscount?inviter_id=${this.user_id}`,
        jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/threeDiscount?inviter_id=${this.user_id}`,
    }

    public popNewInfo: any = {
        type: 1, // 1: 开小店 2: 您尚未解锁购买资格 3: 您在本轮活动已购买此件商品
        content2: '开店铺货即可邀请好友',
        content1: '您尚未成为小店店主',
        btnText: '去开店',
        btnUrl: '/pagesC/threeDiscount/threeDiscount',
    };

    get unlocked() {
        return this.inviteCount >= this.baseInfo.required_invite_number && this.baseInfo && this.baseInfo.shop_purchase && this.baseInfo.shop_purchase.has_order;
    }

    public assistUsers: any[] = []; // 助力用户

    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    public activityId: any = 0;
    public storeDetail: any = {};
    public productList: any[] = [];
    // 助力弹窗相关数据
    public popupCountdownTime = '00:00:00';
    private popupCountdownTimer: any = null;
    private popupEndTime: number = 0;
    public activityRule: string = '';
    public assistTarget = {
        avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6894b4be1c3c41160012390.png',
        nickname: '用户A',
    };

    public seckillId: number = 0;

    get statusBarHeight() {
        return AppModule.statusBarHeight;
    }

    get sellPointList() {
        const { sell_point }: any = GoodsModule.goodsInfo;
        if (!sell_point) return [];
        return (sell_point || '').replaceAll(/；/g, ';').split(';');
    }

    public timeData: any = {};
    public fromPage: string = '';
    public taskCode: string = '';
    // 去订单页
    goOrderPage() {
      this.isBackShow = false
      this.isOrderShow = false
      Utils.navigateTo('/pagesA/orderDetail/orderDetail?order_no=' + this.baseInfo.order_no);
    }

    onChange(e: any) {
        this.timeData = e;
    }

    goShopPage() {
        Utils.navigateTo('/pagesC/shopStore/index');
    }

    // 立即购买
    handleBuyNow(item) {
      console.log('itemitemitemitemitem', item)
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_BUY_CLICK,
            },
            true,
        );
        this.initActivity()
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_BUY_CLICK,
            id: Number(this.productInfoPopData.goods_id),
            name: this.productInfoPopData.show_name,
        }, true);
        uni.navigateTo({
            url: `/pagesA/settlement/settlement?buy_goods_number=${this.baseInfo.goods_info.buy_goods_number
                }&orderType=THIRTY_PERCENT&disable_all_discount=1&seckill_id=${this.seckillId}&sParams=${JSON.stringify([
                    { gid: this.productInfoPopData.goods_id, sid: '0', num: 1, gini_id: 0 },
                ])}`,
        });
    }

    getOrderInfo() {
        orderInfo({ order_no: this.baseInfo.order_no }).then(res => {
            this.isInit = true;
            this.orderInfo = res;
            if (res.goods.length) {
                this.orderInfoGoods = res.goods[0];
            }
        })
    }

    @CheckAppJump()
    handleMprice(item: any) {
      console.log('3333333333333333333333333', item)
      this.productInfoPopData = item;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_BUY_CLICK,
            id: Number(item.goods_id),
            name: item.show_name,
        }, true);
        this.productInfoPopData = item;
        if (this.orderInfo.status == 0) {
            if (this.baseInfo.order_no && this.baseInfo.goods_info.gid === item.goods_id) {
                this.isOrderShow = true;
            } else {
                this.isBackShow = true;
            }
            return;
        }
        if (this.unlocked) {
            if (item.stock === 0) {
                this.isEmptyPopup = true;
                return;
            }
            this.isConfirmPopup = true;
            return;
        }

        if (this.unlocked) {
            if (item.stock === 0) {
                this.isEmptyPopup = true;
                return;
            }
            this.handleAssist(item)
        } else {
            this.showPopNew = true;
            this.popNewInfo.type = 2;
            this.popNewInfo.content1 = '您尚未解锁购买资格';
            this.popNewInfo.content2 = '完成所有任务即可3折购好物';
            this.popNewInfo.btnText = '去做任务';
            this.popNewInfo.btnUrl = '';
        }
    }

    // 确认购买
    handleConfirmBuy() {
        this.isConfirmPopup = false;
        console.log(11111111111111)
        getOneYuanFlashSelectGoods({ gid: this.productInfoPopData.goods_id, seckill_id: this.seckillId }).then(res => {
            this.handleBuyNow(this.productInfoPopData)
        })
    }

    async openProduct() {
        this.showProduct = true;
        let params = {} as any;
        params = { gid: this.productInfoPopData.goods_id };
        if (GoodsModule.isInshop) {
            params.is_internal_purchase = 1;
        }
        await GoodsModule.asyncGetGoodsInfo({ params, pointsMall: false });
    }

    get goodsData(): IGoodsInfo {
        return GoodsModule.goodsInfo;
    }

    closeProduct() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_PRODUCT_CLOSE_CLICK,
                id: Number(this.productInfoPopData.goods_id),
                name: this.productInfoPopData.show_name,
            },
            true,
        );
        this.showProduct = false;
        GoodsModule.setGoodsInfo({} as IGoodsInfo);
    }

    closeRulePopupPay() {
        this.unPayPopup = false;
    }

    @CheckAppJump()
    seeMore() {
        // 去商品分类
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_MORE_CLICK,
            },
            true,
        );
        Utils.navigateTo('/pagesA/brandType/brandType');
    }

    lazyLoad() {
        const imgList = document.getElementsByTagName('img');
        console.log('imgList', imgList)
        for (let i = 0; i < imgList.length; i++) {
            const img = imgList[i];
            if (img.dataset.src) {
                console.log('img', img)
                img.src = img.dataset.src;
                img.style.width = '100%';
                img.removeAttribute('data-src');
            }
        }
    }

    closeEmptyPopup() {
        this.isEmptyPopup = false;
        Utils.navigateTo('/pagesC/ambassador/login/index');
    }

    closeActivityNotStartPopup() {
        this.isActivityNotStartPopup = false;
        Utils.navigateTo('/pagesC/ambassador/login/index');
    }

    goBack() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RETURN,
            },
            true,
        );
        Utils.goBack();
    }

    @CheckAppJump()
    openRulePopup() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RULE_CLICK,
            },
            true,
        );
        this.showRulePopup = true;
    }

    closeRulePopup() {
        this.showRulePopup = false;
    }

    onFinish() {
        this.isEndAcitivity = true;
    }

    getOrderPayStatus() {
        uni.navigateTo({
            url: `/pagesA/orderDetail/orderDetail??order_no=${this.order_no}&user_order_type=1`,
        });
        this.unPayPopup = false;
    }

    // 格式化时间
    private formatTime(seconds: number): string {
        const days = Math.floor(seconds / (24 * 3600));
        const hours = Math.floor((seconds % (24 * 3600)) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${days}天${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
            .toString()
            .padStart(2, '0')}`;
    }

    // 更新弹窗倒计时
    private updatePopupCountdown() {
        const now = new Date().getTime();
        const diff = Math.max(0, this.popupEndTime - now);
        const seconds = Math.floor(diff / 1000);
        if (seconds <= 0) {
            this.popupCountdownTime = '0天00:00:00';
            this.stopPopupCountdown();
            return;
        }
        this.popupCountdownTime = this.formatTime(seconds);
    }

    // 开始弹窗倒计时
    private startPopupCountdown() {
        // 设置弹窗倒计时结束时间为当前时间加1小时（示例）
        this.popupEndTime = this.baseInfo.end_time * 1000;
        this.updatePopupCountdown();
        this.popupCountdownTimer = setInterval(() => {
            this.updatePopupCountdown();
        }, 1000);
    }

    // 停止弹窗倒计时
    private stopPopupCountdown() {
        if (this.popupCountdownTimer) {
            clearInterval(this.popupCountdownTimer);
            this.popupCountdownTimer = null;
        }
    }

    // 显示助力弹窗
    openAssistPopup() {
        this.showAssistPopup = true;
        this.startPopupCountdown();
    }

    @CheckAppJump()
    async handleOpenStore() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHOP_CLICK,
            },
            true,
        );
        if (this.baseInfo?.shop_purchase?.has_store) {
            if (UserModule.sdkVersion < 13) {
                Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
                return;
            }
            this.shareContent = {
                url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/shopStore/shareProduct?inviter_id=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png' +
                    '?x-oss-process=image/resize,h_300,w_300',
                title: '邀请您来追觅开小店',
                desc: '',
            }
            this.shareConfig = {
                target: 'wechat,weixin_circle,qq,sina,image_template,download',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/createShop/index?inviter_id=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/createShop/index?inviter_id=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
            }
            const randomIndex = Math.floor(Math.random() * this.productList.length);
            const goodsInfoData = await goodsInfo({ gid: this.productList[randomIndex].goods_id })
            this.productInfo = {
                name: this.productList[randomIndex].show_name,
                image: this.productList[randomIndex].goods_image,
                price: this.productList[randomIndex].sale_price,
                desc: goodsInfoData?.goods?.introduce ?? '',
                priceColor: '#FF1F0E',
                descColor: '#C59245',
                imageBg: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png',
            };
            this.show = true;
        } else {
            this.showPopNew = true;
            this.popNewInfo.type = 1;
            this.popNewInfo.content1 = '您尚未成为小店店主';
            this.popNewInfo.content2 = '开店铺货即可去做任务';
            this.popNewInfo.btnText = '去开店';
            this.popNewInfo.btnUrl = '/pagesC/createShop/index';
        }
    }

    @CheckAppJump()
    async handleOpenStoreBuy() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_INVITE_SHARE_SHOP_CLICK,
            },
            true,
        );
        if (this.baseInfo?.shop_purchase?.has_store) {
            if (UserModule.sdkVersion < 13) {
                Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
                return;
            }
            this.shareContent = {
                url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/shopStore/shareProduct?storekeeper=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png' +
                    '?x-oss-process=image/resize,h_300,w_300',
                title: `【${this.storeDetail.store_name}】这是我开的追觅小店，快进来看看吧！`,
                desc: '这是我精选的追觅商品清单，快进来看看吧！',
            }
            this.shareConfig = {
                target: 'wechat,weixin_circle,qq,sina,image_template,download',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/shopStore/shareProduct?storekeeper=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/shopStore/shareProduct?storekeeper=${this.user_id}&relate_id=${this.seckillId}&activity_id=${this.activityId}`,
            }
            const randomIndex = Math.floor(Math.random() * this.productList.length);
            const goodsInfoData = await goodsInfo({ gid: this.productList[randomIndex].goods_id })

            this.productInfo = {
                name: this.productList[randomIndex].show_name,
                image: this.productList[randomIndex].goods_image,
                price: this.productList[randomIndex].sale_price,
                desc: goodsInfoData?.goods?.introduce ?? '',
                priceColor: '#FF1F0E',
                descColor: '#C59245',
                imageBg: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png',
            };
            this.show = true;
        } else {
            this.showPopNew = true;
            this.popNewInfo.type = 1;
            this.popNewInfo.content1 = '您尚未成为小店店主';
            this.popNewInfo.content2 = '开店铺货即可去做任务';
            this.popNewInfo.btnText = '去开店';
            this.popNewInfo.btnUrl = '/pagesC/createShop/index';
        }
    }

    // 右上角分享
    async shareActivity() {
        if (UserModule.sdkVersion < 13) {
            Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
            return;
        }
        const randomIndex = Math.floor(Math.random() * this.productList.length);
        this.shareContent = {
            url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/aThreeDiscount?inviter_id=${this.user_id}&v=1`,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c7634d15568570010697.png' + '?x-oss-process=image/resize,h_300,w_300',
            title: `惊爆3折！【${this.productList[randomIndex].show_name}】仅【${this.productList[randomIndex].sale_price}元】`,
            desc: '年度大促！Dreame App官方正品！库存告急！点击立享折扣>> 比双11还便宜！错过拍大腿！',
        }
        this.shareConfig = {
            target: 'wechat,weixin_circle,qq,sina,image_template,download',
            link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/aThreeDiscount?inviter_id=${this.user_id}&v=1`,
            jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/threeDiscount/aThreeDiscount?inviter_id=${this.user_id}&v=1`,
        }
        const goodsInfoData = await goodsInfo({ gid: this.productList[randomIndex].goods_id })
        this.productInfo = {
            name: this.productList[randomIndex].show_name,
            image: this.productList[randomIndex].goods_image,
            price: this.productList[randomIndex].sale_price,
            desc: goodsInfoData?.goods?.introduce ?? '',
            priceColor: '#FF1F0E',
            descColor: '#C59245',
            imageBg: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c4c49caead8310149763.png',
        };
        this.show = true;
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.cardShare(type)(
            this.shareConfig,
            {
                content: this.shareContent,
                extras: {
                    type: 'activity',
                    id: this.activityId,
                    goods: {
                        name: this.productInfo.name,
                        desc: this.productInfo.desc,
                        imageBg: this.productInfo.imageBg,
                        image: this.productInfo.image,
                        price: this.productInfo.price,
                        priceColor: '#FF1F0E',
                        descColor: '#C59245',
                    },
                    copyText: '我正在参与追觅3折购活动！还能开自己的专属小店，最高赚20%佣金！',
                },
            },
        );
    }

    // 关闭助力弹窗
    closeAssistPopup() {
        this.showAssistPopup = false;
        this.stopPopupCountdown();
    }

    handlePopNewBtn() {
        this.showPopNew = false;
        if (this.popNewInfo.btnUrl) {
            Utils.navigateTo(this.popNewInfo.btnUrl);
        } else {
          uni.pageScrollTo({
            scrollTop: 0,
            duration: 300
          })
        }
    }

    // 处理助力
    handleAssistAction() {
        // 判断当前时间是否在活动时间内
        const now = Math.floor(Date.now() / 1000);
        if (now < this.baseInfo.start_time) {
            Utils.Toast('当前不在活动时间内，无法进行下一步操作');
            return;
        }
        if (now > this.baseInfo.end_time) {
            this.isEndAcitivity = true;
            return;
        }
        joinInvite({
            invite_type: 1,
            inviter_id: this.inviter_id,
            relate_id: this.relate_id,
        }).then((res) => {
            this.closeAssistPopup();
            this.goToSelectPopup = true;
        });
    }

    handleSelectAction() {
        this.goToSelectPopup = false;
    }

    initActivity() {
        getOneYuanFlashSaleId({
            activity_id: Number(this.activityId),
            gid: 0,
        }).then((res) => {
            if (res?.seckill_id) {
                this.seckillId = res.seckill_id;
                getOneYuanFlashSaleDetail({ seckill_id: this.seckillId }).then((r) => {
                    this.baseInfo = r;
                    this.inviteCount = r.help_friends.length;
                    const helpFriends = r.help_friends;
                    if (helpFriends.length > this.baseInfo.required_invite_number) {
                        helpFriends.length = this.baseInfo.required_invite_number
                    }
                    if (helpFriends.length < this.baseInfo.required_invite_number) {
                        helpFriends.push(...Array(this.baseInfo.required_invite_number - helpFriends.length).fill(null));
                    }
                    this.assistUsers = helpFriends;
                    this.isLock = r.is_lock;
                    if (this.baseInfo.order_no) {
                        this.getOrderInfo();
                    } else {
                        this.isInit = true;
                      this.orderInfo = {};
                      this.orderInfoGoods = {};
                    }
                });
            }
        });
    }

    @CheckAppJump()
    handleAssist(item) {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_PRODUCT_CLICK,
                id: Number(item.goods_id),
                name: item.show_name,
            },
            true,
        );
        this.productInfoPopData = item;
        this.showProduct = true;
        this.$nextTick(() => {
            setTimeout(() => {
                this.lazyLoad();
            }, 1000);
        })
    }

    async onLoad(options: any) {
       this.fromPage = options.from || '';
        this.taskCode = this.fromPage == 'goldCoins' ? 'viewGoodsGold' : 'viewGoodsMoney';
        Utils.setPageTitle('追觅好物，限时三折购');
        this.link = options.link;
        this.seckillId = options.relate_id || Number(process.env.VUE_APP_ALL_THREE_DISCOUNT_ACTIVITY_ID);
        this.activityId = Number(process.env.VUE_APP_ALL_THREE_DISCOUNT_ACTIVITY_ID);
        const { modules, base_info } = await getThemeActivity({ id: Number(this.activityId) });
        if (modules.length > 0) {
            modules.forEach((item) => {
                if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                    this.productList = item.extra;
                }
            });
        }
        this.baseInfo = base_info;
        this.activityRule = base_info?.rule ?? '';
        this.loading = true;
        this.countdownTime =
            new Date(base_info.end_time * 1000).getTime() - new Date().getTime() > 0
                ? new Date(base_info.end_time * 1000).getTime() - new Date().getTime()
                : 0;
        if (this.baseInfo.end_time * 1000 < new Date().getTime()) {
            this.isEndAcitivity = true;
            this.loading = true;
            return true;
        } else {
            this.isEndAcitivity = false;
            this.loading = true;
        }
        if (!this.user_id) {
            this.isOpenStore = false
            if (modules?.[0]?.extra?.[0]?.invite_number) {
                this.baseInfo.required_invite_number = Number(modules[0]?.extra?.[0]?.invite_number);
                const inviteCount = Number(modules[0]?.extra?.[0]?.invite_number);
                this.assistUsers = Array(inviteCount).fill(null);
            } else {
              this.assistUsers = [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            ]
            }
        }
        if (this.user_id) {
            this.initActivity()
        } else {
            this.isInit = true;
        }
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_THREE_DISCOUNT_EXPOSURE,
            },
            true,
        );
    }

    async onShow() {
        if (this.user_id) {
            const storeDetail = await salesStoreDetail({ store_id: this.user_id });
            this.storeDetail = storeDetail;
            this.isOpenStore = storeDetail.status === '1'
            this.isOpenStore = false
        }
        if (this.seckillId && this.assistUsers.length) {
            getOneYuanFlashSaleDetail({ seckill_id: this.seckillId }).then((r) => {
                this.baseInfo = r;
                this.inviteCount = r.help_friends.length;
                const helpFriends = r.help_friends;
                if (helpFriends.length > this.baseInfo.required_invite_number) {
                    helpFriends.length = this.baseInfo.required_invite_number
                }
                if (helpFriends.length < this.baseInfo.required_invite_number) {
                    helpFriends.push(...Array(this.baseInfo.required_invite_number - helpFriends.length).fill(null));
                }
                this.assistUsers = helpFriends;
                this.isLock = r.is_lock;
                if (this.baseInfo.order_no) {
                    this.getOrderInfo();
                } else {
                  this.orderInfo = {};
                  this.orderInfoGoods = {};
                }
            });
        }
        setTimeout(() => {
            // this.$refs.TimeoutTips.getViewGoodsTaskStatus();
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 500);
    }

    onUnload() {
        // 清理弹窗倒计时定时器
        this.stopPopupCountdown();
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
      this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
      this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
  }
}
