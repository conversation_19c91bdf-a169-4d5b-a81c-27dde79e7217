<template>
    <view class="search-container">
        <!-- #ifdef H5 -->
        <view class="search-area" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
            <view class="serach_heard">
                <image class="search_back" src="https://wpm-cdn.dreame.tech/images/202308/972703-1691654023213.png" @click="goBack"></image>
                <view class="search_inputs">
                    <view class="search_border">
                        <!-- <lazy-image :lazyLoad="true" class="search_icon"
                            src="https://wpm-cdn.dreame.tech/images/202306/170933-1686362508396.png" /> -->
                        <view class="search_input">
                            <input ref="input" class="input" @blur="searchBlur" @focus="searchFocus" type="text"
                                v-model.trim="keyword" :focus="focus" @keydown.enter="searchKeydown" placeholder="扫地机新品上市！"
                                confirm-type="search" />
                        </view>
                        <image :lazyLoad="true" class="search_clear" v-show="keyword.length > 0" @click="clearInput"
                            src="https://wpm-cdn.dreame.tech/images/202306/446725-1686362627223.png" />
                    </view>
                </view>
                <view class="search_cancel" @click="searchKeydown">搜索</view>
            </view>
        </view>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <CustomBar :title="title" background="#F4F4F4"></CustomBar>
        <CustomSearch ref="search" type="text" :focus="focus" searchIcon="search" placeholder="请输入要搜索的关键词"
            :actionText="'取消'" v-model="keyword" bgColor="#ffffff" :customStyle="{ marginLeft: '32rpx' }"
            :actionStyle="{ color: '#8C6533 ', marginLeft: '20rpx', fontSize: '28rpx', marginRight: '32rpx' }"
            @search="handleSearch" @custom="searchClick" @clear="keyword = ''">
        </CustomSearch>
        <!-- #endif -->
        <SearchHistory ref="SearchHistory" v-show="!isSearch" @onSearch="handleHistorySearch"
            :search_type="search_type"
            :goldCoin="goldCoin"
            >
        </SearchHistory>
        <block v-if="isSearch">
            <!-- 普通商品 -->
            <scroll-view v-if="searchList.length > 0 && search_type == 1" scroll-y class="search-result"
                lower-threshold="50" @scrolltolower="onreachBottom">
                <GoodItem v-for="(r, i) in searchList" :key="i" :good="r" :isShowSelf="false"></GoodItem>
                <view class="no-more" v-if="isLoaded && searchList.length > 0">没有更多了~</view>
            </scroll-view>
            <!-- 积分商品 -->
            <scroll-view v-if="searchList.length > 0 && search_type == 2" scroll-y class="search-result"
                lower-threshold="50" @scrolltolower="onreachBottom">
                <view class="content">
                    <pointProducts v-for="item in searchList" :key="item.id" :pointProductsInfo="item" />
                </view>
                <view class="no-more" v-if="isLoaded && searchList.length > 0">没有更多了~</view>
            </scroll-view>
            <u-empty v-if="searchList.length == 0" text="未找到相关结果"
                icon="https://wpm-cdn.dreame.tech/images/202306/308776-1686360313376.png" textSize="28rpx"
                textColor="#777777" marginTop="338rpx" width="508rpx" height="508rpx">
            </u-empty>
            <EarnMoneySpendTimeoutTips
                v-if="fromPage == 'goldCoins'"
                ref="EarnMoneySpendTimeoutTips"
                :position="{ top: '360rpx' }"
                :task-code="'searchViewGoods'"
                :fromPage="fromPage"
                :watchTime="30000"
            />
        </block>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import SearchHistory from './components/SearchHistory.vue';
import GoodItem from './components/GoodItem/GoodItem.vue';
import CustomSearch from '@/components/CustomSearch/CustomSearch.vue';
import { GoodsModule } from '@/store/modules/goods';
import { AppModule } from '@/store/modules/app';
import { throttle } from 'lodash';
import Utils from '@/common/Utils';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

@Component({
    components: {
        SearchHistory,
        GoodItem,
        CustomSearch,
        EarnMoneySpendTimeoutTips,
    },
})
export default class Search extends Vue {
    public title: string = '搜索';
    public bgColor: string = 'transparent'; // 导航栏背景
    public isLoaded: boolean = false;
    public keyword: string = '';
    public isSearch: boolean = false;
    public focus: boolean = true;
    public page: number = 1;
    public page_size: number = 20;
    public list = [];
    public searchList: Array<any> = [];
    public borderBottomLine: string = '#121212';
    private throttledSearch: any; // 声明节流函数
    public search_type: number = 0; // 1：普通会员商品; 2：积分商城商品
    public hotSearchList: Array<any> = [];
    public fromPage: string = '';
    public goldCoin: string = '';
    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    $refs!: {
        input;
        SearchHistory: SearchHistory;
        EarnMoneySpendTimeoutTips;
    };

    async onLoad(options) {
        this.fromPage = options.from || '';
        this.goldCoin = options.goldCoin || '';
        this.search_type = options.search_type;
        // #ifdef H5
        setTimeout(() => {
            this.$refs.input._focus();
        }, 2000);
        // #endif
        uni.$on('updateHotSearchList', this.handleUpdateHotSearchList);
    }

    onUnload() {
        uni.$off('updateHotSearchList');
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    handleUpdateHotSearchList(val) {
        this.hotSearchList = val
    }

    goBack() {
        Utils.goBack();
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }

    onreachBottom() {
        this.search();
    }

    handleSearch(val, isHistory = false) {
        if (val === '') return;
        for (let i = 0; i < this.hotSearchList.length; i++) {
            if (this.hotSearchList[i].name === val) {
                switch (Number(this.hotSearchList[i].jump_type)) {
                    case 1:
                        Utils.navigateTo(`${this.hotSearchList[i].jump_url}`);
                        break;
                    case 4:
                        Utils.newMessageChannel('PAGE', 'push', { path: val.jump_url });
                        break;
                }
                break
            }
        }
        this.keyword = val;
        this.isLoaded = false;
        this.page = 1;
        this.searchBlur();
        this.search(isHistory);
        this.isSearch = true;
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
        this.searchList = [];
        this.$refs.SearchHistory.updateSearchHistory(this.keyword);
    }

    handleHistorySearch(val) {
        this.handleSearch(val, true)
    }

    async search(isHistory = false) {
        console.log(this.search_type);

        if (this.isLoaded) return;
        try {
            uni.showLoading({ title: '' });
            const res: any = await GoodsModule.asyncSearchGoods({
                keyword: this.keyword,
                page: this.page++,
                page_size: this.page_size,
                search_type: this.search_type
            });
            uni.hideLoading();
            console.log('🚀 ~ Search ~ search ~ res:', res)
            if (res.list && res.list.length > 0) {
                this.searchList = [...this.searchList, ...res.list];
                if (res.list.length < this.page_size) {
                    this.isLoaded = true;
                }
            } else {
                this.isLoaded = true;
            }
            if (isHistory) {
                Utils.reportEvent('history_search', { history_search_keyword: this.searchList.length ? '有结果' : '无结果' })
            } else {
                Utils.reportEvent('search_result', { is_search_result: this.searchList.length ? '有结果' : '无结果' })
            }
        } catch (error) {
            uni.hideLoading();
        }
    }

    clearInput() {
        this.keyword = '';
        this.$refs.input._focus();
        this.isSearch = false;
    }

    created() {
        // 在组件创建时初始化节流函数
        this.throttledSearch = throttle(this.handleSearch, 300, {
            leading: true, // 允许首次立即执行
            trailing: true // 允许最后一次执行
        });
    }

    searchKeydown() {
         // 使用节流函数代替直接调用
        this.throttledSearch(this.keyword);
       // this.handleSearch(this.keyword)
        // throttle(() => {  }, 100)
    }

    searchClick() {
        Utils.reportEvent('search_back', {})
        return Utils.goBack();
    }

    searchBlur() {
        this.borderBottomLine = '#121212';
    }

    searchFocus() {
        this.borderBottomLine = '#AB8C5E';
    }
}
</script>
<style lang="scss" scoped>
@import './search.scss';
</style>
