<template>
    <view v-if="wxAuth && isPhone && timer" class="timeout-tips">
        <view class="timeout">{{ isShow ? `浏览${time}s获得奖励` : '任务完成' }}</view>
        <view class="point-dreame" v-if="!isShow">
            <view class="point">
                <image class="img" src="https://wpm-cdn.dreame.tech/images/202307/146288-1689662119319.png"></image>
                <text>+{{ point }}</text>
            </view>
            <view class="dreame">
                <image class="img" src="https://wpm-cdn.dreame.tech/images/202307/561337-1689662440681.png"></image>
                <text>+{{ grow }}</text>
            </view>
            <view class="dreame" v-if="!complete">
                <image class="img" src="https://wpm-cdn.dreame.tech/images/202307/561337-1689662440681.png"></image>
                <text>+0.5元</text>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator';
import { viewGoodsTask, getTaskInfo, doGoldTask } from '@/http/vip';
import { VIPModule } from '@/store/modules/vip';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';

@Component
export default class TimeoutTips extends Vue {
    public isShow: boolean = false;
    public point: number = 0;
    public grow: number = 0;
    public time: number = 15;
    public timer: any = null;
    public complete: boolean = false;

    @Watch('time', { immediate: true })
    timeChange(newVal: number, oldVal: number) {
        if (this.time == 0) {
            this.isShow = false;
            this.setViewGoodsTaskStatus();
            this.doTask();
        }
    }

    @Watch('isShow', {})
    isShowChange(newVal: number, oldVal: number) {
        if (this.isShow == false) {
            setTimeout(() => {
                this.timer = null;
            }, 1000);
        }
    }

    // 是否授权
    get wxAuth(): boolean {
        let wxAuth = true;
        // #ifdef MP-WEIXIN
        wxAuth = UserModule.wxAuth;
        // #endif
        return wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        let isPhone = true;
        // #ifdef MP-WEIXIN
        isPhone = UserModule.isPhone;
        // #endif
        return isPhone;
    }

    async getViewGoodsTaskStatus() {
        try {
            await this.getUserTaskInfo();
            const res = await viewGoodsTask({ type: 'check' });
            if (res && res.event === 'count_down') {
                await VIPModule.getTaskList().then((res: Array<any>) => {
                    const a = res.filter((item) => item.code == 'view_goods');
                    a.forEach((item2) => {
                        this.point = item2.point;
                        this.grow = item2.grow;
                    });
                });
                this.time = res.view_goods_limit;
                this.isShow = true;
                this.timer = setInterval(() => {
                    this.time--;
                }, 1000);
            } else {
                this.isShow = false;
            }
        } catch (error) {}
    }

    async setViewGoodsTaskStatus() {
        await Utils.getTaskInfo('mall/dreame/view_goods');
        await viewGoodsTask({ type: 'push' });
        this.clearTimer();
    }

    clearTimer() {
        this.timer && clearInterval(this.timer);
        this.timer = null;
        this.isShow = false;
        this.time = 15;
    }

    /* 用户进入页面判断用户是否可以执行浏览商城任意商品60秒，获得消费金奖励, 如果可以创建60s倒计时，结束时请求接口，并清除倒计时 */
    getUserTaskInfo() {
        getTaskInfo({ taskCode: 'mall/dreame/view_goods_money' }).then((res) => {
            this.complete = res.completed;
        });
    }

    async doTask() {
        await doGoldTask({ type: 'viewGoodsMoney' });
    }
}
</script>
<style lang="scss" scoped>
$fill-color-bg-linear-gradient: linear-gradient(101deg, #d2c3aa 0%, #94846b 100%);

.timeout-tips {
    position: fixed;
    right: 0;
    top: 210rpx;
    z-index: 4;

    .timeout {
        display: inline-block;
        height: 42rpx;
        padding: 0 18rpx;
        color: $text-color-white;
        font-size: 24rpx;
        background: $fill-color-bg-linear-gradient;
        border-bottom-left-radius: 16rpx;
        line-height: 42rpx;
    }

    .point-dreame {
        height: 110rpx;
        padding-top: 10rpx;
        font-size: 24rpx;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: $fill-color-primary-active;
        line-height: 34rpx;

        .point,
        .dreame {
            height: 34rpx;
            @include flex(row, center, center, none);
            animation: move;
            animation-duration: 5s;
            animation-fill-mode: forwards;

            .img {
                width: 24rpx;
                height: 22rpx;
            }
        }

        .dreame {
            margin-top: 12rpx;
            animation: move;
            animation-duration: 4s;
            animation-fill-mode: forwards;
        }

        @keyframes move {
            0% {
                transform: translateY(0rpx);
                opacity: 1;
            }

            100% {
                transform: translateY(66rpx);
                opacity: 0;
            }
        }
    }
}
</style>
