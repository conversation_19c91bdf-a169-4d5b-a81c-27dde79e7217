<template>
    <view>
        <APPSHARE :link="link" />
        <view
            v-if="!isLoad"
            class="group-goods"
            :style="{ paddingTop: `${statusBarHeight + 96}rpx` }"
            :class="{ bg: groupingList.length === 0 && !$isInApp() }"
        >
            <view
                class="header"
                :style="{ paddingTop: `${statusBarHeight}rpx` }"
                v-if="!(groupingList.length === 0 && !$isInApp())"
            >
                <img
                    class="back"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6883a563688974280011027.png"
                    alt=""
                    @click="back"
                />
                <view class="header-title">拼团购</view>
                <img
                    class="icon"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/924764-1753783146165.png"
                    alt=""
                />
                <view class="share" @click="shareActivity" v-if="user_id && groupingList.length > 0"></view>
            </view>

            <view class="count-down" v-if="countdownTime > 0 && groupingList.length > 0 && user_id">
                <img
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/063161-1753703823948.png"
                    alt=""
                />
                <u-count-down
                    :time="countdownTime"
                    format="DD:HH:mm:ss"
                    autoStart
                    millisecond
                    @finish="onFinish"
                    @change="onChange"
                >
                    <view class="time">
                        <view style="margin-right: 8rpx">您最近发起的拼团，将在</view>
                        <view class="time__item">
                            <view class="time__item_text">
                                {{ timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours }}
                            </view>
                            <view style="margin: 0rpx 8rpx">:</view>
                        </view>
                        <view class="time__item">
                            <view class="time__item_text">
                                {{ timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes }}
                            </view>
                            <view style="margin: 0rpx 8rpx">:</view>
                        </view>
                        <view class="time__item">
                            <view class="time__item_text">
                                {{ timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds }}
                            </view>
                        </view>
                        <view style="margin-left: 8rpx">后拼团失败，快去分享</view>
                    </view>
                </u-count-down>
            </view>
            <view class="group-goods-title-wrapper" v-if="groupingList.length > 0">
                <view class="group-goods-title">正在拼</view>
                <!-- <view class="share" @click="shareActivity" v-if="user_id"></view> -->
            </view>

            <!-- 正在拼列表 -->
            <view class="group-list" v-if="groupingList.length > 0">
                <scroll-view
                    class="group-list__scroll"
                    scroll-x
                    enable-flex
                    :show-scrollbar="false"
                    @scrolltolower="getGroupingList"
                >
                    <!-- <GroupingItem
                    v-for="(item, index) in groupingList"
                    :key="index"
                    :item="item"
                    countdownType="1"
                    @handleGroupingCilck="handleGroupingCilck"
                /> -->
                    <GroupingItemNew
                        v-for="(item, index) in groupingList"
                        :key="index"
                        :item="item"
                        @handleGroupingCilck="handleGroupingCilck"
                    />
                </scroll-view>
            </view>

            <view v-if="groupingList.length === 0 && !$isInApp()" class="group-goods-bg">
                <!-- <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/321277-1755511430219.png" alt=""> -->
            </view>

            <!-- <view class="group-task">
            <img
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/804966-1753768506164.png"
                alt=""
                class="icon"
            />
            <view class="text">限时做任务邀好友，得免拼卡</view>
            <view class="task">去免拼</view>
        </view> -->

            <view class="group-goods-container">
                <!-- 搜索栏 -->
                <!-- <view class="search-bar-wrapper">
                <view class="search-bar">
                    <view class="search-bar__input" @click="navigateToGroupSearch">
                        <view class="search-bar__input-name">请输入商品名称</view>
                        <img src="https://wpm-cdn.dreame.tech/images/202306/170933-1686362508396.png" alt="" />
                    </view>
                </view>
            </view> -->

                <view class="container-tip">
                    <img
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/419474-1753708663833.png"
                        alt=""
                    />
                    <view class="container-tip__title">不成团自动退款，超低价商品拼着买</view>
                    <view class="container-tip__rule" @click="ruleModal = true" v-if="group_rules">活动规则</view>
                </view>

                <view class="container-rank" v-if="leaderboardList.length > 0">
                    <view
                        v-for="(item, index) in leaderboardList"
                        :key="index"
                        class="rank-item"
                        @click="handleRankCilck(item)"
                    >
                        <img :src="item.cover_image" alt="" />
                        <view>{{ item.name }}</view>
                    </view>
                </view>

                <view class="goods-tab" :style="{ top: `${goodTabTop}rpx` }">
                    <view
                        v-for="(item, index) in goodTab"
                        :key="index"
                        class="goods-item"
                        :class="{ active: currentGoodTabIndex === index, isIconTag: !!item.icon }"
                        @click="handleGoodsItemClick(index, item)"
                    >
                        <img v-if="item.icon" :src="item.icon" style="height: 54rpx" />
                        <view v-else>
                            {{ item.label }}
                        </view>
                        <view
                            v-if="currentGoodTabIndex === index"
                            class="slider"
                            :style="{
                                width: sliderWidth + 'rpx',
                            }"
                        />
                    </view>
                </view>

                <!-- 团购列表 -->
                <view class="goods-list" v-if="groupList.length > 0">
                    <GroupGoodItem
                        v-for="(item, index) in groupList"
                        :key="index"
                        :item="item"
                        @handleGoodCilck="handleGoodCilck"
                    />
                </view>

                <!-- 空态 -->
                <view class="group-empty" v-if="groupList.length === 0 && groupingList.length === 0">
                    当前活动已结束，请关注其他活动~
                </view>
            </view>

            <!-- 是否注册追觅产品弹窗 -->
            <CustomModal
                :show="registerModal"
                width="640rpx"
                @close="registerModal = false"
                closeable
                title="温馨提示"
                confirmStyle="width: 100%"
                :confirmText="registerConfirmText"
                @confirm="navigateToGroupingSearch"
            >
                <view class="register-modal">
                    <view class="register-modal-title">
                        抱歉，您未注册追觅产品暂时不能发起团购。如您已经购买使用追觅产品可到"我的--注册有礼"添加产品后再发起团购；或扫描二维码添加心享官给您发送参与拼团邀请链接
                    </view>
                    <image
                        show-menu-by-longpress
                        src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202505/980202-1747194275454.png"
                        alt=""
                        class="register-modal-img"
                    />
                </view>
            </CustomModal>

            <!-- 活动结束弹窗 -->
            <CustomModal
                :show="groupEndModal"
                @close="handleReturnHome"
                closeable
                width="640rpx"
                contentStyle="color:#404040;font-weight:400;font-size: 32rpx;text-align: center;"
                content="当前的团购活动已结束，您可关注一下商城其他活动"
                confirmText="知道了"
                @confirm="handleReturnHome"
                title="温馨提示"
                confirmStyle="width: 100%"
            >
            </CustomModal>

            <!-- 活动规则 -->
            <u-popup
                :show="ruleModal"
                v-prevent-scroll="ruleModal"
                mode="center"
                :round="18"
                :safe-area-inset-bottom="false"
                catchtouchmove
            >
                <view class="rule-popup-content">
                    <view class="rule-popup-header">
                        <view class="rule-title">
                            <img
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a2c4ddd118a8560012341.png"
                                alt=""
                            />
                        </view>
                        <view class="rule-popup-close" @click="ruleModal = false">
                            <view class="close-icon"></view>
                        </view>
                    </view>
                    <view class="rule-container">
                        <view v-html="group_rules"></view>
                        <!-- <img
                            class="rule-img"
                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/682911-1754397083544.png"
                            alt=""
                        /> -->
                    </view>
                </view>
            </u-popup>

            <!-- #ifdef MP-WEIXIN -->
            <WxLogin @success="init"></WxLogin>
            <!-- #endif -->

            <CustomToast></CustomToast>
        </view>

        <view v-else style="padding: 32rpx 32rpx 32rpx; box-sizing: border-box; height: 100vh; overflow: hidden">
            <u-skeleton
                :animate="true"
                :title="false"
                :loading="true"
                rows="3"
                :rowsHeight="[60, 200, 800]"
                :rowsWidth="['100%', '100%', '100%']"
            ></u-skeleton>
        </view>
        <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'">
        </share-dialog>
        <EarnMoneySpendTimeoutTips
            v-if="$isInApp()"
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '320rpx' }"
            :task-code="taskCode"
            :fromPage="fromPage"
            :watchTime="15000"
        />
    </view>
</template>
<script lang="ts">
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import GroupGoodItem from '../components/GroupGoodItem/index.vue';
import GroupingItem from '../components/GroupingItem/index.vue';
import GroupingItemNew from '../components/GroupingItemNew/index.vue';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import {
    // getGroupActivityId,
    getGroupGoodList,
    getGroupingGoodList,
    GoodItem,
    GroupingItem as IGroupingItem,
    getMyGroupList,
    realTimeLeaderboard,
    groupPurchaseTags,
    // GoodItemStatusEnum,
} from '@/http/groupGoods';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { UserModule } from '@/store/modules/user';
import { CheckAppJump } from '@/common/decorators';
import { getGroupPurchaseRules } from '@/http/goods';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

@Component({
    components: { WxLogin, GroupGoodItem, GroupingItem, GroupingItemNew, ShareDialog, EarnMoneySpendTimeoutTips },
})
export default class GroupGoods extends Vue {
    $refs!: {
        EarnMoneySpendTimeoutTips;
    };

    public productInfo: any = {};
    public show: boolean = false;
    public shareId: any = '';
    public link: string = '';
    public title: string = '';
    public isLoad: boolean = true;
    public activityId: string = '';

    public page: number = 1;
    public groupPage: number = 1;
    public page_size: number = 30;
    public moreGrouping: boolean = true;
    public moreGroup: boolean = true;

    public groupList: GoodItem[] = [];
    public groupOption = [];
    public totalGroupList: GoodItem[][] = [];
    public groupingList: IGroupingItem[] = [];

    public registerModal: boolean = false;
    public registerConfirmText: string = '';
    public groupEndModal: boolean = false;
    public isEndAcitivity: boolean = false;
    public timeData: any = {};
    public countdownTime: number = 0; // 倒计时时间
    public leaderboardList = []; // 榜单商品列表
    public ruleModal = false; // 榜单商品列表
    public group_rules = ''; // 团购规则

    public goodTabTop = 0; // tab距离顶部的距离
    public currentGoodTabIndex = 0; // 当前tabindex
    public tagId = ''; // 当前tabindex
    public sliderX = 0; // 当前tabindex
    public sliderWidth = 0; // 当前tabindex
    public goodTab = [];
    public fromPage: string = '';

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get user_id(): any {
        let user_id: any = 0;
        // #ifdef MP-WEIXIN
        user_id = UserModule.userInfo.user_id;
        // #endif

        // #ifdef H5
        user_id = UserModule.user_id;
        // #endif
        return user_id;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    public taskCode: string = '';

    onLoad(options: any) {
        this.fromPage = options.from || '';
        this.taskCode = this.fromPage == 'goldCoins' ? 'viewGoodsGroupGold' : 'viewGoodsGroup';
        Utils.setPageTitle('一起拼，更便宜');
        this.activityId = `${process.env.VUE_APP_GROUP_PURCHASE_ACTIVITY_ID}`;
        this.link = options.link;
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_EXPOSURE,
        });
        this.init();
    }

    clearStatus() {
        this.page = 1;
        this.groupPage = 1;
        this.moreGrouping = true;
        this.moreGroup = true;
        this.groupList = [];
        this.groupOption = [];
        this.totalGroupList = [];
        this.groupingList = [];
    }

    async init() {
        try {
            this.clearStatus();

            // 获取活动 ID
            // const activeRes = await getGroupActivityId();

            await this.getGroupPurchaseTags();

            // 并行请求商品列表
            const promiseList = [
                this.getGroupList(),
                this.getGroupingList(),
                this.loadLeaderboard(),
                this.getThemeActivity(),
            ];
            if (this.user_id) {
                promiseList.push(this.setCountdownTime());
            }
            await Promise.all(promiseList);

            setTimeout(() => {
                // this.getGoodTabTop();
                this.updateSlider();
            }, 200);
        } catch (error) {
            this.groupList = [];
            this.groupingList = [];
        } finally {
            this.isLoad = false;
        }
    }

    onChange(e: any) {
        this.timeData = e;
    }

    onFinish() {
        this.isEndAcitivity = true;
    }

    async getGroupList() {
        if (!this.moreGroup) {
            return;
        }
        const res = await getGroupGoodList({
            activity_id: this.activityId,
            page: this.groupPage,
            page_size: this.page_size,
            tag_id: this.tagId,
        });
        this.title = res.title;
        this.totalGroupList = [...this.totalGroupList, ...res.goods];
        const optionRes = res.goods.map((v) => {
            return v.map((j) => {
                return {
                    groupo_rate: `${(Number(j.rate) * 10).toFixed(0)}折`,
                    label: `${j.max_members}人${(Number(j.rate) * 10).toFixed(0)}折`,
                };
            });
        });
        this.groupOption = [...this.groupOption, ...optionRes];
        const result = res.goods.map((v) => v.reduce((max, item) => (item.max_members > max.max_members ? item : max)));
        this.groupList = [...this.groupList, ...result];
        console.log('this.groupList', this.groupList);
        this.groupPage += 1;
        this.moreGroup = res.total_page >= this.groupPage;
    }

    async getGroupingList() {
        if (!this.moreGrouping) {
            return;
        }
        const res = await getGroupingGoodList({
            activity_id: this.activityId,
            page: this.page,
            page_size: this.page_size,
            type: 1,
        });
        this.groupingList = [...this.groupingList, ...res.list];
        // this.groupingList = [];
        this.page++;
        this.moreGrouping = res.list.length >= this.page_size;
    }

    async setCountdownTime() {
        const myListRes = await getMyGroupList({ type: 3, page: 1, page_size: 1 });
        const groupEndTime = myListRes.list?.[0]?.group_end_time;

        if (groupEndTime) {
            const now = Date.now();
            const end = groupEndTime * 1000;
            this.countdownTime = Math.max(end - now, 0);
        } else {
            this.countdownTime = 0;
        }
    }

    async loadLeaderboard() {
        const res = await realTimeLeaderboard({ activity_id: this.activityId });
        this.leaderboardList = res || [];
    }

    async getGroupPurchaseTags() {
        const res = await groupPurchaseTags();
        this.goodTab = res || [];
        this.tagId = this.goodTab[0].value || '';
    }

    navigateToGroupSearch() {
        uni.navigateTo({
            url: `/pagesB/groupGoodsSearch/index?activityId=${this.activityId}`,
        });
    }

    navigateToGroupingSearch() {
        this.registerModal = false;
        if (this.groupingList.length !== 0) {
            uni.navigateTo({
                url: `/pagesB/groupingGoodsSearch/index?activityId=${this.activityId}`,
            });
        }
    }

    navigateToMyGroup() {
        uni.navigateTo({
            url: '/pagesB/myGroupList/index',
        });
    }

    // public tooltipOpenIndex = -1;

    @CheckAppJump()
    handleGoodCilck(item) {
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&orderType=GROUP_PURCHASE`,
        });
        // if (this.tooltipOpenIndex > -1 && this.tooltipOpenIndex !== index) {
        //     this.groupList[this.tooltipOpenIndex].selected = false;
        // }
        // this.tooltipOpenIndex = index;
        // this.$set(this.groupList, index, { ...this.groupList[index], selected: true });
    }

    // handleGoodTooltipClose(index) {
    //     this.$set(this.groupList, index, { ...this.groupList[index], selected: false });
    // }

    // handleGoodTooltipCilck(item) {
    //     const hasJumped = Utils.jumpToDreameApp(this.link);
    //     if (hasJumped) {
    //         return; // 如果执行了 window.location.href，则不继续执行
    //     }
    //     this.handleGoodTooltipClose(0);
    //     uni.navigateTo({
    //         url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&orderType=GROUP_PURCHASE`,
    //     });
    // }

    @CheckAppJump()
    handleGroupingCilck(item: IGroupingItem) {
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&group_purchase_id=${item.group_purchase_id}&orderType=GROUP_PURCHASE`,
        });
    }

    onReachBottom() {
        this.getGroupList();
    }

    handleReturnHome() {
        this.groupEndModal = false;
        uni.reLaunch({ url: '/pages/index/index' });
    }

    back() {
        Utils.goBack();
    }

    // handleMainClick() {
    //     this.handleGoodTooltipClose(this.tooltipOpenIndex);
    // }

    @CheckAppJump()
    handleRankCilck(item: GoodItem) {
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}`,
        });
    }

    handleShare(type: 'image' | 'web') {
        this.show = false;
        Utils.cardShare(type)(
            {
                target: 'wechat,weixin_circle,qq,sina,image_template,download',
                link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/groupGoods/groupGoods`,
                jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/groupGoods/groupGoods`,
            },
            {
                content: {
                    url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/groupGoods/groupGoods`,
                    image:
                        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c46fb574783570012161.png' +
                        '?x-oss-process=image/resize,w_200',
                    title: '一起拼，更便宜！',
                    desc: '高端购物选追觅，限时拼团购优惠立享3折！',
                },
                extras: {
                    type: 'activity',
                    id: this.shareId,
                    goods: this.productInfo,
                    copyText: '一起拼，更便宜！高端购物选追觅，限时拼团优惠！',
                },
            },
        );
    }

    shareActivity() {
        const shareProductInfo = this.groupList[Math.floor(Math.random() * this.groupList.length)];
        console.log('shareProductInfo', shareProductInfo);
        this.shareId = shareProductInfo.gid;
        this.productInfo = {
            name: shareProductInfo.name,
            desc: '',
            image: shareProductInfo.cover_image,
            price: shareProductInfo.price,
            priceColor: '#FF1F0E',
            descColor: '#C59245',
            imageBg:
                'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202508/321277-1755511430219.png',
        };
        this.show = true;
        // const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/groupGoods/groupGoods`;
        // const data = {
        //     ...Constants.GEN_SHARE_LINK_TYPE,
        //     jumpLink: url,
        // };
        // Utils.newMessageChannel('SHARE', 'genShareLink', data, async (res) => {
        //     const params = {
        //         target: 'wechat,weixin_circle,qq,sina',
        //         type: 'web',
        //         content: {
        //             url:
        //                 `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesB/groupGoods/groupGoods?link=` +
        //                 res.data,
        //             share_image:
        //                 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c46fb574783570012161.png' +
        //                 '?x-oss-process=image/resize,w_200',
        //             share_title: `一起拼，更便宜`,
        //             share_desc: `高端购物选追觅，限时拼团优惠立享6折！`,
        //         },
        //     };
        //     const res1: any = await Utils.messageChannel('share', params);
        //     console.log('335655', res1, res);
        //     if (res1) {
        //         // this.showShareSucess = true;
        //     }
        // });
    }

    async getThemeActivity() {
        const res = await getGroupPurchaseRules({ activity_id: process.env.VUE_APP_GROUP_PURCHASE_ACTIVITY_ID });
        this.group_rules = res.details;
    }

    getGoodTabTop() {
        uni.createSelectorQuery()
            .in(this)
            .select('.header')
            .boundingClientRect((rect) => {
                this.goodTabTop = Utils.pxToRpx(rect.bottom);
            })
            .exec();
        // const el = document.querySelector('.header');
        // if (!el) return null;
        // this.goodTabTop = el.getBoundingClientRect().bottom * 2;
    }

    updateSlider() {
        uni.createSelectorQuery()
            .in(this)
            .selectAll('.goods-item')
            .boundingClientRect((rects) => {
                const rect = rects[this.currentGoodTabIndex];
                if (rect) {
                    this.sliderX = rect.left * 2;
                    this.sliderWidth = rect.width * 2;
                }
            })
            .exec();
    }

    handleGoodsItemClick(index, item) {
        this.currentGoodTabIndex = index;
        this.tagId = item.value;
        this.groupPage = 1;
        this.moreGroup = true;
        this.groupList = [];
        this.groupOption = [];
        this.totalGroupList = [];
        this.getGroupList();
        this.updateSlider();
    }

    onShow() {
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onUnload() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }
}
</script>
<style lang="scss" scoped>
@import './index.scss';

.group-goods {
    ::v-deep .group-good-item {
        margin: 0 16rpx 16rpx 16rpx;

        &:last-child {
            margin-bottom: 0;
        }
    }

    ::v-deep .register-modal {
        font-size: 32rpx;
        line-height: 44rpx;
        text-align: center;
        color: #404040;

        .register-modal-img {
            width: 244rpx;
            height: 244rpx;
            margin-top: 48rpx;
        }
    }
}
</style>
