<template>
    <view class="container" :style="{ 'padding-top': pagePaddingTop + 'rpx' }">
        <view class="header">
            <custom-bar2 title="追觅收银台" backTrigger="cashierBack"></custom-bar2>
        </view>
        <view class="main u-flex-col u-col-center" v-if="payStatus === 1 && isLoaded">
            <view class="payment">实付金额</view>
            <CustomAmount :totalPrice="orderDetail.real_price" unitSize="46" :cashierShow="true" />
            <view class="count-down u-flex">
                <view class="desc">剩余支付时间</view>
                <u-count-down
                    :time="orderDetail.remain_time * 1000"
                    format="HH:mm:ss"
                    @change="onChangeTime"
                    @finish="onFinishTime"
                >
                    <view class="time u-flex u-row-right">
                        <template v-if="timeData.days">
                            <view class="time__custom">
                                <text class="time__custom__item">{{ timeData.days }}</text>
                            </view>
                            <text class="time__doc">天</text>
                        </template>
                        <template v-if="timeData.hours || timeData.days">
                            <view class="time__custom">
                                <text class="time__custom__item">{{
                                    timeData.hours > 9 ? timeData.hours : '0' + timeData.hours
                                }}</text>
                            </view>
                            <text class="time__doc">:</text>
                        </template>
                        <view class="time__custom">
                            <text class="time__custom__item">{{
                                timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes
                            }}</text>
                        </view>
                        <text class="time__doc">:</text>
                        <view class="time__custom">
                            <text class="time__custom__item">{{
                                timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds
                            }}</text>
                        </view>
                    </view>
                </u-count-down>
            </view>
            <view class="pay-list">
                <!-- #ifdef MP-WEIXIN -->
                <view class="paytype">请选择支付方式</view>
                <view
                    class="pay-item u-flex u-row-between pay-item__wechat_mp"
                    :class="{ wechat_pay_mp: payType == 'wechat_pay_mp' }"
                    @click.stop="onSelectPaytype('wechat_pay_mp')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/wechat.png"></u-image>
                        <text class="name">微信支付</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        :src="
                            payType === 'wechat_pay_mp'
                                ? require('@/static/checkbox_active.png')
                                : require('@/static/check.png')
                        "
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view class="line" v-if="isJdPaySupport"></view>
                <view
                    class="pay-item u-flex u-row-between pay-item__jd_mp"
                    v-if="isJdPaySupport"
                    style="margin-bottom: 0"
                    :class="{ wechat_pay_mp: payType == 'jd_pay_mp' }"
                    @click.stop="onSelectPaytype('jd_pay_mp')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/jd.png"></u-image>
                        <text class="name">京东白条</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        :src="
                            payType === 'jd_pay_mp'
                                ? require('@/static/checkbox_active.png')
                                : require('@/static/check.png')
                        "
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view class="jdPay" v-if="isJdPaySupport">
                    <scroll-view
                        scroll-x
                        enable-flex
                        :scroll-into-view="choosedInterestTab"
                        scroll-with-animation
                        @scroll="onScroll"
                        :class="[showScroll ? 'showScroll' : '']"
                        class="tab-view u-flex scroll-container"
                    >
                        <block v-for="(item, index) in interestList" :key="index">
                            <view
                                :class="[
                                    'item',
                                    choosedInterestIndex === index && payType == 'jd_pay_mp' ? 'selected' : '',
                                ]"
                                :id="'tab-' + (index + 1)"
                                @click="chooseJdInterest(index)"
                            >
                                <view class="top">{{
                                    item.name === '不分期' ? item.name : `¥${item.per_installment} ${item.name}`
                                }}</view>
                                <view class="bottom"
                                    >{{ item.service_fee !== 0 ? `${item.service_fee}服务费` : '0服务费' }}
                                </view>
                                <view class="freeTip" v-if="item.isFree">{{ '免息' }}</view>
                            </view>
                        </block>
                    </scroll-view>
                </view>
                <!-- #endif -->

                <!-- #ifdef H5 -->
                <view
                    v-if="mallVersion == 0"
                    class="pay-item u-flex u-row-between"
                    @click.stop="onSelectPaytype('wechat_pay_h5')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/wechat.png"></u-image>
                        <text class="name">微信支付</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        src="https://wpm-cdn.dreame.tech/images/202306/079449-1687762466789.png"
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view
                    v-if="mallVersion > 0"
                    class="pay-item u-flex u-row-between pay-item__wechat_native"
                    :class="{ wechat_pay: payType == 'wechat_pay' }"
                    @click.stop="onSelectPaytype('wechat_pay')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/wechat.png"></u-image>
                        <text class="name">微信支付</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        :src="
                            payType === 'wechat_pay'
                                ? require('@/static/checkbox_active.png')
                                : require('@/static/check.png')
                        "
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view
                    v-if="mallVersion > 0"
                    class="pay-item u-flex u-row-between pay-item__alipay_native"
                    :class="{ alipay: payType == 'alipay' }"
                    @click.stop="onSelectPaytype('alipay')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/alipay.png"></u-image>
                        <text class="name">支付宝支付</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        :src="
                            payType === 'alipay'
                                ? require('@/static/checkbox_active.png')
                                : require('@/static/check.png')
                        "
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view
                    class="pay-item u-flex u-row-between pay-item__jd_native"
                    v-if="mallVersion > 5 && isJdPaySupport"
                    style="margin-bottom: 0"
                    :class="{ jd_pay_h5: payType == 'jd_pay_h5' }"
                    @click.stop="onSelectPaytype('jd_pay_h5')"
                >
                    <view class="u-flex">
                        <u-image width="48rpx" height="48rpx" mode="scaleToFill" src="@/static/jd.png"></u-image>
                        <text class="name">京东白条支付</text>
                    </view>
                    <u-image
                        width="40rpx"
                        height="40rpx"
                        :src="
                            payType === 'jd_pay_h5'
                                ? require('@/static/checkbox_active.png')
                                : require('@/static/check.png')
                        "
                        mode="aspectFit"
                    ></u-image>
                </view>
                <view class="jdPay" v-if="mallVersion > 5 && isJdPaySupport">
                    <scroll-view
                        scroll-x
                        enable-flex
                        :scroll-into-view="choosedInterestTab"
                        scroll-with-animation
                        @scroll="onScroll"
                        :class="[showScroll ? 'showScroll' : '']"
                        class="tab-view u-flex scroll-container"
                    >
                        <block v-for="(item, index) in interestList" :key="index">
                            <view
                                :class="[
                                    'item',
                                    choosedInterestIndex === index && payType == 'jd_pay_h5' ? 'selected' : '',
                                ]"
                                :id="'tab-' + (index + 1)"
                                @click="chooseJdInterest_h5(index)"
                            >
                                <view class="top">{{
                                    item.name === '不分期' ? item.name : `¥${item.per_installment} ${item.name}`
                                }}</view>
                                <view class="bottom"
                                    >{{ item.service_fee !== 0 ? `${item.service_fee}服务费` : '0服务费' }}
                                </view>
                                <view class="freeTip" v-if="item.isFree">{{ '免息' }}</view>
                            </view>
                        </block>
                    </scroll-view>
                </view>
                <!-- #endif -->
            </view>
            <view class="pay">
                <CustomButton
                    class="confirm"
                    hover-class="btn-hover"
                    text="确认并支付"
                    :style="{ opacity: forbidPay ? '0.5' : '' }"
                    @click="handleSubmit"
                ></CustomButton>
            </view>
        </view>
        <view class="main success u-flex-col u-col-center" v-if="isPaySuccess && payStatus === 3">
            <LazyImage
                v-if="disable_all_discount != 1"
                customStyle="margin-top: 15rpx; width: 508rpx; height: 508rpx;"
                src="https://wpm-cdn.dreame.tech/images/202306/768842-1687769965758.png"
            ></LazyImage>
            <LazyImage
                v-else
                customStyle="margin-top:130rpx; width:320rpx; height: 250rpx;"
                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c774c849bd5430011613.png"
            ></LazyImage>
            <view class="title">支付成功</view>
            <view
                class="desc"
                v-if="
                    orderDetail.user_order_type !== 3 && orderDetail.user_order_type !== 4 && disable_all_discount != 1
                "
            >
                确认收货后可获得{{ orderDetail.will_coin }}积分</view
            >
            <view class="desc" v-else-if="orderDetail.user_order_type !== 4 && disable_all_discount != 1">
                定金支付成功，付尾款可在我的订单-待付款中查看支付</view
            >
            <view class="btns1" v-if="disable_all_discount != 1">
                <CustomButton
                    class="btn"
                    hover-class="btn-hover"
                    text="查看订单"
                    @click="gotoOrderDetail"
                ></CustomButton>
                <CustomButton class="btn btn-comfirm" hover-class="btn-hover" text="继续逛逛" @click="gotoShop">
                </CustomButton>
            </view>
            <view class="btns1" v-else>
                <view
                    style="color: #404040; font-weight: 600; font-size: 30rpx"
                    class="btn btn-comfirm"
                    hover-class="btn-hover"
                    @click="gotoOrderDetail"
                >
                    查看订单
                </view>
                <view
                    style="margin-top: 32rpx; color: #8c6533; font-weight: 600; font-size: 30rpx"
                    class="btn"
                    hover-class="btn-hover"
                    @click="gotoShopSKill"
                >
                    再来一单
                </view>
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <u-gap height="32rpx" bgColor="#F4F4F4"></u-gap>
            <view class="footer u-flex u-col-center u-row-between">
                <view class="tips u-flex u-flex-col u-row-left u-col-top">
                    <text class="tips-top">关注追觅公众号</text>
                    <text>了解更多产品资讯以及使用指南</text>
                </view>
                <view class="btn" hover-class="btn-hover" @click="toWebPage">去关注</view>
            </view>
            <!-- #endif -->
        </view>
        <view class="main fail u-flex-col u-col-center" v-if="!isPaySuccess && payStatus === 3">
            <view class="img"></view>
            <view class="title">支付失败</view>
            <view class="desc">支付过程中出现错误，如需购买请继续支付</view>
            <view class="btns2">
                <CustomButton class="btn" hover-class="btn-hover" text="继续支付" @click="continuePay"></CustomButton>
                <CustomButton class="btn btn-comfirm" hover-class="btn-hover" text="查看订单" @click="gotoOrderDetail">
                </CustomButton>
            </view>
        </view>
        <CustomModal
            :show="isBackShow"
            width="616rpx"
            content="您的订单在剩余支付时间内未支付将被取消，请尽快完成支付。"
            confirmText="继续支付"
            :showCancelButton="true"
            cancelText="取消"
            contentStyle="text-align: center;font-size: 32rpx;font-weight: 400;"
            @confirm="handleComfirm"
            @cancel="handleCancel"
        >
        </CustomModal>
        <u-popup :show="payStatus === 2" v-prevent-scroll="payStatus === 2" mode="center" :round="24">
            <view class="loading-container u-flex-col u-col-center u-row-center">
                <image class="icon" src="https://wpm-cdn.dreame.tech/images/202307/64c35edfdb6cf8983507819.png"></image>
                <view class="text">正在获取付款信息...</view>
            </view>
        </u-popup>
        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
        <!-- 支付成功弹窗 -->
        <activity-popup
            :showPopup="oneYuanGouPopup"
            bgImg="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d8d633507c2170011273.png"
            @link="goToShop"
            @close="closePaySuccessPopup"
        ></activity-popup>
        <view v-if="qixiFestivalLog && false"> </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import CustomAmount from '@/components/CustomAmount/CustomAmount.vue';
import { orderPay, orderPayStatus, orderPayList, getOrderPayStatus, salesStoreDetail } from '@/http/order';
import { checkGroupOrderNo } from '@/http/goods';
import { waresOrderPay } from '@/http/pointsMall';
import { depositPay, tailOrderDetail } from '@/http/deposit';
import { homeOa } from '@/http/home';
import { UserModule } from '@/store/modules/user';
import { IOrderInfo, IWxPayParams } from '@/store/interface/order';
import Utils from '@/common/Utils';
import { OrderModule } from '@/store/modules/order';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import ActivityPopup from '@/pagesC/oneYuanFlashSale/components/activityPopup/activityPopup.vue';
interface payItem {
    pay_type: number;
    order_no: string;
    pay_source: number;
    payment_plan?: string;
}
let timer = null;
@Component({
    components: {
        CustomAmount,
        ActivityPopup,
    },
})
export default class Cashier extends Vue {
    public user_order_type: number | string = 1; // 订单类型
    public forbidPay: Boolean = false;
    private orderNo: string = ''; // 当前订单id
    public tail_order_no: string = ''; // 尾款订单id
    public fromPage: string = ''; // 来源页面 orderDetail 订单详情页 settlement 确认订单页 tailSettlement 尾款确认订单页
    public coin: number = 0; // 获取积分
    public need_pay: number = 0; // 是否需要支付
    public is_leader: string = ''; // 是否是团长
    public group_purchase_id: string = ''; // 拼团id
    public showScroll: boolean = false; // 是否显示滚动条
    public scrollTimeout: any = null; // 滚动条隐藏定时器
    public disable_all_discount: number = 0; // 是否禁用全场折扣
    public typeActivity: number = 0; // 活动类型
    public interestList: Array<any> = [
        {
            key: 'NO_INST_',
            name: '不分期',
            service_fee: 1.5,
            per_installment: '1',
        },
        {
            key: 'INST_3',
            name: '3期',
            service_fee: 2.5,
            per_installment: '3',
        },
        {
            key: 'INST_6',
            name: '6期',
            service_fee: 4.5,
            per_installment: '6',
        },
        {
            key: 'INST_12',
            name: '12期',
            service_fee: 8.5,
            per_installment: '12',
        },
    ];

    public choosedInterestIndex: number = 0; // 选择的分期
    public lastChoosedInterestIndex: number = 0; // 上一次选择的分期
    public choosedInterestTab: string = 'tab-0'; // 选择的分期tab
    public isJdPaySupport: boolean = false; // 是否支持京东支付
    public homeOaUrl: string = ''; // 公众号链接

    // #ifdef MP-WEIXIN
    public payType: string = 'wechat_pay_mp'; // 当前支付方式
    // #endif
    // #ifdef H5
    // @ts-ignore
    public payType: string = 'wechat_pay'; // 当前支付方式
    // #endif
    public isLoaded: Boolean = false; // 是否加载完
    public timeData: any = {
        minutes: 0,
        seconds: 0,
    }; // 自定义倒计时

    public activityType: string = ''; // 活动类型
    public isBackShow: boolean = false; // 返回提示
    public payStatus: number = 1; // 支付中状态 1：未开始支付，2：支付中，3：支付完成
    public isPaySuccess: boolean = false; // 是否支付成功
    public pollingTimeout: number = 0; // 已经轮询支付状态的时间
    public timeout: number = 1000; // 多少毫秒轮询一次
    public polling: boolean = false; // 是否在轮询
    public format: string = 'HH:mm:ss'; // 时间格式化
    public orderType: string = ''; // 订单类型
    public payTypeObj: Object = {
        wechat_pay_h5: Constants.WECHAT_H5,
        wechat_pay_mp: Constants.WECHAT_MP,
        wechat_pay: Constants.WECHAT_NATIVE,
        alipay: Constants.ALIPAY,
        jd_pay_mp: Constants.JDPAY_MP,
        jd_pay_h5: Constants.JDPAY_H5,
    }; // 支付类型

    public totalOutStyle: Object = {
        color: '#226DF2',
        'font-size': '64rpx',
        'line-height': '90rpx',
    }; // 金额样式

    public isPaySuccessPopup: boolean = true; // 支付成功弹窗
    public isOpenStore: boolean = false; // 是否开启小店
    public gid: string = ''; // 商品id
    public qixiIsLog: boolean = false; // 七夕活动日志
    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    get orderDetail(): IOrderInfo {
        return OrderModule.orderDetail;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get wxPayParams(): IWxPayParams {
        return OrderModule.wxPayParams;
    }

    get mallVersion(): number {
        return UserModule.mallVersion;
    }

    get oneYuanGouPopup(): boolean {
        const isPopup = this.orderType === 'ONE_YUAN_PURCHASE' && this.isPaySuccess && this.isPaySuccessPopup;
        if (isPopup) {
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_PAY_SUCCESS,
                id: Number(this.gid),
            });
        }
        return isPopup;
    }

    get qixiFestivalLog() {
        if (this.activityType === 'qixiFestival' && this.isPaySuccess && this.qixiIsLog === false) {
            this.qixiIsLog = true;
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_QIXI_FESTIVAL_ACTIVITY__ORDER,
                id: Number(this.gid),
                name: this.orderDetail.order_no,
            });
            return true;
        }
        return false;
    }

    async getOrderInfo() {
        await OrderModule.asyncGetOrderInfo({ order_no: this.orderNo, user_order_type: this.user_order_type });
        this.$nextTick(() => {
            this.isLoaded = true;
        });
    }

    goToShop() {
        this.isPaySuccessPopup = false;
        if (this.isOpenStore) {
            Utils.navigateTo('/pagesC/shopStore/index');
        } else {
            Utils.navigateTo('/pagesC/createShop/index');
        }
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_PAY_SUCCESS_LINK,
        });
    }

    closePaySuccessPopup() {
        this.isPaySuccessPopup = false;
    }

    get user_id(): any {
        return UserModule.userInfo.user_id;
    }

    async handleStoreDetail() {
        if (this.user_id) {
            const storeDetail = await salesStoreDetail({ store_id: this.user_id });
            // this.storeDetail = res;
            this.isOpenStore = storeDetail.status === '1';
        } else {
            this.isOpenStore = false;
        }
    }

    onSelectPaytype(type) {
        this.payType = type;
        if (this.payType === 'jd_pay_mp') {
            this.choosedInterestIndex = this.lastChoosedInterestIndex;
            this.choosedInterestTab = 'tab-' + (this.choosedInterestIndex + 1);
        } else {
            // 保留上次选择的分期 切换其他支付方式时置为0 便于再次点击时触发自动滚动
            this.lastChoosedInterestIndex = this.choosedInterestIndex;
            this.choosedInterestIndex = 0;
            this.choosedInterestTab = 'tab-0'; // 置为其他值，再次点击才会触发自动滚动
        }
    }

    chooseJdInterest(index) {
        // if (this.payType !== 'jd_pay_mp') return;
        this.payType = 'jd_pay_mp';
        this.choosedInterestIndex = index;
        this.choosedInterestTab = 'tab-' + (index + 1);
    }

    chooseJdInterest_h5(index) {
        // if (this.payType !== 'jd_pay_mp') return;
        this.payType = 'jd_pay_h5';
        this.choosedInterestIndex = index;
        this.choosedInterestTab = 'tab-' + (index + 1);
    }

    onFinishTime() {
        this.forbidPay = true;
        setTimeout(() => {
            this.gotoOrderDetail();
        }, 500);
    }

    // 自定义倒计时
    onChangeTime(e) {
        this.timeData = e;
    }

    handleComfirm() {
        this.isBackShow = false;
    }

    handleCancel() {
        if (this.disable_all_discount == 1) {
            this.gotoShop();
            return;
        }
        this.gotoOrderDetail();
    }

    // 跟新详情页的拼团id group_purchase_id 避免返回时页面显示错误
    async updateGoodsDetails_groupPurchase() {
        const pages = getCurrentPages();
        const prevPage: any = pages[pages.length - 2];
        const _pre_route: any = prevPage.route;
        if (_pre_route === 'pagesB/goodsDetail/goodsDetail') {
            prevPage.$vm.group_purchase_id = this.group_purchase_id;
        }
    }

    gotoShop() {
        // #ifdef H5
        if (uni.getStorageSync('from') === 'plugin') {
            uni.reLaunch({
                url: '/pages/shop/shop',
            });
        } else {
            Utils.messageChannel('navigation', { type: 'mall', path: 'home/h5' });
        }
        // #endif
        // #ifdef MP-WEIXIN
        uni.reLaunch({
            url: '/pages/index/index',
        });
        // #endif
    }

    gotoShopSKill() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                    .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ANOTHER_ORDER_CLICK,
            },
            true,
        );
        if (this.typeActivity == 1) {
            uni.navigateTo({
                url: '/pagesC/offPurchase/offPurchase',
            });
        } else if (this.typeActivity == 2) {
            uni.navigateTo({
                url: '/pagesC/eightDiscount/eightDiscount',
            });
        } else if (this.typeActivity == 6) {
            // 六折购
            uni.navigateTo({
                url: '/pagesC/sixDiscount/sixDiscount',
            });
        } else {
            uni.navigateTo({
                url: '/pagesC/oneYuanFlashSale/oneYuanFlashSale',
            });
        }
    }

    gotoOrderDetail() {
        let user_order_type = this.user_order_type;
        if (user_order_type === '3') {
            if (this.isPaySuccess) {
                user_order_type = '2';
                this.orderNo = this.tail_order_no;
            }
        }
        if (this.is_leader === 'true') {
            this.updateGoodsDetails_groupPurchase();
        }
        uni.redirectTo({
            url: `/pagesA/orderDetail/orderDetail?order_no=${this.orderNo}&user_order_type=${user_order_type}`,
        });
    }

    continuePay() {
        this.payStatus = 1;
        this.isPaySuccess = false;
        this.polling = false;
    }

    async getTailOrderDetail() {
        // 定金订单支付成功后特殊处理 会转成尾款订单
        try {
            const res = await tailOrderDetail({ order_no: this.orderNo });
            this.tail_order_no = res.order_no;
        } catch (e) {
            console.error('getTailOrderDetail e', e);
        }
    }

    // 京东支付
    async pay_jd_h5(params) {
        const { prepay_id } = await orderPay(params);
        if (!prepay_id) {
            Utils.Toast('跳转京东支付失败');
            return;
        }
        // 跳转 京东白条信用购 小程序
        wx.navigateToMiniProgram({
            appId: 'wx9c31a06b4a0f91bc', //  小程序appid
            path: 'pages/jdbtPay/jdbtPay?url=' + encodeURIComponent(prepay_id),
            success: (result) => {
                this.polling = true; // 触发轮询
                console.log('result', result);
            },
            fail: (err) => {
                console.log('err', err);
            },
        });
    }

    async handleSubmit() {
        const params: payItem = {
            pay_type: this.payTypeObj[this.payType].code,
            order_no: this.orderNo,
            pay_source: 1,
        };
        // #ifdef MP-WEIXIN
        // 京东支付
        if (this.payType === 'jd_pay_mp') {
            params.payment_plan = this.interestList[this.choosedInterestIndex].key;
            this.pay_jd(params);
            return;
        }
        // #endif
        // #ifdef H5
        // 京东支付
        params.payment_plan = this.interestList[this.choosedInterestIndex].key;
        // #endif
        let result: any = {};
        if (this.user_order_type == 3) {
            // 定金订单
            params.pay_source = 3;
            result = await depositPay(params);
        } else if (this.user_order_type == 4) {
            // 积分商城订单
            params.pay_source = 4;
            result = await waresOrderPay(params);
        } else {
            result = await orderPay(params);
        }
        // #ifdef H5
        this.pay_h5(result);
        // #endif
        // #ifdef MP-WEIXIN
        this.pay_mp(result);
        // #endif
    }

    // app h5支付
    async pay_h5(result) {
        this.polling = true;
        if (this.mallVersion == 0) {
            Utils.messageChannel('openWechatPage', result.h5_url);
        } else {
            const params = {
                type: this.payType,
                info: {},
            };
            if (this.payType === 'wechat_pay') {
                params.info = result;
            } else if (this.payType === 'alipay') {
                params.info = {
                    orderInfo: result,
                    env: '1',
                };
            } else if (this.payType === 'jd_pay_h5') {
                params.info = {
                    orderInfo: result,
                    env: '1',
                };
            }
            const res: any = await Utils.nativePay(params);
            if (res.code === -101) {
                Utils.Toast(`请安装客户端`);
            } else {
                this.payStatus = 3;
                if (res.code === 1) {
                    this.isPaySuccess = true;
                    this.onPaySuccess(result);
                } else {
                    this.isPaySuccess = false;
                    this.onPayError(result);
                    this.polling = false;
                }
            }
        }
    }

    // 微信小程序支付
    pay_mp(wxParams: IWxPayParams) {
        if (!wxParams.paySign) return;
        const self = this;
        const params: any = { ...wxParams };
        delete params.order_no;
        uni.requestPayment({
            ...params,
            success: () => {
                Utils.reportEvent('payment', {
                    payment_status: '成功',
                });
                this.payStatus = 3;
                this.isPaySuccess = true;
                self.onPaySuccess(wxParams);
            },
            fail: () => {
                Utils.reportEvent('payment', {
                    payment_status: '失败',
                });
                this.payStatus = 3;
                this.isPaySuccess = false;
                self.onPayError(wxParams);
            },
        });
    }

    // 京东支付
    async pay_jd(params) {
        const { prepay_id } = await orderPay(params);
        if (!prepay_id) {
            Utils.Toast('跳转京东支付失败');
            return;
        }
        // 跳转 京东白条信用购 小程序
        wx.navigateToMiniProgram({
            appId: 'wx9c31a06b4a0f91bc', //  小程序appid
            path: 'pages/jdbtPay/jdbtPay?url=' + encodeURIComponent(prepay_id),
            success: (result) => {
                this.polling = true; // 触发轮询
                console.log('result', result);
            },
            fail: (err) => {
                console.log('err', err);
            },
        });
    }

    async onPaySuccess(wxParams: IWxPayParams) {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ORDER_TYPE_PAY,
                name: this.orderType,
            },
            true,
        );

        // if (wxParams && wxParams.order_no) {
        if (this.group_purchase_id) {
            // 是团长跳转拼团进行中
            // uni.$once('purchaseGroupBack', () => {
            //     uni.redirectTo({
            //         url: `/pagesA/orderDetail/orderDetail?order_no=${this.orderNo}&user_order_type=${this.user_order_type}`,
            //     });
            // });
            // 查询该订单是否插入到拼团中
            const promiseMaker = () =>
                checkGroupOrderNo({ order_no: this.orderNo }).then((res) => {
                    return res.status;
                });
            Utils.promiseSequence(promiseMaker).then((status) => {
                if (status) {
                    uni.redirectTo({
                        url: `/pagesB/inGroupPurchase/indexNew?group_purchase_id=${this.group_purchase_id}&backUrl=/pagesB/groupGoods/groupGoods&order_no=${this.orderNo}&user_order_type=${this.user_order_type}&pay=1`,
                    });
                }
            });
        } else {
            this.user_order_type === '3' && this.getTailOrderDetail();
        }
        // }
    }

    async onPayError(wxParams: IWxPayParams) {
        if (wxParams && wxParams.order_no) {
            // 暂无处理
        }
    }

    async getHomeOa() {
        try {
            const { art = '' } = await homeOa();
            this.homeOaUrl = art;
        } catch (e) {
            console.error('getHomeOa e=', e);
        }
    }

    // 跳转公众号
    toWebPage() {
        uni.navigateTo({
            url: `/pages/webView/webView?web_url=${encodeURIComponent(this.homeOaUrl)}`,
        });
    }

    onLoad(options) {
        console.log('cashierOptions', options);
        this.typeActivity = options.typeActivity || 0;
        this.user_order_type = options.user_order_type || '1';
        this.orderNo = options.order_no || '';
        this.coin = options.coin || 0;
        this.need_pay = options.need_pay || 0;
        this.is_leader = options.is_leader || '';
        this.group_purchase_id = options.group_purchase_id || '';
        this.fromPage = options.fromPage || '';
        this.disable_all_discount = options.disable_all_discount || 0;
        this.pollingTimeout = 0;
        this.orderType = options.orderType || 'COMMON';
        this.gid = options.gid || '';
        this.activityType = options.activityType || '';
        uni.$on('cashierBack', () => {
            if (this.payStatus === 1) {
                this.isBackShow = true;
            } else if (this.disable_all_discount == 1) {
                this.gotoShop();
            } else if (this.payStatus === 3) {
                this.gotoOrderDetail();
            }
        });
        // 是否支持京东支付 积分商城 定金订单 先试后买 不支持京东支付
        if (this.user_order_type == 4 || this.user_order_type == 3 || this.user_order_type == 6) {
            this.isJdPaySupport = false;
        } else {
            try {
                orderPayList(this.orderNo).then((res) => {
                    console.log('orderPayList', res);
                    this.isJdPaySupport = !!res.pay_list[1];
                    this.interestList = res.pay_list[1].interests.map((item, index) => {
                        return {
                            key: item.key,
                            name:
                                item.name === '不分期'
                                    ? item.name
                                    : item.name.includes('免息')
                                    ? item.name.replace('免息', '')
                                    : item.name.includes('分期')
                                    ? item.name.replace('分期', '')
                                    : item.name,
                            service_fee: item.service_fee,
                            isFree: (item.key as string).includes('FREE'),
                            per_installment: item.per_installment,
                        };
                    });
                });
            } catch (e) {
                console.error('orderPayList e', e);
            }
        }

        // #ifdef H5
        if (this.mallVersion == 0) {
            this.payType = 'wechat_pay_h5';
            const win: any = window;
            win.onShow = () => {
                this.getOrderInfo();
                // 支付中并且未开始计时的时候开始轮询支付状态

                if (this.polling) {
                    this.payStatus = 2;
                    this.pollPayStatus();
                }
            };
        }
        // #endif
        this.getHomeOa();
    }

    // 轮询支付状态
    async getPayStatus() {
        const res = await orderPayStatus(this.orderNo);
        if (res.status === 1) {
            timer && clearTimeout(timer);
            this.isPaySuccess = true;
            this.payStatus = 3;
            this.user_order_type === '3' && this.getTailOrderDetail();
        } else {
            this.isPaySuccess = false;
            this.payStatus = 2;
        }
    }

    pollPayStatus() {
        if (this.payStatus === 2 && this.pollingTimeout < Constants.POLLING_TIMEOUT) {
            this.getPayStatus();
            timer = setTimeout(() => {
                this.pollingTimeout += this.timeout;
                this.pollPayStatus();
            }, this.timeout);
        } else {
            this.payStatus = 3;
            this.pollingTimeout = 0;
            this.polling = false;
        }
    }

    onUnload() {
        uni.$off('cashierBack');
        const win: any = window;
        win.onShow = null;
    }

    onBackPress() {
        if (this.payStatus === 1) {
            this.isBackShow = true;
        } else if (this.payStatus === 3) {
            this.gotoOrderDetail();
        }
        return true;
    }

    onShow() {
        this.getOrderInfo();

        // #ifdef H5
        if (this.mallVersion === 0 && this.polling) {
            this.payStatus = 2;
            this.pollPayStatus();
        }

        if (this.payType === 'jd_pay_h5' && this.polling) {
            this.payStatus = 2;
            const promiseMaker = () =>
                getOrderPayStatus({
                    order_no: this.orderNo,
                    order_type:
                        this.user_order_type == 1 || this.user_order_type == 2
                            ? 1
                            : this.user_order_type == 3
                            ? 2
                            : null,
                }).then((res) => {
                    if (this.polling == false) return true;
                    const pay_status = res.pay_status;
                    if (pay_status == 1) {
                        // 已支付 / 已取消
                        this.isPaySuccess = pay_status == 1;
                        this.payStatus = 3;
                        pay_status == 1 && this.user_order_type === '3' && this.getTailOrderDetail();
                        return true;
                    } else if (pay_status == 2) {
                        // 未支付
                        this.pollingTimeout += this.timeout;
                        if (this.pollingTimeout < Constants.POLLING_TIMEOUT) {
                            // 继续
                            return false;
                        } else {
                            // 超时
                            this.isPaySuccess = false;
                            this.payStatus = 3;
                            this.pollingTimeout = 0;
                            this.polling = false;
                            return true;
                        }
                    }
                });
            // 轮询
            Utils.promiseSequence(promiseMaker, { immediate: true, timeout: this.timeout }).then((status) => {
                if (status) {
                    console.log('status', status);
                }
            });
        }
        // #endif
        // #ifdef MP-WEIXIN
        if (this.payType === 'jd_pay_mp' && this.polling) {
            this.startPolling();
        }
        // #endif
        if (Utils.isInApp()) {
            this.handleStoreDetail();
        }
    }

    // 通用轮询逻辑
    startPolling() {
        this.payStatus = 2;
        const promiseMaker = () =>
            // 1 ：普通订单、尾款订单 2：定金订单
            getOrderPayStatus({
                order_no: this.orderNo,
                order_type:
                    this.user_order_type == 1 || this.user_order_type == 2 ? 1 : this.user_order_type == 3 ? 2 : null,
            }).then((res) => {
                const pay_status = res.pay_status;
                if (pay_status == 1) {
                    // 已支付 / 已取消
                    this.isPaySuccess = pay_status == 1;
                    this.payStatus = 3;
                    pay_status == 1 && this.user_order_type === '3' && this.getTailOrderDetail();
                    return true;
                } else if (pay_status == 2) {
                    // 未支付
                    this.pollingTimeout += this.timeout;
                    if (this.pollingTimeout < Constants.POLLING_TIMEOUT) {
                        // 继续
                        return false;
                    } else {
                        // 超时
                        this.isPaySuccess = false;
                        this.payStatus = 3;
                        this.pollingTimeout = 0;
                        this.polling = false;
                        return true;
                    }
                }
            });
        // 轮询
        Utils.promiseSequence(promiseMaker, { immediate: true, timeout: this.timeout }).then((status) => {
            if (status) {
                console.log('status', status);
            }
        });
    }

    beforeDestroy() {
        timer && clearTimeout(timer);
    }

    onScroll(e) {
        this.showScroll = true;
        clearTimeout(this.scrollTimeout);
        this.scrollTimeout = setTimeout(() => {
            this.showScroll = false;
        }, 2000); // 停止滚动2秒后隐藏滚动条
    }
}
</script>
<style lang="scss" scoped>
@import './cashier.scss';

.scroll-container {
    ::v-deep .uni-scroll-view {
        &::-webkit-scrollbar {
            display: block;
            width: 8rpx !important;
            height: 0rpx !important;
            opacity: 0; // 不生效
            transition: height 2s;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 10rpx !important;
        }

        &::-webkit-scrollbar-thumb {
            background: #888 !important;
            border-radius: 10rpx !important;
        }
    }
}

.showScroll {
    ::v-deep .uni-scroll-view {
        &::-webkit-scrollbar {
            height: 4rpx !important;
            transition: height 2s;
        }
    }
}
</style>
