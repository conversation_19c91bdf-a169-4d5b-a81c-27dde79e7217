<template>
    <view class="pointProducts" :style="pointProductStyle" @click="toGoodsDetail(pointProductsInfo)">
        <view class="img">
            <view :style="pointProductsInfo.stock <= 0 ? 'filter: opacity(0.5)' : 'filter: opacity(1)'">
                <lazy-image
                    :customStyle="`width: 100%; height: ${imgHeight}rpx; border-radius: 16rpx 16rpx 0 0`"
                    :lazyLoad="true"
                    :src="pointProductsInfo.cover_image"
                    mode="scaleToFill"
                ></lazy-image>
            </view>
            <!-- <view class="levelIllustrate" :style="levelIllustrateStyle" v-if="pointProductsInfo.levels_tag != ''"
                >{{ pointProductsInfo.levels_tag }}
            </view>
            <view class="listingTime" v-if="pointProductsInfo.online_time_tag != ''"
                >{{ pointProductsInfo.online_time_tag }}
            </view>
            <image
                v-if="pointProductsInfo.stock <= 0 && pointProductsInfo.online_time_tag == ''"
                class="soldOut"
                src="https://wpm-cdn.dreame.tech/images/202308/64f006dc11d1e0735195217.png"
            ></image> -->
        </view>

        <view class="content" :style="{ height: `calc(100% - ${imgHeight}rpx)` }">
            <view class="title u-line-2">
                <image
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e2a0b2dff71880011529.png"
                    style="vertical-align: middle;width: 52rpx; height: 25rpx; align"
                ></image>
                <!-- <text class="sign uni-genaral-tag" v-if="pointProductsInfo.label_tag != ''">{{
                    pointProductsInfo.label_tag
                }}</text> -->
                <text class="titleText">{{ pointProductsInfo.name }}</text>
            </view>
            <!-- <view v-if="pointProductsInfo.introduce != ''" class="sellingPoint u-line-1"
                >{{ pointProductsInfo.introduce }}
            </view> -->
            <view class="box-bottom">
                <view class="price-point">
                    <view class="memberWrap">
                        <text class="unit">￥</text>
                         <text class="price">{{ (pointProductsInfo.price - ( Number(pointProductsInfo.can_use_coin) / Number(pointProductsInfo.deduction_rate))- Number(pointProductsInfo.subsidy_price)).toFixed(2) }}</text>
                          <text class="flag">会员价</text>

                    </view>
                </view>
                <view class="pointWrap" >
                    <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f6f413b9642440010926.png" mode=""
						class="pointImage" ></image >
                    <view class="point_count"  v-if="pointProductsInfo.can_use_coin > 100">
                       积分抵扣 ￥{{ (Number(pointProductsInfo.can_use_coin) / Number(pointProductsInfo.deduction_rate)).toFixed(2) }}
                    </view>
                    <view class="point_count"  v-else>
                       无积分可抵扣
                    </view>
                </view>
                <!-- <view class="to_buy">去兑换</view> -->
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator';
// import Utils from '@/common/Utils';

@Component({
    components: {},
})
export default class pointProducts extends Vue {
    // 商品样式
    @Prop({ type: String, default: '' })
    readonly pointProductStyle;

    // 图片高度
    @Prop({ type: Number, default: 400 })
    readonly imgHeight;

    // 等级说明样式
    @Prop({ type: String, default: '' })
    readonly levelIllustrateStyle;

    // 积分和价格总字号
    @Prop({ type: Number, default: 28 })
    readonly pricePointFontSize;

    // +、¥字号
    @Prop({ type: Number, default: 24 })
    readonly addFontSize;

    // 数据
    @Prop({
        type: Object,
        default: () => ({
            cover_image: '', // 图片链接
            levels_tag: '', // 等级说明
            label_tag: '', // 标签
            online_time_tag: '', // 上架时间
            name: '', // 标题文本
            introduce: '', // 卖点文本
            point: 0, // 积分
            price: 0, // 优惠价格
            mprice: 0, // 原始价格
        }),
    })
    readonly pointProductsInfo;

    toGoodsDetail(item) {
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&type=` + 'Mall',
        });
        // #ifdef H5
        // Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&type=` + 'Mall');
        // #endif
        // #ifdef MP-WEIXIN
        uni.navigateTo({
            url: `/pagesB/goodsDetail/goodsDetail?gid=${item.gid}&type=` + 'Mall',
        });
        // #endif
    }
}
</script>

<style lang="scss" scoped>
.pointProducts {
    background: $fill-color-bg-white;
    margin-top: 22rpx;
    width: calc(50vw - 16rpx - 8rpx);
    height: 602rpx;
    border-radius: 16rpx;
    flex-shrink: 0;
    overflow: hidden;

    .img {
        position: relative;

        .levelIllustrate {
            width: 100%;
            height: 42rpx;
            position: absolute;
            bottom: 0;
            left: 0;
            text-align: center;
            background: linear-gradient(97deg, #f3eacb 0%, #b59a6e 99%);
            border-radius: 16rpx 16rpx 0 0;
            font-size: 24rpx;
            color: $brand-color-btn-text;
            line-height: 42rpx;
        }

        .listingTime {
            width: 100%;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            text-align: center;
            background: linear-gradient(270deg, rgba(0, 0, 0, 0) 4%, rgba(0, 0, 0, 0.42) 47%, rgba(0, 0, 0, 0) 92%);
            font-size: 24rpx;
            font-weight: 500;
            color: $text-color-white;
            line-height: 54rpx;
        }

        .soldOut {
            width: 124rpx;
            height: 124rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .content {
        padding: 15rpx 15rpx 0;
        position: relative;
        line-height: 1.5;

        .title {
            font-size: 28rpx;
            font-weight: normal;
            color: $text-color-primary;
            word-break: break-all;
            display: flex;
            .sign {
                // padding: 2rpx 12rpx;
                // margin-right: 8rpx;
                // border-top-left-radius: 20rpx;
                // border-top-right-radius: 20rpx;
                // border-bottom-right-radius: 20rpx;
                // text-align: center;
                // font-size: 22rpx;
                // color: $text-color-white;
                // background: linear-gradient(222deg, #dc1e1e 0%, #e25252 100%);
                // vertical-align: middle;
                margin-right: 16rpx;
                transform: translateY(-2rpx);
            }

            .titleText {
                word-break: break-all;
                margin-left: 16rpx;
            }
        }

        .sellingPoint {
            margin-top: 4rpx;
            font-size: 22rpx;
            color: $text-color-secondary;
            line-height: 30rpx;
        }

        .box-bottom {
            position: absolute;
            width: 100%;
            // flex: 1;
            bottom: 0;
            padding-bottom: 24rpx;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: flex-start;
            .price-point{
                  display: flex;
                align-items: center;
                justify-content: flex-start;
                width: 100%;
                font-size: 28rpx;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0px;

                font-variation-settings: "opsz" auto;
                color: #F3558C;
            }
            .pointWrap{
                display: flex;
                align-items: center;
                justify-content: flex-start;
                width: 100%;
                height: 32rpx;
                 .pointImage{
                width: 32rpx;
                height: 32rpx;
            }
            }

            .memberWrap{
                // font-size: 28rpx;
                font-weight: 500;
                color: #F3558C;
                width: 100%;
                 display: flex;
                align-items: baseline;
                justify-content: flex-start;
                margin-bottom: 10rpx;
                .unit{
                    font-size: 26rpx;
                }
                .price{
                     font-size: 28rpx;
                     margin-right: 8rpx;
                }
                .flag{
                     font-size: 24rpx;

                }
            }

            .price-point {
                font-weight: 500;
                color: #f3558c;
                flex-wrap: wrap;

                .point-text {
                    font-size: 27rpx;
                }

                .add {
                    margin: 0 4rpx;
                }
            }

            .mprice {
                text-decoration: line-through;
                font-size: 20rpx;
                color: $text-color-disable;
                line-height: 26rpx;
            }
        }
    }
}
.to_buy {
    width: 118rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background-color: #f3558c;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 24rpx;
    color: $text-color-white;
}
.point_count {
    font-size: 20rpx;
    font-weight: 500;
    color: #F3558C;
}
.cost {
    font-size: 24rpx;
    font-weight: normal;
    color: #F3558C;
}
</style>
