.container {
    min-height: 100vh;
    height: 100%;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a668d79ba84990015327.png')
        no-repeat center center;
    background-size: 100% 100%;

    .header {
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
        .header-box {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            /* 浅黄色渐变背景，参考图二 UI */
            border-radius: 24rpx;
            padding: 0rpx 20rpx 40rpx 43rpx;

            .point-section {
                .point {
                    height: 48rpx;
                    display: flex;
                    align-items: center;
                    font-family: Alibaba PuHuiTi 3, Alibaba PuHuiTi 30;
                    font-weight: normal;
                    font-size: 28rpx;
                    color: #96865f;
                    line-height: 48rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    .point-icon {
                        width: 40rpx;
                        height: 45rpx;
                        margin-right: 12rpx;
                        margin-top: 8rpx;
                    }
                    .detail-arrow {
                        width: 20rpx;
                        height: 20rpx;
                        margin-left: 8rpx;
                    }
                }

                .pointContent {
                    @include flex(column, flex-start, flex-start, none);
                    margin-left: 0;
                    margin-top: 22rpx;

                    .pointAmount {
                        @include flex(row, flex-start, baseline, none);

                        .amountNum {
                            font-size: 64rpx;
                            font-family: DouyinSans;
                            line-height: 64rpx;
                            font-weight: 600;
                            color: #121212;
                        }

                        .amountUnit {
                            font-size: 29rpx;
                            line-height: 38rpx;
                            color: #555555;
                            margin-left: 10rpx;
                        }
                    }

                    .pointDetail {
                        @include flex(row, center, center, none);
                        margin-top: 12rpx;
                        cursor: pointer;

                        .detail-text {
                            font-size: 26rpx;
                            color: #fe7f3c;
                            font-weight: 400;
                        }

                        .detail-arrow {
                            width: 20rpx;
                            height: 20rpx;
                            margin-left: 8rpx;
                        }
                    }

                    .pointNumOne {
                        font-size: 29rpx;
                        line-height: 38rpx;
                        font-weight: 600;
                        color: #fe7f3c;
                    }

                    .pointNumTwo {
                        @include flex(row, center, center, none);
                        font-size: 29rpx;
                        font-weight: 500;
                        color: rgba(#404040, 0.5);

                        .pointNum_image {
                            width: 46rpx;
                            height: 46rpx;
                            margin-right: 12rpx;
                        }
                    }

                    .pointContentRight {
                        @include flex(row, center, center, none);
                        margin-left: 12rpx;
                        margin-bottom: 12rpx;

                        .pointIntro {
                            font-size: 24rpx;
                            color: $text-color-secondary;
                        }

                        .arrow-right {
                            width: 32rpx;
                            height: 32rpx;
                        }
                    }
                }
            }

            .header-points-bottom {
                margin: 0;
                background: transparent;
                position: relative;
                height: auto;
                flex: 1;
                margin-left: 16rpx;
                @include flex(column, flex-start, center, none);

                .points-box-bottom-left {
                    width: 100%;
                    display: flex;
                    flex-wrap: nowrap;
                    overflow-x: scroll;

                    body::-webkit-scrollbar {
                        display: none;
                    }

                    .task2:nth-child(1) {
                        margin-left: 32rpx;
                    }

                    .task2 {
                        margin: 34rpx 0 0 38rpx;
                        flex-shrink: 0;
                    }
                }

                .points-box-bottom-right {
                    width: 100% !important;
                    justify-content: center !important;
                    margin: 24rpx 0 0 0 !important;
                }
            }
        }

        .header-title {
            position: absolute;
            left: 0;
            right: 0;
            top: 0rpx;
            text-align: center;
            font-size: 44rpx;
            font-weight: 600;
            color: #121212;
            z-index: 1;
        }
    }
    .getCoin {
        width: 688rpx;
        height: 171rpx;
        border-radius: 15rpx;
        opacity: 0.9;
        background: #fff9e8;
        box-shadow: 0px 0px 16px 0px rgba(255, 255, 255, 0.5);
        border-radius: 24rpx;
        padding: 0 29rpx;
        margin: 0 32rpx;
        @include flex(row, space-between, center, none);
        .getCoin-content {
            display: flex;
            align-items: center;
            gap: 32rpx;
            .getCoin-item {
                display: flex;
                align-items: center;
                flex-direction: column;
                height: 110rpx;
                width: 77rpx;
                .getCoin-item-bg {
                    width: 86rpx;
                    flex-shrink: 0;
                    @include flex(row, center, center, none);
                    height: 100rpx;
                    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68745ee560bbf3960011861.png');
                    background-size: 100% 100%;
                    position:relative;
                    background-repeat: no-repeat;
                    background-position: center;
                    .getCoin-item-bg-text {
                        font-family: MiSans;
                        font-size: 23rpx;
                        font-weight: 600;
                        background: linear-gradient(180deg, #fffde5 0%, #fef78c 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        text-fill-color: transparent;
                        position:absolute;
                        top:25rpx;
                    }
                }
                .getCoin-item-text {
                    font-size: 18rpx;
                    margin-top: -10rpx;
                    color: #F68D13;
                    font-weight: 500;
                    text-align: center;
                }
            }
        }
        .getCoin-btn {
            width: 146rpx;
            height: 62rpx;
            border-radius: 100px;
            opacity: 1;
            font-size: 26rpx;
            color: #fff;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(113deg, #ff8d50 7%, #f9580d 52%, #ff2e20 95%);
            box-shadow: 0px 5px 7px -2px rgba(254, 134, 71, 0.51);
        }
    }

    .checkInContainer {
        min-height: 348rpx;
        bottom:0;
        background: $fill-color-bg-white;
        border-radius: 24rpx;
        padding: 32rpx 28rpx 0rpx;
        z-index: 1;
        .title{
            font-size:36rpx;
            color: #121212;
            font-weight:600;
       
        }
        .alreadyDays {
            height: 92rpx;
            font-size: 32rpx;
            border-radius: 16rpx;
            font-family: PingFang SC-中黑体, PingFang SC;
            font-weight: 600;
            color: $text-color-primary;
            line-height: 42rpx;
            margin-top:26rpx;
            padding: 28rpx 0 22rpx 24rpx;
            background: linear-gradient(90deg, #FFFAED 0%, rgba(255, 250, 237, 0) 100%);

            .coin-red {
                font-family: DouyinSans;
                color: #C2A271;
                font-size:23rpx;
                font-weight:normal;
                margin-left:8rpx;
            }
        }
        .calendar_container{
            background: radial-gradient(69% 69% at 50% 30%, #FCE2AC 0%, #FFFCF6 100%);
        }


        .line {
            height: 1rpx;
            background: $fill-color-dark;
            margin: 38rpx -32rpx 24rpx;
        }

        .hotspot {
            width: 60rpx;
            height: 40rpx;
            margin: 0 auto;
            display: flex;
            justify-content: center;

            .downArrow {
                width: 20rpx;
                height: 12rpx;
                background-image: url('https://wpm-cdn.dreame.tech/images/202307/030622-1689650243736.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }

            .upArrow {
                width: 20rpx;
                height: 12rpx;
                background-image: url('https://wpm-cdn.dreame.tech/images/202307/346374-1689650643387.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
        }

        // .buyText {
        //     height: 28rpx;
        //     font-size: 20rpx;
        //     font-family: PingFang SC-常规体, PingFang SC;
        //     font-weight: 400;
        //     color: $text-color-secondary;
        //     line-height: 28rpx;
        // }

        // .cardList {
        //     display: flex;
        //     justify-content: space-between;
        //     margin: 30rpx 0 30rpx 0;

        //     .singalCard {
        //         display: flex;
        //         justify-content: space-between;
        //         flex-direction: column;
        //         align-items: center;

        //         .activeStyle {
        //             width: 76rpx;
        //             height: 99rpx;
        //             background: #ffd183;
        //             border-radius: 4rpx;
        //             opacity: 1;
        //             border: 2rpx solid #c6a365;
        //             padding: 15rpx 13rpx;
        //             display: flex;
        //             flex-direction: column;
        //             justify-content: space-between;
        //             align-items: center;

        //             .activePointNumContent {
        //                 .activePointNumTop {
        //                     display: flex;
        //                     align-items: center;
        //                     justify-content: center;

        //                     .activePointNum {
        //                         height: 36rpx;
        //                         font-size: 27rpx;
        //                         font-family: MiSans-Semibold, MiSans;
        //                         font-weight: 600;
        //                         color: #ffffff;
        //                     }

        //                     .addIcon {
        //                         width: 19rpx;
        //                         height: 19rpx;
        //                         background-image: url("@/static/checkIn/add.png");
        //                         background-repeat: no-repeat;
        //                         background-size: 100% 100%;
        //                     }
        //                 }
        //             }

        //             .gou {
        //                 width: 23rpx;
        //                 height: 15rpx;
        //                 background-image: url("@/static/checkIn/duigou.png");
        //                 background-repeat: no-repeat;
        //                 background-size: 100% 100%;
        //             }
        //         }

        //         .singalDate {
        //             height: 27rpx;
        //             font-size: 19rpx;
        //             font-family: PingFang SC-常规体, PingFang SC;
        //             font-weight: normal;
        //             color: #564933;
        //             line-height: 22rpx;
        //             margin-top: 8rpx;
        //         }
        //     }

        //     .singalCard {
        //         display: flex;
        //         justify-content: space-between;
        //         flex-direction: column;
        //         align-items: center;

        //         .inactiveStyle {
        //             width: 76rpx;
        //             height: 99rpx;
        //             background: rgba(255, 209, 131, 0.2);
        //             border-radius: 4rpx;
        //             opacity: 1;
        //             display: flex;
        //             align-items: center;
        //             justify-content: center;

        //             .queue {
        //                 width: 57rpx;
        //                 height: 57rpx;
        //                 background: rgba(255, 209, 131, 0.2);
        //                 opacity: 1;
        //                 border-radius: 100%;
        //                 font-size: 27rpx;
        //                 font-family: MiSans-Semibold, MiSans;
        //                 font-weight: 600;
        //                 color: #e5b562;
        //                 display: flex;
        //                 align-items: center;
        //                 justify-content: center;

        //                 .inActiveAdd {
        //                     width: 19rpx;
        //                     height: 19rpx;
        //                     background-image: url("@/static/checkIn/add1.png");
        //                     background-repeat: no-repeat;
        //                     background-size: 100% 100%;
        //                 }

        //                 .inActivePointNum {
        //                     //   line-height: 31rpx;
        //                 }
        //             }
        //         }

        //         .singalDate {
        //             height: 27rpx;
        //             font-size: 19rpx;
        //             font-family: PingFang SC-常规体, PingFang SC;
        //             font-weight: normal;
        //             color: #564933;
        //             line-height: 22rpx;
        //             margin-top: 8rpx;
        //         }
        //     }

        //     .singalCard {
        //         display: flex;
        //         justify-content: space-between;
        //         flex-direction: column;
        //         align-items: center;

        //         .haveSignStyle {
        //             width: 76rpx;
        //             height: 99rpx;
        //             background: #ffd183;
        //             border-radius: 4rpx;
        //             opacity: 1;
        //             padding: 15rpx 13rpx;
        //             display: flex;
        //             align-items: center;
        //             justify-content: center;

        //             .gou {
        //                 width: 23rpx;
        //                 height: 15rpx;
        //                 background-image: url("@/static/checkIn/duigou.png");
        //                 background-repeat: no-repeat;
        //                 background-size: 100% 100%;
        //             }
        //         }
        //     }

        //     .singalCard {
        //         display: flex;
        //         justify-content: space-between;
        //         flex-direction: column;
        //         align-items: center;

        //         .lackSignStyle {
        //             width: 76rpx;
        //             height: 99rpx;
        //             background: rgba(216, 216, 216, 0.2);
        //             border-radius: 4rpx;
        //             opacity: 1;
        //             display: flex;
        //             align-items: center;
        //             justify-content: center;

        //             .queue {
        //                 width: 57rpx;
        //                 height: 57rpx;
        //                 background: rgba(200, 200, 200, 0.2);
        //                 opacity: 1;
        //                 border-radius: 100%;
        //                 font-size: 27rpx;
        //                 font-family: MiSans-Semibold, MiSans;
        //                 font-weight: 600;
        //                 color: #c2c2c2;
        //                 display: flex;
        //                 align-items: center;
        //                 justify-content: center;

        //                 .inActiveAdd {
        //                     width: 19rpx;
        //                     height: 19rpx;
        //                     background-image: url("@/static/checkIn/grayAdd.png");
        //                     background-repeat: no-repeat;
        //                     background-size: 100% 100%;
        //                 }

        //                 .inActivePointNum {
        //                     //   line-height: 31rpx;
        //                 }
        //             }
        //         }

        //         .singalDate {
        //             height: 27rpx;
        //             font-size: 19rpx;
        //             font-family: PingFang SC-常规体, PingFang SC;
        //             font-weight: normal;
        //             color: #564933;
        //             line-height: 22rpx;
        //             margin-top: 8rpx;
        //         }
        //     }
        // }
    }

    /* 金币权益区域 */
    .coin-rights {
        background: #fff9e8;
        border-radius: 24rpx;
        padding: 12rpx 62rpx 40rpx;
        margin: 24rpx 32rpx;

        .coin-rights-content {
            .rights-cards {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20rpx;

                .rights-card {
                    // 使上方图标区域宽度与下方胶囊节点一致，保证左右对齐
                    width: 114rpx;
                    flex: 0 0 114rpx;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: relative;
                    cursor: pointer;

                    // 新增：除了第一个卡片，其余卡片左侧添加竖线分隔
                    &:not(:first-child)::before {
                        content: '';
                        position: absolute;
                        /* 使用自身宽度 50% 的偏移量，使分隔线始终居于两卡片之间 */
                        left: -50%;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 1rpx;
                        height: 80%;
                        background: rgba(0, 0, 0, 0.2);
                    }

                    .card-icon {
                        position: relative;
                        width: 80rpx;
                        height: 80rpx;
                        .rights-icon {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                        }
                    }

                    .card-title {
                        font-size: 28rpx;
                        color: #121212;
                        font-weight: 500;
                    }
                }
            }

            .rights-progress {
                .progress-bar {
                    position: relative;
                    
                    .capsule-progress {
                        display: flex;
                        align-items: center;

                        /* 外层包裹，纵向排列胶囊与标签 */
                        .capsule-wrapper {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            flex-shrink: 0;
                            position: relative;

                            .capsule-label {
                                margin-top: 12rpx;
                                font-size: 20rpx;
                                color: #FE351C;
                                line-height: 20rpx;
                                &.placeholder {
                                    visibility: hidden;
                                }
                            }
                            .capsule-progress {
                                position: absolute;
                                bottom:-40rpx;
                                left: 50%;
                                transform: translateX(-50%);
                                width: fit-content;
                                height: 100%;
                                font-size: 20rpx;
                                color: #A6A6A6;
                                line-height: 20rpx;
                            }

                        }
                        
                        .capsule-node {
                            position: relative;
                            width: 114rpx;
                            height: 48rpx;
                            border-radius: 24rpx;
                            background: #f0f0f0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 1;
                            transition: all 0.3s ease;
                            flex-shrink: 0;
                            
                            .capsule-status {
                                font-size: 24rpx;
                                color: #999;
                                line-height:48rpx;
                                @include flex(row, center, center, none);
                                font-weight: normal;
                                transition: color 0.3s ease;
                            }
                            
                            &.active {
                                background: linear-gradient(112deg, #FF8D50 7%, #F9580D 52%, #FF2E20 95%);
                                box-shadow: 0px 9rpx 14rpx -4rpx rgba(254, 134, 71, 0.51);
                                
                                .capsule-status {
                                    color: #fff;
                                }
                            }
                        }
                        
                        .progress-segment {
                            flex: 1;
                            height: 8rpx;
                            background: #f0f0f0;
                            position: relative;
                            overflow: hidden;
                            
                            .segment-fill {
                                height: 100%;
                                background: linear-gradient(90deg, #FE351C 0%, #FF8C4E 100%);
                                transition: width 0.3s ease;
                            }
                            
                            &.active {
                                margin-bottom: 32rpx;
                                .segment-fill {
                                    background: linear-gradient(90deg, #FE351C 0%, #FF8C4E 100%);
                                }
                            }
                        }

                        .progress-segmentLock {
                            flex: 1;
                            height: 8rpx;
                            background: #f0f0f0;
                            position: relative;
                            overflow: hidden;
                            margin-bottom: 32rpx;
                            
                            .segment-fill {
                                height: 100%;
                                background: linear-gradient(90deg, #FE351C 0%, #FF8C4E 100%);
                                transition: width 0.3s ease;
                            }
                            
                            &.active {
                                .segment-fill {
                                    background: linear-gradient(90deg, #FE351C 0%, #FF8C4E 100%);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/* 积分领取区域 */
.points-box-bottom {
    display: flex;
    height: 174rpx;
    background-color: $fill-color-bg-white;
    border-radius: 24rpx;
    margin: -50rpx 32rpx 0;
    z-index: 1;

    .points-box-bottom-left {
        flex: 1;
        height: 100%;
        display: flex;
        flex-wrap: nowrap;
        overflow-x: scroll;

        .task2:nth-child(1) {
            margin: 34rpx 0rpx 0 32rpx;
        }

        .task2:last-child {
            margin: 34rpx 38rpx 0 38rpx;
        }

        .task2 {
            margin: 0 40rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-shrink: 0;

            .perform {
                font-size: 26rpx;
                font-weight: 500;
                color: white;
                width: 64rpx;
                height: 64rpx;
                border-radius: 50%;
                box-sizing: border-box;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686bad634fcfe3270012280.png');
                background-repeat: no-repeat;
                background-size: 100%;
                text-align: center;
                line-height: 64rpx;
                margin-bottom: 12rpx;
            }

            text {
                font-size: 22rpx;
                color: #121212;
                line-height: 30rpx;
            }
        }
    }

    .points-box-bottom-right {
        width: 242rpx;
        height: 100%;
        display: flex;
        justify-content: flex-end;
        margin-right: 24rpx;
        align-items: center;
        background-color: $fill-color-bg-white;
        box-shadow: -16rpx 0rpx 20rpx 0rpx rgba(255, 255, 255, 1);
        z-index: 1;

        .get {
            font-size: 28rpx;
            font-weight: 500;
            color: white;
            border-radius: 96rpx;
            background: #f96012;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 174rpx;
            height: 60rpx;
        }
    }
}

/* ------------- 新增：头部区域一键领取卡片透明背景 ------------- */
.header {
    .points-box-bottom {
        background-color: transparent !important;
    }

    .points-box-bottom-right {
        background-color: transparent !important;
        box-shadow: none !important;
    }
}
