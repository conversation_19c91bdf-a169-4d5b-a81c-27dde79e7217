<template>
    <view class="device-item" @click="gotoDevice">
        <!-- <div @click.stop="editDeviceInfo"><img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6866360d757f94810015349.png" class="device-item-more" alt="more" /></div> -->
        <div class="device-info-card">
            <div class="device-info-area">
                <div class="device-info-image-area">
                    <img :src="image" alt="设备图片" class="device-info-image" />
                    <div class="info_container">
                        <div class="device-info-name">{{ name }}</div>
                        <!-- v-if="status" -->
                        <div class="device-info-status" v-if="status">{{ status}}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="device-control-item" @click.stop="startClean">
            <img :src="workIcon" alt="开始清洁" class="device-control-icon" />
        </div>
        <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687dd1e7dd3bc9060011128.png" class="device_bg_image" />
    </view>
</template>
<script lang="ts">
import Utils from '@/common/Utils';

const iconCharge =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686637a2539cb3420010450.png';
const iconPause =
   'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b385e9b18b6350010674.png';
const iconBackCharge =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686636812a0251720010377.png';
const defaultDeviceImage =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png';

const deviceCanStopStatus = [1, 7, 9, 10, 11, 12, 15, 16, 17, 18, 20, 22, 23, 25, 26, 27, 28, 37, 38, 97, 98, 101, 107];

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
            supportVideo: false, // 是否支持视频
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        supportFastCommand() {
            const { deviceInfo = {}} = this.currentDevice;
            const { feature = '' } = deviceInfo;
            // 支持快捷指令的设备型号
            return feature === 'fastCommand';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage;
        },
        workIcon() {
            const device = this.currentDevice;
            if (!device) return '';
            if (device.online) {
                if (deviceCanStopStatus.includes(device.latestStatus)) {
                    return iconPause;
                }
                return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b36e4765194850020187.png';
            }
            return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b36e4765194850020187.png';
        },
        chargeIcon(): string {
            const device = this.currentDevice;
            if (!device) return '';
            if (device.online) {
                if (device.latestStatus === 5) {
                    return iconCharge;
                }
                return iconBackCharge;
            }
            return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686659321b9071130010815.png';
        },
        name() {
            return (
                this.currentDevice.customName ||
                this.currentDevice.deviceInfo?.displayName ||
                this.currentDevice.displayName ||
                ''
            );
        },
        hasVideoPermission() {
            const { permissions = '' } = this.currentDevice;
            if (permissions) {
                const split = permissions.split(',').map((item) => item.toUpperCase());
                return split.includes('VIDEO');
            }
            return permissions;
        },
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true,
        },
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber();
            const bind_id = bindDomain.split('.')[0];
            const data = {
                did,
                id,
                from: 'mapp',
                method: 'get_properties',
                params: [
                    { did, siid: 2, piid: 1 },
                    { did, siid: 3, piid: 1 },
                    { did, siid: 4, piid: 38 },
                    { did, siid: 4, piid: 83 },
                    { did, siid: 4, piid: 48 },
                ],
            };
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data),
            };
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter((r) => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter((r) => r.siid === 3 && r.piid == 1);
                        const supportVideo1 = result.filter((r) => r.siid === 4 && r.piid == 38);
                        const supportVideo2 = result.filter((r) => r.siid === 4 && r.piid == 83);
                        const supportVideoStatus1 = supportVideo1[0]?.value;
                        const supportVideoStatus2 = supportVideo2[0]?.value;
                        const _supportVideo = supportVideoStatus1 | supportVideoStatus2;
                        this.supportVideo = (_supportVideo & 1) !== 0;
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel(
                                'DEVICE',
                                'getStatusStr',
                                {
                                    model: model,
                                    latestStatus: onlineStatus[0].value,
                                },
                                (res) => {
                                    newStatus.status = res.data;
                                    this.currentDeviceStatus = { ...newStatus };
                                },
                            );
                        }
                    }
                }
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: body,
                },
                commandResultCB,
            );
        },
        // 进入设备详情，插件
        gotoDevice() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 开始清洁
        startClean() {
            const { did = '', bindDomain = '', latestStatus, model } = this.currentDevice;
            const id = this.getRandomNumber();
            const bind_id = bindDomain.split('.')[0];
            let data = {};
            if (deviceCanStopStatus.includes(latestStatus)) {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 },
                };
                Utils.reportEvent('device_pause', {
                    device_model: model,
                });
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    from: 'mapp',
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '2,1,{"app_auto_clean":1}' }], aiid: 1 },
                };
                Utils.reportEvent('device_start', {
                    device_model: model,
                });
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data),
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: params,
                },
                (res) => {
                    console.log(' start clean ', res);
                },
            );
        },
        // 返回基站
        goBackHome() {
            const { did = '', bindDomain = '', latestStatus } = this.currentDevice;
            const id = this.getRandomNumber();

            const bind_id = bindDomain.split('.')[0];
            let data = {};
            if (latestStatus === 5) {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 },
                };
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id: id,
                    from: 'mapp',
                    method: 'action',
                    params: { did: did, siid: 3, in: [{ piid: 100, value: '{"charge":1}' }], aiid: 1 },
                };
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data),
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: params,
                },
                (res) => {
                    console.log(' start charge ', res);
                },
            );
        },
        // 进入视频页面
        goVideoPage() {
            if (this.hasVideoPermission) {
                Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'video' });
            } else {
                // 弹出提示
                uni.showToast({
                    title: '若要启用此功能，请联系设备主任',
                    icon: 'none',
                    duration: 2500,
                });
            }
        },
        // 进入快捷指令页面
        goQuickOrderPage() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'quickCommand' });
        },
    },
};
</script>
<style scoped>
.device-item {
    overflow: hidden;
    width: 360rpx;
    height: 156rpx;
    box-sizing: border-box;
    padding: 14rpx;
    background: linear-gradient(112deg, #EBF4FF 0%, #F7F9FA 48%, #FFFFFF 100%, #FFFFFF 100%);
    background-repeat: no-repeat;
    background-position:left top, right bottom;
    position: relative;
    background-size: 100% 100%, 212rpx auto;
    position: relative;
    border-radius: 24rpx;
    border-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%) 1;
}
.device-info-area {
    width: 60%;
}
.device-item-more {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    width: 52rpx;
    height: 52rpx;
}
.device-info-card {
    display: flex;
    justify-content: space-between;
}
.device-info-name {
    color: #404040;
    font-size: 34rpx;
    font-weight: 500;
    margin-top: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 220rpx;
}
.device-info-status {
    color: #A6A6A6;
    font-size: 28rpx;
    font-weight: regular;
    line-height: 32rpx;
    width: calc(100% - 0rpx);
    white-space: nowrap;
    overflow: hidden;
    margin-top: 10rpx;
    text-overflow: ellipsis;
}
.device-info-battery {
    width: 32rpx;
    height: 32rpx;
}
.col-line {
    width: 1rpx;
    height: 16rpx;
    background-color: #777777;
    margin: 0 16rpx;
}
.device-info-image-area {
    height: 100%;
    width: 100%;
    display: flex;
    position: relative;
}
.info_container{
    display: flex;
    flex-direction: column;
    margin-left: 20rpx;
    max-width: 60%;
}
.device-info-image {
    width: 88rpx;
    /* height: 94rpx; */
    object-fit: contain;
}

.device-control-icon {
    width: 63rpx;
    height: 63rpx;
}
.device_bg_image{
    position: absolute;
    right: 0rpx;
    bottom: 0;
    width: 112rpx;
    height: 120rpx;
    object-fit: contain;
    z-index: 0;
}
.nopermission {
    opacity: 0.5;
}
.col-line2 {
    width: 1rpx;
    height: 42rpx;
    background-color: #d8d8d8;
    margin: 0 16rpx;
}
.device-control-item {
    position: absolute;
    /* top: 60%; */
    bottom: 10%;
    /* transform: translateY(-50%); */
    right: 16rpx;
    width: 60rpx;
    height: 60rpx;
    z-index: 300;
}
</style>
