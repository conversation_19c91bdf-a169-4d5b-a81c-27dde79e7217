<template>
     <view class="point-container" @click="goMore">
    <!-- 左侧文案区域 -->
    <view class="left-box">
      <text class="sale-tag">NICE SALE</text>
      <text class="mall-title">积分</text>
      <text class="mall-title">商城</text>
      <text class="more-btn" @click="goMore">更多 ></text>
    </view>
    <!-- 右侧商品列表区域 -->
    <view class="right-list">
      <view
        class="goods-item"
        v-for="(item, index) in goodsList"
        :key="index"
      >
        <image
          class="goods-img"
          :src="item.icon"
          mode="widthFix"
        ></image>
        <text class="price">¥{{ item.price }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({
    components: {
        // uScrollList,
        // CoinPoints,
        // MemberLevel,
    }
})
export default class TabList extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Array, default: [] })
    readonly goodsList!: Array<any>;

    async created() {
        // await this.fetchTagList();
    }

    goMore() {
        this.$emit('more');
    }
}
</script>

<style lang="scss" scoped>
 .point-container {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  margin-top: 10rpx;
  /* 可根据需求调整阴影等样式 */
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background: linear-gradient(151deg, #FFE4EE 0%, #FFFFFF 40%);
  margin: 0 16rpx;
}

.left-box {
  display: flex;
  flex-direction: column;
  margin-right: 20px;
}

.sale-tag {
  font-size: 12px;
  color: #ff8b94;
  margin-bottom: 4px;
}

.mall-title {
  font-size: 20px;
  font-weight: bold;
  color: #F3558C;
  /* 模拟下划线，也可用 border-bottom 实现 */
//   text-decoration: underline;
//   text-underline-offset: 4px;
}

.more-btn {
  font-size: 20rpx;
  color: #FFC0D6;
  margin-top: 14rpx;
}

.right-list {
  display: flex;
  flex-wrap: nowrap; /* 横向排列，不换行 */
  overflow-x: auto; /* 超出可滚动 */
  flex: 1; /* 占满剩余空间 */
  gap: 20rpx;
  justify-content: space-between;
}

.goods-item {
  display: flex;
  flex-direction: column;
  align-items: center;

}

.goods-img {
  width: 90rpx; /* 可根据设计稿调整 */
//   margin-bottom: 8px;

}

.price {
    font-size: 24rpx;
    color: #555555;
    margin-top: 38rpx;
     font-weight: 500;
    /* 禁止换行 */
    white-space: nowrap;
    text-align: center;
}
</style>
