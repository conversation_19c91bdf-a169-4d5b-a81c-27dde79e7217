<template>
    <!-- 打卡日历页面 -->
    <view class="all">
        <view class="myDateTable" v-if="!isShowDetail">
            <!-- 九宫格布局 -->
            <view class="grid-container">
                <!-- 上面3个 -->
                <view class="top-row">
                    <template v-for="(item, j) in dayItemsTopThree">
                        <NormalDayCell :item="item" :index="item.index" :key="`top-${j}`" />
                    </template>
                </view>

                <!-- 中间3个 -->
                <view class="bottom-row">
                    <template v-for="(item, j) in dayItemsBottomThree">
                        <NormalDayCell :item="item" :index="item.index" :key="`bottom-${j}`" />
                    </template>
                </view>

                <!-- 底部第7天（满行） -->
                <view class="bottom-row">
                    <NormalDayCell
                        v-if="dayItemsLastSome.length > 1"
                        :item="dayItemsLastSome[0]"
                        :index="dayItemsLastSome[0].index"
                        :key="`bottom-0`"
                    />
                    <SevenDayCell
                        :isShortLength="dayItemsLastSome.length > 1"
                        :item="dayItemsLastItem"
                        :index="dayItemsLastItem.index"
                        :key="`bottom-1`"
                    />
                </view>
            </view>
            <view class="signin-btn" @click="handleSignIn" v-if="!todaySigned"> </view>
            <!-- <view class="bottom-notice">
        <view class="notice-text">
          签到提醒
        </view>
        <u-switch v-model="openSigninRemind" asyncChange @change="handleToggleRemind" inactiveColor="#FFCCC7"
          activeColor="#121212" size="9"></u-switch>
      </view> -->
        </view>
    </view>
</template>

<script>
import Utils from '@/common/Utils';
import SevenDayCell from './SenvenDayCell.vue';
import NormalDayCell from './NormalDayCell.vue';

export default {
    data() {
        return {
            calendarDays: [],
            simpleCalendarDays: [],
            SignData: [], // 已经签到的数据
            nowYear: 0, // 当前选的年
            nowMonth: 0, // 当前选的月
            today: parseInt(new Date().getDate()), // 系统本日
            toMonth: parseInt(new Date().getMonth() + 1), // 系统本月
            toYear: parseInt(new Date().getFullYear()), // 系统本年
            weeksTxt: {
                ch: ['日', '一', '二', '三', '四', '五', '六'],
                en: ['Sun', 'Mon', 'Tues', 'Wed', 'Thur', 'Fri', 'Sat'],
            },
            langType: 'zh',
            isDetail: false,
            isShowToday: false,
            openSigninRemind: this.isOpenedRemind,
        };
    },
    components: {
        SevenDayCell,
        NormalDayCell,
    },
    props: {
        todaySigned: {
            type: Boolean,
            default: false,
        },
        dateList: {
            type: Array,
            default: () => [],
        },
        isOpenedRemind: {
            // 是否开启了签到提醒
            type: Boolean,
            default: false,
        },
        isReplenishSign: {
            // 是否允许过期补签
            type: Boolean,
            default: false,
        },
        isFullCalendar: {
            // 是否需要填满日历，前后空的格子填入上/下个月的日期
            type: Boolean,
            default: true,
        },
        isShowDetail: {
            type: Boolean,
            default: false,
        },
        signDataTotal: {
            type: Object,
            default: () => ({
                dataSource: [],
                continueDay: 0,
                todaySigned: false,
                addPoint: 1000,
            }),
        },
        yearMonth: {
            // 2022-01 这种格式，默认当前年月
            type: String,
            default: new Date().getFullYear() + '-' + new Date().getMonth() + 1,
        },
    },
    created() {
        if (!/en|ch/g.test(this.langType)) {
            this.langType = 'ch'; // 非中英，则固定中文
        }
        const ymArr = this.yearMonth.split('-');
        this.isDetail = this.isShowDetail;
        this.isShowToday = this.todaySigned;
        this.buildCalendar(ymArr[0], ymArr[1]);
    },
    watch: {
        signDataTotal: {
            deep: true,
            handler() {
                this.onSignDataChange();
            },
        },
        isShowDetail: 'showDetail',
    },
    computed: {
        dayItems() {
            return this.dateList.map((item, index) => {
                return {
                    ...item,
                    index,
                };
            });
        },
        dayItemsTopThree() {
            return this.dayItems.slice(0, 3);
        },
        dayItemsBottomThree() {
            return this.dayItems.slice(3, 6);
        },
        dayItemsLastSome() {
            return this.dayItems.slice(6);
        },
        dayItemsLastItem() {
            return this.dayItems.slice(-1)[0];
        },
        dataSource() {
            return this.signDataTotal.dataSource;
        },
        continueDay() {
            return this.signDataTotal.continueDay;
        },
        // todaySigned() {
        //     return this.signDataTotal.todaySigned;
        // },
        addPoint() {
            return this.signDataTotal.addPoint;
        },
        // 九宫格布局数据分组
        gridLayoutDays() {
            if (this.isDetail || !this.simpleCalendarDays || this.simpleCalendarDays.length === 0) {
                return {
                    topThree: [],
                    today: null,
                    bottomThree: [],
                };
            }

            const days = [...this.simpleCalendarDays];

            // 找到今日的索引
            let todayIndex = -1;
            for (let i = 0; i < days.length; i++) {
                if (days[i] && days[i].fullDate === this.toYear + '-' + this.toMonth + '-' + this.today) {
                    todayIndex = i;
                    break;
                }
            }

            if (todayIndex === -1) {
                // 如果没有找到今日，按照中间位置分组
                todayIndex = Math.floor(days.length / 2);
            }

            // 确保有7个元素
            while (days.length < 7) {
                days.push({
                    date: null,
                    fullDate: null,
                    simpleDate: null,
                    isBeforeToday: false,
                    isSign: false,
                    isThisMonth: false,
                    point: 1000,
                });
            }

            const topThree = days.slice(Math.max(0, todayIndex - 3), todayIndex);
            const today = days[todayIndex];
            const bottomThree = days.slice(todayIndex + 1, todayIndex + 4);

            // 确保前后各3个
            while (topThree.length < 3) {
                topThree.unshift({
                    date: null,
                    fullDate: null,
                    simpleDate: null,
                    isBeforeToday: false,
                    isSign: false,
                    isThisMonth: false,
                    point: 1000,
                });
            }

            while (bottomThree.length < 3) {
                bottomThree.push({
                    date: null,
                    fullDate: null,
                    simpleDate: null,
                    isBeforeToday: false,
                    isSign: false,
                    isThisMonth: false,
                    point: 1000,
                });
            }

            return {
                topThree: topThree.slice(-3), // 取最后3个
                today,
                bottomThree: bottomThree.slice(0, 3), // 取前3个
            };
        },
    },
    methods: {
        handleSignIn() {
            this.$emit('signIn');
        },
        async handleToggleRemind(value) {
            // TODO 设置签到提醒
            // this.$emit('setRemind', value);
            if (value) {
                this.$emit('setRemind');
            } else {
                this.$emit('closeRemind');
            }
        },
        // 构建日历数据
        buildCalendar(y, m) {
            this.nowYear = y;
            this.nowMonth = m;
            this.calculateEmptyGrids(y, m);
            this.calculateDays(y, m);
            if (this.isFullCalendar) {
                this.fullCell();
            }
        },
        // 监听到签到数据源改变
        onSignDataChange(newData, oldData = []) {
            this.SignData = newData;
            this.matchSign(newData);
        },
        showDetail(newVal) {
            this.isDetail = newVal;
            if (!this.isDetail) {
                // 非详情的时候重新构建日历
                const ymArr = this.yearMonth.split('-');
                this.buildCalendar(ymArr[0], ymArr[1]);
                this.$emit('dateChange', this.nowYear + '-' + this.nowMonth);
            }
            this.matchSign();
        },
        // 匹配标记已经签到的日子
        matchSign() {
            const signs = this.dataSource;
            const daysArr = this.calendarDays;
            this.isShowToday = this.todaySigned;
            for (let i = 0; i < signs.length; i++) {
                const current = this.$u.timeFormat(signs[i].eventTime, 'yyyy-mm-dd');
                const dayPoint = signs[i].numValue;

                // let current = new Date(this.toIOSDate(signs[i])).getTime(); // ios只认/格式的日期
                for (let j = 0; j < daysArr.length; j++) {
                    if (current == this.formatDate(daysArr[j].fullDate)) {
                        daysArr[j].isSign = true;
                        daysArr[j].point = dayPoint;
                    }
                }
            }

            // 因为后端返回数据延迟问题 需要立即判断当天是否签到此处不要加0 因为fulldate里面的数据也没加0
            for (let j = 0; j < daysArr.length; j++) {
                if (this.toYear + '-' + this.toMonth + '-' + this.today == daysArr[j].fullDate && this.isShowToday) {
                    daysArr[j].isSign = true;
                }
            }

            // 再次判断日期在当天之前还是之后
            daysArr.map((item, index) => {
                if (
                    new Date(this.toIOSDate(item.fullDate)).getTime() -
                        new Date(this.toYear + '/' + this.toMonth + '/' + this.today).getTime() >=
                    0
                ) {
                    item.isBeforeToday = false;
                } else {
                    item.isBeforeToday = true;
                }
            });

            // 判断当前第一天是不是大于当天日期,并且大于几天
            const currentMonthFirstDay =
                (new Date(this.toIOSDate(daysArr[0].fullDate)).getTime() -
                    new Date(this.toYear + '/' + this.toMonth + '/' + this.today).getTime()) /
                1000 /
                24 /
                60 /
                60;
            if (currentMonthFirstDay > 0) {
                if (currentMonthFirstDay >= 6) {
                    daysArr.forEach((item, index) => {
                        item.point = 2000;
                    });
                } else if (currentMonthFirstDay < 7) {
                    daysArr.map((item, index) => {
                        if (index >= this.continueDay - currentMonthFirstDay + 1) {
                            item.point = 2000;
                        } else {
                            item.point = 1000;
                        }
                    });
                }
            }
            let currentIndex;
            daysArr.forEach((item, index) => {
                if (item.fullDate === this.toYear + '-' + this.toMonth + '-' + this.today) {
                    currentIndex = index;
                }
            });

            // 旧 _continueDay 逻辑块已删除，改用 streakLen 逻辑实现窗口与翻倍奖励

            if (!this.isDetail) {
                const cloneDaysArr = uni.$u.deepClone(daysArr);

                /* --------------- 计算连续签到天数（含今天） --------------- */
                let streakLen = 0;
                for (let idx = currentIndex; idx >= 0; idx--) {
                    if (cloneDaysArr[idx] && cloneDaysArr[idx].isSign) {
                        streakLen++;
                    } else {
                        break;
                    }
                }

                /* -------------------- 确定窗口起始索引 -------------------- */
                let windowStart;
                if (streakLen < 2) {
                    // 情况 1：不在连续签到（连续 <2)
                    windowStart = currentIndex;
                } else if (streakLen < 6) {
                    // 情况 2：连续 2-5 天
                    windowStart = currentIndex - (streakLen - 1);
                } else {
                    // 情况 3：连续 ≥6 天
                    windowStart = currentIndex;
                }
                if (windowStart < 0) windowStart = 0;
                if (windowStart + 7 > cloneDaysArr.length) {
                    windowStart = Math.max(0, cloneDaysArr.length - 7);
                }

                const sevenDays = cloneDaysArr.slice(windowStart - 3, windowStart + 4);

                /* ------------------ 设置翻倍奖励 ------------------ */
                if (streakLen >= 6) {
                    // 情况 3：窗口内除第一天外全部翻倍
                    sevenDays.forEach((d, idx) => {
                        if (idx > 0) d.point = 2000;
                    });
                } else {
                    sevenDays.forEach((d, idx) => {
                        if (idx > 0) d.point = 1000;
                    });
                }

                /* ---- 同步至 calendarDays 以便详情模式保持一致 ---- */
                if (streakLen >= 6) {
                    for (let i = currentIndex + 1; i <= currentIndex + 6; i++) {
                        if (daysArr[i]) daysArr[i].point = 2000;
                    }
                } else {
                    sevenDays.forEach((d, idx) => {
                        if (idx > 0) d.point = 1000;
                    });
                }

                this.simpleCalendarDays = sevenDays;
            } else {
                // 详情视图：calendarDays 已在前面更新过翻倍奖励
                this.calendarDays = daysArr;
            }
        },
        formatDate(str) {
            // 根据 - 符号拆分
            return str
                .split('-')
                .map((item) => {
                    // +item 将item字符串转换为数字
                    // 小于10的时候就补全一个前缀0
                    if (+item < 10) {
                        return '0' + +item;
                    }

                    // 大于10的时候不用补0
                    return item;
                })
                .join('-'); // 最后重组回来
        },
        simplifyDate(str) {
            // 简化日期显示
            return str
                .split('-')
                .map((item) => {
                    // +item 将item字符串转换为数字
                    // 小于10的时候就补全一个前缀0
                    if (+item < 10 && item.charAt(0) == 0) {
                        return item.slice(1, 2);
                    }

                    // 大于10的时候不用补0
                    return item;
                })
                .join('-'); // 最后重组回来
        },
        // 计算当月1号前空了几个格子，把它填充在calendarDays数组的前面
        calculateEmptyGrids(year, month) {
            // 计算每个月时要清零
            this.calendarDays = [];
            const firstDayOfWeek = this.getFirstDayOfWeek(year, month);
            if (firstDayOfWeek > 0) {
                for (let i = 0; i < firstDayOfWeek; i++) {
                    this.calendarDays.push({
                        date: null, // 显示的日期
                        fullDate: null, // 日期yyyy-mm-dd格式
                        simpleDate: null,
                        isBeforeToday: true, // 今日之前
                        isSign: false, // 是否签到
                        isThisMonth: false, // 是本月,
                        point: 1000,
                    });
                }
            }
        },
        // 绘制当月天数占的格子，并把它放到days数组中
        calculateDays(year, month) {
            const thisMonthDays = this.getMonthDayLength(year, month);
            const toDate = new Date(this.toYear + '/' + this.toMonth + '/' + this.today);
            for (let i = 1; i <= thisMonthDays; i++) {
                const fullDate = year + '-' + month + '-' + i;
                const simpleDate = month + '.' + i;
                const isBeforeToday = new Date(this.toIOSDate(fullDate)) < toDate;
                this.calendarDays.push({
                    date: i,
                    fullDate,
                    simpleDate,
                    isBeforeToday,
                    isSign: false,
                    isThisMonth: true,
                    point: 1000,
                });
            }
        },
        // 切换控制年月，上一个月，下一个月
        changeMonth(type) {
            const nowYear = parseInt(this.nowYear);
            const nowMonth = parseInt(this.nowMonth);
            const newObj = this.getOperateMonthDate(nowYear, nowMonth, type);
            if (parseInt((newObj.year - this.toYear) * 12) + newObj.month - this.toMonth > 1) {
                Utils.Toast('往后最多查看1月', 1000, false);
                return;
            }

            if (parseInt((this.toYear - newObj.year) * 12) + this.toMonth - newObj.month > 11) {
                Utils.Toast('往前最多查看11月', 1000, false);
                return;
            }
            this.buildCalendar(newObj.year, newObj.month); // 重新构建日历数据
            this.$emit('dateChange', this.nowYear + '-' + this.nowMonth);
        },
        // 获取当月共多少天，也就是获取月的最后一天
        getMonthDayLength(year, month) {
            return new Date(year, month, 0).getDate();
        },
        // 获取当月第一天星期几
        getFirstDayOfWeek(year, month, day = 1) {
            return new Date(Date.UTC(year, month - 1, day)).getDay();
        },
        toIOSDate(strDate) {
            // iso不认识"-"拼接的日期，所以转/
            return strDate ? strDate.replace(/-/g, '/') : strDate;
        },
        // 需要填满格子，上/下个月的日期拉出来填满格子
        fullCell() {
            const endDay = this.getMonthDayLength(this.nowYear, this.nowMonth);
            const beforeEmptyLength = this.getFirstDayOfWeek(this.nowYear, this.nowMonth);
            const afterEmptyLength = 7 - this.getFirstDayOfWeek(this.nowYear, this.nowMonth, endDay);
            const last = this.getOperateMonthDate(this.nowYear, this.nowMonth, -1);
            const lastMonthEndDay = this.getMonthDayLength(last.year, last.month);
            for (let i = 0; i < beforeEmptyLength; i++) {
                const date = lastMonthEndDay - beforeEmptyLength + i + 1;
                this.calendarDays[i].date = date;
                this.calendarDays[i].fullDate = last.year + '-' + last.month + '-' + date;
                this.calendarDays[i].simpleDate = last.month + '.' + date;
            }
            const next = this.getOperateMonthDate(this.nowYear, this.nowMonth, 1);
            for (let i = 1; i <= afterEmptyLength; i++) {
                this.calendarDays.push({
                    date: i, // 显示的日期
                    simpleDate: next.month + '.' + i, // 日期mm-dd格式
                    fullDate: next.year + '-' + next.month + '-' + i, // 日期yyyy-mm-dd格式
                    isBeforeToday: false, // 今日之前
                    isSign: false, // 是否签到
                    isThisMonth: false, // 是本月
                    point: 1000,
                });
            }
        },
        // 获取加/减一个月的日期
        getOperateMonthDate(yy, mm, num) {
            let month = parseInt(mm) + parseInt(num);
            let year = parseInt(yy);
            if (month > 12) {
                month = 1;
                year++;
            } else if (month < 1) {
                month = 12;
                year--;
            }
            return {
                month,
                year,
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.signin-btn {
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a29b12727124690024099.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 400rpx;
    height: 112rpx;
    margin: 32rpx auto 0;
}

.all {
    border-radius: 0 0 16rpx 16rpx;

    .bottom-notice {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 22rpx;
        margin-top: 28rpx;

        .notice-text {
            height: 22rpx;
            font-size: 22rpx;
            font-weight: normal;
            line-height: 100%;
            text-align: center;
            margin-right: 8rpx;
            color: #a6a6a6;
        }
    }

    .bar {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 32rpx 0rpx;
        // padding: 10rpx;
        align-items: center;

        .previous {
            width: 46rpx;
            height: 46rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202307/333053-1689574488935.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .date {
            font-size: 32rpx;
            font-weight: 500;
            color: $text-color-regular;
            line-height: 42rpx;
        }

        .next {
            width: 46rpx;
            height: 46rpx;
            background-image: url('https://wpm-cdn.dreame.tech/images/202307/565403-1689574576427.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
    }
}

.bar .barbtn {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
}

.all .week-area {
    display: flex;
    justify-content: space-between;
    margin: 32rpx auto 16rpx;
    flex-wrap: wrap;
}

.all .week-txt {
    text-align: center;
    font-size: 20rpx;
    font-family: PingFang SC-常规体, PingFang SC;
    font-weight: 400;
    color: $text-color-disable;
    line-height: 27rpx;
    width: 14%;
}

.myDateTable {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    // 九宫格布局容器
    .grid-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .top-row,
        .bottom-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            gap: 10rpx;
        }

        .middle-row {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }
    }

    // 今日签到特殊样式
    .dateCell-today {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        overflow: hidden;

        .cell-today {
            display: flex;
            flex-direction: column;
            height: 200rpx;
            width: 580rpx;
            align-items: center;
            border-radius: 28rpx;
            position: relative;
            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889db393dd502530011913.png');
            background-repeat: no-repeat;
            background-position: right 20rpx bottom 20rpx;
            background-size: 244rpx 162rpx;
            background-color: #fff;
            overflow: hidden;

            .cell_info {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .right-big-icon {
                width: 301rpx;
                height: 169rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689edad6c36218000030692.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                position: absolute;
                right: 0;
                top: 33rpx;
            }

            .signinactiveStyle-today {
                height: 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889e17a1edbd1260011625.png'),
                //     linear-gradient(316deg, #ffb547 13%, #ff8826 43%, #fd5922 72%);
                // background-repeat: no-repeat;
                // background-size: 244rpx 162rpx, 100% 100%;
                // background-position: right 20rpx bottom 20rpx, 0 0;
                background-color: transparent;

                .haveSignStyle-today {
                    width: 100%;
                    height: 100%;
                    padding-left: 48rpx;
                    font-size: 24rpx;
                    font-weight: normal;
                    color: #777777;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }

                .smile-today {
                    font-size: 56rpx;
                    font-weight: 600;
                    color: #ff2300;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
            }

            .lackinactiveStyle-today {
                height: 140rpx;
                border-radius: 20rpx;
                opacity: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(316deg, #ffb547 13%, #ff8826 43%, #fd5922 72%);

                .lackSignStyle-today {
                    width: 120rpx;
                    height: 140rpx;
                    border-radius: 20rpx;
                    opacity: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 3rpx solid $fill-color-dark;
                    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a6f5bed05a9710011497.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    background-color: transparent;
                    position: relative;

                    .lackPointStyle-today {
                        font-size: 36rpx;
                        font-family: PingFang SC-常规体, PingFang SC;
                        font-weight: 600;
                        color: #ffffff;
                        line-height: 50rpx;
                        margin-bottom: 40rpx;
                        position: relative;
                        z-index: 2;
                    }
                }
            }

            // .afterStyle-today {
            //     height: 140rpx;
            //     border-radius: 20rpx;
            //     opacity: 1;
            //     display: flex;
            //     align-items: center;
            //     justify-content: center;

            //     .inactiveStyle-today {
            //         width: 120rpx;
            //         height: 140rpx;
            //         background-image: url("https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a6f5bed05a9710011497.png");
            //         background-repeat: no-repeat;
            //         background-size: 100% 100%;
            //         background-color: transparent;
            //         border-radius: 20rpx;
            //         opacity: 1;
            //         display: flex;
            //         align-items: center;
            //         justify-content: center;
            //         border: 3rpx solid $fill-color-dark;

            //         .pointStyle-today {
            //             font-size: 36rpx;
            //             font-family: PingFang SC-常规体, PingFang SC;
            //             font-weight: 600;
            //             color: #FFFFFF;
            //             line-height: 50rpx;
            //             margin-bottom: 40rpx;
            //             position: relative;
            //             z-index: 2;
            //         }
            //     }
            // }

            .afterStyle-today {
                height: 100%;
                background-color: #fff !important;
                justify-content: center;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889dcbe723ae4680011184.png');
                background-repeat: no-repeat;
                background-size: 120rpx 80rpx;
                background-position: 50% 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 16rpx 0;
                justify-content: space-between;

                .lack_data {
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: 32rpx;
                    color: #777777;
                }

                .lack_text {
                    font-size: 36rpx;
                    font-weight: 600;
                    color: #a6a6a6;
                }
            }

            .singalDate-today {
                height: 60rpx;
                font-size: 24rpx;
                font-family: PingFang SC-常规体, PingFang SC;
                font-weight: 600;
                color: $text-color-disable;
                line-height: 60rpx;
                margin-top: 15rpx;
                text-align: center;
            }

            .singalDate_default-today {
                width: 120rpx;
                background: linear-gradient(122deg, #ff8d50 7%, #f9580d 52%, #ff2e20 96%);
                border-radius: 100rpx;
                display: flex;
                text-align: center;
                align-items: center;
                margin: 15rpx auto 0;
                justify-content: center;
                font-size: 24rpx;
                font-family: PingFang SC-常规体, PingFang SC;
                font-weight: 600;
                color: #fff;
                line-height: 32rpx;
            }
        }
    }
}

.dateCell .whiteColor {
    color: #fff;
}

.greenColor {
    color: #01b90b;
    font-weight: bold;
}

.bgWhite {
    background-color: #fff;
}

.bgGray {
    background-color: rgba(255, 255, 255, 0.42);
}

.bgBlue {
    font-size: 14px;
    background-color: #4b95e6;
}

.redColor {
    color: #ff0000;
}

.outSignStyle {
    border: 1px solid #ffffff;
    color: #ffffff;
}

.redDot {
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: red;
}
</style>
