<template>
  <view>
    <view class="container" :style="{ 'padding-top': pagePaddingContent + 'rpx' }">
      <APPSHARE :link="link"></APPSHARE>
      <!-- <u-status-bar /> -->
      <!-- <view class="header" > v-if="$isInApp()" -->
      <view class="header" v-if="$isInApp()">
        <custom-bar2 :zIndex="10" background="#F4F4F4" :customBack="true" @back="handleCancel()"
          :BackIcon="'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68948336b89f97560010818.png'">
          <template #customTitle>
            <view class="header_right_item">
              <view class="header_right_item_image_box">
                <image class="header_right_item_image" :src="storeDetail.store_photo" />
                <view class="header_right_item_image_cover"></view>
              </view>
              <text class="header_right_item_text">{{ storeDetail.store_name || '我的小店' }}</text>
            </view>
          </template>

          <template #moreBtn>
            <view class="header_right_more">
              <view class="header_right_rule" @click="openRulePopup"> </view>
              <view class="header_right_share" @click="handleShareClick(null, 'active')" v-show="list &&
                list[0] &&
                list[0].list &&
                list[0].list.length > 0
                ">
              </view>
            </view>
          </template>
        </custom-bar2>
      </view>

      <!-- 搜索框 -->
      <view class="search-box">
        <u--input class="search-input" placeholder="X60新品首发，上下水热销扫地机！" prefixIcon="search" border="none"
          prefixIconStyle="font-size: 22px;color: #A6A6A6;margin-left:24rpx;" v-model="searchText"
          @input="inputChange"></u--input>
        <view class="search-cancel" v-if="searchText" @click="cancelSearch">取消</view>
      </view>
      <!-- 店铺数据 -->
      <view class="store-data-container">
        <view class="store-data-header">
          <text class="store-data-title">店铺数据</text>
          <view class="store-data-detail" @click="handleOrderDetail">
            <text>订单详情</text>
            <image
              src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d4627589bd3630011890.png" />
          </view>
        </view>
        <view class="store-data-content">
          <view class="store-data-item">
            <text class="item-title">成交金额(元)</text>
            <text class="item-value">{{ storeStatistics.actual_money || 0.0 }}</text>
          </view>
          <view class="line"></view>
          <view class="store-data-item">
            <text class="item-title">订单量(个)</text>
            <text class="item-value">{{ storeStatistics.actual_num || 0 }}</text>
          </view>
          <view class="line"></view>
          <view class="store-data-item">
            <text class="item-title">结算佣金(元)</text>
            <text class="item-value">{{ storeStatistics.actual_commission || 0.0 }}</text>
          </view>
        </view>
      </view>
      <!-- 状态导航 -->
      <view class="status-tabs-box">
        <view class="status-tab" :class="{ curStatusTab: swiperCurrent === index }"
          v-for="(item, index) in statusTabList" :key="index" @click="changeStatusTab(index)">
          <text>{{ item.name }}</text>
          <text>{{ item.count }}</text>
        </view>
      </view>
      <!-- 排序 -->
      <view class="sort-container" @click="showSort = true">
        <text>{{ sortOptions[selectedSort].name }}</text>
        <image
          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b28ce850195450124710.png" />
      </view>
      <swiper class="swiper-box" :current="swiperCurrent" @change="swiperChange" @animationfinish="animationfinish">
        <block v-for="(item, index) in statusTabList" :key="index">
          <block v-if="index === 0">
            <swiper-item class="swiper-item swiper-box-has-data">
              <scroll-view scroll-y class="swiper-item-view" :scroll-top="scrollTop" @scroll="handleScroll"
                refresher-background="#f4f4f4" :refresher-enabled="canPullDown" :upper-threshold="0"
                :refresher-triggered="isRefreshing" @refresherrefresh="refresherrefresh" @scrolltolower="onreachBottom"
                @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
                <view class="page-box" :style="{ 'padding-bottom': '204rpx' }" v-if="
                  listData &&
                  listData[index] &&
                  listData[index].list &&
                  listData[index].list.length > 0
                ">
                  <!-- <GoodsLists :Item="item" @navToDetail="navToDetail"
                                      @shareGoods="handleShareClick(item, 'default')" /> -->
                  <u-swipe-action :key="`swipe-${item.gid}`" v-if="isUpdate" :autoClose="true">
                    <u-swipe-action-item :disabled="true" style="margin-bottom: 16rpx; border-radius: 24rpx"
                      v-for="item in listData[index].list" :key="item.order_no" :options="options1">
                      <GoodsLists :Item="item" :isSale="true" @navToDetail="navToDetail" @off="handleTip(0, item)"
                        @deleteGoods="handleTip(1, item)" @shareGoods="handleShareClick(item, 'default')" />
                    </u-swipe-action-item>
                  </u-swipe-action>
                </view>
                <view class="empty_node u-flex-col u-col-center" v-if="
                  (listData &&
                    listData[index] &&
                    listData[index].list &&
                    listData[index].list.length == 0) ||
                  (listData && listData[index] && !listData[index].list)
                ">

                  <image class="image" mode="widthFix"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689493314dcf13190018343.png">
                  </image>
                  <view class="empty_text">暂无商品, 快去选购吧~</view>
                  <!-- <view class="get_goods" hover-class="btn-hover" @click="addGoodsInfo()">去添加</view> -->
                </view>
              </scroll-view>
            </swiper-item>
          </block>
          <block v-else>
            <swiper-item class="swiper-item swiper-box-has-data">
              <scroll-view scroll-y class="swiper-item-view-order" :scroll-top="scrollTop" @scroll="handleScroll"
                refresher-background="#f4f4f4" :refresher-enabled="canPullDown" :upper-threshold="0"
                :refresher-triggered="isRefreshing" @refresherrefresh="refresherrefresh" @scrolltolower="onreachBottom"
                @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">

                <view class="page-box" :style="{ 'padding-bottom': '204rpx' }" v-if="
                  listData &&
                  listData[index] &&
                  listData[index].list &&
                  listData[index].list.length > 0
                ">
                  <!-- <GoodsLists :Item="item" @navToDetail="navToDetail"
                                      @shareGoods="handleShareClick(item, 'default')" /> -->
                  <u-swipe-action :key="`swipe-${item.gid}`" v-if="isUpdate" :autoClose="true">
                    <u-swipe-action-item :disabled="true" style="margin-bottom: 16rpx; border-radius: 24rpx"
                      v-for="item in listData[index].list" :key="item.order_no" :options="options1">
                      <GoodsLists :Item="item" :isSale="false" @navToDetail="navToDetail" @up="handleUp(item)"
                        @deleteGoods="handleTip(1, item)" />
                    </u-swipe-action-item>
                  </u-swipe-action>
                </view>
                <view class="empty_node u-flex-col u-col-center" v-if="
                  (listData &&
                    listData[index] &&
                    listData[index].list &&
                    listData[index].list.length == 0) ||
                  (listData && listData[index] && !listData[index].list)
                ">

                  <image class="image" mode="widthFix"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689493314dcf13190018343.png">
                  </image>
                  <view class="empty_text">暂无下架商品</view>
                  <!-- <view class="get_goods" hover-class="btn-hover" @click="navToHome()">去逛逛</view> -->
                </view>
              </scroll-view>
            </swiper-item>
          </block>
        </block>
      </swiper>
      <view style="height: 0">
        <PopReceipt v-if="showReceiptPop" :isShow.sync="showReceiptPop" :itemData="receiptData" />
        <!-- <PopOrderCancelItem :isShow.sync="showOcancelPop" :rData="orderCdata" @refresh="init" /> -->
      </view>
      <!-- <view class="addGoods">
              <image class="addGoodsIcon"
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68933b75098a90390010315.png"
                  ></image>
          </view> -->
      <view class="add_goods">
        <view class="add_goods_item" @click="addGoodsInfo">添加选品</view>
      </view>
      <custom-toast ref="customToast" />
      <!-- #ifdef MP-WEIXIN -->
      <privacy />

      <!-- #endif -->
    </view>
    <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="shareType" />
    <!-- 活动规则弹窗 -->
    <u-popup :show="showRulePopup" v-prevent-scroll="showRulePopup" mode="center" :round="18"
      :safe-area-inset-bottom="false" catchtouchmove>
      <view class="rule-popup-content">
        <view class="rule-popup-header">
          <view class="rule-title">
            <text class="rule-title-text">追觅小店规则</text>
          </view>
          <view class="rule-popup-close" @click="closeRulePopup">
            <view class="close-icon"></view>
          </view>
        </view>
        <scroll-view scroll-y class="rule-container">
          <view class="rule-content">
            <view class="rule-content-text">
              <view class="rule-section">
                <img class="rule-section-img"
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b2786648d84120081576.png"
                  alt="" />
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>

    <!-- 商品下架或删除 -->
    <CustomModal :show="showTipPopup" width="616rpx"
      contentStyle="color:#404040;font-weight:500;font-size: 32rpx;text-align: center;" :title="operContent.title"
      :content="operContent.tip" :confirmText="operContent.confirmText" :cancelText="operContent.cancelText"
      showCancelButton @confirm="handleTipBtn" @cancel="(showTipPopup = false), (operContent = { type: '' })">
    </CustomModal>
    <!-- 排序弹窗 -->
    <u-popup :show="showSort" :round="10" mode="top" :duration="0" bgColor="transparent"
      :overlay-style="{ inset: pagePaddingContent + 'rpx 0 0 0' }" @close="showSort = false"
      :customStyle="{ top: pagePaddingContent + 'rpx' }">
      <view class="sort-popup-content">
        <view class="sort-item" :class="{ selectedSort: selectedSort === index }" v-for="(item, index) in sortOptions"
          :key="index" @click="selectSort(index)">
          <text>{{ item.name }}</text>
          <image v-show="selectedSort === index"
            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689b3ca390dbe5930011883.png">
          </image>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { IOrderList, IUnionSource } from '@/store/interface/order';
import { storeGoodsList, salesCommission, salesStoreDetail, deleteOrder, setGoodsStatus } from '@/http/order';
import Utils from '@/common/Utils';
import TabsSwiper from '@/components/TabsSwiper/TabsSwiper.vue';
import OrderItem from './components/OrderItem/OrderItem.vue';
import PopReceipt from './components/PopReceipt/PopReceipt.vue';
import {
  getShareContent,
} from '@/http/goods'
// import PopOrderCancelItem from '../orderDetail/components/PopOrderCancelItem.vue';
import Constants from '@/common/Constants';
import CardList from './components/CardList.vue';
import GoodsLists from './components/List/List.vue';
import { AppModule } from '@/store/modules/app';
import { UserModule } from '@/store/modules/user';
import shareDialog from '@/components/shareDialog/shareDialog.vue';
import { CheckAppJump } from '@/common/decorators';
import { GoodsModule } from '@/store/modules/goods';
export interface OperContent {
  type: 'follow' | 'block' | '';
  title?: string;
  tip?: '商品再次上架售卖，需要重新提交审核' | '商品删除后，需要重新提交审核' | '';
  confirmText?: '下架' | '删除' | '';
  cancelText?: '取消' | '不保存' | '';
}
@Component({
  components: {
    TabsSwiper,
    OrderItem,
    PopReceipt,
    CardList,
    GoodsLists,
    shareDialog,
    // PopOrderCancelItem,
  },
})
export default class order extends Vue {
  $refs!: {
    tabs: any;
    [key: string]: any;
  };

  public showTipPopup: Boolean = false;
  public shareContentList: Array<any> = [];
  public showSort: Boolean = false;
  public selectedSort: number = 0;
  public showRulePopup: Boolean = false;
  public isRefreshing: Boolean = false;
  public isUpdate: Boolean = true;
  public timer: any = null;
  public pagesTitle: string = '我的店铺';
  private orderStatusArr: Array<number> = [
    Number(Constants.ORDER_STATUS_WAIT_PAY),
    -1,
    Number(Constants.ORDER_STATUS_WAIT_SEND),
    Number(Constants.ORDER_STATUS_TAKE_GOODS),
    Number(Constants.ORDER_STATUS_FINISH),
  ]; // 订单状态

  private orderData: {
    list: Array<IOrderList>;
    pages: number;
  };

  public searchText: String = ''; // 搜索内容
  public upStatus: number = 0; // 上架状态
  public tabList: Array<any> = [{ name: '小店商品' }, { name: '小店订单' }];
  public sortOptions: Array<any> = [
    { id: 1, name: '最新上架在上' },
    { id: 2, name: '最新上架在下' },
    { id: 3, name: '价格从高到低' },
    { id: 4, name: '价格从低到高' },
  ];

  public statusTabList: Array<any> = [
    { id: 0, name: '售卖中', count: 0 },
    { id: 1, name: '已下架', count: 0 },
  ];

  public operContent: OperContent = {
    type: '',
    title: '',
    tip: '',
    confirmText: '',
    cancelText: '',
  };

  public tipContent: Array<any> = [
    {
      title: '确认下架商品',
      tip: '商品再次上架售卖，需要重新提交审核',
      confirmText: '下架',
    },
    {
      title: '确认删除该商品吗？',
      tip: '商品删除后，需要重新提交审核',
      confirmText: '删除',
    },
  ];

  public goodsItem: any = {};
  public tipIndex: number = 0;
  public show: Boolean = false;
  public isLoaded: boolean = false;
  public listData: any = [];
  public current: Number = 0;
  public startX: any = 0;
  public startY: any = 0;
  public canPullDown: boolean = true;
  public isLeftMove: boolean = true;
  public storeDetail: any = {};
  public storeStatistics: any = {};
  public swiperCurrent: number = 0;
  public scrollTop: Number = 0;
  public shareType: string = 'default';
  public productInfo: any = {
    name: '',
    desc: '',
    image: '',
    imageBg: '',
    price: '',
  };

  public options1 = [
    {
      text: '下架',
      style: {
        backgroundColor: '#f56c6c',
      },
    },
  ];

  public old: any = { scrollTop: 0 };
  public goodsData: any = {};
  public link: any = '';
  public storekeeper: any = '0';

  public finished: Boolean = false; // 是否加载完成
  public page: number = 0; // 由于分页一般页面要+1，所以为了方便分页查询，这里初始设置页码为0
  public showReceiptPop: Boolean = false; // 展示确认收货弹窗
  public showOcancelPop: Boolean = false; // 展示取消订单弹窗
  public orderCdata: Object = { type: 1, order_no: '', deposit_order_no: '' }; // 取消订单信息
  public receiptData: Object = null; // 确认收货数据
  public barStyle: Object = { bottom: '6rpx', backgroundColor: '#7473C5' };
  public timerDelay: any = null;
  public list: any = [];

  get pagePaddingTop(): number {
    return AppModule.pagePaddingTop;
  }

  get pagePaddingContent(): number {
    return Number(AppModule.pagePaddingTop) + 10;
  }

  get swiperBoxStyle(): Object {
    return { height: `100vh` };
  }

  get userInfo(): any {
    return UserModule.userInfo;
  }

  get user_id(): any {
    return UserModule.user_id || 12507;
  }

  inputChange(e) {
    clearTimeout(this.timerDelay);
    this.timerDelay = setTimeout(() => {
      this.searchText = e;
      this._initPageData();
      this.listData = [];
      this._getList();
    }, 200);
  }

  cancelSearch() {
    this.searchText = '';
  }

  handleUp(item) {
    this.goodsItem = item;
    this.setGoodsStatus(0, this.goodsItem);
  }

  // 下架
  // index:0 下架
  // index:1 删除
  handleTip(index, item) {
    this.tipIndex = index;
    this.operContent = this.tipContent[index];
    this.goodsItem = item;
    this.showTipPopup = true;
  }

  handleTipBtn() {
    if (this.tipIndex === 0) {
      this.setGoodsStatus(1, this.goodsItem);
    } else {
      this.handleDelete(this.goodsItem);
      //    调删除接口
    }
    this.showTipPopup = false;
  }

  selectSort(index) {
    this.selectedSort = index;
    this.showSort = false;
    this.init();
  }

  handleOrderDetail() {
    Utils.navigateTo('/pagesC/shopStore/orderDeatil');
  }

  async setGoodsStatus(status, item) {
    try {
      await setGoodsStatus({
        goods_ids: item.gid,
        status: status,
      });
      this.init();
    } catch (error) {
      console.error('setGoodsStatus error:', error);
    }
  }

  async handleDelete(item) {
    // data 包含被点击的选项信息
    console.log('handleDelete', item);
    try {
      await deleteOrder({
        goods_id: item.gid,
      });
      // 删除成功后，重新初始化数据
      await this.init();
      // 等待下一个tick，确保组件状态被正确重置
      await this.$nextTick();
      this.isUpdate = false;
      this.$nextTick(() => {
        this.isUpdate = true;
      });
      const goodsList = [...GoodsModule.linkGoodsList];
      // 从 goodsList 中查找并删除 gid 和 item.gid 相同的项
      const idx = goodsList.findIndex((g) => g.gid === item.gid);
      if (idx !== -1) {
        goodsList.splice(idx, 1);
      }
      GoodsModule.setLinkGoodsList(goodsList);

      // 可以添加成功提示
      // Utils.Toast('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      // 可以在这里添加错误提示
      // Utils.Toast('删除失败');
    }
  }

  changeStatusTab(index) {
    this.upStatus = this.statusTabList[index].id;
    this.swiperCurrent = index;
  }

  openRulePopup() {
    this.showRulePopup = true;
  }

  closeRulePopup() {
    this.showRulePopup = false;
  }

  refresherrefresh() {
    this.isRefreshing = true;
    clearTimeout(this.timer);
    this.timer = setTimeout(async () => {
      this.init();
      this.isRefreshing = false;
    }, 200);
  }

  handleUpdateGoodsList() {
    Utils.Toast('商品上架成功');
  }

  onLoad(option) {
    uni.$on('HANDLE_RECEIPT_POP', this.handleReceiptPop);
    uni.$on('HANDLE_ORDER_CANCEL_POP', this.handleOrderCancelPop);
    uni.$on('updateOrderList', this.handleUpdateOrderList);
    uni.$on('updateGoodsList', this.handleUpdateGoodsList);
    this.handleStoreDetail();
    this.link = option.link;
    this.storekeeper = option.storekeeper;
    this.handleSalesCommission();
    if (option.Index) {
      return this.changeIndexFormMine(option.Index);
    }
  }

  onUnLoad() {
    uni.$off('HANDLE_RECEIPT_POP');
    uni.$off('HANDLE_ORDER_CANCEL_POP');
    uni.$off('updateOrderList');
    uni.$off('updateGoodsList');
  }

  onBackPress(event) {
    if (event.from === 'backbutton') {
      if (this.showReceiptPop) {
        this.showReceiptPop = false;
      } else {
        Utils.goBack();
      }
      return true;
    }
    return false;
  }

  onShow() {
    this.init();
  }

  beforeDestroy() {
    uni.$off('HANDLE_RECEIPT_POP');
    uni.$off('HANDLE_ORDER_CANCEL_POP');
  }

  async init() {
    this._initPageData();
    this.getStoreGoodsCount();
    this.handleSalesCommission();
    await this._getListFactory();
  }

  handleUpdateOrderList() {
    this.page = 1;
    this.finished = false;
    this._getList();
  }

  changeIndexFormMine(index) {
    this.current = index;
    this.swiperCurrent = index;
  }

  // 初始化页面的数据
  _initPageData() {
    this.finished = false;
    this.page = 0;
    if (this.listData && this.listData.length === 0) {
      this.listData = [];
    }
  }

  handleCancel() {
    Utils.goBack();
  }

    // 跳转详情
    @CheckAppJump()
    navToDetail(item) {
        const { gid, name } = item;
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
                id: Number(gid),
                name: name,
            },
            true,
        );
        Utils.reportEvent('product_click', { product_id: gid });
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}&storekeeper=${this.user_id}&from=shopStore&unionSource=${IUnionSource.DREAME_SMALL_SHOP}`);
    }

  // 分享商品
  shareGoods(item) {
    // uni.showShareMenu({
    //     withShareTicket: true,
    //     menus: ['shareAppMessage', 'shareTimeline'],
    // })
  }

  onreachBottom() {
    this._getListFactory();
  }

  @CheckAppJump()
  navToHome() {
    // #ifdef H5
    Utils.messageChannel('navigation', { type: 'home', path: 'home/h5' });
    // #endif

    // #ifdef MP-WEIXIN
    uni.switchTab({ url: '/pages/shop/shop' });
    // #endif
  }

  touchStart(e) {
    this.startX = e.touches[0].pageX;
    this.startY = e.touches[0].pageY;
  }

  @CheckAppJump()
  addGoodsInfo() {
    // Utils.goBack()
    GoodsModule.setLinkGoodsList([]);
    uni.redirectTo({ url: '/pagesC/shopProduct/shop' + '?is_selected=1' })
  }

  async handleStoreDetail() {
    try {
      const res = await salesStoreDetail({ store_id: this.user_id });
      this.storeDetail = res;
    } catch (e) {
      console.error('handleSalesCommission e=', e);
      this.storeDetail = {};
    }
  }

  async handleSalesCommission() {
    try {
      const res = await salesCommission();
      this.storeStatistics = res;
    } catch (e) {
      console.error('handleSalesCommission e=', e);
      this.storeStatistics = {};
    }
  }

  touchMove(e) {
    if (!this.isLeftMove) return;
    const moveX = e.touches[0].pageX;
    const moveY = e.touches[0].pageY;
    const diffX = moveX - this.startX;
    const diffY = moveY - this.startY;

    // 如果是左右滑动，禁用下拉刷新
    if (Math.abs(diffX) > Math.abs(diffY)) {
      this.canPullDown = false;
    } else {
      this.isLeftMove = false;
    }
  }

  touchEnd() {
    // 在滑动结束后，启用下拉刷新
    this.canPullDown = true;
    this.isLeftMove = true;
  }

  async _getListFactory() {
    // 如果已经全部加载完成直接终止执行
    if (this.finished) return;
    this.page++;
    await this._getList();
  }

  // async getStoreGoodsList() {
  //     try {
  //         const res = await storeGoodsList({
  //             page: this.page,
  //             page_size: 20,
  //         });
  //         if (res.code == 0) {
  //             console.log(res.data.list);
  //             // this.goodsList = res.data.list;
  //         }
  //     } catch (e) {
  //         console.error('getStoreGoodsList e=', e);
  //     }
  // }
  // 获得上下架商品数量
  async getStoreGoodsCount() {
    const updata = await storeGoodsList({ page: 1, store_id: this.user_id, status: 0 }, false);
    const offdata = await storeGoodsList({ page: 1, store_id: this.user_id, status: 1 }, false);
    this.statusTabList[0].count = updata.total || 0;
    this.statusTabList[1].count = offdata.total || 0;
  }

  // 获取列表数据
  async _getList() {
    try {
      const lastSwiperIndex = this.swiperCurrent;
      const page: string | number = this.page;
      const page_size: string | number = 10;
      this.orderData = await storeGoodsList({
        page,
        store_id: this.user_id,
        page_size,
        name: this.searchText,
        order_type: this.sortOptions[this.selectedSort].id,
        status: this.upStatus,
      });
      console.log('this.orderData', this.orderData);
      if (this.page > this.orderData.pages) {
        this.finished = true;
        Utils.Toast('没有更多了');
        return;
      }
      // if (status != -1) {
      //     this.orderData.list = this.handleListFilter(this.orderData.list);
      // }
      const _list = this.orderData.list || [];
      if (lastSwiperIndex !== this.swiperCurrent) return;
      if (page > 1) {
        this.listData[this.swiperCurrent].list = this.listData[this.swiperCurrent].list.concat(_list);
      } else {
        this.$set(this.listData, this.swiperCurrent, this.orderData || []);
      }
      if (!this.searchText) {
        this.list = this.listData;
      }
    } catch (e) {
      console.error('_getList e=', e);
    }
  }

  handleListFilter(list) {
    // 过滤退款中状态数据
    let data = [];
    if (list) {
      data = list.filter((item) => item.status != Constants.ORDER_STATUS_REFUNDING);
    }
    return data;
  }

  // tabs通知swiper切换
  tabsChange(index) {
    this.swiperCurrent = index;
    this.scrollGoTop();
  }

  swiperChange(e) {
    console.log('swiperChange', e);
    const current = e.detail.current;
    this.swiperCurrent = current;
    this.upStatus = this.statusTabList[current].id;
    this.init();
  }

  animationfinish(e) {
    const current: number = e.detail.current;
    // (this.$refs.tabs as any).setFinishCurrent(current);
    this.swiperCurrent = current;
    this.current = current;
  }

  // 回到顶部
  scrollGoTop() {
    this.scrollTop = this.old.scrollTop;
    this.$nextTick(() => {
      this.scrollTop = 0;
    });
  }

  handleScroll(e) {
    this.old.scrollTop = e.detail.scrollTop;
  }

  // 开启/关闭确认收货弹窗
  handleReceiptPop(data) {
    if (!data.order_no) {
      this.receiptData = null;
      return;
    }
    this.receiptData = data;
    this.showReceiptPop = true;
  }

  // 开启取消订单弹窗
  handleOrderCancelPop(data) {
    this.orderCdata = data;
    this.showOcancelPop = true;
  }

  /* 分享商品 */
  @CheckAppJump()
  async handleShareClick(item, type) {
    // if (UserModule.sdkVersion < 13) {
    //   Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
    //   return;
    // }
    this.show = true;
    this.shareType = type; // active default
    this.goodsData = item || this.list[0].list[Math.floor(Math.random() * this.list[0].list.length)];
    console.log('this.goodsData', this.goodsData);
    const shareImage =
      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png';
    console.log(item, type, this.goodsData);
    const { name, introduce, cover_image, price } = this.goodsData;
    this.productInfo = {
      name: name,
      desc: introduce,
      image: cover_image,
      price: price,
      imageBg: type === 'active' ? shareImage : '',
    };
  }

  async getShareContentData() {
    const res = await getShareContent({ tid: this.goodsData.tids.length > 0 ? this.goodsData.tids[0] : -1 });
    this.shareContentList = res;
  }

  async handleShare(type: 'web' | 'image') {
    const pagePath =
      this.shareType == 'active'
        ? `/pagesC/shopStore/shareProduct?storekeeper=${this.user_id}`
        : `/pagesB/goodsDetail/goodsDetail?gid=${this.goodsData.gid}&storekeeper=${this.user_id}`;
    const path = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0`;
    this.show = false;
    if (this.shareType === 'active') {
      Utils.cardShare(type)(
        {
          target: 'wechat,weixin_circle,qq,sina,image_template,download',
          link: `${path}#${pagePath}`,
          jumpLink: `${path}#${pagePath}`,
        },
        {
          content: {
            url: `${path}#${pagePath}`,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ab08b3fbdf2612701195.png' + '?x-oss-process=image/resize,w_200',
            title: `【${this.storeDetail.nickname}】这是我开的追觅小店，快进来看看吧！`,
            desc: '这是我精选的追觅商品清单，快进来看看吧！',
          },
          extras: {
            type: 'activity',
            id: this.goodsData.gid,
            goods: this.productInfo,
            copyText: `${this.storeDetail.nickname}这是我开的追觅小店，快进来看看吧！精选追觅全球好物清单，尽在追觅小店！`,
          },
        },
      );
    } else {
      await this.getShareContentData();
      Utils.cardShare(type, 'goods')(
        {
          target: 'image_template,download,wechat,weixin_circle,qq,sina',
          link: `${path}#${pagePath}`,
          jumpLink: `${path}#${pagePath}`,
        },
        {
          content: {
            url: `${path}#${pagePath}`,
            image: this.goodsData.cover_image + '?x-oss-process=image/resize,w_200',
            title: `【${this.goodsData.price}元】 ${this.goodsData.name}`,
            desc:
              this.goodsData.introduce ||
              this.shareContentList[Math.floor(Math.random() * this.shareContentList.length)].value,
          },
          extras: {
            type: 'goods',
            id: this.goodsData.gid,
            goods: {
              name: this.goodsData.name,
              desc: this.goodsData.introduce,
              image: this.goodsData.cover_image,
              price: this.goodsData.price,
              priceColor: '#FF1F0E',
              descColor: '#C59245',
            },
            copyText: `${this.goodsData.price}元, ${this.goodsData.name}`,
          },
        },
      );
            Utils.taskComplete('shareShopGoodsFriend');
    }
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
