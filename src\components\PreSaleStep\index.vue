<template>
  <view>
    <view class="deposit-position">
      <view v-if="depositTip" class="deposit-tip-bg"></view>
      <view v-if="depositTip" class="deposit-tip">{{ depositTip }}</view>
    </view>
    <view class="pre-step u-flex u-row-left" :style="{ height }">
      <view class="line">
        <image mode="heightFix" :src="lineImage"></image>
      </view>
      <view class="step-list u-flex-col u-col-left u-row-between">
        <view class="step step-1">
          <view class="label">{{ step1.label }} </view>
          <view class="num">￥{{ step1.num }}</view>
          <view class="extra" :class="{ highlight: isStepExtra1Highlight }">{{ stepExtra1 }}
          </view>
        </view>
        <view class="step step-2" :style="{ marginTop: step2MarginTop }">
          <view class="label">{{ step2.label }} </view>
          <view class="num">￥{{ step2.num }}</view>
          <view v-if="isShowExtra" class="extra1">{{ stepExtra2 }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { IPresale } from '@/http/interface/IPresale';

const lineImageList = {
  '-1': 'https://wpm-cdn.dreame.tech/images/202306/296112-1687249700776.png',
  0: 'https://wpm-cdn.dreame.tech/images/202303/64001473677a04233769335.png',
  1: 'https://wpm-cdn.dreame.tech/images/202303/64001473980ed6223769310.png',
  2: 'https://wpm-cdn.dreame.tech/images/202303/6400147396a606173389289.png',
  3: 'https://wpm-cdn.dreame.tech/images/202303/640014736e0424503423352.png',
};
@Component
export default class PreSaleStep extends Vue {
  @Prop({
    type: Object,
    default: () => ({
      coefficient: 0,
      deposit: '0.00',
      deposit_status: 0,
      end_payment: 0,
      expand_price: '0.00',
      presale_time: 0,
      scheduled_number_now: 0,
      start_payment: 0,
      surplus_time: 0,
      tail_order_status: 0,
      tail_price: '00.00',
    }),
  })
  readonly presaleInfo: IPresale;

  @Prop({ type: String, default: '80rpx' })
  readonly height!: string;

  @Prop({ type: Boolean, default: true })
  readonly isShowExtra!: boolean;

  @Prop({ type: Number, default: 1 })
  readonly step2lineNum!: number;

  public stepStatus: string = '-1';
  public isStepExtra1Highlight: Boolean = false;
  public step1: any = {
    label: '定金 ',
    num: 0,
    extra: '',
  };

  public step2: any = {
    label: '尾款 ',
    num: 0,
    extra: '',
  };

  get lineImage(): String {
    return lineImageList[this.stepStatus];
  }

  get step2MarginTop(): String {
    return (this.step2lineNum - 1) * 38 + 'rpx';
  }

  get stepExtra1(): String {
    let stepExtra1 = '';
    switch (this.stepStatus) {
      case '-1':
        stepExtra1 = '';
        break;
      case '0':
        stepExtra1 =
          uni.$u.timeFormat(this.presaleInfo.presale_time, 'mm.dd hh:MM') +
          '截止';
        break;
      case '1':
        if (
          this.presaleInfo.deposit_status &&
          Number(this.presaleInfo.deposit) <
          Number(this.presaleInfo.expand_price)
        ) {
          stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
          this.isStepExtra1Highlight = true;
        } else {
          stepExtra1 = '';
        }
        break;
      case '2':
        if (
          this.presaleInfo.deposit_status &&
          Number(this.presaleInfo.deposit) <
          Number(this.presaleInfo.expand_price)
        ) {
          stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
          this.isStepExtra1Highlight = true;
        } else {
          stepExtra1 = '';
        }
        break;
      case '3':
        if (
          this.presaleInfo.deposit_status &&
          Number(this.presaleInfo.deposit) <
          Number(this.presaleInfo.expand_price)
        ) {
          stepExtra1 = `定金已膨胀至￥${this.presaleInfo.expand_price}`;
          this.isStepExtra1Highlight = true;
        } else {
          stepExtra1 = '';
        }
        break;
      default:
        stepExtra1 = '';
        break;
    }
    return stepExtra1;
  }

  get stepExtra2(): String {
    let stepExtra2 = '';
    switch (this.stepStatus) {
      case '-1':
        stepExtra2 = '';
        break;
      case '0':
        stepExtra2 = `请在${uni.$u.timeFormat(
          this.presaleInfo.end_payment,
          'mm.dd hh:MM'
        )}点前完成尾款支付，可享受积分优惠`;
        break;
      case '1':
        stepExtra2 = `请在${uni.$u.timeFormat(
          this.presaleInfo.end_payment,
          'mm.dd hh:MM'
        )}点前完成尾款支付，可享受积分优惠`;
        break;
      case '2':
        stepExtra2 = `请在${uni.$u.timeFormat(
          this.presaleInfo.end_payment,
          'mm.dd hh:MM'
        )}点前完成尾款支付，可享受积分优惠`;
        break;
      default:
        stepExtra2 = '';
        break;
    }
    return stepExtra2;
  }

  get depositTip(): String {
    if (this.stepStatus == '-1') {
      return `${uni.$u.timeFormat(
        this.presaleInfo.presale_time,
        'mm-dd hh:MM'
      )}点前支付定金`;
    }
    return '';
  }

  @Watch('presaleInfo', { immediate: true, deep: true })
  genPresaleData() {
    this.step1.num = this.presaleInfo.deposit;
    this.step2.num = this.presaleInfo.tail_price;
    const { deposit_status, tail_order_status, can_pay_deposit, can_pay_tail } = this.presaleInfo;
    if (can_pay_deposit === undefined) {
      this.stepStatus = '-1';
    } else if (deposit_status === 0) {
      this.stepStatus = '0';
    } else if (deposit_status === 1 && tail_order_status === 0 && can_pay_tail === 0) {
      this.stepStatus = '1';
    } else if (deposit_status === 1 && tail_order_status === 0 && can_pay_tail === 1) {
      this.stepStatus = '2';
    } else {
      this.stepStatus = '3';
    }
  }
}
</script>
<style lang="scss" scoped>
.step-content {
  display: flex;
  justify-content: flex-end
}

.deposit-tip {
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.deposit-position {
  position: relative;
  // margin-top: 16rpx;
}

.deposit-tip-bg {
  // max-width: 200rpx;
  // height: 38rpx;
  height: 38rpx;
  border-radius: 8rpx 0rpx 0rpx 8rpx;
  margin-bottom: 16rpx;
  font-size: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 500;
  color: $fill-color-bg-serch;
  line-height: 38rpx;
  -webkit-text-size-adjust: none;
  // white-space: nowrap;
  border: none;
}

.deposit-tip {
  white-space: nowrap;
  // height: 38rpx;
  border-radius: 8rpx 0rpx 0rpx 8rpx;
  top: 0rpx;
  left: 0rpx;
  position: absolute;
  font-size: 20rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 500;
  color: $fill-color-bg-serch;
  background: $fill-color-bg-buyNow-time;
  line-height: 38rpx;
  padding: 6rpx 18rpx;
  -webkit-text-size-adjust: none;
  border: none;
}

.pre-step {
  width: calc(100% - 174rpx);

  .line {
    height: 100%;
    padding: 8rpx 0;
    margin-top: 16rpx;

    image {
      height: 100%;
    }
  }

  .step-list {
    margin-top: 16rpx;
    height: 100%;
    margin-left: 12rpx;
    font-size: 24rpx;
    font-weight: 500;
    line-height: 34rpx;
    color: $text-color-regular;
    font-family: PingFang SC-Medium, PingFang SC;

    .step {
      display: flex;
      flex-wrap: nowrap;
      justify-content: flex-start;
      align-items: flex-start;
    }

    .label {
      min-width: 60rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: $text-color-primary;
    }

    .num {
      // color: $fill-color-primary-active;
      margin-right: 16rpx;
      color: $text-color-regular;
      // width: 64rpx;
      font-size: 24rpx;
      font-weight: 500;
      line-height: 34rpx;
    }

    .extra {
      flex: 1;
      // width: 306rpx;
      font-size: 24rpx;
      font-weight: 400;
      font-family: PingFang SC-Regular, PingFang SC;
      color: $func-color-danger-text;

      &.highlight {
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #f44e4e;
      }
    }

    .extra1 {
      flex: 1;
      // width: 306rpx;
      font-size: 24rpx;
      font-weight: 400;
      font-family: PingFang SC-Regular, PingFang SC;
      color: $text-color-disable;

      &.highlight {
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #f44e4e;
      }
    }

    .twoline {
      margin-top: 38rpx;
    }
  }
}</style>
