<template>
  <view class="oneYuanFlashSale_container" v-if="loading">
      <view class="oneYuanFlashSale_content" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
          <view class="header" :style="{ top: statusBarHeight + 'rpx' }">
              <view class="left">
                  <image
                      class="search_back"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc328c9a8e8260014034.png"
                      @click="goBack"
                  ></image>
              </view>
              <!-- <view class="right">
                  <view class="rule">活动规则</view>
              </view> -->
          </view>
          <!-- 头图 -->
          <!-- <view class="header_image">
              <image class="header_image_img"
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a3fb82cf711840011358.png" />
          </view> -->

          <view class="content" :style="{ 'margin-top': 621 - statusBarHeight + 'rpx' }">
              <view class="content_title" @click.stop="showProducts">
                  <view class="content_img_wrap">
                      <image class="content_img" :src="basactiveInfo.goods_info.cover_image" mode="aspectFill" />
                  </view>
                  <view class="content_title_text">
                      <view class="content_title_text_title">{{ basactiveInfo.goods_info.show_name }}</view>
                      <view class="content_title_text_content">
                          <!-- 抢商品 -->
                          <view class="content_title_text_grab">
                              <!-- <image class="content_title_text_grab_img"
                                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a031332db22080011199.png" /> -->
                              <view class="content_title_text_grab_text"
                                  >优惠前: {{ Math.floor(basactiveInfo.goods_info.mprice) }}元</view
                              >
                              <view class="content_title_text_grab_bg">
                                  <view
                                      class="content_title_text_grab_bg_img"
                                      :class="{
                                          content_title_text_grab_bg_img_active:
                                              Number(basactiveInfo.invite_members) !=
                                              Number(basactiveInfo.required_invite_number),
                                      }"
                                      style="display: flex; align-items: center; justify-content: center"
                                  >
                                      <view
                                          style="
                                              font-size: 24rpx;
                                              font-weight: 600;
                                              height: 60rpx;
                                              line-height: 60rpx;
                                              display: flex;
                                              align-items: center;
                                          "
                                      >
                                          ¥</view
                                      >
                                      <view
                                          style="
                                              font-size: 38rpx;
                                              font-weight: 600;
                                              height: 60rpx;
                                              line-height: 60rpx;
                                              display: flex;
                                              align-items: center;
                                          "
                                      >
                                          {{ String(basactiveInfo.goods_info.price).split('.')[0] }}.
                                      </view>
                                      <view
                                          v-if="String(basactiveInfo.goods_info.price).indexOf('.') !== -1"
                                          style="
                                              font-size: 24rpx;
                                              font-weight: 600;
                                              height: 60rpx;
                                              line-height: 60rpx;
                                              display: flex;
                                              align-items: center;
                                              margin-top: 8rpx;
                                          "
                                      >
                                          {{ String(basactiveInfo.goods_info.price).split('.')[1] }}
                                      </view>
                                  </view>
                              </view>
                          </view>
                      </view>
                  </view>
              </view>
              <!-- 助力还在助力图 -->
              <view class="content_title_text_assist">
                  <view
                      style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          height: 114rpx;
                          padding: 0rpx 30rpx;
                      "
                  >
                      <image
                          style="height: 22rpx; width: 94rpx"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a08841797e0970011355.png"
                      />
                      <view
                          style="
                              margin-left: 16rpx;
                              margin: 0rpx 32rpx;
                              display: flex;
                              flex-direction: column;
                              align-items: center;
                              height: 86rpx;
                          "
                      >
                          <view
                              style="font-size: 36rpx; color: #ff0084; font-weight: 600"
                              v-if="
                                  Number(basactiveInfo.required_invite_number) -
                                      Number(basactiveInfo.invite_members) >
                                  0
                              "
                          >
                              还差{{
                                  Number(basactiveInfo.required_invite_number) - Number(basactiveInfo.invite_members)
                              }}名新用户加入</view
                          >
                          <view v-else style="font-size: 36rpx; color: #ff0084; font-weight: 600"
                              >已完成新用户助力</view
                          >
                          <view style="font-size: 22rpx; color: #ff0084; font-weight: 600; margin-top: 8rpx"
                              >即可享1元购资格
                          </view>
                      </view>
                      <image
                          style="height: 22rpx; width: 94rpx"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a08ae8d18c5780010587.png"
                      />
                  </view>
                  <view class="content_title_text_content_progresss">
                      <view class="progress">
                          <view class="progress-bar">
                              <view
                                  class="progress-inner"
                                  :style="{
                                      width:
                                          Math.min(
                                              (Number(basactiveInfo.invite_members) /
                                                  Number(basactiveInfo.required_invite_number)) *
                                                  100,
                                              100,
                                          ) + '%',
                                  }"
                              >
                              </view>
                          </view>
                      </view>
                      <view class="progress-text">助力进度 {{ progress }}% </view>
                  </view>
                  <!-- 助力人数 -->
                  <view class="content_title_text_assist_number">
                      <view class="content_title_text_assist_number_content">
                          <!-- 当人数小于等于3时，固定显示3个位置 -->
                          <view v-if="assistUsers.length <= 5" class="assist_users_fixed">
                              <view v-for="(user, index) in assistUsers" :key="index" class="assist_user_item">
                                  <image
                                      v-if="assistUsers[index]"
                                      class="assist_user_avatar"
                                      :src="
                                          assistUsers[index].avatar ||
                                          'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png'
                                      "
                                  />
                                  <image
                                      v-else
                                      class="assist_user_avatar"
                                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png"
                                  />
                                  <text v-if="assistUsers[index]" class="assist_user_nickname">{{
                                      assistUsers[index].nick || '用户名'
                                  }}</text>
                                  <text v-else class="assist_user_nickname">用户名</text>
                              </view>
                          </view>
                          <!-- 当人数超过3人但不超过10人时，两行显示 -->
                          <view
                              v-else-if="assistUsers.length > 5 && assistUsers.length <= 10"
                              class="assist_users_grid"
                          >
                              <view v-for="(user, index) in assistUsers" :key="index" class="assist_user_item">
                                  <image
                                      class="assist_user_avatar"
                                      :src="
                                          user.avatar ||
                                          'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png'
                                      "
                                  />
                                  <text class="assist_user_nickname">{{ user.nick || '用户名' }}</text>
                              </view>
                          </view>
                          <!-- 当人数超过10人时，两行显示并上下滚动 -->
                          <view v-else class="assist_users_scroll_vertical">
                              <view class="assist_users_scroll_vertical_container">
                                  <view v-for="(user, index) in assistUsers" :key="index" class="assist_user_item">
                                      <image
                                          class="assist_user_avatar"
                                          :src="
                                              user.avatar ||
                                              'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png'
                                          "
                                      />
                                      <text class="assist_user_nickname">{{ user.nick || '用户名' }}</text>
                                  </view>
                              </view>
                          </view>
                      </view>
                  </view>
                  <!-- 助力失效时间 -->
                  <view class="countdown_container">
                      <view class="countdown_text">
                          <text class="countdown_time">{{ countdownTime }}</text>
                          <text class="countdown_desc">后未达成，本轮助力将失效</text>
                      </view>
                  </view>
                  <!-- 邀请好友助力按钮 -->
                  <view class="invite_button_container">
                      <view
                          v-if="
                              Number(basactiveInfo.required_invite_number) - Number(basactiveInfo.invite_members) > 0
                          "
                          class="invite_button"
                          @click="inviteFriends"
                      >
                          <text class="invite_button_text">邀请新用户加入</text>
                      </view>
                      <view v-else class="success_buy_button" @click="handleBuyNow(basactiveInfo)">
                          <text class="buy_button_text">立即购买</text>
                      </view>
                  </view>
              </view>

              <!-- 已成功完成户助力 -->
              <!-- <view class="success_assistance_container" v-if="assistanceCompleted"> -->
              <!-- 头部标题区域 -->
              <!-- <view class="success_header">
                      <view class="success_title_wrapper">
                          <view class="success_title_decorator_left"></view>
                          <view class="success_title_main">
                              <view class="success_title">已成功完成户助力</view>
                              <view class="success_subtitle">立享1元购资格</view>
                          </view>
                          <view class="success_title_decorator_right"></view>
                      </view>
                  </view> -->

              <!-- 进度条区域 -->
              <!-- <view class="content_title_text_content_progresss">
                      <view class="progress">
                          <view class="progress-bar">
                              <view class="progress-inner" :style="{ width: '100%' }">
                              </view>
                          </view>
                      </view>
                      <view class="progress-text">助力进度100%</view>
                  </view> -->

              <!-- 产品展示区域 -->
              <!-- <view class="success_products">
                      <view class="product_item" v-for="(product, index) in successProducts" :key="index">
                          <image class="product_image" :src="product.image" />
                          <view class="product_name">{{ product.name }}</view>
                      </view>
                  </view> -->

              <!-- 倒计时区域 -->
              <!-- <view class="success_countdown">
                      <view class="countdown_time">{{ countdownTime }}</view>
                      <view class="countdown_desc">后未达成,助力将失效</view>
                  </view> -->

              <!-- 购买按钮 -->
              <!-- <view class="success_buy_button_container">
                      <view class="success_buy_button" @click="handleBuyNow(basactiveInfo)">
                          <text class="buy_button_text">立即购买</text>
                      </view>
                  </view>
              </view> -->

              <!-- 参与步骤 -->
              <view class="participation_steps">
                  <image
                      class="participation_steps_img"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c51dd66c494210011391.png"
                  />
              </view>
              <!-- 助力记录 -->
              <view class="assistance_record">
                  <view class="assistance_record_title">助力记录</view>
                  <view class="assistance_record_list">
                      <view v-if="assistanceRecords.length === 0" class="assistance_record_empty">
                          <view class="assistance_record_empty_img"></view>
                          <view class="assistance_record_empty_text">暂无助力记录</view>
                      </view>
                      <block v-else>
                          <view
                              v-for="(record, index) in assistanceRecords"
                              :key="index"
                              class="assistance_record_item"
                          >
                              <view class="assistance_record_avatar">
                                  <image :src="record.avatar" class="avatar_img" />
                              </view>
                              <view class="assistance_record_info">
                                  <view class="assistance_record_nickname">{{ record.nick }}</view>
                                  <view class="assistance_record_time">{{ record.help_time_text }}</view>
                              </view>
                              <view class="assistance_record_status">已助力</view>
                          </view>
                      </block>
                  </view>
              </view>
          </view>
      </view>
      <!-- 三折购活动弹窗 -->
      <activity-popup
          :showPopup.sync="isThreeDiscountPopup"
          bgImg="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a68276949016090010574.png"
          @link="goToThreeDiscount"
          @close="closeThreeDiscountPopup"
      >
      </activity-popup>
      <!-- 助力弹窗 -->
      <u-popup :show="showAssistPopup" mode="center" :round="10" width="600rpx">
          <view class="passist_popup">
              <!-- 头部标题 -->
              <view class="assist_popup_header">
                  <image
                      class="assist_popup_header_left"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png"
                  />
                  <text class="assist_popup_title">快帮TA助力</text>
                  <image
                      class="assist_popup_header_right"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png"
                  />
              </view>

              <!-- 内容区域 -->
              <view class="assist_popup_content">
                  <!-- 头像昵称 -->
                  <view class="assist_popup_user">
                      <image class="assist_popup_avatar" :src="assistTarget.avatar" />
                      <text class="assist_popup_nickname">{{ assistTarget.nickname }}</text>
                  </view>

                  <!-- 倒计时 -->
                  <view class="assist_popup_countdown">
                      <text class="assist_popup_countdown_time">{{ popupCountdownTime }}</text>
                      <text class="assist_popup_countdown_desc">后未达成，本轮助力将失效</text>
                  </view>

                  <!-- 助力按钮 -->
                  <view class="assist_popup_button_container">
                      <view class="assist_popup_button" @click="handleAssist">
                          <text class="assist_popup_button_text">为TA助力</text>
                      </view>
                  </view>
              </view>
          </view>
      </u-popup>
      <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'">
          <template #active_tag>
              <img
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891d4845695e3550011642.png"
                  style="
                      width: 64rpx;
                      height: 64rpx;
                      position: absolute;
                      left: 20rpx;
                      top: 10rpx;
                      transform: translate(-50%, -50%);
                  "
              />
          </template>
      </share-dialog>
      <!-- 商品详情弹窗 -->
      <u-popup :show="showProduct" v-prevent-scroll="showProduct" :safeAreaInsetBottom="false" bg-color="transparent" round="30" mode="bottom"  @close="closeProduct" @open="openProduct">
        <view class="product-popup-close" style="" @click="closeProduct"></view>

        <view class="product-popup-wrap" style="background-color: transparent;transform: translateY(0);">
              <!-- <view class="product-popup-close" style="position: fixed;" @click="closeProduct"></view> -->
              <view class="warp" style="background-color: #fff;">
                  <view class="product-popup-content">
                      <view class="product-popup-header">
                          <GoodsGallery />
                      </view>
                      <view class="product-popup-title-new">
                          <view class="product-popup-title-text-new">
                              {{ basactiveInfo.goods_info.show_name }}
                          </view>
                          <view class="product-popup-title-text-price-new">
                              <view class="product-popup-title-text-price-text-new">
                                  ￥<span style="font-size: 42rpx; color: #404040; font-weight: 600;">{{ String(basactiveInfo.goods_info.price).split('.')[0] }}</span>
                              </view>
                          </view>
                      </view>
                      <view class="feature-wrap" v-if="sellPointList.length">
                          <view class="u-flex feature" v-for="(item, index) in sellPointList" :key="index">
                              <view class="dot"></view>{{ item }}
                          </view>
                      </view>
                      <view class="product-popup-content-new">
                          <view class="product-popup-content-text-new" v-html="goodsData.detail" v-lazy-html></view>
                      </view>
                  </view>
              </view>
          </view>
      </u-popup>
  </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import { getOneYuanFlashSaleDetail } from '@/http/oneYuanFlashSalePage';
import { UserModule } from '@/store/modules/user';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import ActivityPopup from './components/activityPopup/activityPopup.vue';
import GoodsGallery from './components/GoodsGallery.vue';
import { GoodsModule, IGoodsInfo } from '@/store/modules/goods';
import { IUnionSource } from '@/store/interface/order';
@Component({
  name: 'MemberActivity',
  components: {
      ShareDialog,
      ActivityPopup,
      GoodsGallery,
  },
  filters: {},
})
export default class OneYuanFlashSale extends Vue {
  get progress(): any {
      const percent = Math.min(
          (Number(this.basactiveInfo.invite_members) / Number(this.basactiveInfo.required_invite_number)) * 100,
          100,
      );
      return percent == 100 ? 100 : percent.toFixed(2);
  }

  public isThreeDiscountPopup = false; // 三折购活动弹窗
  public finishThreeDiscountPopup: boolean = false;
  // public arr: any[] = [];
  public basactiveInfo: any = {}; // 活动基础信息
  public loading = false;
  public productList: any[] = [
      {
          cover_img:
              'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879e0fb763264840011264.png',
          title: '轻户外系列双肩背包',
          sales: 100,
          total: 200,
      },
  ];

  public help_friends: any = [];

  get user_id(): any {
      return UserModule.userInfo.user_id;
  }

  get goodsData(): IGoodsInfo {
      return GoodsModule.goodsInfo;
  }

  get sellPointList() {
    const { sell_point }: any = GoodsModule.goodsInfo;
    if (!sell_point) return [];
    return (sell_point || '').replaceAll(/；/g, ';').split(';');
  }

  public seckillId = '';

  public showProduct = false;
  // 助力用户数据
  public assistUsers: any[] = [
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户1'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户2'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户3'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户4'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户5'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户6'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户7'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户8'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户9'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户10'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户11'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户12'
      // }
  ];

  // 倒计时相关
  public countdownTime = '00:00:00';
  private countdownTimer: any = null;
  private endTime: number = 0;
  public productInfo: any = {};
  public show: boolean = false;

  // 弹窗相关
  public showAssistPopup = false;
  public popupCountdownTime = '00:00:00';
  private popupCountdownTimer: any = null;
  private popupEndTime: number = 0;
  public assistTarget = {
      avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      nickname: '用户A',
  };

  // 助力记录数据
  public assistanceRecords: any[] = [
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户A',
      //     assistTime: '2024-01-15 14:30:25'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户B',
      //     assistTime: '2024-01-15 14:28:10'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户C',
      //     assistTime: '2024-01-15 14:25:45'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户D',
      //     assistTime: '2024-01-15 14:22:30'
      // },
      // {
      //     avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      //     nickname: '用户E',
      //     assistTime: '2024-01-15 14:20:15'
      // }
  ];

  // 成功助力相关数据
  public assistanceCompleted = true; // 控制是否显示成功助力模块
  public successCountdownTime = '1天.01:30:23'; // 成功助力倒计时
  public successProducts: any[] = [
      {
          image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879e0fb763264840011264.png',
          name: '自然醒',
      },
      {
          image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879e0fb763264840011264.png',
          name: '自然醒',
      },
      {
          image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879e0fb763264840011264.png',
          name: '自然醒',
      },
  ];

  get statusBarHeight() {
      return AppModule.statusBarHeight;
  }

  closeProduct() {
      this.showProduct = false;
      GoodsModule.setGoodsInfo({} as IGoodsInfo);
  }

  showProducts() {
      this.showProduct = true;
      GoodsModule.setGoodsInfo({} as IGoodsInfo);
      this.$nextTick(() => {
          setTimeout(() => {
              this.lazyLoad();
          }, 1000);
      })
  }

  lazyLoad() {
      const imgList = document.getElementsByTagName('img');
      console.log('imgList', imgList)
      for (let i = 0; i < imgList.length; i++) {
          const img = imgList[i];
          if (img.dataset.src) {
              console.log('img', img)
              img.src = img.dataset.src;
              img.style.width = '100%';
              img.removeAttribute('data-src');
          }
      }
  }

  async openProduct() {
      this.showProduct = true;
      let params = {} as any;
      params = { gid: this.basactiveInfo.goods_info.gid };
      console.log(params, 222222);
      console.log('this.basactiveInfo', this.basactiveInfo)
      if (GoodsModule.isInshop) {
          params.is_internal_purchase = 1;
      }
      await GoodsModule.asyncGetGoodsInfo({ params, pointsMall: false });
  }

  goBack() {
    Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ASSISTANE_BACK_BTN,
          });
    if (!this.finishThreeDiscountPopup) {
          this.finishThreeDiscountPopup = true;
          this.isThreeDiscountPopup = true;
      } else {
          Utils.goBack();
      }
  }

  closeThreeDiscountPopup() {
      this.isThreeDiscountPopup = false;
  }

  // 跳转三折购
  goToThreeDiscount() {
      this.isThreeDiscountPopup = false;
      // 一元购邀请页三折购活动弹窗跳转三折购
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
              .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT_LINK,
      });
      Utils.navigateTo('/pagesC/threeDiscount/aThreeDiscount');
  }

  // 格式化时间
  private formatTime(seconds: number): string {
      const days = Math.floor(seconds / (24 * 3600));
      const hours = Math.floor((seconds % (24 * 3600)) / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (days > 0) {
          return `${days}天${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
              .toString()
              .padStart(2, '0')}`;
      } else {
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
              .toString()
              .padStart(2, '0')}`;
      }
  }

  // 更新倒计时
  private updateCountdown() {
      const now = new Date().getTime();
      const diff = Math.max(0, this.endTime - now);
      const seconds = Math.floor(diff / 1000);
      if (seconds <= 0) {
          this.countdownTime = '00:00:00';
          this.stopCountdown();
          Utils.goBack();
          return;
      }
      this.countdownTime = this.formatTime(seconds);
  }

  // 更新弹窗倒计时
  private updatePopupCountdown() {
      const now = new Date().getTime();
      const diff = Math.max(0, this.popupEndTime - now);
      const seconds = Math.floor(diff / 1000);
      if (seconds <= 0) {
          this.popupCountdownTime = '00:00:00';
          this.stopPopupCountdown();
          return;
      }
      this.popupCountdownTime = this.formatTime(seconds);
  }

  // 开始倒计时
  private startCountdown() {
      // 设置结束时间为当前时间加3天（示例）
      this.endTime = new Date(this.basactiveInfo.activity_info.end_time * 1000).getTime();
      this.updateCountdown();
      this.countdownTimer = setInterval(() => {
          this.updateCountdown();
      }, 1000);
  }

  // 开始弹窗倒计时
  private startPopupCountdown() {
      // 设置弹窗倒计时结束时间为当前时间加1天（示例）
      this.popupEndTime = new Date().getTime() + 1 * 24 * 60 * 60 * 1000;
      this.updatePopupCountdown();
      this.popupCountdownTimer = setInterval(() => {
          this.updatePopupCountdown();
      }, 1000);
  }

  // 停止倒计时
  private stopCountdown() {
      if (this.countdownTimer) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
      }
  }

  // 停止弹窗倒计时
  private stopPopupCountdown() {
      if (this.popupCountdownTimer) {
          clearInterval(this.popupCountdownTimer);
          this.popupCountdownTimer = null;
      }
  }

  async onLoad(options: any) {
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_SHARE_EXPOSURE,
      });

      if (options.seckill_id) {
          this.seckillId = options.seckill_id;
      }

      if (this.seckillId) {
          const res = await getOneYuanFlashSaleDetail({ seckill_id: this.seckillId });
          this.basactiveInfo = res;
          const currentFriendsCount = res.help_friends ? res.help_friends.length : 0;
          const requiredCount = res.required_invite_number || 0;
          const emptySlotsCount = Math.max(0, requiredCount - currentFriendsCount);

          // 创建空对象数组
          const emptySlots = Array(emptySlotsCount)
              .fill(null)
              .map(() => ({ empty: true }));

          // 拼接原有的助力好友和空对象
          this.assistUsers = [...(res.help_friends || []), ...emptySlots];
          this.assistanceRecords = res.help_friends || []; // 当前显示15人，可以修改数字测试
      }
      this.loading = true;

      // 启动倒计时
      this.startCountdown();
      Utils.setPageTitle(this.basactiveInfo.goods_info.show_name);
  }

  async onShow() {
      if (this.seckillId) {
          const res = await getOneYuanFlashSaleDetail({ seckill_id: this.seckillId });
          this.basactiveInfo = res;
          const currentFriendsCount = res.help_friends ? res.help_friends.length : 0;
          const requiredCount = res.required_invite_number || 0;
          const emptySlotsCount = Math.max(0, requiredCount - currentFriendsCount);

          // 创建空对象数组
          const emptySlots = Array(emptySlotsCount)
              .fill(null)
              .map(() => ({ empty: true }));

          // 拼接原有的助力好友和空对象
          this.assistUsers = [...(res.help_friends || []), ...emptySlots];
          this.assistanceRecords = res.help_friends || []; // 当前显示15人，可以修改数字测试
          console.log(this.assistUsers, 222222);
      }
      this.loading = true;

      // 启动倒计时
      this.startCountdown();
      const win: any = window;
      win.onAppPageShow = async () => {
          if (this.seckillId) {
              const res = await getOneYuanFlashSaleDetail({ seckill_id: this.seckillId });
              this.basactiveInfo = res;
              const currentFriendsCount = res.help_friends ? res.help_friends.length : 0;
              const requiredCount = res.required_invite_number || 0;
              const emptySlotsCount = Math.max(0, requiredCount - currentFriendsCount);

              // 创建空对象数组
              const emptySlots = Array(emptySlotsCount)
                  .fill(null)
                  .map(() => ({ empty: true }));

              // 拼接原有的助力好友和空对象
              this.assistUsers = [...(res.help_friends || []), ...emptySlots];
              this.assistanceRecords = res.help_friends || []; // 当前显示15人，可以修改数字测试
              console.log(this.assistUsers, 222222);
          }
          this.loading = true;

          // 启动倒计时
          this.startCountdown();
      };
  }

  onBackPress(options) {
      if (options.from === 'backbutton') {
          Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_ASSISTANE_BACK,
          });
      }
  }

  onUnload() {
      // 清理倒计时定时器
      this.stopCountdown();
      // 清理弹窗倒计时定时器
      this.stopPopupCountdown();
  }

  // 邀请好友助力
  inviteFriends() {
      if (UserModule.sdkVersion < 13) {
          Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
          return;
      }
      this.productInfo = {
          name: this.basactiveInfo.goods_info.show_name,
          image: this.basactiveInfo.goods_info.cover_image,
          price: this.basactiveInfo.goods_info.price,
          desc: '',
          priceColor: '#FF1F0E',
          descColor: '#C59245',
          imageBg:
              'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109a76e6e34520010690.png',
      };
      this.show = true;
  }

  handleShare(type: 'web' | 'image') {
      this.show = false;
      Utils.logTrace(
          {
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_INVITE_CLICK,
          },
          true,
      );
      const jumpLink = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?relate_id=${this.seckillId}&inviter_id=${this.user_id}`;
      // const data = {
      //     ...Constants.GEN_SHARE_LINK_TYPE,
      //     jumpLink: url,
      // };
      // Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
      //     const params = {
      //         target: 'wechat,weixin_circle,qq,sina',
      //         type: 'web',
      //         content: {
      //             url:
      //                 `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/newUser?link=` +
      //                 res.data +
      //                 '&required_invite_number=' +
      //                 this.basactiveInfo.required_invite_number +
      //                 '&invite_members=' +
      //                 this.basactiveInfo.invite_members,
      //             share_image: this.basactiveInfo.goods_info.cover_image + '?x-oss-process=image/resize,w_200',
      //             share_title: `我正在参与追觅一元购活动！`,
      //             share_desc: `低至一元拿走，${this.basactiveInfo.goods_info.show_name}！`,
      //         },
      //     };
      //     Utils.messageChannel('share', params);
      // });
      const url =
          `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/newUser?` +
          'required_invite_number=' +
          this.basactiveInfo.required_invite_number +
          '&invite_members=' +
          this.basactiveInfo.invite_members;
      Utils.cardShare(type)(
          {
              target: 'wechat,weixin_circle,qq,sina,image_template,download',
              link: jumpLink,
              jumpLink: url,
          },
          {
              content: {
                  url: url,
                  image: this.basactiveInfo.goods_info.cover_image + '?x-oss-process=image/resize,w_200',
                  title: `我正在参与追觅一元购活动！`,
                  desc: `低至一元拿走，${this.basactiveInfo.goods_info.show_name}！`,
              },
              extras: {
                  type: 'activity',
                  id: Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID).toString(),
                  goods: {
                      name: this.productInfo.name,
                      // desc: '邀请新用户加入，新款扫地机器人一元拿走！',
                      imageBg:
                          'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109a76e6e34520010690.png',
                      image: this.productInfo.image,
                      price: this.productInfo.price,
                      priceColor: '#FF1F0E',
                      descColor: '#C59245',
                  },
                  copyText: `我正在参与追觅一元购活动！低至一元拿走，${this.basactiveInfo.goods_info.show_name}！`,
              },
          },
      );
  }

  // 处理助力
  handleAssist() {
      console.log('为TA助力');
      Utils.Toast('助力成功！', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
      this.showAssistPopup = false;
  }

  // 立即购买
  handleBuyNow(item) {
      // 判断是否在活动时间内
      const now = Date.now();
      const startTime = new Date(this.basactiveInfo.activity_info.start_time * 1000).getTime();
      const endTime = new Date(this.basactiveInfo.activity_info.end_time * 1000).getTime();
      if (now < startTime || now > endTime) {
          Utils.Toast(
              '当前不在活动时间内，无法进行下一步操作',
              null,
              null,
              'color: #111111;font-weight: 500;width: 500rpx;',
          );
          return;
      }
      Utils.logTrace(
          {
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_BUY_CLICK,
          },
          true,
      );
      uni.navigateTo({
          url: `/pagesA/settlement/settlement?buy_goods_number=${
              this.basactiveInfo.goods_info.buy_goods_number
          }&orderType=ONE_YUAN_PURCHASE&unionSource=${IUnionSource.ONE_YUAN_PURCHASE}&disable_all_discount=1&seckill_id=${this.seckillId}&sParams=${JSON.stringify([
              { gid: this.basactiveInfo.goods_info.gid, sid: '0', num: 1, gini_id: 0 },
          ])}`,
      });
  }
}
</script>

<style lang="scss" scoped>
.oneYuanFlashSale_container {
  position: relative;
  background: #ffecde;
  min-height: 100vh;
  height: 100%;
  // background-image:
  //     linear-gradient(to bottom, transparent 500rpx, #f6f6f6 500rpx, #f6f6f6 100%),
  //     url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a3fb82cf711840011358.png');
  // background-size:
  //     100% 100%,
  //     100% 500rpx;
  // background-repeat: no-repeat;
  // background-position:
  //     top center,
  //     top center;

  .oneYuanFlashSale_content {
      width: 100%;
      height: 100%;
      width: 100%;
      background-image: linear-gradient(to bottom, transparent 700rpx, #ffecde 700rpx, #ffecde 100%),
          url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5de271b2d4660030511.png');
      background-size: 100% 100%, 100% 700rpx;

      .header {
          height: 108rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;

          .left {
              padding-left: 38rpx;
              color: #ffffff;
              font-size: 32rpx;
              display: flex;
              align-items: center;

              .search_back {
                  width: 46rpx;
                  height: 46rpx;
                  margin-right: 26rpx;
              }
          }

          .right {
              .rule {
                  border: 2rpx solid #ffffff;
                  border-radius: 38rpx;
                  width: 165rpx;
                  height: 52rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 24rpx;
                  color: #ffffff;
                  margin-right: 22rpx;
              }
          }
      }

      .header_image {
          height: 500rpx;
          width: 100%;
          overflow: hidden;
          position: relative;

          .header_image_img {
              width: 100%;
              height: 100%;
              object-fit: cover;
          }
      }

      .content {
          // background: linear-gradient(180deg, #FFC8A6 0%, #F53F3F 100%);
          padding: 0rpx 46rpx 46rpx 44rpx;

          .content_title {
              background: #ffffff;
              border-radius: 40rpx;
              height: 300rpx;
              padding: 24rpx;
              display: flex;

              .content_img_wrap {
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  position: relative;

                  .content_img {
                      width: 282rpx;
                      height: 252rpx;
                      border-radius: 20rpx;
                  }

                  .content_img_icon {
                      position: absolute;
                      top: -36rpx;
                      right: -48rpx;
                      width: 94rpx;
                      height: 68rpx;
                      animation: scale 0.8s infinite alternate;
                      transform-origin: center;

                      @keyframes scale {
                          0% {
                              transform: scale(0.8);
                          }

                          100% {
                              transform: scale(1);
                          }
                      }
                  }
              }

              .content_img {
                  min-width: 282rpx;
                  width: 282rpx;
                  height: 252rpx;
                  border-radius: 20rpx;
                  overflow: hidden;
              }

              .content_title_text {
                  margin-left: 26rpx;
                  width: calc(100% - 282rpx - 24rpx);
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;

                  .content_title_text_title {
                      margin-top: 28rpx;
                      font-size: 28rpx;
                      color: #000000;
                      font-weight: 500;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      white-space: normal;
                      word-break: break-all;
                      // width: calc(100% - 48rpx);
                  }

                  .content_title_text_content {
                      .content_title_text_grab {
                          display: flex;
                          margin-top: 10rpx;
                          padding-bottom: 24rpx;
                          position: relative;

                          .content_title_text_grab_img {
                              position: absolute;
                              top: -28rpx;
                              left: 0;
                              width: 42rpx;
                              height: 42rpx;
                              object-fit: contain;
                          }

                          .content_title_text_grab_text {
                              color: #999999;
                              font-size: 20rpx;
                              margin-right: 8rpx;
                              margin-bottom: 4rpx;
                              display: flex;
                              align-items: flex-end;
                              margin-bottom: 8rpx;
                          }

                          .content_title_text_grab_bg_img {
                              min-width: 150rpx;
                              padding: 0rpx 24rpx;
                              height: 60rpx;
                              border-radius: 2978rpx;
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              font-size: 24rpx;
                              color: #f53f3f;
                              background: linear-gradient(270deg, #ffcfcf 14%, #fff7f7 100%);
                              font-weight: 500;
                          }

                          .content_title_text_grab_bg_img_active {
                              opacity: 0.3;
                          }
                      }
                  }
              }
          }

          .content_title_text_assist {
              border-radius: 40rpx;
              background: #ffffff;
              width: 660rpx;
              margin-left: auto;
              margin-right: auto;
              margin-top: 32rpx;
              padding: 28rpx 0rpx 0rpx 0rpx;
              overflow: hidden;

              .content_title_text_content_progresss {
                  margin-top: 20rpx;
                  width: calc(100% - 60rpx);
                  margin-left: auto;
                  margin-right: auto;
                  line-height: 40rpx;
                  padding: 0 22rpx 0rpx 22rpx;
                  background: linear-gradient(270deg, #ffd6d6 2%, #ffffff 45%, #faeeee 100%);
                  border-radius: 200rpx;
                  height: 40rpx;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;

                  .progress {
                      width: 358rpx;
                      height: 22rpx;
                      background: rgba(255, 151, 151, 0.31);
                      border-radius: 200rpx;

                      .progress-bar {
                          height: 22rpx;
                          border-radius: 200rpx;

                          .progress-inner {
                              background: url(https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png)
                                  no-repeat center center;
                              height: 22rpx;
                              border-radius: 200rpx;
                          }
                      }
                  }

                  .progress-text {
                      font-size: 20rpx;
                      color: #ff3c3c;
                      font-weight: 500;
                  }
              }

              .content_title_text_assist_number {
                  margin-top: 20rpx;
                  width: 596rpx;
                  margin-left: auto;
                  margin-right: auto;
                  border: 2rpx solid #ff4848;
                  background: #fff4ed;
                  border-radius: 32rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .content_title_text_assist_number_content {
                      width: 100%;
                      padding: 22rpx 0rpx;

                      // 固定3人布局
                      .assist_users_fixed {
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          width: 100%;
                          height: 186rpx;
                          gap: 12rpx;

                          .assist_user_item {
                              .assist_user_avatar {
                                  width: 100rpx;
                                  height: 100rpx;
                              }

                              .assist_user_nickname {
                                  font-size: 20rpx;
                                  height: 30rpx;
                                  line-height: 30rpx;
                                  max-width: 100rpx;
                              }
                          }
                      }

                      // 网格布局（6-10人）
                      .assist_users_grid {
                          display: flex;
                          flex-wrap: wrap;
                          width: 100%;
                          height: 296rpx;
                          overflow-y: auto;
                          justify-content: center;

                          .assist_user_item {
                              margin-right: 12rpx;

                              .assist_user_avatar {
                                  width: 100rpx;
                                  height: 100rpx;
                              }

                              .assist_user_nickname {
                                  font-size: 20rpx;
                                  max-width: 100rpx;
                              }

                              &:nth-child(5n) {
                                  margin-right: 0;
                              }
                          }
                      }

                      // 滚动布局（超过10人）
                      .assist_users_scroll_vertical {
                          display: flex;
                          flex-wrap: wrap;
                          width: 100%;
                          // height: 298rpx;
                          // overflow-y: auto;
                          // justify-content: center;

                          .assist_users_scroll_vertical_container {
                              display: flex;
                              flex-wrap: wrap;
                              width: 100%;
                              height: 296rpx;
                              overflow-y: auto;
                              justify-content: center;
                          }

                          .assist_user_item {
                              margin-right: 12rpx;

                              &:nth-child(5n) {
                                  margin-right: 0;
                              }

                              .assist_user_avatar {
                                  width: 100rpx;
                                  height: 100rpx;
                              }

                              .assist_user_nickname {
                                  font-size: 20rpx;
                                  max-width: 100rpx;
                              }
                          }
                      }

                      // 用户项目样式
                      .assist_user_item {
                          display: flex;
                          flex-direction: column;
                          align-items: center;
                          gap: 6rpx;
                          padding-bottom: 20rpx;

                          // 最后五个元素不添加padding-bottom
                          &:nth-last-child(-n + 5) {
                              padding-bottom: 0;
                          }

                          .assist_user_avatar {
                              width: 100rpx;
                              height: 100rpx;
                              border-radius: 50%;
                              object-fit: cover;
                          }

                          .assist_user_nickname {
                              font-size: 20rpx;
                              color: #333333;
                              text-align: center;
                              max-width: 100rpx;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              white-space: nowrap;
                          }
                      }
                  }
              }

              // 倒计时样式
              .countdown_container {
                  margin-top: 76rpx;
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  .countdown_text {
                      display: flex;
                      align-items: center;
                      gap: 8rpx;

                      .countdown_time {
                          font-size: 36rpx;
                          color: #f53f3f;
                          font-weight: 600;
                      }

                      .countdown_desc {
                          font-size: 24rpx;
                          color: #666666;
                      }
                  }
              }
          }

          // 邀请好友助力按钮样式
          .invite_button_container {
              // background: linear-gradient(180deg, rgba(255, 233, 219, 0) 0%, rgba(255, 233, 219, 0) 11%, #FFE9DB 100%);
              display: flex;
              justify-content: center;
              margin-top: 36rpx;
              padding-bottom: 58rpx;

              .invite_button {
                  width: 590rpx;
                  height: 122rpx;
                  background: linear-gradient(90deg, rgba(255, 62, 74, 0) 0%, rgba(255, 118, 216, 0.41) 100%),
                      linear-gradient(180deg, #f33c4b -1%, #f33c4b 18%, #ff4230 103%, #e7322b 129%);
                  border-radius: 792rpx;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  cursor: pointer;

                  .invite_button_text {
                      font-size: 32rpx;
                      color: #ffffff;
                      font-weight: 600;
                  }
              }

              .success_buy_button {
                  width: calc(100% - 70rpx);
                  margin-left: auto;
                  margin-right: auto;
                  // background: linear-gradient(180deg, #FFD700 -5%, #FFD000 13%, #FFB138 93%, #FFAD44 117%);
                  // box-shadow: inset 0px -4rpx 10rpx 0px #FFEB66;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c6e5ce94509550014242.png')
                      no-repeat center center;
                  background-size: 100% 100%;
                  border-radius: 792rpx;
                  height: 122rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;

                  .buy_button_text {
                      font-size: 48rpx;
                      color: #ffffff;
                      font-weight: 600;
                  }
              }
          }
      }

      // 参与步骤样式
      .participation_steps {
          width: 100%;
          margin-top: 32rpx;

          .participation_steps_img {
              width: 100%;
              height: 320rpx;
              object-fit: cover;
          }
      }

      // 助力记录样式
      .assistance_record {
          background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c52b6704ae4600010861.png')
              no-repeat center center;
          background-size: 100% 100%;
          border-radius: 40rpx;
          padding: 30rpx;
          margin-top: 32rpx;

          .assistance_record_title {
              font-size: 32rpx;
              color: #cb2533;
              font-weight: 600;
              text-align: center;
              margin-bottom: 30rpx;
          }

          .assistance_record_list {
              .assistance_record_empty {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;

                  .assistance_record_empty_img {
                      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687b52d69fa266540011987.png')
                          no-repeat center center;
                      background-size: 100% 100%;
                      border-radius: 966rpx;
                      padding: 0rpx 64rpx 0rpx 6rpx;
                      width: 354rpx;
                      height: 354rpx;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      margin: 118rpx auto 0rpx;
                  }

                  .assistance_record_empty_text {
                      color: #3d3d3d;
                      font-size: 22rpx;
                      text-align: center;
                      line-height: 42rpx;
                      margin-bottom: 234rpx;
                  }
              }

              .assistance_record_item {
                  display: flex;
                  align-items: center;
                  padding: 20rpx 0;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a3d3f3d4572510011184.png')
                      no-repeat center center;
                  background-size: 100% 100%;
                  border-radius: 966rpx;
                  padding: 0rpx 64rpx 0rpx 6rpx;
                  height: 112rpx;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;

                  &:not(:last-child) {
                      margin-bottom: 34rpx;
                  }

                  &:last-child {
                      border-bottom: none;
                  }

                  .assistance_record_avatar {
                      margin-right: 28rpx;

                      .avatar_img {
                          width: 106rpx;
                          height: 100rpx;
                          border-radius: 50%;
                          object-fit: cover;
                      }
                  }

                  .assistance_record_info {
                      flex: 1;
                      display: flex;
                      flex-direction: column;

                      .assistance_record_nickname {
                          font-size: 24rpx;
                          color: #3d3d3d;
                          font-weight: 600;
                          display: flex;
                          align-items: center;
                          height: 44rpx;
                      }

                      .assistance_record_time {
                          font-size: 22rpx;
                          /* NeutralColor中性色/Gray 4 */
                          color: #777777;
                          display: flex;
                          align-items: center;
                          height: 44rpx;
                      }
                  }

                  .assistance_record_status {
                      font-size: 24rpx;
                      /* NeutralColor中性色/Gray 2 */
                      color: #ff0084;
                      font-weight: 600;
                      padding: 8rpx 16rpx;
                      white-space: nowrap;
                  }
              }
          }
      }
  }
}

// 助力弹窗样式
::v-deep .passist_popup {
  width: 664rpx;
  background: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;

  .assist_popup_header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 82rpx 0 0rpx;
      position: relative;

      .assist_popup_header_left {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          left: 30rpx;
      }

      .assist_popup_title {
          font-size: 36rpx;
          color: #cb2533;
          font-weight: 600;
      }

      .assist_popup_header_right {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          right: 30rpx;
      }
  }

  .assist_popup_content {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .assist_popup_user {
          margin-top: 108rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 10rpx;
          background: #fff4ed;
          border: 2rpx solid #ff4848;
          width: calc(100% - 64rpx);
          height: 186rpx;
          border-radius: 32rpx;

          .assist_popup_avatar {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              object-fit: cover;
          }

          .assist_popup_nickname {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
          }
      }

      .assist_popup_countdown {
          margin-top: 64rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          .assist_popup_countdown_time {
              font-size: 28rpx;
              color: #cb2533;
              font-weight: 600;
          }

          .assist_popup_countdown_desc {
              font-size: 24rpx;
              color: #666666;
          }
      }

      .assist_popup_button_container {
          background: linear-gradient(180deg, rgba(255, 233, 219, 0) 0%, rgba(255, 233, 219, 0) 11%, #ffe9db 100%);
          height: 218rpx;
          width: 100%;
          display: flex;
          justify-content: center;

          .assist_popup_button {
              background: linear-gradient(90deg, rgba(255, 62, 74, 0) 0%, rgba(255, 118, 216, 0.41) 100%),
                  linear-gradient(180deg, #f33c4b -1%, #f33c4b 18%, #ff4230 103%, #e7322b 129%);
              border-radius: 792rpx;
              padding: 20rpx 60rpx;
              width: 590rpx;
              height: 122rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              margin-top: 36rpx;

              .assist_popup_button_text {
                  font-size: 32rpx;
                  color: #ffffff;
                  font-weight: 600;
              }
          }
      }
  }
}

// 成功助力模块样式
.success_assistance_container {
  background: #ffffff;
  border-radius: 40rpx;
  margin-top: 32rpx;
  padding: 28rpx 0rpx 0rpx 0rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;

  // 头部标题区域
  .success_header {
      display: flex;
      justify-content: center;
      margin-bottom: 30rpx;

      .success_title_wrapper {
          display: flex;
          align-items: center;
          position: relative;
          height: 86rpx;

          .success_title_decorator_left {
              width: 92rpx;
              height: 20rpx;
              background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a08841797e0970011355.png')
                  no-repeat center center;
              background-size: 100% 100%;
              margin-right: 20rpx;
              position: relative;
          }

          .success_title_main {
              text-align: center;

              .success_title {
                  font-size: 36rpx;
                  color: #cb2533;
                  font-weight: 600;
                  line-height: 1.2;
              }

              .success_subtitle {
                  font-size: 22rpx;
                  color: #cb2533;
                  font-weight: 500;
                  margin-top: 8rpx;
              }
          }

          .success_title_decorator_right {
              width: 92rpx;
              height: 20rpx;
              background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a08ae8d18c5780010587.png')
                  no-repeat center center;
              background-size: 100% 100%;
              margin-left: 20rpx;
              position: relative;
          }
      }
  }

  // 进度条区域
  .content_title_text_content_progresss {
      margin-top: 18rpx;
      width: calc(100% - 60rpx);
      margin-left: auto;
      margin-right: auto;
      line-height: 40rpx;
      padding: 0 22rpx 0rpx 22rpx;
      background: linear-gradient(270deg, #ffd6d6 2%, #ffffff 45%, #faeeee 100%);
      border-radius: 200rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .progress {
          width: 358rpx;
          height: 22rpx;
          background: rgba(255, 151, 151, 0.31);
          border-radius: 200rpx;

          .progress-bar {
              height: 22rpx;
              border-radius: 200rpx;

              .progress-inner {
                  background: url(https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png)
                      no-repeat center center;
                  // background-size: 100% 100%;
                  height: 22rpx;
                  border-radius: 200rpx;
              }
          }
      }

      .progress-text {
          font-size: 20rpx;
          color: #ff3c3c;
          font-weight: 500;
      }
  }

  // 产品展示区域
  .success_products {
      display: flex;
      justify-content: space-around;
      margin-top: 58rpx;
      margin-bottom: 76rpx;
      padding: 0 20rpx;

      .product_item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          max-width: 160rpx;

          .product_image {
              width: 120rpx;
              height: 120rpx;
              border-radius: 50%;
              object-fit: cover;
              margin-bottom: 12rpx;
              background: linear-gradient(135deg, #ffe5f0, #fff0f5);
          }

          .product_name {
              font-size: 24rpx;
              color: #333333;
              text-align: center;
              font-weight: 500;
          }
      }
  }

  // 倒计时区域
  .success_countdown {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;

      .countdown_time {
          font-size: 36rpx;
          color: #f53f3f;
          font-weight: 600;
          margin-right: 4rpx;
      }

      .countdown_desc {
          font-size: 22rpx;
          color: #a6a6a6;
      }
  }

  // 购买按钮
  .success_buy_button_container {
      padding-top: 36rpx;
      height: 218rpx;
      background: linear-gradient(180deg, rgba(255, 233, 219, 0) 0%, rgba(255, 233, 219, 0) 11%, #ffe9db 100%);

      .success_buy_button {
          width: calc(100% - 70rpx);
          margin-left: auto;
          margin-right: auto;
          background: linear-gradient(180deg, #ffd700 -5%, #ffd000 13%, #ffb138 93%, #ffad44 117%);
          box-shadow: inset 0px -4rpx 10rpx 0px #ffeb66;
          border-radius: 792rpx;
          height: 122rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .buy_button_text {
              font-size: 48rpx;
              color: #ffffff;
              font-weight: 600;
          }
      }
  }
}

.product-popup-close {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 60rpx;
    height: 60rpx;
    background-color: #fff;
    z-index: 10;
    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c29ecbc6ad7720010388.png')
    no-repeat center center;
    background-size: 100% 100%;
}
.product-popup-wrap {
    border-radius: 30rpx 30rpx 0 0;
    overflow-y: scroll;
    max-height: 80vh;
    position: relative;
}
.product-popup-title-new {
    background-color: #fff;
    .product-popup-title-text-new {
        font-size: 34rpx;
        color: #121212;
        font-weight: 500;
        margin-top: 40rpx;
        margin-left: 38rpx;
        margin-right: 38rpx;
    }
    .product-popup-title-text-price-new {
        font-size: 36rpx;
        padding-left: 38rpx;
        padding-right: 38rpx;
        padding-top: 30rpx;
        .product-popup-title-text-price-text-new {
            color: #404040;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 56rpx;
        }
    }
}

.product-popup-content-new {
    background-color: #fff;
    padding: 20rpx 38rpx;
    .product-popup-content-text-new {
        font-size: 28rpx;
        color: #777777;
        font-weight: 500;
    }
}
.feature-wrap {
    margin-top: 40rpx;
}
.feature {
    margin-bottom: 12rpx;
    font-size: 26rpx;
    padding: 0 40rpx;
    color: #aaa;
    .dot {
        width: 4rpx;
        height: 4rpx;
        border-radius: 50%;
        margin-right: 16rpx;
        background-color: #777;
    }
}
</style>
