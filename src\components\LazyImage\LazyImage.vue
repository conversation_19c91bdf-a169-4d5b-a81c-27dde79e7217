<!-- eslint-disable vue/no-multiple-template-root -->
<template>
    <!-- #ifdef MP-WEIXIN -->
    <image :src="src" :lazy-load="lazyLoad" :mode="mode" :style="customStyle" @click="onConfirm"></image>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <img :src="src" loading="lazy" :width="width" :height="height" :style="customStyle"  @click="onConfirm" class="object-fit-cover">
    <!-- #endif -->
</template>

<script lang="ts">
import { Vue, Component, Prop, } from 'vue-property-decorator';

@Component
export default class LazyImage extends Vue {
@Prop({ type: String, default: '' })
readonly src;

@Prop({ type: String, default: '' })
readonly width;

@Prop({ type: String, default: '' })
readonly height;

@Prop({ type: Boolean, default: true })
readonly lazyLoad;

@Prop({ type: String, default: 'aspectFit' })
readonly mode;

@Prop({ type: String, default: '' })
readonly customStyle

onConfirm() {
    this.$emit('click');
}

created() {

}
}
</script>

<style lang="scss" scoped>
::v-deep image {
vertical-align: baseline;
}
.object-fit-cover {
    object-fit: contain;
}
</style>
