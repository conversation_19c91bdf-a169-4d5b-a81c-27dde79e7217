<template>
    <view>
        <view
            class="narBar"
            :class="{ 'navbar-fixed': isFixed, 'border-bottom': borderBottom }"
            :style="{ background, zIndex, ...customStyle }"
        >
            <!-- 状态栏 -->
            <view class="status-bar" :style="{ height: statusBarHeight + 'rpx' }"></view>
            <!-- 内容区域 -->
            <view class="content">
                <!-- 返回模块的补充，仅用于tabbar页面的搜索和跳转购物车 -->
                <tabNavBarSlot
                    v-if="isTabbarPage"
                    :isContrast="isContrast"
                    :isShowSearch="isShowSearch"
                    :isShowCart="isShowCart"
                />

                <!-- 返回按钮 -->
                <view class="back_box" v-if="isBack" @tap.stop="goBack">
                    <view v-if="backText" class="back_box-text">{{ backText }}</view>
                    <image v-else class="backicon" :src="backIcon" mode="scaleToFill"></image>
                    <image
                        v-if="isGoHome"
                        class="icon-home-back"
                        src="https://wpm-cdn.dreame.tech/images/202206/155259-1655952599026.png"
                        @tap.stop="goHome"
                    >
                    </image>
                    <!-- #ifdef MP-WEIXIN -->
                    <view class="customerImg2" v-if="customer" @click.stop="handleContact">
                        <image src="https://wpm-cdn.dreame.tech/images/202307/245433-1690437168233.png"></image>
                    </view>
                    <view class="ruleImg2" v-if="ruleImg" @click.stop="gotoRulePage">
                        <image :src="ruleImgUrl"></image>
                    </view>
                    <!-- #endif -->
                </view>

                <!-- 标题名 -->
                <slot>
                    <view
                        class="title"
                        :style="{ color: titleColor, fontSize: titleSize + 'rpx', fontWeight: titleWeight }"
                    >
                        <view class="title-text" :style="titleTextStyle">{{ title }}</view>
                    </view>
                </slot>

                <!-- #ifdef H5 -->
                <view class="customerImg" v-if="customer" @click="handleContact">
                    <image src="https://wpm-cdn.dreame.tech/images/202307/245433-1690437168233.png"></image>
                </view>
                <view class="ruleImg" v-if="ruleImg" @click="gotoRulePage">
                    <image :src="ruleImgUrl"></image>
                </view>

                <slot name="operate"></slot>
                <!-- #endif -->
            </view>

            <view class="info" id="info">
                <slot name="info"></slot>
            </view>
        </view>
        <view v-if="isFixed && !hideContent" :style="{ width: '100%', height: fixedHeight + 'rpx' }"></view>
    </view>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import tabNavBarSlot from '@/components/tabNavBarSlot/TabNavBarSlot.vue';
import Utils from '@/common/Utils';

@Component({
    components: { tabNavBarSlot },
})
export default class CustomBar extends Vue {
    public slotHeight: number | string = 0;
    @Prop({ type: String, default: '' })
    readonly title!: string;

    @Prop({ type: String, default: '' })
    readonly titleTextStyle!: String;

    @Prop({ type: String, default: 'https://wpm-cdn.dreame.tech/images/202308/972703-1691654023213.png' })
    readonly backIcon!: string;

    @Prop({ type: Boolean, default: true })
    readonly isBack!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isShowSearch: boolean;

    @Prop({ type: String, default: '#fff' })
    readonly background!: string;

    @Prop({ type: String, default: '#333333' })
    readonly titleColor!: string;

    @Prop({ type: [String, Number], default: 36 })
    readonly titleSize!: string | number;

    @Prop({ type: [String, Number], default: '600' })
    readonly titleWeight!: string | number;

    @Prop({ type: Number, default: 22 })
    readonly bottomHeight!: number;

    @Prop({ type: Number, default: 9999 })
    readonly zIndex!: number;

    @Prop({ type: Boolean, default: false })
    readonly customBack!: boolean;

    @Prop({ type: Boolean, default: true })
    readonly isFixed!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly borderBottom!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isNavbarSlot!: boolean;

    @Prop({ type: Boolean, default: false })
    readonly isGoHome!: boolean;

    @Prop({ type: String, default: '' })
    readonly backText: string;

    @Prop({ type: Boolean, default: false })
    readonly isContrast: boolean;

    @Prop({ type: Boolean, default: true })
    readonly isShowCart: boolean;

    @Prop({ type: Boolean, default: false })
    readonly customer!: boolean;

    @Prop({ type: Object, default: {}})
    readonly customStyle!: object;

    @Prop({ type: Boolean, default: false })
    readonly ruleImg!: boolean;

    @Prop({ type: String, default: 'https://wpm-cdn.dreame.tech/images/202307/349052-1689574639971.png' })
    readonly ruleImgUrl!: string;

    @Prop({ type: String, default: '' })
    readonly backTrigger;

    // 是否文档流区域
    @Prop({ type: Boolean, default: false })
    readonly hideContent!: boolean;

    constructor() {
        super();
    }

    get isTabbarPage(): Boolean {
        const pages = (uni as any).$u.pages();
        if (Constants.TAB_BAR_LIST.includes(`/${pages[pages.length - 1].route}`)) {
            return true;
        }
        return false;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get fixedHeight() {
        let height = 0;
        height = this.pagePaddingTop;
        return height;
    }

    created() {
        this.$nextTick(() => {
            const query = uni.createSelectorQuery().in(this);
            query
                .select('#info')
                .boundingClientRect((data) => {
                    this.slotHeight = data.height;
                })
                .exec();
            this.$emit('height', this.fixedHeight);
        });
    }

    goBack() {
        // 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑
        if (this.customBack) {
            this.$emit('back');
        } else {
            Utils.goBack(this.backTrigger);
        }
    }

    goHome() {
        uni.reLaunch({
            url: '/pages/index/index',
        });
    }

    gotoRulePage() {
        this.$emit('rule');
    }

    handleContact(e) {
        this.$emit('handleContact');
    }
}
</script>
<style lang="scss" scoped>
::v-deep img,
image {
    vertical-align: middle !important;
}

.navbar-fixed {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
}

.border-bottom {
    border-bottom: 1rpx solid #ebeaea;
}

.narBar {
    z-index: 1;
    width: 750rpx;
    height: auto;

    .status-bar {
        width: 100%;
        height: auto;
    }

    .content {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        height: 108rpx;

        .back_box {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            position: absolute;
            left: 0;
            top: 31rpx;
            z-index: 2;

            .backicon {
                margin-left: 38rpx;
                width: 46rpx;
                height: 46rpx;
            }

            .icon-home-back {
                width: 46rpx;
                height: 46rpx;
                margin-left: 12rpx;
            }

            &-text {
                font-weight: 400;
                font-size: 32rpx;
                color: #404040;
                padding-left: 38rpx;
            }
        }

        .title {
            font-size: 36rpx;
            font-weight: 600;
            color: $text-color-primary;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            width: max-content;
        }

        .customerImg {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            width: 46rpx;
            height: 46rpx;
            margin-right: 42rpx;
            @include flex(row, center, center, none);

            image {
                width: 100%;
                height: 100%;
            }
        }

        .customerImg2 {
            width: 46rpx;
            height: 46rpx;
            margin-left: 12rpx;
            @include flex(row, center, center, none);

            image {
                width: 100%;
                height: 100%;
            }
        }

        .title-text {
            height: 46rpx;
            line-height: 46rpx;
        }

        .ruleImg {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            width: 46rpx;
            height: 46rpx;
            margin-right: 38rpx;
            @include flex(row, center, center, none);

            image {
                width: 100%;
                height: 100%;
            }
        }

        .ruleImg2 {
            width: 46rpx;
            height: 46rpx;
            margin-left: 8rpx;
            @include flex(row, center, center, none);

            image {
                width: 100%;
                height: 100%;
            }
        }
    }

    .info {
        width: 100%;
        height: auto;
    }
}
</style>
