export default class LastMayday {
    palette(data: any, userInfo, imgList) {
        return {
            width: '500rpx',
            height: '1084rpx',
            background: '#ffffff',
            borderRadius: '22rpx',
            views: [
                {
                    type: 'image',
                    url: data.cover,
                    css: {
                        width: '100%',
                        height: '100%',
                        top: '0rpx',
                        left: '0rpx',
                    },
                },
                {
                    type: 'rect',
                    css: {
                        color: '#F7F7F7',
                        width: '100%',
                        height: '178rpx',
                        borderRadius: '0 0 12rpx 12rpx',
                        bottom: '0rpx',
                        left: '0rpx',
                    },
                },
                {
                    type: 'image',
                    url: userInfo.avatar,
                    css: {
                        width: '52rpx',
                        height: '52rpx',
                        borderRadius: '50%',
                        bottom: '90rpx',
                        left: '18rpx',
                    },
                },
                // #ifdef MP-WEIXIN
                {
                    type: 'text',
                    text: userInfo.nick.length > 15 ? userInfo.nick.substr(0, 8) + '...\u3000' : userInfo.nick,
                    css: {
                        color: '#1D1E20',
                        bottom: '105rpx',
                        left: '80rpx',
                        fontFamily: 'HarmonyOS_Sans_SC',
                    },
                },
                {
                    type: 'text',
                    text: '向你推荐',
                    css: {
                        fontSize: '20rpx',
                        color: 'rgba(34, 34, 34, 0.5)',
                        bottom: '50rpx',
                        left: '80rpx',
                        fontFamily: 'HarmonyOS_Sans_SC',
                    },
                },
                {
                    type: 'text',
                    text: '长按保存图片',
                    css: {
                        fontSize: '20rpx',
                        color: 'rgba(34, 34, 34, 0.5)',
                        bottom: '50rpx',
                        left: '170rpx',
                        fontFamily: 'HarmonyOS_Sans_SC',
                    },
                },
                // #endif
                // #ifdef H5
                {
                    type: 'image',
                    url: imgList[0],
                    css: {
                        height: '70rpx',
                        width: '241rpx',
                        bottom: '95rpx',
                        left: '80rpx',
                    },
                },
                {
                    type: 'image',
                    url: imgList[1],
                    css: {
                        height: '60rpx',
                        width: '200rpx',
                        bottom: '50rpx',
                        left: '80rpx',
                    },
                },
                {
                    type: 'image',
                    url: imgList[2],
                    css: {
                        height: '60rpx',
                        width: '200rpx',
                        bottom: '50rpx',
                        left: '170rpx',
                    },
                },
                // #endif
                {
                    type: 'image',
                    url: data.qrCodeUrl,
                    css: {
                        width: '128rpx',
                        height: '128rpx',
                        bottom: '25rpx',
                        right: '25rpx',
                        borderRadius: '50%',
                    },
                },
            ],
        };
    }
}
