<template>
    <view class="cart-card flex" @click="showGoodsMask">
        <view class="image-box">
            <image class="goods-image" :src="goods.cover_image" />
        </view>
        <view class="u-flex u-flex-1 u-row-between">
            <view>
                <view class="u-flex">
                    <image class="goods-tag" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b87c5df403850022268.png" />
                    <text class="goods-name ellipsis">{{ goods.name }}</text>
                    <view class="goods-line"></view>
                </view>
                <view>
                    <text class="goods-price">¥{{ goods.price }}</text>
                    <text class="goods-sales">已售 {{ goods.sales }}</text>
                </view>
            </view>
            <view class="u-flex flex-shrink-0">
                <text class="goods-amount" v-if="goodsIds.length > 1">共计{{ goodsIds.length }}件商品</text>
                <image class="arrow-icon" src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888b8362ad381750010659.png" />
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { goodsInfo } from '@/http/goods'

@Component
export default class CardLink extends Vue {
    @Prop({ type: Array, default: () => [] })goodsIds: Number[]

    @Watch('goodsIds', { immediate: true })
    showCardInfo(goodsIds) {
        const id = goodsIds[0]
        // const id = 854
        this.getGoodsInfo(id)
    }

    public goods: any = {}

    async getGoodsInfo(gid: number) {
        const response = await goodsInfo({ gid })
        this.goods = Object(response.goods)
    }

    showGoodsMask() {
        this.$emit('showGoodsMask', this.goodsIds)
    }
}
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.cart-card {
    margin: 24rpx;
    padding: 12rpx 20rpx;
    width: 716rpx;
    border-radius: 16rpx;
    background: #F6F6F6;

    .image-box {
        margin-right: 12rpx;

        .goods-image {
            width: 88rpx;
            height: 88rpx;
            border-radius: 8rpx;
        }
    }

    .goods-tag {
        width: 64rpx;
        height: 32rpx;
    }
    .goods-name {
        max-width: 330rpx;
        margin-left: 8rpx;
        display: inline-block;
        font-size: 28rpx;
        height: 40rpx;
        line-height: 40rpx;
        color: #404040;
    }
    .goods-price {
        display: inline-block;
        font-size: 24rpx;
        height: 32rpx;
        line-height: 32rpx;
        color: #404040;
    }

    .goods-sales {
        margin-left: 16rpx;
        display: inline-block;
        font-size: 24rpx;
        height: 32rpx;
        line-height: 32rpx;
        color: #A6A6A6;
    }

    .goods-amount {
        font-size: 24rpx;
        height: 32rpx;
        line-height: 32rpx;
        color: #A6A6A6;
    }

    .arrow-icon {
        width: 24rpx;
        height: 24rpx;
    }
}
.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
