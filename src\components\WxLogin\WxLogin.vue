<template>
    <div>
        <u-popup
            :show="showPopRegister"
            v-prevent-scroll="showPopRegister"
            :closeOnClickOverlay="false"
            bgColor="transparent"
            mode="center"
            @close="onClose"
        >
            <view class="pop_content">
                <!-- <view class="bg">//showPopRegister</view> -->
                <view class="title">新人大礼包</view>
                <view class="sub-title">尊敬的会员，{{ isReceive ? '见面礼已奉上' : '快领取见面礼吧' }}</view>
                <image
                    class="btn-close"
                    src="https://wpm-cdn.dreame.tech/images/202306/457894-1687142726265.png"
                    @touchstart.stop.prevent="onClose"
                />

                <scroll-view class="conpon-list" scroll-y>
                    <view v-for="(item, index) in list" class="" :key="index">
                        <CouponItem :itemData="item" @showRule="handleShowRule" width="574rpx" />
                    </view>
                    <view>
                        <PointItem
                            v-if="Array.isArray(point) ? point.length : point"
                            :itemData="point"
                            width="574rpx"
                        ></PointItem>
                    </view>
                </scroll-view>

                <view v-if="!isReceive" class="btn-row u-flex u-row-center u-col-cener">
                    <view class="btn-receive" hover-class="btn-hover" @touchstart.stop.prevent="getUserInfo">一键领取</view>
                </view>
                <view v-else class="btn-row u-flex u-col-cener">
                    <view class="btn btn-cancel" hover-class="btn-hover" @touchstart.stop.prevent="onClose">知道了</view>
                    <view class="btn btn-view" hover-class="btn-hover" @touchstart.stop.prevent="toCoupon">查看</view>
                </view>
            </view>
            <CustomModal
                :show="showRuleModel"
                width="534rpx"
                contentStyle="color:#1D1E20;font-weight:500;font-size: 28rpx;"
                :content="rule"
                confirmText="知道了"
                @confirm="showRuleModel = false"
            >
            </CustomModal>
        </u-popup>
        <u-popup
            :show="handleShowPopInBind"
            v-prevent-scroll="handleShowPopInBind"
            bgColor="transparent"
            mode="center"
            @close="onCloseInBind"
        >
            <view class="pop_content_bind">
                <image class="bg" src="https://wpm-cdn.dreame.tech/images/202206/62a954e1b37437352901999.png" />
                <image
                    class="btn-close"
                    src="https://wpm-cdn.dreame.tech/images/202206/336117-1655951836495.png"
                    @tap="onCloseInBind"
                />
                <view class="content-box u-flex u-flex-col u-row-left u-col-center">
                    <view class="title">注册有礼</view>
                    <view class="tip">{{ bindRewardData.point }}积分购物礼金已到账</view>
                    <view class="tip_2">下单可抵{{ bindRewardData.price }}元</view>
                    <image class="image" src="https://wpm-cdn.dreame.tech/images/202206/606701-1655951809830.png" />
                    <view class="btn" @tap="onCloseInBind">开心收下</view>
                </view>
            </view>
        </u-popup>
        <u-popup
            :show="handleShowPopOtherInBind"
            v-prevent-scroll="handleShowPopOtherInBind"
            bgColor="transparent"
            mode="center"
            @close="onCloseOtherInBind"
        >
            <view class="pop_content_other_bind">
                <image
                    class="btn-close"
                    src="https://wpm-cdn.dreame.tech/images/202206/336117-1655951836495.png"
                    @tap="onCloseOtherInBind"
                />
                <view class="content-box u-flex u-flex-col u-row-left u-col-center">
                    <view class="title">提示</view>
                    <view class="tip">
                        <text :decode="true">您已领取{{ bindRewardData.point }}积分购物礼金\n每人限领1次哦~</text>
                    </view>
                    <view class="btn" @tap="onCloseOtherInBind">我知道了</view>
                </view>
            </view>
        </u-popup>
        <u-popup
            :show="handleShowPopOtherInBind"
            v-prevent-scroll="handleShowPopOtherInBind"
            bgColor="transparent"
            mode="center"
            @close="onCloseOtherInBind"
        >
            <view class="pop_content_other_bind">
                <image
                    class="btn-close"
                    src="https://wpm-cdn.dreame.tech/images/202206/336117-1655951836495.png"
                    @tap="onCloseOtherInBind"
                />
                <view class="content-box u-flex u-flex-col u-row-left u-col-center">
                    <view class="title">提示</view>
                    <view class="tip">
                        <text :decode="true">您已领取{{ bindRewardData.point }}积分购物礼金\n每人限领1次哦~</text>
                    </view>
                    <view class="btn" @tap="onCloseOtherInBind">我知道了</view>
                </view>
            </view>
        </u-popup>
        <!-- 隐私协议 -->
        <!-- #ifdef MP-WEIXIN -->
         <!-- 应该只有小程序需要显示 -->
        <u-popup
            :show="privacyVisible && AppModule.platform != 'ohos'"
            v-prevent-scroll
            :closeOnClickOverlay="false"
            bgColor="transparent"
            mode="center"
            @close="onPrivacyDialogClose"
        >
            <view class="privacy_content">
                <!-- <view class="bg"></view> -->
                <view class="privacy_title">隐私政策更新</view>
                <scroll-view class="privacy_text" scroll-y show-scrollbar>
                    <!-- <rich-text :nodes="privacyContent"></rich-text> -->
                    <u-parse :content="privacyContent.replaceAll(/\n/g, '<br/>')"></u-parse>
                </scroll-view>
                <view class="btn-row u-flex u-col-cener">
                    <view class="btn btn-cancel" hover-class="btn-hover" @touchstart.stop.prevent="onPrivacyDialogClose">拒绝并退出</view>
                    <view class="btn btn-view" hover-class="btn-hover" @touchstart.stop.prevent="onPrivacyDialogAgree">同意并继续</view>
                </view>
            </view>
        </u-popup>
        <!-- #endif -->
    </div>
</template>
<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator';
import { UserModule } from '@/store/modules/user';
import { CartModule } from '@/store/modules/cart';
import { regAwardInfo, receiveAward, lockAppRegister } from '@/http/user';
import { updatePopupCount } from '@/http/recommend';
import CouponItem from '@/components/CouponItem/CouponItem.vue';
import PointItem from '@/components/CouponItem/PointItem.vue';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';

@Component({
    components: {
        CouponItem,
        PointItem,
    },
})
export default class WxLogin extends Vue {
    // 是否显示新人礼包弹框
    public showPopRegister: boolean = false;

    public point: any = null;

    // 确认关闭新人礼包弹框
    public showPopRegistered: boolean = false;

    // 礼包内容
    public list: any[] = [];

    // 是否已领取新人礼包
    public isReceive: boolean = false;

    // 是否显示邀请绑定奖励弹框
    public showPopInBind: boolean = false;

    // 是否显示其他人邀请绑定奖励弹框
    public showPopOtherInBind: boolean = false;

    // 绑定奖励信息
    public bindRewardData = {
        point: 0, // 奖励积分
        price: 0, // 奖励价格
    };

    public showNewLift: boolean = true;

    public showRuleModel: Boolean = false;
    public rule: String = '';

    public firstIn: boolean = true;

    // 隐私协议内容
    public privacyVisible: boolean = false;
    public privacyContent: string = '';
    public lastIsLoginStatus: boolean = null;
    public lastIsPhoneStatus: boolean = null;
    public pageRouteStr: string = '';

    get totalStyle() {
        return {
            color: this.isReceive ? 'rgba(34, 34, 34, .25)' : '#EA083F',
            'font-size': '48rpx',
        };
    } // 金额样式

    private unitSize: String = '28'; // 金额单位字号

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否登录
    get isLogin(): boolean {
        return UserModule.isLogin;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    // 隐私协议数据
    get PrivacyData(): any {
        return UserModule.privacyData;
    }

    // 是否授权手机号
    get uniPlatform(): string {
        return AppModule.uniPlatform;
    }

    // 处理邀请绑定奖励弹框
    get handleShowPopInBind(): boolean {
        if (!this.isLogin) return false;
        if (this.showPopRegistered && this.showPopInBind) return true;
        return false;
    }

    // 处理其他人邀请绑定奖励弹框
    get handleShowPopOtherInBind(): boolean {
        if (!this.isLogin) return false;
        if (this.showPopRegistered && this.showPopOtherInBind) return true;
        return false;
    }

    get loginStatus(): boolean {
        if (this.isLogin && this.isPhone) this.onLoginChange(this.isLogin)
        else {
            this.privacyVisible = false
            uni.showTabBar();
        }
        return this.isLogin && this.isPhone
    }

    // #ifdef MP-WEIXIN
    @Watch('isLogin', { immediate: true, deep: true })
    async onLoginChange(newVal: boolean) {
        !this.pageRouteStr && (this.pageRouteStr = this.getPageStr())
        if (newVal && this.isPhone) {
            if (this.lastIsLoginStatus === newVal && this.lastIsPhoneStatus === this.isPhone) return
            this.lastIsLoginStatus = newVal
            this.lastIsPhoneStatus = this.isPhone
            const res: any = await UserModule.getPrivaryList();
            if (res.changelog && UserModule.privacyData && UserModule.privacyData.agree_privacy !== true && AppModule.platform != 'ohos') {
               this.$nextTick(() => {
                if (this.pageRouteStr === this.getPageStr()) {
                    wx.hideTabBar({});
                    this.privacyContent = res.changelog
                    this.privacyVisible = true
                }
               })
            }
        } else {
            this.privacyVisible = false
            wx.showTabBar({});
        }
    }

    @Watch('PrivacyData', { deep: true })
    async onAgreeChange(newVal: any) {
        if (newVal && newVal.agree_privacy) {
            this.privacyVisible = false
            wx.showTabBar({});
        }
    }
    // #endif

    created() {
        if (uni.getStorageSync('showNewLift') == 'false') {
            this.showNewLift = false;
        }
        // #ifdef MP-WEIXIN
        if (this.isLogin) {
            // uni.showTabBar({ animation: false });
            // 已登录时, 返回LOGGED状态
            this.$emit('success', 'LOGGED');
            if (this.isPhone) {
                this.isReceive = true;
            }
            return;
        }
        // #endif
        this.workFlow();
    }

    onShow() {
    }

    getPageStr() {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        return route
    }

    beforeDestroy() {
        uni.$off('phoneAuth');
    }

    // 小程序登录
    workFlow() {
        // #ifdef H5
        UserModule.getUserInfo().then((res) => {
            if (res.isAppRegister) {
                this.showPop();
            }
            this.$emit('success', 'LOGIN');
        });
        // #endif

        // #ifdef MP-WEIXIN
        if (this.isLogin) return;
        UserModule.asyncWxLogin().then(async (res) => {
            // 邀请用户绑定
            if (this.firstIn && AppModule.employee_uid) {
                try {
                    await UserModule.bindUser(0);
                } finally {
                    this.firstIn = false;
                }
            }
            // 成功登录后, 返回LOGIN状态
            this.$emit('success', 'LOGIN');
            // 邀请新用注册接口
            // uni.showTabBar({ animation: false });
            if (this.isPhone) {
                this.isReceive = true;
                CartModule.loadList();
            }
            // 如果是首次登录, 则弹出领取新人礼包弹窗
            if (res.isRegister && res.is_new_gift == '0') {
                this.showPop();
            } else {
                this.showPopRegistered = true;
            }
            this.showInBindPop();
        });
        // #endif
    }

    // 授权用户信息
    async getUserInfo() {
        // #ifdef H5

        await receiveAward({ grant_type: 1 });
        this.isReceive = true;
        // #endif

        // #ifdef MP-WEIXIN
        const target = 'popRegister';
        UserModule.authFlow({ target });
        uni.$once('phoneAuth', (val: string) => {
            if (target === val) {
                console.log('==========> 用户已领取新人奖励');
                this.showInBindPop();
                this.isReceive = true;
            }
        });
        // #endif
    }

    showPop() {
        // 获取新人礼包信息
        const grant_typeGRANT_TYPE_ = '1';
        regAwardInfo(grant_typeGRANT_TYPE_).then((res) => {
            console.log('showPop res', res);
            if (Array.isArray(res) && res.length == 0) {
                return;
            }
            this.list = res.coupon || [];
            this.point = res.point;
            if ((this.list && this.list.length > 0) || !Array.isArray(this.point)) {
                this.showPopRegister = true;
            }
        });
    }

    showInBindPop() {
        const userInfo = UserModule.userInfo;
        // console.log('showInBindPop userInfo', userInfo);
        // 邀请绑定弹窗
        this.bindRewardData.point = userInfo.bind_reward_point || 0;
        this.bindRewardData.price = userInfo.bind_reward_price || 0;
        if (userInfo.bind_popup_count && userInfo.bind_popup_count > 0) {
            this.showPopInBind = true;
        }
        if (userInfo.other_bind_popup && userInfo.other_bind_popup > 0) {
            this.showPopOtherInBind = true;
        }
    }

    // 更新绑定奖励弹窗次数
    async getUpdatePopupCount() {
        const ACTION = '2';
        try {
            await updatePopupCount(ACTION);
        } catch (e) {
            console.error(`getUpdatePopupCount params=${ACTION} err=`, e);
        }
    }

    handleShowRule(rule) {
        this.rule = rule;
        this.showRuleModel = true;
    }

    onClose() {
        this.showPopRegister = false;
        this.showPopRegistered = true;
        lockAppRegister({});
        this.showNewLift = false;
        uni.setStorageSync('showNewLift', 'false');
    }

    onCloseInBind() {
        this.getUpdatePopupCount();
        this.showPopInBind = false;
    }

    onCloseOtherInBind() {
        this.showPopOtherInBind = false;
    }

    toCoupon() {
        this.showPopRegister = false;
        lockAppRegister({});
        // #ifdef H5
        if (!Array.isArray(this.point) && Object.keys(this.point).length && this.list.length) {
            // Utils.messageChannel('navigation', { type: 'mall', path: 'home/vip' });
            Utils.navigateTo('/pages/vipCenter/vipCenter');
        } else if (this.list.length) {
            Utils.navigateTo('/pagesA/coupon/coupon');
        } else {
            Utils.navigateTo('/pagesA/pointDetails/pointDetails');
        }
        // #endif
        // #ifdef MP-WEIXIN
        if (!Array.isArray(this.point) && Object.keys(this.point).length && this.list.length) {
            Utils.navigateTo('/pages/vipCenter/vipCenter');
        } else if (this.list.length) {
            uni.navigateTo({
                url: '/pagesA/coupon/coupon',
            });
        } else {
            Utils.navigateTo('/pagesA/pointDetails/pointDetails');
        }
        // #endif
    }

    // #ifdef MP-WEIXIN
    onPrivacyDialogClose() {
        wx.exitMiniProgram({
            success() {
                console.log('小程序已退出');
            },
            fail(err) {
                console.error('退出失败:', err);
            },
        });

        this.privacyVisible = false;
        uni.showTabBar();
    }

    async onPrivacyDialogAgree() {
        await UserModule.agreePrivary();
        this.privacyVisible = false;
        uni.showTabBar();
    }
    // #endif
}
</script>
<style lang="scss">
@import './WxLogin.scss';
</style>
