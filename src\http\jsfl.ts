import req from './requestGo/index.js';
/* 获取用户信息接口 */
export const getPrizeDrawingInfo = (params) => req.post('/api/v1/prize-drawing/info', { ...params });
/* 收集用户信息 */
export const updatePrizeDrawingStatus = (params) => req.post('/api/v1/prize-drawing/update', { ...params });
/* 收集用户信息 */
export const sharePrizeDrawing = (params) => req.post('/api/v1/prize-drawing/share', { ...params });
/* 用户报名 */
export const signPrizeDrawing = (params) => req.post('/api/v1/prize-drawing/update-status', { ...params });
/* 中奖名单公示*/
export const getPrizeDrawingList = (params) => req.post('/api/v1/prize-drawing/list-winner', { ...params });
/* 中奖或未中奖弹窗 */
export const updatePrizeDrawingTipStatus = (params) => req.post('/api/v1/prize-drawing/update-tip-status', { ...params });
