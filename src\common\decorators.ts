import Utils from './Utils';

/**
 * 检查是否需要跳转到DreameApp的方法装饰器工厂
 * 如果需要跳转，则直接返回，不执行被装饰的方法
 * @param linkProperty 获取link的属性名，默认为'link'
 */
export function CheckAppJump(linkProperty: string = 'link') {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;
        descriptor.value = function (...args: any[]) {
            let link = this[linkProperty]
            if (!link) {
                // 从this中获取link
                link = findPropertyUpward(this, linkProperty);
            }
            // 没查找到有效的link，直接执行原方法
            if (!link) {
                console.log(method);
                return method.apply(this, args);
            }
            // 执行跳转检查
            const hasJumped = Utils.jumpToDreameApp(link);
            if (hasJumped) {
                return; // 如果执行了 window.location.href，则不继续执行
            }

            // 如果没有跳转，则执行原方法
            return method.apply(this, args);
        };

        return descriptor;
    };
}

function findPropertyUpward(context: any, propertyName: string) {
    // 检查 context 是否有效
    if (!context) {
        return null;
    }
    let parent = context.$parent;
    // 如果没有父组件，直接返回 null
    if (!parent) {
        return null;
    }

    let property = parent[propertyName];

    while (parent && (!property)) {
        parent = parent.$parent;
        if (parent) {
            property = parent[propertyName];
        }
    }
    return property;
}
