<template>
    <view class="clean-card">
        <view class="info">
            <view class="info-title" @click="gotoDevice('detail')">
                <view class="title">{{ name }}</view>
                <view class="status" v-if="status">{{ status }}</view>
                <view class="battery-wrap" v-if="battery">
                    <text class="battery-text">{{ battery }}</text>
                    <view class="battery-unit-wrap">
                        <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d4849a78fc6860096749.png" alt="电量" class="device-info-battery" />
                        <text class="battery-unit-text">%</text>
                    </view>
                </view>
            </view>
            <view class="info-image">
                <image  class="info-image-icon" :src="image" alt="设备图片" mode="aspectFit" />
            </view>
        </view>
        <view class="more" @click="gotoDevice('list')">
            查看更多
        </view>
    </view>
</template>
<script lang="ts">
import Utils from '@/common/Utils';

const iconCharge =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686637a2539cb3420010450.png';
const iconPause =
   'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b385e9b18b6350010674.png';
const iconBackCharge =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686636812a0251720010377.png';
const defaultDeviceImage =
    'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686638eeea5e89600012418.png';

const deviceCanStopStatus = [1, 7, 9, 10, 11, 12, 15, 16, 17, 18, 20, 22, 23, 25, 26, 27, 28, 37, 38, 97, 98, 101, 107];

export default {
    name: 'WashMachine',
    props: {
        currentDevice: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
            supportVideo: false, // 是否支持视频
        };
    },
    computed: {
        battery() {
            return this.currentDeviceStatus.battery || 0;
        },
        status() {
            return this.currentDeviceStatus.status || '';
        },
        supportFastCommand() {
            const { deviceInfo = {}} = this.currentDevice;
            const { feature = '' } = deviceInfo;
            // 支持快捷指令的设备型号
            return feature === 'fastCommand';
        },
        image() {
            const { deviceInfo = {}} = this.currentDevice;
            const { mainImage = {}} = deviceInfo;
            const { imageUrl = '' } = mainImage;
            return imageUrl || defaultDeviceImage;
        },
        workIcon() {
            const device = this.currentDevice;
            if (!device) return '';
            if (device.online) {
                if (deviceCanStopStatus.includes(device.latestStatus)) {
                    return iconPause;
                }
                return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b36e4765194850020187.png';
            }
            return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b36e4765194850020187.png';
        },
        chargeIcon(): string {
            const device = this.currentDevice;
            if (!device) return '';
            if (device.online) {
                if (device.latestStatus === 5) {
                    return iconCharge;
                }
                return iconBackCharge;
            }
            return 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686659321b9071130010815.png';
        },
        name() {
            return (
                this.currentDevice.customName ||
                this.currentDevice.deviceInfo?.displayName ||
                this.currentDevice.displayName ||
                ''
            );
        },
        hasVideoPermission() {
            const { permissions = '' } = this.currentDevice;
            if (permissions) {
                const split = permissions.split(',').map((item) => item.toUpperCase());
                return split.includes('VIDEO');
            }
            return permissions;
        },
    },
    watch: {
        currentDevice: {
            handler() {
                this.getDeviceStatus();
            },
            deep: true,
        },
    },
    mounted() {
        // 获取当前设备状态
        this.getDeviceStatus();
    },
    methods: {
        // 获取设备状态
        getDeviceStatus() {
            const { did = '', bindDomain = '', model } = this.currentDevice;
            const id = this.getRandomNumber();
            const bind_id = bindDomain.split('.')[0];
            const data = {
                did,
                id,
                from: 'mapp',
                method: 'get_properties',
                params: [
                    { did, siid: 2, piid: 1 },
                    { did, siid: 3, piid: 1 },
                    { did, siid: 4, piid: 38 },
                    { did, siid: 4, piid: 83 },
                    { did, siid: 4, piid: 48 },
                ],
            };
            const body = {
                scene: 'SEND_COMMAND',
                id,
                did,
                data: JSON.stringify(data),
            };
            const commandResultCB = (data) => {
                if (data.code == 0) {
                    const res = JSON.parse(data.data);
                    if (res.success) {
                        const result = res.data.result;
                        const onlineStatus = result.filter((r) => r.siid === 2 && r.piid == 1);
                        const batteryStatus = result.filter((r) => r.siid === 3 && r.piid == 1);
                        const supportVideo1 = result.filter((r) => r.siid === 4 && r.piid == 38);
                        const supportVideo2 = result.filter((r) => r.siid === 4 && r.piid == 83);
                        const supportVideoStatus1 = supportVideo1[0]?.value;
                        const supportVideoStatus2 = supportVideo2[0]?.value;
                        const _supportVideo = supportVideoStatus1 | supportVideoStatus2;
                        this.supportVideo = (_supportVideo & 1) !== 0;
                        const newStatus = { ...this.currentDeviceStatus };
                        if (batteryStatus.length > 0) {
                            newStatus.battery = batteryStatus[0].value;
                            this.currentDeviceStatus = newStatus;
                        }
                        if (onlineStatus.length > 0) {
                            Utils.newMessageChannel(
                                'DEVICE',
                                'getStatusStr',
                                {
                                    model: model,
                                    latestStatus: onlineStatus[0].value,
                                },
                                (res) => {
                                    newStatus.status = res.data;
                                    this.currentDeviceStatus = { ...newStatus };
                                },
                            );
                        }
                    }
                }
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: body,
                },
                commandResultCB,
            );
        },
        // 进入设备详情，插件
        // gotoDevice() {
        //   //  Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        //     // Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        // },
        gotoDevice(type) {
          if (type === 'detail') {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
          } else {
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
          }
            // Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'main' });
        },
        // 编辑设备信息
        editDeviceInfo() {
            this.$emit('edit-device');
        },
        getRandomNumber() {
            return Math.floor(Math.random() * 500000) + 1;
        },
        // 开始清洁
        startClean() {
            const { did = '', bindDomain = '', latestStatus, model } = this.currentDevice;
            const id = this.getRandomNumber();
            const bind_id = bindDomain.split('.')[0];
            let data = {};
            if (deviceCanStopStatus.includes(latestStatus)) {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 },
                };
                Utils.reportEvent('device_pause', {
                    device_model: model,
                });
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    from: 'mapp',
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '2,1,{"app_auto_clean":1}' }], aiid: 1 },
                };
                Utils.reportEvent('device_start', {
                    device_model: model,
                });
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data),
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: params,
                },
                (res) => {
                    console.log(' start clean ', res);
                },
            );
        },
        // 返回基站
        goBackHome() {
            const { did = '', bindDomain = '', latestStatus } = this.currentDevice;
            const id = this.getRandomNumber();

            const bind_id = bindDomain.split('.')[0];
            let data = {};
            if (latestStatus === 5) {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id,
                    method: 'action',
                    params: { did, siid: 2, in: [{ piid: 100, value: '1,{"app_pause":1}' }], aiid: 2 },
                };
            } else {
                // eslint-disable-next-line object-curly-spacing
                data = {
                    id: id,
                    from: 'mapp',
                    method: 'action',
                    params: { did: did, siid: 3, in: [{ piid: 100, value: '{"charge":1}' }], aiid: 1 },
                };
            }
            const params = {
                scene: 'SEND_COMMAND',
                id,
                did,
                bind_id,
                data: JSON.stringify(data),
            };
            Utils.newMessageChannel(
                'HTTP',
                'request',
                {
                    method: 'post',
                    path: `/dreame-iot-com-${bind_id}/device/sendCommand`,
                    body: params,
                },
                (res) => {
                    console.log(' start charge ', res);
                },
            );
        },
        // 进入视频页面
        goVideoPage() {
            if (this.hasVideoPermission) {
                Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'video' });
            } else {
                // 弹出提示
                uni.showToast({
                    title: '若要启用此功能，请联系设备主任',
                    icon: 'none',
                    duration: 2500,
                });
            }
        },
        // 进入快捷指令页面
        goQuickOrderPage() {
            Utils.newMessageChannel('RN_PLUGIN', 'open', { device: this.currentDevice, entrance: 'quickCommand' });
        },
    },
};
</script>
<style lang="scss" scoped>
.clean-card {
    width: 250rpx;
    height: 201rpx;
    background-color: rgb(218, 228, 240);
    padding: 16rpx;
    border-radius: 40rpx;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    .info {
        // width: 250rpx;
        width: 100%;
        height: 80%;
        display: flex;
        justify-content: space-between;
        .info-title {
            font-size: 24rpx;
            font-weight: bold;
            color: #121212;;
            margin-bottom: 6px;
            .title{
                font-size: 20rpx;
                white-space: nowrap;    /* 不换行 */
                overflow: hidden;      /* 超出部分隐藏 */
                text-overflow: ellipsis; /* 显示省略号 */
                width: 100%;
                max-width: 80rpx;
            }
            .status{
                font-size: 22rpx;
                width: 114rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .battery-wrap{
                display: flex;
                align-items: center;
                .battery-unit-wrap{
                    display: flex;
                    align-items: center;
                    flex-direction: column;
                    justify-content: last baseline;
                    padding-top: 10rpx;
                    .device-info-battery{
                        width: 28rpx;
                        height: 26rpx;

                    }
                }
                .battery-unit-text{
                    font-size: 22rpx;
                    color: #fff;
                }
                .battery-text{
                    font-size: 48rpx;
                    color: #fff;

                }
            }
        }

        .info-image {
          //  min-width: 50%;
            width: 100rpx;
            height: 90%;
            .info-image-icon{
                width: 100%;
                height: 100%;
            }

        }
    }

    .more {
        width: 208rpx;
        height: 38rpx;
        color: #AAC4E4;
        font-size: 24rpx;
        text-align: center;
        line-height: 38rpx;
        border-radius: 40rpx;
        background-color: #fff;
        margin: 0 auto;
        // margin-top: 20rpx;
    }
}
</style>
