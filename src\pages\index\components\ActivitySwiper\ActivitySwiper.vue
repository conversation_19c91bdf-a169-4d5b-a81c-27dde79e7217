<template>
    <view class="prod">
        <view class="swiper">
            <swiper
                class="swiper-box"
                :circular="true"
                :autoplay="false"
                :interval="3000"
                :previous-margin="'30rpx'"
                :next-margin="'30rpx'"
                :center-slide="true"
            >
                <!-- 拼团 -->
                <swiper-item>
                    <view class="swiper-item" @click="jumpHandler(swiperList[3])">
                        <img :src="swiperList[3].image" mode="aspectFill" class="swiper-item-image" />
                    </view>
                </swiper-item>

                <!-- 五折购 -->
                <!-- <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem :oneGoodsList="fiveHalfGoodsList" jumpUrl="/pagesC/offPurchase/offPurchase" bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888cf5aac9077070011045.png" type="fiveHalf"></oneYunBuyItem>
                    </view>
                </swiper-item> -->
                <!-- 6折购 -->
                <!-- <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem
                            :oneGoodsList="sixHalfGoodsList"
                            jumpUrl="/pagesC/sixDiscount/sixDiscount"
                            bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68909f71f33539960010776.png"
                            type="sixDis"
                        ></oneYunBuyItem>
                    </view>
                </swiper-item> -->

                <!-- 追觅合伙人 -->
                <swiper-item>
                    <view class="swiper-item" @click="jumpHandler(swiperList[1])">
                        <img :src="swiperList[1].image" mode="aspectFill" class="swiper-item-image" />
                    </view>
                </swiper-item>

                <!-- 赚钱花 -->
                <swiper-item>
                    <view class="swiper-item" @click="jumpHandler(swiperList[0])">
                        <img :src="swiperList[0].image" mode="aspectFill" class="swiper-item-image" />
                    </view>
                </swiper-item>

                <!-- 赚金币 -->
                <swiper-item>
                    <view class="swiper-item" @click="jumpHandler(swiperList[4])">
                        <img :src="swiperList[4].image" mode="aspectFill" class="swiper-item-image" />
                    </view>
                </swiper-item>

                <!-- 一元购 -->
                <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem
                            :oneGoodsList="oneGoodsList"
                            type="oneYun"
                            jumpUrl="/pagesC/oneYuanFlashSale/oneYuanFlashSale"
                            bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888c7bd2d9d11870015529.png"
                        ></oneYunBuyItem>
                    </view>
                </swiper-item>

                <!-- 八折内购 -->
                <swiper-item>
                    <view class="swiper-item" @click="jumpHandler(swiperList[5])">
                        <img :src="swiperList[5].image" mode="aspectFill" class="swiper-item-image" >
                    </view>
                </swiper-item>

                <!-- <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem :oneGoodsList="oneGoodsList" type="oneYun" jumpUrl="/pagesC/oneYuanFlashSale/oneYuanFlashSale" bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888c7bd2d9d11870015529.png"></oneYunBuyItem>
                    </view>
                </swiper-item>
                <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem :oneGoodsList="sixHalfGoodsList" jumpUrl="/pagesC/sixDiscount/sixDiscount" bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c9eb197efc6220015472.png" type="sixHalf"></oneYunBuyItem>
                    </view>
                </swiper-item>
                <swiper-item>
                    <view class="swiper-item">
                        <oneYunBuyItem :oneGoodsList="eightDisGoodsList" jumpUrl="/pagesC/eightDiscount/eightDiscount" bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688a003b72df44710010788.png" type="eightDis"></oneYunBuyItem>
                    </view>
                </swiper-item>
                <swiper-item>
                    <view class="swiper-item">
                        <PointSwiper :points="points" jumpUrl="/pagesA/point/shop_point" bgUrl="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d4981df331230011379.png" :deduction_rate="deduction_rate" :point="points"></PointSwiper>
                    </view>
                </swiper-item>
                <swiper-item v-for="item in swiperList" :key="item.id">
                    <view class="swiper-item" @click="jumpHandler(item)">
                        <img :src="item.image" mode="aspectFill" class="swiper-item-image" >
                    </view>
                </swiper-item> -->
            </swiper>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import oneYunBuyItem from './oneYunBuyItem.vue';
import PointSwiper from './PointSwiper.vue';
import Utils from '@/common/Utils';

@Component({
    components: {
        oneYunBuyItem,
        PointSwiper,
    },
})
export default class ActivitySwiper extends Vue {
    @Prop({ type: Array, default: [] })
    readonly oneGoodsList!: Array<any>;

    @Prop({ type: Array, default: [] })
    readonly fiveHalfGoodsList!: Array<any>;

    @Prop({ type: Array, default: [] })
    readonly sixHalfGoodsList!: Array<any>;

    @Prop({ type: Array, default: [] })
    readonly eightDisGoodsList!: Array<any>;

    @Prop({ type: Array, default: [] })
    readonly points!: Array<any>;

    @Prop()
    readonly deduction_rate!: number;

    public name: string = '';

    get fullName() {
        return this.name;
    }

    public swiperList: any[] = [
        {
            id: 1,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888bc9ee9c859580012170.png',
            alt: '赚钱花',
            jumpUrl: '/pagesC/earnMoneySpend/earnMoneySpend',
        },
        {
            id: 2,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68901ab5db1ce8970011485.png',
            alt: '追觅合伙人',
            jumpUrl: '/pagesC/ambassador/login/index',
        },
        {
            id: 3,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888d2656deb74500030980.png',
            alt: '百亿购物金',
            jumpUrl: '/pagesC/billionsOfShoppingGold/billionsOfShoppingGold',
        },
        {
            id: 4,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c57f92ba2b1790010662.png',
            alt: '拼团',
            jumpUrl: '/pagesB/groupGoods/groupGoods',
        },
        {
            id: 5,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688ad6beafea77210012072.png',
            alt: '金币',
            jumpUrl: '/pagesC/goldCoins/goldCoins',
        },
        {
            id: 6,
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688cde88186ed1000011325.png',
            alt: '八折内购',
            jumpUrl: '/pagesC/purchase/purchase'
        },
    ];

    jumpHandler(item) {
        console.log('%c item: ', 'font-size:16px;background-color: #ED9EC7;color:#fff;', item)
        Utils.navigateTo(item.jumpUrl);
    }
}
</script>
<style lang="scss" scoped>
.swiper {
    height: 160rpx;
    .swiper-box {
        height: 100%;
        .swiper-item {
            width: 100%;
            height: 100%;
            padding: 0 10rpx; /* 添加左右间距 */
            box-sizing: border-box;
            .swiper-item-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }
}
</style>
