<template>
    <view class="search-container" :style="{ paddingTop: `${statusBarHeight}rpx` }">
        <!-- #ifdef MP-WEIXIN -->
        <CustomBar :title="title" background="#fff"></CustomBar>
        <!-- #endif -->
        <view class="search-area">
            <view class="serach_heard">
                <!-- #ifdef H5 -->
                <view class="search_return" @click="searchBack">
                    <img src="https://wpm-cdn.dreame.tech/images/202302/508756-1676371936507.png" alt="" />
                </view>
                <!-- #endif -->
                <view class="search_inputs">
                    <view class="search_border" :style="{ borderBottom: `2rpx solid ${borderBottomLine}` }">
                        <view class="search_input">
                            <!-- #ifdef MP-WEIXIN -->
                            <img
                                src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png"
                                class="input-icon"
                            />
                            <!-- #endif -->
                            <input
                                ref="input"
                                class="input"
                                type="text"
                                v-model.trim="keyword"
                                :focus="focus"
                                :maxlength="50"
                                :placeholder="placeholder"
                                confirm-type="search"
                                @input="handleInputSearch"
                                @confirm="handleSearchClick"
                                @keydown.enter="handleSearchClick"
                                placeholder-style="font-size: 28rpx;color: #777777;"
                            />
                        </view>
                        <image
                            :lazyLoad="true"
                            class="search_clear"
                            v-show="keyword.length > 0"
                            @click="clearInput"
                            src="https://wpm-cdn.dreame.tech/images/202306/446725-1686362627223.png"
                        />
                    </view>
                </view>
                <!-- #ifdef MP-WEIXIN -->
                <view class="search_cancel" @click="cancelClick">取消</view>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <view class="search_box" @click="handleSearchClick">搜索</view>
                <!-- #endif -->
            </view>
        </view>

        <view class="result-tabs" v-if="searchType === 2">
            <view
                v-for="(item, index) in resultTabList"
                :key="item.value"
                :class="{ active: index === resultTabIndex }"
                @click="handleResultTabClick(index)"
            >
                {{ item.label }}
            </view>
        </view>

        <!-- 历史搜索 -->
        <SearchHistory
            ref="SearchHistory"
            v-if="searchType === 1 && searchList.length === 0 && searchThinkList.length === 0"
            style="margin-top: 40rpx"
            @onSearch="handleHistorySearch"
        />

        <!-- 探索发现 -->
        <SearchDiscover
            v-if="searchType === 1 && searchList.length === 0 && searchThinkList.length === 0"
            @onSearch="handleHistorySearch"
        />
        <HotSearch v-if="searchType === 1 && searchList.length === 0 && searchThinkList.length === 0" />
        <block v-if="hasSearchResult">
            <scroll-view
                scroll-y
                class="search-result"
                :style="{ height: `calc(100vh - ${scrollHeight}rpx)` }"
                lower-threshold="100"
                @scrolltolower="onreachBottom"
            >
                <view>
                    <!-- 联想列表 -->
                    <view
                        v-if="searchList.length > 0 || searchThinkList.length > 0"
                        v-show="searchType === 1"
                        :style="{ paddingTop: '8rpx' }"
                    >
                        <block v-for="(item, index) in searchThinkList" :key="item.content_id">
                            <view class="search-item" @click="handleJump(item, index)">
                                <!-- #ifdef MP-WEIXIN -->
                                <view class="search-item-text">
                                    <rich-text class="u-line-1" :nodes="item.titleContent"></rich-text>
                                </view>
                                <!-- #endif -->
                                <!-- #ifdef H5 -->
                                <view class="search-item-text u-line-1" v-html="item.titleContent"></view>
                                <!-- #endif -->
                                <img src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png" alt="" />
                            </view>
                        </block>
                        <view class="no-more" v-if="isLoaded && searchThinkList.length > 0">没有更多了~</view>
                    </view>
                    <view v-show="searchType === 2">
                        <!-- 结果列表 -->
                        <view class="page-box" v-if="!isShow">
                            <view class="my-waterfall" v-if="resultTabIndex === 0">
                                <my-Waterfall
                                    :showView="false"
                                    :finished="isLoaded"
                                    v-model="searchList"
                                    v-if="searchList"
                                    @change="handleJump"
                                    ref="uWaterfall"
                                    paddingBottom="70rpx"
                                ></my-Waterfall>
                            </view>

                            <view v-else :style="{ paddingTop: '10rpx' }">
                                <UserRelationItem
                                    v-for="item in searchList"
                                    :r="item"
                                    :key="item"
                                    @load="handleSearchClick()"
                                />
                                <view class="no-more" v-if="isLoaded && searchList.length > 0">没有更多了~</view>
                            </view>
                        </view>
                        <!-- 暂无数据 -->
                        <view v-else>
                            <view class="no-more"> 暂无搜索记录，快去探索吧～ </view>
                            <FriendList1 v-if="resultTabIndex === 0" :tabsIndex="0" :ListData="ListData" />
                            <view v-if="resultTabIndex === 1" class="searchType2">
                                <view class="u-recommend-box" style="margin-top: 40rpx">
                                    <view class="u-flex u-row-between">
                                        <view class="u-recommend-title">推荐关注</view>
                                        <view class="u-flex" style="margin-right: 24rpx" @click="goRefresh">
                                            <img
                                                src="@/static/friend/icon_friend_refresh.png"
                                                style="width: 40rpx; height: 40rpx"
                                            />
                                            <view style="font-size: 28rpx; color: #777777; margin-left: 10rpx"
                                                >刷新
                                            </view>
                                        </view>
                                    </view>
                                    <view
                                        class="u-recommend-content"
                                        v-for="(item, index) in recommentList"
                                        :key="index"
                                    >
                                        <view class="u-recommend-left">
                                            <!-- <view style="width: 96rpx; height: 96rpx; border-radius: 48rpx;" @click="goUserInfo(item.user_id)">
                                        <img :src="item.avatar" style="width: 40rpx; height: 40rpx; border-radius: 48rpx;" />
                                    </view> -->
                                    <view
                                                style="width: 96rpx; height: 96rpx; border-radius: 48rpx; position: relative;"
                                                @click="goUserInfo(item.user_id)"
                                            >
                                                <img
                                                    :src="item.avatar"
                                                    style="width: 96rpx; height: 96rpx; border-radius: 48rpx"
                                                />
                                              <img v-if="item.user_avatar_icon" :src="item.user_avatar_icon" style="width: 35rpx; height:35rpx; border-radius: 17.5rpx; position: absolute;bottom: 0;right: 0;" />
                                            </view>
                                    <view class="u-flex-col" style="margin-left: 32rpx; width: 320rpx;">
                                        <view style="font-size: 32rpx; color: #121212;">{{ item.nick_name }}
                                        </view>
                                        <view style="font-size: 28rpx; color: #777777;">{{ item.uid }}</view>
                                    </view>
                                        </view>
                                        <view
                                            class="u-recomment-right"
                                            v-if="item.follow_status == 0"
                                            @click="handleUserFollow(item, index)"
                                        >
                                            <img
                                                src="@/static/friend/icon_recomment_add.png"
                                                style="width: 32rpx; height: 32rpx"
                                            />
                                            关注
                                        </view>
                                        <view
                                            class="u-recomment-right-yes"
                                            v-if="item.follow_status == 1"
                                            @click="handleUserFollow(item, index)"
                                        >
                                            已关注
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <FriendList1 v-if="resultTabIndex === 1" :tabsIndex="0" :ListData="ListData" />
                        </view>
                    </view>
                </view>
            </scroll-view>
        </block>

        <custom-toast ref="customToast" />
        <!-- #ifdef MP-WEIXIN -->
        <privacy />
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import SearchHistory from './components/SearchHistory.vue';
import GoodItem from './components/GoodItem/GoodItem.vue';
import CustomSearch from '@/components/CustomSearch/CustomSearch.vue';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import {
    createSearchHistory,
    getContentListNew,
    getSearchUser,
    userFollow,
    userRelation,
    userFollowDelete,
    getRecommentList,
    getExVideoList,
} from '@/http/requestGo/community';
import MyWaterfall from '@/pages/contents/components/MyWaterfall.vue';
import FriendList1 from '@/pages/contents/components/FriendList/FriendList.vue';
import { UserModule } from '@/store/modules/user';
import UserRelationItem from '../components/UserRelationItem/index.vue';
import SearchDiscover from './components/SearchDiscover/SearchDiscover.vue';
import HotSearch from './components/HotSearch/HotSearch.vue';
@Component({
    components: {
        SearchHistory,
        GoodItem,
        CustomSearch,
        MyWaterfall,
        UserRelationItem,
        SearchDiscover,
        HotSearch,
        FriendList1,
    },
})
export default class Search extends Vue {
    public currActiveId: any = process.env.VUE_APP_TOPIC_ID;
    public last_content_id1: string = '';
    public title: string = '搜索';
    public bgColor: string = 'transparent'; // 导航栏背景
    public isLoaded: boolean = false;
    public keyword: string = '';
    public placeholder: string = '请输入要搜索的关键词';
    // 是否展示搜索列表
    public hasSearchResult: boolean = false;
    public focus: boolean = true;
    public list = [];

    public borderBottomLine: string = '#121212';
    // 查询结果tab
    public resultTabIndex: number = 0;
    public lastContentId: string = '';
    /** 1-联想列表搜索 2-结果列表搜索 */
    public searchType = 1;
    public isSearching = false;
    public page: number = 1;
    public page_size = 20;
    public contentPage = 1;
    public userPage = 1;
    public searchList: Array<any> = [];
    public searchThinkList: Array<any> = [];
    public ListData: Array<any> = [];
    public commentCount: number = 0;
    public allCommentList: any = [];
    public currentPage: number = 1;
    public recommentList: any = [];
    public index: number = 0;
    public creator: any = -1;
    public operContentOpen: boolean = false; // 二次确认弹框
    public page1: number = 1;
    public page_size1: number = 10;
    public isShow: boolean = false;
    public isLoading: Boolean = false;
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    resultTabList = [
        {
            label: '全部',
            value: 0,
        },
        {
            label: '用户',
            value: 1,
        },
    ];

    get statusBarHeight(): number {
        let statusBarHeight;
        // #ifdef H5
        statusBarHeight = AppModule.statusBarHeight;
        // #endif
        // #ifdef MP-WEIXIN
        statusBarHeight = 0;
        // #endif
        return statusBarHeight;
    }

    get scrollHeight() {
        let result;
        // #ifdef H5
        result = AppModule.statusBarHeight + 88 + 100;
        // #endif
        // #ifdef MP-WEIXIN
        result = 370;
        // #endif
        return result;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    // 是否授权
    get wxAuth(): boolean {
        return UserModule.wxAuth;
    }

    // 是否授权手机号
    get isPhone(): boolean {
        return UserModule.isPhone;
    }

    $refs!: {
        input;
        SearchHistory: SearchHistory;
    };

    onLoad(options) {
        // const list = decodeURIComponent(options.listData);
        // const arr = JSON.parse(list);
        // this.ListData = arr;
        // #ifdef H5
        if (options.type === 'user') {
            this.resultTabIndex = 1;
            this.placeholder = '搜索用户名字';
        }
        // setTimeout(() => {
        //     this.$refs.input._focus();
        // }, 2000);
        this.getRecommentList();
        this.getListData();
        // #endif
    }

    goRefresh() {
        if (this.commentCount >= 10) {
            this.commentCount = 0;
            this.getRecommentList();
        } else {
            const count = this.allCommentList.length / 3;
            if (this.currentPage == count) {
                this.currentPage = 1;
            } else {
                this.currentPage++;
            }
            this.recommentList = this.allCommentList.slice((this.currentPage - 1) * 3, (this.currentPage - 1) * 3 + 3);
        }
    }

    async getRecommentList() {
        this.recommentList = [];
        const res = await getRecommentList({ user_id: '', page: this.page, page_size: 21 });
        if (res.success) {
            this.allCommentList = res.data.list;
            if (res.data.list) {
                if (res.data.list.length >= 3) {
                    for (let i = 0; i < 3; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                } else {
                    for (let i = 0; i < res.data.list.length; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                }
            }
        }
    }

    handleUserFollow(item, index) {
        console.log(item);
        this.index = index;
        this.creator = item.user_id;
        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.user_id);
            if (item.follow_status === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    this.commentCount = +this.commentCount;
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        item.follow_status = res.data.follow_status;
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +this.creator });
                if (res.success) {
                    Utils.Toast('取消关注');
                    this.commentCount = -this.commentCount;
                    userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                }
                this.operContentOpen = false;
                setTimeout(() => {
                    this.operContent = { type: '' };
                }, 500);
            }
        }, 500);
    }

    changeFollowStatus(item, status) {
        this.recommentList.forEach((i) => {
            if (i.user_id === item.user_id) {
                i.follow_status = status;
            }
        });
    }

    goUserInfo(creator) {
        console.log('goUserInfo', creator);
        Utils.jumpPersonHome(creator);
    }

    onShow() {
        if (!this.keyword) {
            return;
        }
        this.initSearchStatus();
        this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
    }

    goBack() {
        Utils.goBack();
    }

    onBackPress(event) {
        if (event.from === 'backbutton') {
            Utils.goBack();
            return true;
        }
        return false;
    }

    handleHistorySearch(val) {
        this.keyword = val;
        this.searchType = 2;
        this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
    }

    clearInput() {
        this.keyword = '';
        this.searchType = 1;
        this.hasSearchResult = false;
        this.initSearchStatus();

        // #ifdef H5
        this.$refs.input._focus();
        // #endif
    }

    cancelClick() {
        return Utils.goBack();
    }

    searchBack() {
        return Utils.goBack();
    }

    handleJump(item, index) {
        // #ifdef H5
        if (item.jump_url) {
            Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
            return;
        }
        // #endif
        if (this.resultTabIndex === 0) {
            const { content_id, type } = item;
            uni.navigateTo({
                url:
                    type === 1
                        ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                        : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}`,
            });
        } else {
            if (this.searchList[index]) {
                Utils.jumpPersonHome(this.searchList[index].user_id);
            } else {
                Utils.jumpPersonHome(item.user_id);
            }
        }
    }

    handleResultTabClick(index) {
        this.hasSearchResult = false;
        this.resultTabIndex = index;
        this.initSearchStatus();

        this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
    }

    // 每次搜索或者切换tab都需要重置的状态
    initSearchStatus() {
        this.contentPage = 1;
        this.userPage = 1;
        this.searchList = [];
        this.searchThinkList = [];
        this.lastContentId = '';
        this.isLoaded = false;
    }

    getHighlightedText(keyword, item, replaceStr = '<div style="display: inline-block; color: #AB8C5E;">$1</div>') {
        const regex = new RegExp(`(${keyword})`, 'i');
        return item.replace(regex, replaceStr);
    }

    updateSearchResult(res) {
        this.hasSearchResult = true;
        this.lastContentId = res.data.last_content_id;

        const result = (res.data.list || []).map((item) => {
            const titleContent = this.getHighlightedText(this.keyword, item.title || item.nick_name);
            return { ...item, titleContent };
        });

        if (result.length < this.page_size) {
            this.isLoaded = true;
        }

        if (this.searchType === 1) {
            this.searchThinkList = [...this.searchThinkList, ...result];
        } else {
            // #ifdef MP-WEIXIN
            if (this.wxAuth && this.isPhone) {
                createSearchHistory({ keyword: this.keyword });
            }
            // #endif

            // #ifdef H5
            createSearchHistory({ keyword: this.keyword });
            // #endif
            this.searchList = [...this.searchList, ...result];
            console.log(this.searchList, 'this.searchList222');
            if (!this.searchList.length) {
                console.log(this.searchList, 'this.searchList111');
                this.isShow = true;
            } else {
                this.isShow = false;
            }
        }
    }

    handleContentSearch() {
        getContentListNew({
            keyword: this.keyword,
            page: this.contentPage++,
            page_size: this.page_size,
            search_type: this.searchType,
            last_content_id: this.lastContentId,
        })
            .then((res) => this.updateSearchResult(res))
            .finally(() => {
                this.isSearching = false;
            });
    }

    handleUserSearch() {
        uni.showLoading({ title: '加载中' });
        getSearchUser({
            keyword: this.keyword,
            page: this.userPage++,
            page_size: this.page_size,
            search_type: this.searchType,
        })
            .then((res) => {
                this.updateSearchResult(res);
                uni.hideLoading();
            })
            .finally(() => {
                this.isSearching = false;
            });
    }

    // input输入触发
    handleInputSearch() {
        this.searchType = 1;
        this.initSearchStatus();

        uni.$u.debounce(() => {
            // 键盘搜索优先级高，键盘搜索触发时，input输入不执行搜索回调
            if (this.isSearching) return;
            this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
        }, 500);
    }

    // 键盘搜索触发
    handleSearchClick() {
        if (this.keyword === '') {
            this.hasSearchResult = false;
            return;
        }
        if (this.isSearching) return;

        this.isSearching = true;
        this.searchType = 2;
        this.initSearchStatus();
        this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
    }

    async onreachBottom() {
        if (this.isLoading) return;
        this.isLoading = true;
        if (this.searchType === 2 && !this.searchList.length) {
            await this.getListData();
            this.isLoading = false;
        }
        if (this.isLoaded) {
            return;
        }
        this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
    }

    async getExVideoList() {
        const p = this.page1;
        const l = this.last_content_id1;
        const topic_id: number = +this.currActiveId;
        const page: number = p;
        const page_size: number = 10;
        const params = {
            page,
            page_size: page_size,
            topic_id,
            direction: 0,
            last_content_id: l,
        };
        const res = await getExVideoList(params, false);
        if (res.data) {
            this.$emit('refreshFinish');
            const { list = [] } = res.data;
            if (list.length > 0) {
                this.ListData = this.ListData.concat(list);
                this.last_content_id1 = res.data.last_content_id;
            }
        }
    }

    async getListData() {
        uni.showLoading({});
        try {
            const topic_id: number = +this.currActiveId;
            const page: number = this.page1;
            const page_size: number = 10;
            const params = {
                page,
                page_size: page_size,
                topic_id,
                direction: 0,
                last_content_id: this.last_content_id1,
            };
            const res = await getExVideoList(params, false);
            if (res.success) {
                if (res.data) {
                    const { list = [], last_content_id = '' } = res.data;
                    if (list === null || last_content_id == '') {
                        this.page1 = 1;
                        this.last_content_id1 = '';
                        this.ListData.push(null);
                        await this.getExVideoList();
                    } else {
                        if (list.length > 0) {
                            this.ListData = this.ListData.concat(list);
                            this.last_content_id1 = res.data.last_content_id;
                        }
                        if (list.length < 4) {
                            this.last_content_id1 = '';
                            this.ListData.push(null);
                            await this.getExVideoList();
                        }
                    }
                    this.page1++;
                } else {
                    this.last_content_id1 = '';
                    this.ListData.push(null);
                    await this.getExVideoList();
                }
                uni.hideLoading();
            }
        } catch (e) {
            uni.hideLoading();
        }
    }
}
</script>
<style lang="scss" scoped>
@import './search.scss';
::v-deep .u-modal {
    .u-modal__content__text {
        text-align: center;
        font-family: PingFang SC;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: 0px;
        color: $text-color-primary;
    }
}
</style>
