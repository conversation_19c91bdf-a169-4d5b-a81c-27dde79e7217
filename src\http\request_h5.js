import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import Utils from '@/common/Utils';
import axios from 'axios';
import adapter from 'axios-miniprogram-adapter';
import qs from 'qs';

const BASE_URL = process.env.VUE_APP_BASE_URL;

// 是否正在刷新的标记
let isRefreshing = false;

// 重试队列，每一项将是一个待执行的函数形式
let retryRequests = [];

function getSign(obj, security_key) {
    obj.sign_time = parseInt(new Date().getTime() / 1000);
    const newObj = {
        ...obj,
    };
    newObj.security_key = security_key;
    let sortArr = [];
    const keysSorted = Object.keys(newObj).sort(); // 排序名
    for (let i = 0; i < keysSorted.length; i++) {
        sortArr.push(keysSorted[i] + '=' + newObj[keysSorted[i]]);
    }
    sortArr = sortArr.join('&');
    return md5Libs.md5(sortArr);
}

// 设置基本的默认请求配置
axios.defaults.adapter = adapter;
axios.defaults.baseURL = BASE_URL; /* 根域名 */
axios.defaults.timeout = 10000;
axios.defaults.dataType = 'json';
axios.defaults.headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
};

let localConfig = {
    user_id: 0,
    sessid: '',
};

// h5本地开发环境user_id和sessid可以在配置文件改
if (process.env.NODE_ENV === 'development') {
    try {
        localConfig = require('../../local.config');
    } catch (err) {
        console.log('config.local.js不存在');
    }
}

// 请求的前置拦截
axios.interceptors.request.use(
    (config) => {
        const custom = config?.custom;
        // console.warn('config custom', custom);
        // 是否开启loading
        if (custom?.loading) {
            uni.showLoading({ title: custom.title || '正在加载' });
        }
        const BASE_DATA = {
            user_id: UserModule.user_id || localConfig.user_id,
            sessid: UserModule.sessid || localConfig.sessid,
            api: AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268',
            version: AppModule.version,
        };
        const security_key = AppModule.platform == 'ios' ? 'b_m3h^jWfA9jp' : 'b_m3I6PiPgYX#';
        config.data = {
            ...config.data,
            ...BASE_DATA,
        };

        delete config.data.sign;
        delete config.data.sign_time;
        config.data.sign = getSign(config.data, security_key);
        config.data = qs.stringify(config.data);
        return config;
    },
    (config) => {
        // 可使用async await 做异步操作
        return Promise.reject(config);
    },
);

// 请求的回调拦截
axios.interceptors.response.use(
    async (response) => {
        const res = response.data;
        // 自定义参数
        const custom = response.config?.custom;
        if (custom?.loading) {
            uni.hideLoading();
        }

        if (!res.iRet) {
            return res;
        }
        if (res.iRet == Constants.RET_OK) {
            return res.data;
        } else if (res.iRet == Constants.RET_CUSTOM_JSON) {
            return res;
        } else if (res.iRet == Constants.RET_ERR) {
            // 特殊判断
            if (res.sMsg && typeof res.sMsg === 'string' && res.sMsg === '追觅大使不存在') {
                return res.sMsg;
            }
            if (res.sMsg && typeof res.sMsg === 'string' && !custom?.hideErrorToast) {
                Utils.Toast(res.sMsg);
            }
            return Promise.reject(res);
        } else if (res.iRet == Constants.RET_SESSION_EXP) {
            // 清除登录信息重新登录
            UserModule.setSessId('');
            UserModule.setUserId('');
            UserModule.setIsLogin(false);
            const originalRequest = response.config;
            if (!isRefreshing) {
                isRefreshing = true;
                // 重新获取session
                return refreshSession()
                    .then(() => {
                        // 重试队列执行
                        retryRequests.forEach((cb) => cb());
                        retryRequests = [];
                        originalRequest.data = qs.parse(originalRequest.data);
                        return axios(originalRequest);
                    })
                    .catch(() => {
                        // 如果重新获取session也失败了，则直接返回错误
                        isRefreshing = false;
                        retryRequests = [];
                        // if (UserModule.user_id) {
                        //     Utils.Toast('网络错误');
                        // }
                        return Promise.reject(res);
                    });
            } else {
                // 正在刷新session，返回一个未执行resolve的promise
                return new Promise((resolve) => {
                    // 将resolve放进队列，用一个函数形式来保存，等session刷新后直接执行
                    retryRequests.push(() => {
                        originalRequest.data = qs.parse(originalRequest.data);
                        resolve(axios(originalRequest));
                    });
                });
            }
        } else return Promise.reject(res);
    },
    (response) => {
        // if (UserModule.user_id) {
        //     Utils.Toast('网络错误');
        // }
        const res = response.data;
        // 自定义参数
        const custom = response.config?.custom;
        if (custom?.loading) {
            uni.hideLoading();
        }
        if (res.sMsg) {
            Utils.Toast(res.sMsg);
        }
        return Promise.reject(response);
    },
);

// 重新获取session的函数
function refreshSession() {
    Utils.refreshSession();
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            window.onAppMessage = undefined;
            reject();
        }, 5000);
        window.onAppMessage = async (data) => {
            if (typeof data == 'string') {
                // 兼容字符串类型的数据
                data = JSON.parse(data);
            }
            data = data.data || data;
            await UserModule.setCommit(data);
            clearTimeout(timer);
            window.onAppMessage = undefined;
            resolve();
        };
    });
}

const request = {
    post(url, data, custom = {}) {
        return new Promise((resolve, reject) => {
            axios
                .post(url, data, custom)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    get(url, params, custom = {}) {
        return new Promise((resolve, reject) => {
            axios
                .get(url, { params }, custom)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 可添加其他HTTP请求方法
};

export default request;
