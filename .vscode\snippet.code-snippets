{
  // Place your snippets for vue here. Each snippet is defined under a snippet name and has a prefix, body and 
  // description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the 
  // same ids are connected.
  // Example:
  "uni_tem": {
    "prefix": "ubase",
    "body": [
      "<template>",
      "    <view class=\"box\">",
      "    </view>",
      "</template>",
      "<script lang=\"ts\">",
      "import { Vue, Component, Prop } from 'vue-property-decorator';",
      "",
      "@Component({})",
      "export default class Component extends Vue {",
      "    @Prop()",
      "    readonly target: 'serve' | 'product' = 'serve';",
      "",
      "    @Prop()",
      "    readonly prodInfo;",
      "",
      "    public name: string = ''",
      "",
      "    get fullName() {",
      "        return this.name",
      "    }",
      "}",
      "</script>",
      "<style lang=\"scss\"></style>",
      ""
    ],
    "description": "uni_tem"
  }
}