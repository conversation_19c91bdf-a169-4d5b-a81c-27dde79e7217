<template>
    <view class="MemberServices_container">
        <!-- <view class="MemberServices_container_title1">
            <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687766cd57d623600010688.png" class="member_Icon"></image>
            <view class="u-flex ">
                <MemberLevel></MemberLevel>
                <CoinPoints :coin="coin" :points="points" />
            </view>
            个人中心
        </view> -->
        <slot></slot>
        <u-scroll-list
            :indicator="false"
            :indicatorActiveColor="'#DBC49A'"
            :indicatorInactiveColor="'#EEEEEE'"
            :indicatorWidth="46.15"
            :indicatorHeight="7.69"
            :indicatorBarRadius="98.08"
            :indicatorBarStyle="{ margin: '16rpx auto 0' }"
            :showScrollbar="false"
        >
            <!-- @touchstart.stop
                @touchmove.stop
                @touchend.stop  -->
            <view
                @tap.stop="navigateToPage(service)"
                @click.stop="navigateToPage(service)"
                class="service_item"
                v-for="service in ServerList"
                :key="service.type"
            >
                <image class="service_item_icon" :src="service.icon"></image>
                <view class="service_item_name">{{ service.text }}</view>
                <image v-if="service.hover_icon" class="service_item_hover_icon" :src="service.hover_icon"></image>
            </view>
        </u-scroll-list>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { BuType } from '@/components/CustomerServicePicker/customer-butype';
import { getTagList } from '@/http/requestGo/community';
import { AppModule } from '@/store/modules/app';
import Constants from '@/common/Constants';
import CoinPoints from './components/CoinPoints.vue';
// #ifdef H5 || MP-WEIXIN
import MemberLevel from './components/MemberLevel.vue';
// #endif
// 引入uView的u-scroll-list组件
// import uScrollList from 'uview-ui/components/u-scroll-list/u-scroll-list.vue';
import { mineInfo } from '@/http/mine';

// 新增统一的服务项类型定义
type ServiceItem = {
    icon: string;
    text: string;
    url: string;
    type: string;
    event?: number;
    version?: string | number;
    hover_icon?: string;
};

@Component({
    components: {
        // uScrollList,
        CoinPoints,
        MemberLevel,
    },
})
export default class ActivityBar extends Vue {
    constructor() {
        super();
    }

    @Prop({ type: Number, default: 0 })
    readonly coin!: number;

    @Prop({ type: Number, default: 0 })
    readonly points!: number;

    public ServerList: ServiceItem[] = [
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6878c2ecaacb67000010490.png',
        //     text: '在线客服',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_MY_MANAGER_CLICK,
        //     url: '',
        //     type: 'manager',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688a0202c34e88000010534.png',
        //     text: '团购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_GROUP_CLICK,
        //     url: '/pagesB/groupGoods/groupGoods',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688617b6906e15920010434.png',
        //     text: '5折购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FIVE_DISCOUNT_CLICK,
        //     url: '/pagesC/offPurchase/offPurchase',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688c9e260ce0e0530022110.png',
        //     text: '6折购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_ACTIVITY_CLICK,
        //     url: '/pagesC/sixDiscount/sixDiscount',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889fdcb116ce0710021790.png',
        //     text: '赚钱花',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
        //     url: '/pagesC/earnMoneySpend/earnMoneySpend',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687ceefd002040010012236.png',
        //     text: '1元购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
        //     url: '/pagesC/oneYuanFlashSale/oneYuanFlashSale',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688b83bd89be35640018130.png',
        //     text: '赚金币',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
        //     url: '/pagesC/goldCoins/goldCoins',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68888a335040e3290018440.png',
        //     text: '八折购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
        //     url: '/pagesC/eightDiscount/eightDiscount',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688617b6906e15920010434.png',
        //     text: '5折购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_FIVE_DISCOUNT_CLICK,
        //     url: '/pagesC/offPurchase/offPurchase',
        //     type: '1',
        // },
        // {
        //     // icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68888a335040e3290018440.png',
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888ee5dd596d8750011947.png',
        //     text: '8折购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
        //         .LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_ACTIVITY_CLICK,
        //     url: '/pagesC/eightDiscount/eightDiscount',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68837bd52f7231940011713.png',
        //     text: '半价购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_HALF_PRICE_PIRCHASE,
        //     url: '/pagesC/halfPrice/halfPrice',
        //     type: '1'
        // },
        // {
        //     // icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687fab3c474062920011710.png',
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6880cca909b360400010878.png',
        //     text: '暴富计划',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_AMBASSADOR,
        //     url: '/pagesC/ambassador/detail/index',
        //     type: 'ambassador',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68837c0fc35c58000012228.png',
        //     text: '积分购物',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CEO_POINT_CLICK,
        //     url: '/pagesA/point/shop_point',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68837bd52f7231940011713.png',
        //     text: '半价购',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_HALF_PRICE_PIRCHASE,
        //     url: '/pagesC/billionsOfShoppingGold/billionsOfShoppingGold',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6880cc744caa13140012271.png',
        //     text: '会员优惠',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
        //     url: '/pagesA/point/new_point',
        //     type: '1'
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e15519bcdc6380011530.png',
        //     text: '家电国补',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_GUOBU_CLICK,
        //     url: '/pagesB/nationalSubsidy/index',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6878b5fd4ff273270010713.png',
        //     text: '我想要',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_WISH_CLICK,
        //     url: '/pagesA/wish/wish',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6878ccfab78807520011734.png',
        //     text: '朋友推荐',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_RECOMMENDFRIEND_CLICK,
        //     url: '/pagesA/recommendFriend/recommendFriend',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6878ccc5696eb4320012175.png',
        //     text: '购物车',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CART_CLICK,
        //     url: '/pagesA/cart/cart',
        //     type: '1',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e133d6c57b4440024079.png',
        //     text: '觅友空间',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_MIYOUQUAN_CLICK,
        //     url: '/pages/contents/contents',
        //     type: 'content',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686f859a6f4024560055881.png',
        //     text: '出行',
        //     event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_CHUXING_CLICK,
        //     url: 'https://webapp.aitrip123.com/',
        //     type: 'chuxing',
        // },
        // {
        //     icon: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images202507/682823-1753697862216.png',
        //     text: '团购',
        //     url: '/pagesB/groupGoods/groupGoods',
        //     type: '1',
        // },
    ];

    /**
     * 获取服务列表
     */
    public async fetchTagList() {
        try {
            const version = AppModule.VersionCode;
            const res: any = await getTagList({ type: 5, page_size: 100, is_show: 1, tag_version: `${version}` });
            // 根据接口返回的数据结构取 list
            const tagList = (res?.data?.list || res?.data || []) as any[];
            // 仅取 item.version 不超过当前版本的条目
            const validList = tagList.filter((item) => {
                const v = Number(item.version || 0);
                return v !== 0 && v <= Number(version);
            });

            // 找到 <= version 的最大版本号
            const maxVersion = validList.reduce((max, item) => {
                const v = Number(item.version);
                return v > max ? v : max;
            }, -Infinity);

            const extraList = validList
                .filter((item) => Number(item.version) === maxVersion)
                .map((item) => ({
                    icon: item.icon || '',
                    text: item.name || '',
                    url: item.jump_url || '',
                    type: item.jump_type || '',
                    version: item.version || '',
                    // 接口返回 is_show 值为 "1" 或 "2"，此处统一转成字符串便于后续比较
                    isShow: String(item.is_show || ''),
                }));
            // 仅保留 isShow 为 "1" 的数据
            const visibleExtraList = extraList.filter((extra) => extra.isShow === '1');
            // 过滤掉名称(text)已存在的服务项，避免重复追加
            const uniqueExtraList = visibleExtraList.filter(
                (extra) => !this.ServerList.some((service) => service.text === extra.text),
            );
            this.ServerList = [...this.ServerList, ...uniqueExtraList];
        } catch (error) {
            /* eslint-disable no-console */
            console.error('获取服务列表失败', error);
        }
    }

    async created() {
        await this.fetchTagList();
    }

    navigateToPage(item) {
        const { type, url } = item;
         Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                .HOME_BANNER_BANNER_EXPOSURE,
            name: item.text,
        });

        if (['manager', 6].includes(type)) { // 在线客服
            Utils.decryptContact('', BuType.BuType_NONE);
        } else if (['device', 10].includes(type)) { // 设备
            Utils.newMessageChannel('PAGE', 'push', { path: '/device_main' });
        } else if (['chuxing', 9].includes(type)) { // 出行
            Utils.newMessageChannel('PAGE', 'external', { path: `${url}` });
        } else if (['content', 8].includes(type)) { // 觅友空间
            Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' });
        } else if (type === 'ambassador') {
            mineInfo().then(response => {
                const { is_employee } = response
                const isEmployee = is_employee === 1
                if (isEmployee) {
                    Utils.navigateTo(`/pagesC/ambassador/detail/index`);
                } else {
                    Utils.navigateTo(`/pagesC/ambassador/login/index`);
                }
            })
        } else {
            Utils.navigateTo(`${url}`);
        }
        // switch (Number(type)) {
        //     case 1:
        //         Utils.navigateTo(`${url}`);
        //         break;
        //     case 2:
        //         Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(url)}`);
        //         break;
        //     case 3:
        //         Utils.newMessageChannel('PAGE', 'external', { path: url });
        //         break;
        //     case 4:
        //         Utils.newMessageChannel('PAGE', 'push', { path: url });
        //         break;
        //     case 5:
        //         Utils.openMiniProgram({ path: url });
        //         break;
        //     case 6:
        //         Utils.newMessageChannel('PAGE', 'web', { path: url });
        //         break;
        //     case 8:
        //         Utils.newMessageChannel('PAGE', 'tab', { tabType: 'explore' });
        //         break;
        // }
    }
}
</script>

<style lang="scss" scoped>
.MemberServices_container {
    height: auto;
    // background: #ffffff;
    // margin-top: 15rpx;
    // padding: 0rpx 16rpx 0rpx 16rpx;
    padding: 16rpx 16rpx 0rpx 16rpx;
    width: 100%;
    &_title {
        font-size: 24rpx;
        font-weight: 500;
        color: #777777;
    }
    .services_grid {
        display: flex;
        flex-wrap: nowrap;
        gap: 20rpx;
        margin-top: 20rpx;
    }
    .service_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        position: relative;
        width: calc((100vw - 80rpx) / 5); /* 一行5个，减去4个间隔的宽度 */
        min-width: 122rpx;
        overflow: hidden;
        //  &:first-child {
        //     margin-left: 16rpx;
        // }

        + .service_item {
            margin-left: 10rpx;
        }

        &_icon {
            height: 88rpx;
            width: 88rpx;
        }
        &_name {
            font-size: 22rpx;
            // margin-top: 12rpx;
            font-weight: regular;
            color: #404040;
            /* 禁止换行 */
            white-space: nowrap;
            text-align: center;
        }
        &_hover_icon {
            height: 36rpx;
            width: 36rpx;
            right: -20rpx;
            position: absolute;
            top: 0rpx;
            transform-origin: left bottom;
            animation: rotateIcon 1.5s infinite;
        }

        @keyframes rotateIcon {
            0% {
                transform: rotate(0deg);
            }
            50% {
                transform: rotate(30deg);
            }
            100% {
                transform: rotate(0deg);
            }
        }
    }
}
.MemberServices_container_title1 {
    height: 44rpx;
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: MiSans;
    font-size: 28rpx;
    line-height: 48rpx;
    font-weight: 800;
    color: #3d3d3d;
    .member_Icon {
        width: 148rpx;
        height: 44rpx;
    }
}
</style>
