$fill-color-bglg: linear-gradient(90deg, rgba(0, 0, 0, 0.09) 0%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg2: linear-gradient(133deg, #fff2cb 0%, #eedec1 98%);
$fill-color-bglg3: linear-gradient(270deg, #ffffff 55%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg4: linear-gradient(243deg, #ee6b00 -9%, #f6a253 97%);
// 文字颜色
$text-color-more: #7d7d7d;
$top: 58rpx;
$bottm: 60rpx;
$total: $bottm + $top;
.right-box {
    width: 100%;
    flex: 1;
    height: 100%;
    background-color: #f5f5f5;

    // #ifdef H5
    padding-bottom: env(safe-area-inset-bottom);
    // #endif
    -webkit-overflow-scrolling: touch;

    .page-view {
        @include flex(column, space-between, flex-start, nowrap);
        padding:0 16rpx 0;

        @media screen and (min-width: 450px) {
            padding: 16rpx 16rpx 0;
        }

        position: relative;
        -webkit-overflow-scrolling: touch;

        .class-item {
            width: 100%;
            background: #ffffff;
            border-radius: 16rpx;
            margin-bottom: 12rpx;
            display: flex;
            align-items: flex-start;
            flex-direction: row;
            position: relative;
            border-radius: 24rpx;
            padding: 20rpx;
            margin-bottom: 16rpx;

            .class-item-top {
                // width: 59vw;
                // padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
                // margin: 0 auto 0rpx;
                position: relative;
                padding-left: 10rpx;
                 .yushouImg{
                            position: absolute;
                            top: -21rpx;
                            left: -21rpx;
                            width: 150rpx;
                             z-index: 99;
                            height: 70rpx;
                            // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6887b4ebe9fdc9580010955.png');
                            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6baf24410011380.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                        .hotProImg{
                             position: absolute;
                            top: -21rpx;
                            left: -21rpx;
                            width: 150rpx;
                            height: 70rpx;
                            // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d36c841d8200010937.png');
                            background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6adab4380010532.png');
        
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            z-index: 99;
                        }
                        .newProImg{
                            position: absolute;
                            top: -21rpx;
                            left: -21rpx;
                             z-index: 99;
                            width: 150rpx;
                            height: 70rpx;
                            // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d33c15417920012131.png');
                            background-image:url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6a7414360010826.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                .lazyImg{
                    width: 264rpx;
                    height: 264rpx;
                    border-radius: 38rpx;
                }
                .custom_tag{
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 126rpx;
                    height: 58rpx;
                    background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687791bc05b370230010836.png') no-repeat;
                    background-size: cover;
                    border-radius: 0 8rpx 0 0;
                    z-index: 99;
                }
                .service-content {
                    position: absolute;
                    top: 20rpx;
                    right:20rpx;
                    width: 42rpx;
                    height: 36rpx;
                    z-index: 99;
                    .service1 {
                        width: 100%;
                        height: 100%;
                        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687780e260d073970010659.png');
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }
                    .service1-active {
                        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687794fe1cfb61190010236.png');
                    }
                }
        
            }
            .class-item-bottom {
                height: 100%;
                padding: 20rpx;
                position: relative;
                min-height: 270rpx;
                width: 100%;
                .name {
                    position: relative;
                    font-family: MiSans, MiSans;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #121212;
                    line-height: 32rpx;
                    text-align: left;
                    //  display: flex;
                    // flex-direction: column;
                    // justify-content: center;
                        .titleW{
                        line-height: 40rpx;
                    }
                    .tag {
                        position: absolute;
                        left: 0;
                        top: 4rpx;
                    }
                    .brand{
                        width: 64rpx;
                        height: 32rpx;
                        // border-radius: 4px;
                        // background-color: #000000;
                        color: #B59A6E;
                        text-align: center;
                        // padding: 6rpx 8rpx;
                        background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e272faa3206970149982.png');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
        
                    }
                    .strictly-selected{
                            width: 52rpx;
                        height: 26rpx;
                        border-radius: 4px;
                        background: linear-gradient(121deg, #318AF7 13%, #28B0EE 100%, #2997E3 100%);
        
                        color: #fff;
                        text-align: center;
                            padding: 6rpx 8rpx;
                    }
                    .ecology{
                            width: 52rpx;
                        height: 26rpx;
                        border-radius: 4px;
                        background-color: #FFF4EF;
                        color: #FB3019;
                        text-align: center;
                            padding: 6rpx 8rpx;
                    }
                }
                .couponText {
                    box-sizing: content-box;
                    width: 100rpx;
                    // height: 36rpx;
                    margin: 16rpx 0 10rpx;
                    padding: 6rpx 0rpx 6rpx 6rpx;
                    background: linear-gradient(
                        89deg,
                        rgba(252, 46, 46, 0.09) 0%,
                        rgba(228, 43, 43, 0) 100%
                    );
                    border-radius: 4rpx 4rpx 4rpx 4rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #ee3c2f;
                    line-height: 24rpx;
                }
                .price {
                    display: flex;
                    align-items: baseline;
                    margin-top: 16rpx;
                    font-family: MiSans, MiSans;
                    position: absolute;
                    bottom: 10rpx;
                     width: 90%;
                    color: #FF3654;
                    font-size: 28rpx;
                    height: 66rpx;
                    // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png');
                    background-color: #fbf0ef;
                    border-radius: 32rpx;
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    padding: 0rpx 6rpx 0rpx 16rpx;
                    .content_title_text_grab_text{
                      background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png');
                      background-size: 100% 100%;
                      background-repeat: no-repeat;
                      width: 126rpx;
                      height: 66rpx;
                      position: absolute;
                      bottom: 0rpx;
                      // top: -10rpx;
                      right: 0;
                  }
                    .unit {
                        height: 66rpx;
                        font-weight: 500;
                        font-size: 24rpx;
                        color: #FF3654;
                        line-height: 66rpx;
                    }
                    .optimizePrice {
                        height: 66rpx;
                        font-weight: 500;
                        font-size: 40rpx;
                        color: #FF3654;
                        line-height: 66rpx;
                    }
                    .dot {
                        height: 32rpx;
                        font-family: MiSans, MiSans;
                        font-weight: 500;
                        font-size: 24rpx;
                        color: #404040;
                        line-height: 32rpx;
                    }
                    .primaryPrice {
                        height: 32rpx;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #a6a6a6;
                        line-height: 32rpx;
                        text-decoration-line: line-through;
                    }
                    .coupon-text {
                        margin-left: 4rpx;
                        font-family: MiSans, MiSans;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #ee3c2f;
                        line-height: 24rpx;
                    }
                    .price_skeleton {
                        display: flex;
                        ::v-deep .u-skeleton {
                            flex: 0 0 auto;
                            &:nth-child(1) {
                                margin-right: 16rpx;
                            }
                        }
                    }
                }
            }
            // &:nth-child(even) {
            //     transform: translateX(8rpx);
            // }
        }
        
        /* 媒体查询适应折叠屏 */
        @media screen and (min-width: 450px) {
            .class-item {
                width: calc(calc(100vw - 32rpx - 36rpx)/4);

                // &:nth-child(even) {
                //     transform: none;
                // }

                // &:not(:nth-child(4n+1)) {
                //     margin-left: 16rpx;
                // }
            }
        }

        .list-title {
            position: fixed;
            top: 34rpx;
            right: 0;
            left: 0;
            z-index: 100;
            background: #ffffff00;
            .title-img {
                position: absolute;
                top: 0rpx;
                right: 26rpx;
                z-index: 100;
                // width: 100rpx;
                height: 38rpx;
                display: flex;
                align-items: center;
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 28rpx;
                color: #404040;
                line-height: 38rpx;
                text-align: left;
                // background: $fill-color-bglg3;
                .arrowTransform {
                    transition: 0.2s;
                    transform-origin: center;
                }

                .arrowTransformReturn {
                    transition: 0.2s;
                    transform-origin: center;
                    transform: rotate(180deg);
                }
            }

            .drop-down {
                position: relative;
                top: -38rpx;
                max-height: calc(100% - 94rpx);
                overflow: hidden;
                overflow-y: auto;

                // border-radius: 0px 0px 23rpx 23rpx;
                background: #ffffff;
                transition: all 0.3s ease-in-out;

                .down-box {
                    height: 88rpx;
                    padding: 0 16rpx;
                    font-family: MiSans, MiSans;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 88rpx;
                    color: $text-color-regular;
                    &.active {
                        position: relative;
                        font-weight: 400;
                        color: #8c6533;
                        text-transform: none;
                        background: rgba(241, 223, 200, 0.27);
                        border-radius: 4rpx 4rpx 4rpx 4rpx;
                        &::after {
                            content: '';
                            position: absolute;
                            right: 26rpx;
                            top: 24rpx;
                            width: 40rpx;
                            height: 40rpx;
                            background-image: url('https://wpm-cdn.dreame.tech/images/202411/672c6a81546803460056226.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                }
            }
        }

        .epmty {
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            margin-bottom: 8rpx;
            margin-top: 258rpx;
            .text {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #777777;
                line-height: 38rpx;
            }
        }
    }
    .no-more {
        // margin: 46rpx auto;
        font-size: 28rpx;
        color: $text-color-secondary;
        line-height: 100rpx;
        text-align: center;
    }
}
.box-type {
    height: 30rpx;
    font-size: 22rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: $brand-color-btn-text;
    line-height: 30rpx;
}
