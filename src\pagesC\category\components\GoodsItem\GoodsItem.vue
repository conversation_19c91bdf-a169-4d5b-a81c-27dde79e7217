<template>
    <view class="kawaii-product-card" @tap="handleCardClick">
        <view class="product-image-wrapper">
            <image class="product-image" :src="productInfo.image" mode="aspectFit" :lazy-load="true" />
        </view>

        <view class="product-info">
            <view class="product-title">{{ productInfo.title }}</view>
            <view class="product-specs" v-if="productInfo.specs">{{ productInfo.specs }}</view>

            <view class="price-section">
                <view class="current-price">
                    <text class="currency">¥</text>
                    <text class="price-value">{{ productInfo.currentPrice }}</text>
                </view>

                <view class="price-extra" v-if="productInfo.originalPrice || productInfo.couponText">
                    <text class="coupon-text" v-if="productInfo.couponText">{{ productInfo.couponText }}</text>
                    <text class="original-price" v-if="productInfo.originalPrice"
                        >¥{{ productInfo.originalPrice }}</text
                    >
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import { GoodsItemInfo, ProductCardClickEvent } from './types';

@Component({
    name: 'GoodsItem',
})
export default class GoodsItem extends Vue {
    // Props定义
    @Prop({
        type: Object,
        required: true,
    })
    readonly productInfo!: GoodsItemInfo;

    @Prop({ type: Boolean, default: true })
    readonly clickable!: boolean;

    // 处理卡片点击事件
    handleCardClick(): void {
        if (!this.clickable) return;

        // 构建点击事件数据
        const clickEvent: ProductCardClickEvent = {
            product: this.productInfo,
            type: 'card',
        };

        // 发送点击事件给父组件
        this.$emit('card-click', clickEvent);

        // 根据配置进行跳转
        this.navigateToProduct();

        // 埋点统计
        this.trackProductClick();
    }

    // 导航到商品页面
    private navigateToProduct(): void {
        if (this.productInfo.url) {
            Utils.navigateTo(this.productInfo.url);
        } else if (this.productInfo.gid) {
            // 跳转到商品详情页面
            Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${this.productInfo.gid}`);
        }
    }

    // 埋点统计
    private trackProductClick(): void {
        Utils.reportEvent('goods_item_click', {
            product_id: this.productInfo.id,
            product_title: this.productInfo.title,
            current_price: this.productInfo.currentPrice,
            original_price: this.productInfo.originalPrice || '',
        });
    }
}
</script>

<style lang="scss" scoped>
.kawaii-product-card {
    width: 262rpx;
    height: 251rpx;
    background: #ffffff;
    border-radius: 8px;
    padding: 16rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    &:active {
        transform: scale(0.98);
        box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    .product-image-wrapper {
        width: 100%;
        height: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12rpx;

        .product-image {
            max-width: 100%;
            max-height: 100%;
            border-radius: 4rpx;
        }
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .product-title {
            font-size: 28rpx;
            font-weight: 500;
            color: #333333;
            line-height: 36rpx;
            margin-bottom: 4rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-specs {
            font-size: 24rpx;
            color: #999999;
            line-height: 32rpx;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .price-section {
            .current-price {
                display: flex;
                align-items: baseline;
                margin-bottom: 4rpx;

                .currency {
                    font-size: 24rpx;
                    color: #ff4757;
                    font-weight: 500;
                }

                .price-value {
                    font-size: 32rpx;
                    color: #ff4757;
                    font-weight: 600;
                    margin-left: 2rpx;
                }
            }

            .price-extra {
                display: flex;
                align-items: center;
                gap: 8rpx;

                .coupon-text {
                    font-size: 20rpx;
                    color: #ff4757;
                    background: rgba(255, 71, 87, 0.1);
                    padding: 2rpx 6rpx;
                    border-radius: 4rpx;
                }

                .original-price {
                    font-size: 22rpx;
                    color: #999999;
                    text-decoration: line-through;
                }
            }
        }
    }
}
</style>
