<template>
  <view class="remind-popup-body-wrapper">
    <view class="bg-light"></view>
    <view class="remind-popup-body">
      <view class="main-text">
        设置签到提醒
      </view>
      <view class="hint-text">
        一键设置，明天来领更多奖励
      </view>
      <view class="btn-wrapper">
        <view class="not-remind-btn" @click="closeRemindPopup">

        </view>
        <view class="set-remind-btn" @click="setRemind">

        </view>
      </view>
      <view class="signIn-popup-close" @click="closeRemindPopup">
        <image
          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6889012760f323970031451.png">
        </image>
      </view>
    </view>
  </view>
</template>
<script>
// import Utils from '@/common/Utils';

export default {
  data() {
    return {

    };
  },
  props: {

  },
  created() {

  },
  watch: {

  },
  computed: {

  },
  methods: {
    closeRemindPopup() {
      this.$emit('closeRemindPopup');
    },
    setRemind() {
      this.$emit('setRemind');
    }
  }
};
</script>
<style lang="scss" scoped>
.remind-popup-body-wrapper {
  width: 646rpx;
  height: 670rpx;
  position: relative;
}

.bg-light {
  background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689efef80d3640540011221.png');
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1032rpx;
  height: 1139rpx;
  margin-top: -30rpx;
}

.remind-popup-body {
  width: 646rpx;
  height: 670rpx;
  border-radius: 80rpx;
  position: relative;
  background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689efef80af430450010386.png');
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 44rpx;

  .main-text {
    font-size: 52rpx;
    font-weight: 600;
    line-height: 69rpx;
    text-align: center;
    color: #CB2533;
    height: 69rpx;

  }

  .hint-text {
    font-size: 28rpx;
    font-weight: normal;
    text-align: center;
    height: 42rpx;
    line-height: 42rpx;
    margin-top: 4rpx;
    color: #777777;
  }

  .btn-wrapper {
    margin-top: 365rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 106rpx;
  }

  .not-remind-btn {
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689efef8019ed0070012146.png');
    background-size: contain;
    background-repeat: no-repeat;
    width: 275rpx;
    height: 106rpx;
  }

  .set-remind-btn {
    background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689efef8019ec0070011781.png');
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 20rpx;
    width: 275rpx;
    height: 106rpx;
  }

  .signIn-popup-close {
    width: 52rpx;
    height: 52rpx;
    opacity: 1;
    box-sizing: border-box;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -90rpx;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>
