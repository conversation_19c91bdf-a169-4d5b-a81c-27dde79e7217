/* eslint-disable no-redeclare */
import http from './index';
import req from './requestGo/index.js';
/*
 * 商品列表
 * @parma {String} tid 标签id
 * @parma {String} page  获取第N页商品
 * @returns
 */
export const goodsList = (params) => http.post('main/goods/list', params);

export const adGoodsList = (params) => req.post('api/v1/adverts/list', params);

export const getPartnerGoodsList = (params) => http.post('main/dreame-partner-goods/goods-list', params)
/*
 * 计算券后价
 * @param {Array} goods_combines 商品信息
 * @returns
 */
export const calculateGoodPrice = (params) => http.post('main/goods/calculate-price', params);

/*
 * 内购商品列表
 * @parma {String} tid 标签id
 * @parma {String} page  获取第N页商品
 * @returns
 */
export const innerGoodsList = (params) => http.post('main/goods/internal-list', params);

/*
 * 商品详情
 * @parma {String} gid 商品ID
 * @returns
 */
export const goodsInfo = (params) => http.post('main/goods/info', params);

/*
 * 商品标签
 * @returns
 */
export const tagList = (params) => http.post('main/goods/tag-list', params);

/*
 * 商品标签
 * @returns
 */
export const innerTagList = () => http.post('main/goods/internal-tag-list', {});

/*
 * 商品属性是否有库存
 * @parma {String} av_ids 所选属性值id
 * @returns
 */
export const attrStock = (params) => http.post('main/goods/attr-stock', params);

/*
 * 商品规格查询
 * @parma {String} av_ids 所选属性值id
 * @returns
 */
export const specsInfo = (params) => http.post('main/goods/specs-info', params);

/*
 * 商品分享二维码
 * @parma {String} av_ids 所选属性值id
 * @returns
 */
interface ShareCodeParams {
    gid: string;
    code: string;
}
// export const shareCode = (params: ShareCodeParams) =>
//     http.post('main/goods/share-code', params, {
//         responseType: 'arraybuffer',
//     });

export const shareCode = (params: ShareCodeParams) => http.post('main/goods/share-code', params);

/*
 * 获取分享加密串
 * @parma {String} 加密来源（1导购 2推荐人）【必要】
 * @returns
 */
export const shareScode = (source: string) => http.post('main/goods/scode', { source });

/*
 * 搜索商品
 * @parma {String} keyword【必要】
 * @parma {String} 加密来源（1导购 2推荐人）【必要】
 * @returns
 */
export const searchGoods = (params) => http.post('main/search/index', params);

/**
 * 配件陈列-配件类目
 * @returns
 */
export const partCate = () => http.post('main/goods/part-cate', {});

/**
 * 配件陈列-配件数据
 * c_id 三级类目id
 * sort 价格排序 1正序 2倒叙 3综合排序   默认3
 * id 筛选机型id
 */
export const partList = (params) => http.post('main/goods/part', params);

/**
 * 配件陈列-筛选数据
 * c_id 三级类目id
 */
export const choose = (params) => http.post('main/goods/choose', params);

/**
 * 参数详情弹窗
 * sku 商品sku
 */
export const paramsInfo = (params) => http.post('main/goods-param/index', params);

/**
 * 商城二期-搭配购买
 * sku 商品sku
 */
export const tiedSaleList = (params) => http.post('main/goods/tied-sale', params);

/**
 * 服务权益说明
 */
export const benefitInfo = () => http.post('main/member-center/benefit-info', {});

/**
以旧焕新
*/
export const activityOldNew = () => http.post('main/activity/old-to-new');

/**
 *  获取商品详情的优惠卷
 */
export const goodsCouponList = (params) => http.post('main/goods/market-list', params);

/**
 *  领取优惠卷
 */
export const reciveCoupon = (params) => http.post('/main/activity/receive', params);

/**
以旧焕新活动说明
*/
export const tradeInActivity = () => http.post('main/trade-in/activity');

/**
 *  以旧换新商品列表
 */
export const tradeInProductList = () => http.post('main/trade-in/product-list');

/**
 *  以旧换新回收类目
 */
export const tradeInCateList = () => http.post('main/trade-in/cate-list');

/**
 *  以旧换新型号模型
 */
export const tradeInModelList = (cate_id: number) => http.post('main/trade-in/model-list', { cate_id });

/**
 *  以旧换新商品询价
 */
export const tradeInModelInquiry = (params) => http.post('/main/trade-in/inquiry', params);

/**
 *  以旧换新详情
 */
export const tradeInInfo = (order_no: string) => http.post('/main/trade-in/order-info', { order_no });

/**
 *  以旧换新取消订单
 */
export const tradeInCancelOrder = (order_no: string) => http.post('/main/trade-in/cancel-order', { order_no });

/**
 *  以旧换新确认订单
 */
export const tradeInOrderConfirm = (params) => http.post('/main/trade-in/order-confirm', params);

/**
 *  以旧换新物流查询
 */
export const tradeInExpressFind = (params) => http.post('/main/express/find', params);

/**
 *  发起拼团
 */
export const initiateGroupPurchase = (params) => http.post('/main/group-purchase/initiate-group-purchase', params);

/**
 *  获取团购详情
 */
export const getGroupMembers = (params) => http.post('/main/group-purchase/group-members', params);

/**
 *  获取团购商品详情
 */
export const getGroupPurchaseGoodsDetail = (params) => http.post('/main/group-purchase/goods-detail', params);

/**
 *  获取团购活动描述
 */
export const getGroupPurchaseRules = (params) => http.post('main/group-purchase/activity-detail', params, { custom: { hideErrorToast: true }});

/**
 *  分享团购商品链接
 */
export const shareGroupPurchaseLink = (params) => http.post('/main/group-purchase/share-invitation-link', params);

/**
 *  检查是否为团长
 */
export const checkGroupLeader = (params) => http.post('/main/group-purchase/check-group-leader', params);

/**
 *  查询该订单是否插入团中
 */
export const checkGroupOrderNo = (params) => http.post('/main/group-purchase/check-order', params);

/**
 *  以旧换新二期标签列表
 */
export const tradeInTabList = (params) => http.post('/main/trade-in/tab-list', params);

/**
 *  以旧换新二期商品列表
 */
export const tradeInGoodList = (params) => http.post('/main/trade-in/list', params);

/**
 *  以旧换新二期 修改旧机预约上门时间
 */
export const updateDeliveryTime = (params) => http.post('/main/trade-in/update-delivery-time', params);

/**
 *  以旧换新二期 查询订单状态
 */
export const checkOrderStatus = (params) => http.post('/main/trade-in/order-status-check', params);

/**
 *  停车券 查询卡券是否已经发放
 */
export const checkCoupon = (params) => http.post('/main/wares-order/check-coupon', params);

/**
 *  商品列表
 */
export const getRecommendGoods = (params) => http.post('/main/goods-recommend/list', params);

/**
 * 加入/取消心愿单
 * goods_id 商品ID
 * opt 操作  add / remove
 */
export const wishGoods = (params) => http.post('main/goods/wish', params);

/**
 *  获取国补活动
 */
export const getSubsidyActivity = () => http.post('/main/subsidy-activity/current');

/**
 *  获取国补商品
 */
export const getSubsidyGoods = (params) => http.post('/main/subsidy-activity/list', params);

/**
 *  用户推荐/取消推荐商品
 *  gid 商品ID
 *  type 1推荐 0取消推荐
 */
export const userRecommend = (params) => http.post('/main/goods-recommend/user-recommend', params);

// 取消收藏2
export function postCancelCollect(params: any) {
    return req.post('/api/v1/goods/recommendation-reason/delete', params);
}

// 分享文案
export const getShareContent = (params) => http.post('/main/goods/goods-tag-fallback-content', params);

// 生成短链
export const getShortLink = (params) => http.post('/main/data/short-link', params);
