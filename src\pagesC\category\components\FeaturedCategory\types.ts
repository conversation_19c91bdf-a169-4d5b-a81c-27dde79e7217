/**
 * 精选分类组件相关类型定义
 */

// 分类项接口
export interface CategoryItem {
    /** 分类唯一标识 */
    id: string | number;
    /** 分类名称 */
    name: string;
    /** 分类图片URL */
    image: string;
    /** 跳转链接（可选） */
    url?: string;
    /** 分类类型（可选） */
    type?: string;
    /** 商品分类ID（可选，用于跳转到商品列表） */
    tid?: string;
    /** 是否显示（可选） */
    visible?: boolean;
    /** 排序权重（可选） */
    sort?: number;
    /** 扩展数据（可选） */
    extra?: Record<string, any>;
}

// 分类点击事件参数
export interface CategoryClickEvent {
    /** 被点击的分类项 */
    item: CategoryItem;
    /** 分类项在列表中的索引 */
    index: number;
}

// 组件Props接口
export interface FeaturedCategoryProps {
    /** 分类列表数据 */
    categoryList: CategoryItem[];
    /** 组件标题 */
    title: string;
    /** 网格列数 */
    columns: number;
}

// 默认分类数据
export const DEFAULT_CATEGORIES: CategoryItem[] = [
    {
        id: 1,
        name: '扫地机',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
        type: 'product',
        tid: '11'
    },
    {
        id: 2,
        name: '洗地机',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
        type: 'product',
        tid: '10'
    },
    {
        id: 3,
        name: '吸尘器',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
        type: 'product',
        tid: '12'
    },
    {
        id: 4,
        name: '吹风机',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
        type: 'product',
        tid: '13'
    },
    {
        id: 5,
        name: '空气净化器',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
        type: 'product',
        tid: '14'
    },
    {
        id: 6,
        name: '除螨仪',
        image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
        type: 'product',
        tid: '16'
    }
];

// 埋点事件类型
export interface CategoryTrackEvent {
    /** 分类ID */
    category_id: string | number;
    /** 分类名称 */
    category_name: string;
    /** 分类类型 */
    category_type: string;
    /** 点击位置 */
    position: number;
}
