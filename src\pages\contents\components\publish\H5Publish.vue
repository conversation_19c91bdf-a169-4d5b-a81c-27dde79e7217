<template>
    <u-overlay :show="openSync" @click="close">
        <view class="publish" :style="{ top: `${posiTop}rpx`, right: `${posiRight}rpx` }">
            <view class="publish-item" @click="handlePublish('img')">
                <img src="https://wpm-cdn.dreame.tech/images/202412/939066-1735022159624.png" />
                <text>发动态</text>
            </view>

            <view class="publish-item vedio" @click="handlePublish('vedio')">
                <img src="https://wpm-cdn.dreame.tech/images/202412/755992-1735022241316.png" />
                <text>发视频</text>
            </view>
        </view>
    </u-overlay>
</template>

<script lang="ts">
import Utils from '@/common/Utils';
import { AppModule } from '@/store/modules/app';
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

interface Rect {
    right: number;
    bottom: number;
}

@Component
export default class Publish extends Vue {
    @PropSync('open', { type: Boolean }) openSync: boolean;
    @Prop({ type: Object }) rect: Rect;

    get posiTop() {
        return Utils.pxToRpx(this.rect.bottom + 10);
    }

    get posiRight() {
        return Utils.pxToRpx((window.innerWidth - this.rect.right) / 2);
    }

    close() {
        this.$emit('close');
    }

    handlePublish(type: 'img' | 'vedio') {
        Utils.requestPermission()
            .then((hasCamera = false) => {
                if (hasCamera) {
                    if (type === 'img') {
                        if (AppModule.platform == 'ohos') {
                            Utils.navigateTo(
                                `/pagesC/createContent/index?contentType=${type}&tempFilePaths=${JSON.stringify(
                                    [],
                                )}`,
                            );
                            return;
                        }
                        uni.chooseImage({
                            count: 9,
                            sourceType: ['album', 'camera'],
                            success: (res) => {
                                this.$emit('close');
                                const tempFilePaths = res.tempFilePaths;
                                Utils.navigateTo(
                                    `/pagesC/createContent/index?contentType=${type}&tempFilePaths=${JSON.stringify(
                                        tempFilePaths || [],
                                    )}`,
                                );
                            },
                        });
                    } else {
                        if (AppModule.platform == 'ohos') {
                            Utils.navigateTo(
                                `/pagesC/createContent/index?contentType=${type}&tempFilePaths=${JSON.stringify(
                                    [],
                                )}`,
                            );
                            return;
                        }
                        uni.chooseVideo({
                            sourceType: ['album', 'camera'],
                            compressed: false,
                            success: (res) => {
                                this.$emit('close');
                                const tempFilePath = res.tempFilePath;
                                Utils.navigateTo(
                                    `/pagesC/createContent/index?contentType=${type}&tempFilePaths=${tempFilePath}`,
                                );
                            },
                        });
                    }
                } else {
                    Utils.Toast('请在系统设置下打开DREAME APP的相机和照片权限');
                }
            })
            .catch(() => Utils.Toast('请在系统设置下打开DREAME APP的相机和照片权限'));
    }
}
</script>

<style lang="scss" scoped>
.publish {
    z-index: 10001;
    background: #f6f6f6;
    position: absolute;
    right: 16rpx;
    top: 140rpx;
    border-radius: 30rpx;

    &::after {
        content: '';
        position: absolute;
        top: -12rpx;
        right: 30rpx;
        width: 0;
        height: 0;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-bottom: 12rpx solid white;
    }

    &-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 300rpx;
        height: 100rpx;
        background-color: white;
        padding-left: 40rpx;

        img {
            width: 48rpx;
            height: 48rpx;
        }

        text {
            margin-left: 16rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #20252b;
        }

        &:first-child {
            border-radius: 30rpx 30rpx 0 0;
        }

        &:last-child {
            border-radius: 0 0 30rpx 30rpx;
        }
    }

    .vedio {
        border-top: 2rpx solid #f7f8fa;
    }
}
</style>
