<template>
    <view>
        <!-- transition: top 0.3s ease-in-out -->
        <view class="container" v-if="isLoaded" style="position: fixed; left: 0;"
            :style="{ top: `${contentTop}rpx` }">
            <!-- <searchBar></searchBar> -->
            <!-- <u-tabs
                :list="tabsList"
                :current="swiperCurrent"
                @change="onTabsChange"
                class="tabs-view"
                lineWidth="72rpx"
                lineColor="#DBC49A"
                :activeStyle="{ color: '#404040', fontSize: '36rpx', fontWeight: '500' }"
                :inactiveStyle="{
                    color: '#404040',
                    fontSize: '28rpx',
                }"
                itemStyle="height: 96rpx;"
            /> -->

            <block v-if="!isShowTab || (tid === '10000' && !isShowTab) || tid !== '10000'"
                :style="tid == '10000' ? { transform: !isShowTab ? 'translateY(0)' : 'translateY(-100%)', position: 'absolute', top: `${65}rpx` } : {}">
                <u-tabs
                :list="tabsList"
                :current="swiperCurrent"
                @change="onTabsChange"
                class="tabs-view"
                lineWidth="72rpx"
                lineColor="#DBC49A"
                :activeStyle="{ color: '#404040', fontSize: '36rpx', fontWeight: '500' }"
                :inactiveStyle="{
                    color: '#404040',
                    fontSize: '28rpx',
                }"
                itemStyle="height: 96rpx;"
            />
            </block>
            <view v-if="!['10000', '10001', '10002', '10003'].includes(tid)" class="tab-filter">
                <view class="tab-filter__item" :class="{ active: currentFilterIndex === item.value }"
                    v-for="(item, index) in getTabs()" :key="item.value || index"
                    @click="handleFilterClick(item.value)">
                    {{ item.label }}
                </view>
            </view>
            <view class="main u-flex u-col-top u-row-left">
                <view class="menu-wrap u-flex">
                    <swiper class="swiper-container" :current="swiperCurrent" @transition="swiperTransition"
                        @change="swiperChange" @animationfinish="swiperFinish" style="width: 100%; height: 100%">
                        <block v-for="(item, index) in tabsList" :key="index">
                            <!-- 推荐id自定义10000 -->
                            <swiper-item class="swiper-slide" v-if="item.tid == '10000' || !tid">
                                <scroll-view scroll-y style="flex: 1; width: 100%; height: 100%" :lower-threshold="420"
                                    :refresher-enabled="enableRefresher" refresher-background="transparent" :refresher-triggered="isViewRefreshing"
                                    :scroll-top="scrollTop" @scrolltolower="viewOnreachBottom"
                                    @refresherrefresh="viewRefresherrefresh" @scroll="onViewScroll">
                                    <view class="recommend-container">
                                        <Skeleton>
                                            <template #device>
                                                <slot name="device"></slot>
                                                <!-- <MyDevice :deviceList="deviceList" /> -->
                                            </template>
                                        </Skeleton>
                                        <SkeletonTC :title="'产品家族'">
                                            <template>
                                                <TabListBar :BrandTypeLists="seftBrandLists" @switchMenu="swichMenu"/>
                                            </template>
                                        </SkeletonTC>
                                        <SkeletonTC :title="'折扣活动'">
                                            <template>
                                                <SaleActivites></SaleActivites>
                                            </template>
                                        </SkeletonTC>
                                        <slot name="recommend"></slot>
                                    </view>
                                    <!-- 回到顶部按钮 -->
                                    <view v-if="showBackToTop" class="back-to-top" @click="scrollToTop">
                                        <image
                                            src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a7dbe2a56f1730014176.png"
                                            class="back-to-top-icon"></image>
                                        <text>顶部</text>
                                    </view>
                                </scroll-view>
                            </swiper-item>
                            <!-- 商品专区 -->
                            <swiper-item class="swiper-slide" v-else-if="item.tid != '20'">
                                <scroll-view scroll-y class="right-box" :refresher-enabled="enableRefresher"
                                    :upper-threshold="0" :lower-threshold="420" :refresher-triggered="isRefreshing"
                                    @refresherrefresh="refresherrefresh" refresher-background="transparent" @scrolltolower="onreachBottom"
                                    @scroll="onViewScroll">
                                    <block v-if="!isListProduct">
                                        <view v-if="!isPartZone" class="page-view-new"></view>
                                        <block v-for="itm in listData[index].list" :key="itm.gid">
                                            <view @click="gotoGdetailPage(itm.gid, itm.name)"
                                                class='autoclk-component-css' :data-aparam="itm.gid"
                                                :data-pagename="itm.name" data-page_title="商详页">
                                                <ProductList :Item="itm"></ProductList>
                                            </view>
                                        </block>
                                    </block>
                                    <block v-else>
                                        <view v-if="!isPartZone" class="page-view" style="width: 100%;">
                                            <ProductHorizontal :productList="listData[index].list" @goToProduct="(itm)=>{gotoGdetailPage(itm.gid, itm.name)}"></ProductHorizontal>
                                            <!-- <ListHorizontalTwo :productList="listData[index].list"></ListHorizontalTwo> -->
                                            <block v-if="!listData[index].list.length && !listData[index].finished">
                                                <view class="flex-container" style="width: 100%">
                                                    <view class="column right-column" v-for="items in 2" :key="items">
                                                        <view class="skeleton-container" v-for="item in 5" :key="item">
                                                            <u-skeleton :animate="true" :title="false"
                                                                :rowsHeight="[120]" :loading="true" rows="1"
                                                                :rowsWidth="['100%']"></u-skeleton>
                                                        </view>
                                                    </view>
                                                </view>
                                            </block>
                                            <block v-if="!listData[index].list.length && listData[index].finished">
                                                <view class="epmty">
                                                    <LazyImage
                                                        src="https://wpm-cdn.dreame.tech/images/202412/674ef25ad58d98751106040.png">
                                                    </LazyImage>
                                                    <text class="text">暂无商品</text>
                                                </view>
                                            </block>
                                        </view>
                                    </block>

                                    <view class="no-more" v-if="
                                        !isPartZone &&
                                        listData[index].finished &&
                                        listData[index] &&
                                        listData[index].list.length > 0
                                    ">——没有更多了——~
                                    </view>
                                    <view v-if="listData[index].finished && recommendedList.length > 0"
                                        class="recommend-title">
                                        <text>精选好物</text>
                                    </view>
                                    <view>
                                        <block v-if="!isListProduct">
                                            <view v-if="!isPartZone" class="page-view-new"></view>
                                            <block v-for="itm in recommendedList" :key="itm.gid">
                                                <view @click="gotoGdetailPage(itm.gid, itm.name)"
                                                    class='autoclk-component-css' :data-aparam="itm.gid"
                                                    :data-pagename="itm.name" data-page_title="商详页">
                                                    <ProductList :Item="itm"></ProductList>
                                                </view>
                                            </block>
                                        </block>
                                        <block v-else>
                                            <view v-if="!isPartZone" class="page-view">
                                                <ProductHorizontal :productList="recommendedList" @goToProduct="(itm)=>{gotoGdetailPage(itm.gid, itm.name)}"></ProductHorizontal>
                                                <!-- <ListHorizontalTwo :productList="listData[index].list"></ListHorizontalTwo> -->
                                                <block v-if="!listData[index].list.length && !listData[index].finished">
                                                    <view class="flex-container" style="width: 100%">
                                                        <view class="column right-column" v-for="items in 2"
                                                            :key="items">
                                                            <view class="skeleton-container" v-for="item in 5"
                                                                :key="item">
                                                                <u-skeleton :animate="true" :title="false"
                                                                    :rowsHeight="[120]" :loading="true" rows="1"
                                                                    :rowsWidth="['100%']"></u-skeleton>
                                                            </view>
                                                        </view>
                                                    </view>
                                                </block>
                                                <block v-if="!listData[index].list.length && listData[index].finished">
                                                    <view class="epmty">
                                                        <LazyImage
                                                            src="https://wpm-cdn.dreame.tech/images/202412/674ef25ad58d98751106040.png">
                                                        </LazyImage>
                                                        <text class="text">暂无商品</text>
                                                    </view>
                                                </block>
                                            </view>
                                        </block>
                                    </view>

                                    <!-- 推荐商品加载状态 -->
                                    <view v-if="listData[index].finished && recommendedList.length > 0">
                                        <view v-if="recommendedLoading" class="loading-more">
                                            <text>加载中...</text>
                                        </view>
                                        <view v-else-if="recommendedFinished" class="no-more">
                                            没有更多了~
                                        </view>
                                    </view>
                                </scroll-view>
                            </swiper-item>
                        </block>
                    </swiper>
                </view>
                <view v-if="isPartZone" class="reg" @click="toProductRegister">
                    <view class="regLeft">{{ `${products.length === 0 ? '注册您的产品' : '您已注册产品'}` }}，配件无需查找一键购买</view>
                    <CustomButton :text="`${products.length === 0 ? '立即注册' : '查看配件'}`" size="12" :customStyle="{
                        width: '132rpx',
                        height: '54rpx',
                        backgroundColor: '#121212',
                        borderRadius: '235rpx',
                        marginRight: '32rpx',
                        fontweight: '500',
                        color: '#ffffff',
                    }"></CustomButton>
                </view>
            </view>
            <custom-toast ref="customToast" />
            <!-- #ifdef MP-WEIXIN -->
            <privacy />
            <!-- #endif -->
        </view>
        <view v-else>
            <u-skeleton :animate="true" :title="false" :loading="true" rows="1" :rowsHeight="[70]"
                :rowsWidth="['100%']"></u-skeleton>
            <view style="margin-top: 5px">
                <u-skeleton :animate="true" :title="false" :loading="true" rows="1" :rowsHeight="[45]"
                    :rowsWidth="['100%']"></u-skeleton>
            </view>
            <view class="flex-container">
                <view class="column right-column" v-for="items in 2" :key="items">
                    <view class="skeleton-container" v-for="item in 5" :key="item">
                        <u-skeleton :animate="true" :title="false" :rowsHeight="[120]" :loading="true" rows="1"
                            :rowsWidth="['100%']"></u-skeleton>
                    </view>
                </view>
            </view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin v-if="!isLoading" @success="onShowExecute"></WxLogin>
        <!-- #endif -->
    </view>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { goodsList, partCate, wishGoods } from '@/http/goods';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import CustomSearch from '@/components/CustomSearch/CustomSearch.vue';
import { GoodsModule, IGoods, ITag } from '@/store/modules/goods';
import { AppModule } from '@/store/modules/app';
import { ServeModule } from '@/store/modules/serve';
import SearchBar from '../SearchBar/index.vue';
import Title from './components/Title.vue';
import TabItem from './components/TabItem.vue';
import Banner from './components/banner.vue';
import ProductList from './List/List.vue';
import ProductHorizontal from './ProductHorizontal/ProductHorizontal.vue';
import Skeleton from '../skeleton/skeleton.vue';
import SkeletonTC from '../skeleton/skeletonTC.vue';
// import SwitchButton from './components/SwitchBtn.vue';
import Utils from '@/common/Utils';
// import { firstTabs, brandLists } from './DataConst';
import Constants from '@/common/Constants';
import { throttle } from 'lodash';
import TabListBar from './components/TabList.vue';
import SwtichBtn from './components/SwtichBtn.vue';
import ListHorizontal from './ListHorizontal/ListHorizontal.vue';
import ListHorizontalTwo from './ListHorizontalTwo/ListHorizontalTwo.vue';
import SaleActivites from '../saleActivites/index.vue';
import MyDevice from '../MyDevice/MyDevice.vue';

const DataConst = require('./DataConst') as any;
const { firstTabs, brandLists } = DataConst;

const TID_ZONE = '20'; // 配件专区

interface IListData {
    list: Array<
        IGoods & {
            // 券后价是否加载完成
            coupon_finish?: boolean;
        }
    >;
    pages: number;
    finished?: boolean;
    page?: number;
    isLoading?: boolean;
}

@Component({
    components: {
        CustomSearch,
        WxLogin,
        SearchBar,
        TabItem,
        Title,
        TabListBar,
        SwtichBtn,
        ProductList,
        ProductHorizontal,
        ListHorizontal,
        ListHorizontalTwo,
        Skeleton,
        SkeletonTC,
        Banner,
        SaleActivites,
        MyDevice
        // TabList,
    },
})
export default class Shop extends Vue {
    @Prop({ type: Number, default: 0 })
    readonly contentTop!: number;

    @Prop({ type: Boolean, default: true })
    readonly isShowTab!: Boolean;

    @Prop({ type: Boolean, default: true })
    readonly isVisible!: Boolean;

    @Prop({ type: Boolean, default: true })
    readonly isListProduct!: Boolean;

    @Prop({ type: Array, default: () => [] })
    readonly deviceList!: Array<any>;

    public finished: Boolean = false; // 是否加载完成
    public comparisonList: any = [];
    public timer: any = null;
    public showAction: any = false;
    public firstTabLists: any = firstTabs;
    public scrollIntoView: String = 'd_jump-0';
    public tabsList: Array<ITag> = [
        {
            tid: '1',
            name: '新品',
        },
        {
            tid: '2',
            name: '热销',
        },
        {
            tid: '10',
            name: '洗地机',
        },
        {
            tid: '11',
            name: '扫地机器人',
        },
        {
            tid: '12',
            name: '吸尘器',
        },
        {
            tid: '13',
            name: '吹风机',
        },
    ];
    // 官方教程 [11,10,12,13,16]
    // 配件 [11,10,12,13,16,17,14]

    public tabsFilterOption = [
        {
            label: '商品',
            value: 1,
        },
        {
            label: '配件',
            value: 2,
        },
    ];

    public allTabLists: Array<ITag> = [];

    public childrenBrandLists: any = brandLists;
    public isLoaded: boolean = false;
    public isLoading: boolean = true;
    public isRefreshing: Boolean = false;
    public isViewRefreshing: Boolean = false;
    public enableRefresher: Boolean = true;
    public page: number = 1;
    public current: number = 0; // 当前数据索引
    public currentTab: string = 'tab-0'; // 当前tab的id 用于定位
    public swiperCurrent: number = 0; // 当前swiper的index
    private menuHeight: 0; // 左边菜单的高度
    private menuItemHeight: number = 0; // 左边菜单item的高度
    public listData: Array<IListData> = [];
    public seftBrandLists: Array<any> = [];
    public cateList: Array<any> = [];
    public cateList2: Array<any> = []; // 配件列表中有children的列表
    public result: any = {};
    public TID_ZONE: string = TID_ZONE;
    public flag: Boolean = false; // 箭头翻转
    public activePartZoneTab: number = 0; // 配件专区当前tab
    public tid: string = '';
    public currentFilterIndex: number = 1;
    public showBackToTop: boolean = false;
    public scrollTop: number = 0;
    public scrollViewTop: number = 0;
    public scrollFirstViewTop: number = 0;
    public isShowContent: boolean = true; // 滚动到顶部
    public recommendedList: Array<any> = [];
    public recommendedPage: number = 1; // 推荐商品当前页码
    public recommendedFinished: boolean = false; // 推荐商品是否加载完成
    public recommendedLoading: boolean = false; // 推荐商品是否正在加载

    // 商品是否已加入心愿单
    get isGoodsWish(): boolean {
        return true;
    }

    get isPartZone(): boolean {
        const TID_ZONE = '20'; // 配件专区
        return this.tid == TID_ZONE;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get rowWidths_skeleton_coupon() {
        return [uni.upx2px(100), uni.upx2px(56)];
    }

    get rowHeight_skeleton_coupon() {
        return uni.upx2px(50);
    }

    // 已注册产品列表
    get products() {
        return ServeModule.Products;
    }

    // 增加折疊屏 - 每頁請求數
    get isFoldableDevice() {
        return Utils.isFoldableDevice();
    }

    // 优化计算属性，减少重复计算
    get currentFilterTabs() {
        return this.getTabs();
    }

    get currentTabClass() {
        return (index: number) => (this.tid === this.tabsList[index]?.tid ? 'tab-item-active' : '');
    }

    get currentTabId() {
        return (index: number) => 'tab-' + index;
    }

    // 组件创建时
    created() {
        // 获取路由参数
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const options = (currentPage as any).options || {};

        // #ifdef MP-WEIXIN
        console.log(options);
        this.tid = options.tid || uni.getStorageSync('tid');
        this.isLoading = false;
        // #endif

        // #ifdef H5
        this.tid = options.tid;
        // #endif
    }

    // 组件挂载完成后
    async mounted() {
        // 创建防抖函数，增加延迟时间减少频繁触发
        this.debouncedInit = this.debounce(async (current) => {
            // 避免重复加载相同数据
            if (this.current === current && this.listData[current]?.list.length > 0) {
                return;
            }

            if (this.isPartZone) {
                if (this.cateList.length === 0) {
                    await this.init();
                }
            } else {
                await this.init(this.listData[current].list.length === 0);
            }
        }, 200); // 增加延迟时间到200ms

        // console.log('deviceList', this.deviceList);

        // #ifdef H5
        await this.onShowExecute();
        // #endif

        // #ifdef MP-WEIXIN
        // 在小程序中，mounted 时执行显示逻辑
        await this.handleComponentShow();
        // #endif
    }

    // 组件激活时（如果使用了keep-alive）
    activated() {
        this.handleComponentShow();
    }

    // tabbar 切换
    onTabItemTap(item) {
        uni.$emit(Constants.TABBAR_CHANGE, item);
    }

    getTabs() {
        // const hasTutorialTids = ['11', '10', '12', '13', '16'];
        const hasAccessoriesTids = ['11', '10', '12', '13', '16', '17'];
        const result = [
            {
                label: '商品',
                value: 1,
                tid: '',
            },
        ];

        if (hasAccessoriesTids.includes(this.tid)) {
            result.push({
                label: '配件',
                value: 2,
                tid: this.tid,
            });
        }

        // if (hasTutorialTids.includes(this.tid)) {
        //     result.push({
        //         label: '官方教程',
        //         value: 3,
        //         tid: this.tid,
        //     });
        // }

        return result;
    }

    // 处理组件显示逻辑
    async handleComponentShow() {
        if (this.isLoaded) {
            // #ifdef MP-WEIXIN
            const tid = uni.getStorageSync('tid');
            if (tid && this.tid != tid) {
                this.tid = tid;
                this.init();
            }
            // #endif
        }
        setTimeout(() => {
            uni.removeStorageSync('tid');
        }, 200);
        Utils.reportEvent('shop_click', {});
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SHOP_BUTTON,
        });
    }

    async onShowExecute() {
        ServeModule.asyncProducts({ is_act: 0 });
        await this.getTagList();
        await this.init();
    }

    async init(reLoad = true) {
        if (this.isPartZone) {
            await this.getPartCate();
        } else {
            if (reLoad) {
                this.listData[this.current].finished = false;
                this.listData[this.current].page = 1;
                await this._getListFactory();
            }
        }
    }

    async _getListFactory() {
        // 如果已经全部加载完成直接终止执行
        await this.getGoodsList();
    }

    refresherrefresh() {
        if (this.isPartZone) {
            this.isRefreshing = false;
            // this.cateList.splice(0, this.cateList.length);
            // console.log('this.cateList', this.cateList);
            return;
        } else {
            this.listData[this.current].finished = false;
            this.listData[this.current].list = [];
            this.listData[this.current].page = 1;

            // 刷新时重置推荐商品状态
            this.recommendedList = [];
            this.recommendedPage = 1;
            this.recommendedFinished = false;
            this.recommendedLoading = false;
        }
        this.isRefreshing = true;
        // this.tabLoading = true
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
            // this.tabLoading = false
        }, 0);
    }

    async getTagList() {
        try {
            await GoodsModule.asyncGetTagList();
            const _tagList = GoodsModule.tagList;
            this.seftBrandLists = GoodsModule.tagList.filter((tag) => {
                return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            });
            this.listData = [];
            _tagList.unshift(...this.firstTabLists);
            this.tabsList = _tagList.filter((tag) => {
                this.listData.push({ list: [], pages: 1, finished: false, page: 1 });
                return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            });
            // const allTagTemplateLists = [...this.firstTabLists, ..._tagList];
            // this.allTabLists = allTagTemplateLists.filter((tag) => {
            //     this.listData.push({ list: [], pages: 1, finished: false, page: 1 });
            //     return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            // });
            this.tid = this.tid || this.tabsList[0].tid;
            this.swiperCurrent = this.current = this.tabsList.findIndex((item) => item.tid === this.tid);
            this.currentTab = 'tab-' + (this.current - 1);
        } catch (e) {
            console.error('getTagList e=', e);
        }
    }

    // 配件类目列表
    async getPartCate() {
        let parts_type = '';
        let parts_category_name = '';
        const res = await partCate();
        this.cateList = res.children;
        this.cateList2 = this.cateList.filter((item) => item.children);
        this.cateList.forEach((item) => {
            if (Object.keys(this.result).length !== 0 && this.result.constructor === Object) {
                item.isShow = this.result[item.id];
            } else {
                item.isShow = false; // 添加显示展示收起唯一标识
            }
            parts_type = parts_type ? `${parts_type}、${item.name}` : parts_type;
            if (item.children) {
                item.children.map((it) => {
                    parts_category_name = parts_category_name
                        ? `${parts_category_name}、${it.name}`
                        : parts_category_name;
                });
            }
        });
        Utils.reportEvent('parts_type_show', { parts_type, parts_category_name });
        this.isLoaded = true;
    }

    handleWishList(isWish: number, gid: string) {
        // opt: add / remove
        const opt = isWish ? 'remove' : 'add';
        wishGoods({ goods_id: gid, opt })
            .then(() => {
                // 本地更新状态：直接修改当前列表中目标商品的 is_wish 状态，避免重新拉取列表
                const target = this.listData[this.current].list.find((item) => item.gid === gid);
                if (target) {
                    this.$set(target, 'is_wish', opt === 'add' ? 1 : 0);
                }
            })
            .catch((err) => {
                console.error(err);
            });
    }

    // 加载推荐商品
    async loadRecommendedGoods() {
        try {
            this.recommendedLoading = true;
            // const recommendedId = -1; // 推荐商品的tid
            const recommendedParams = {
                tid: -1,
                page: this.recommendedPage,
                page_size: 12,
                single: 1,
            };

            const { list: recommendedList = [] } = await goodsList(recommendedParams);

            // 如果是第一页，直接赋值；否则追加
            if (this.recommendedPage === 1) {
                this.recommendedList = recommendedList;
            } else {
                this.recommendedList = this.recommendedList.concat(recommendedList);
            }
            // 更新分页状态
            this.recommendedPage++;

            this.recommendedLoading = false;
        } catch (error) {
            console.error('加载推荐商品失败:', error);
            this.recommendedLoading = false;
        }
    }

    async getGoodsList() {
        try {
            let product_id = '';
            let tid: string = this.tid;
            const index = this.current;
            let type = null;
            const page: number = this.listData[index].page;
            const obj = {
                10000: -2,
                10001: 0,
                10002: 1,
                10003: 2,
            };
            let params: Object = {
                tid,
                page,
                page_size: 12,
                single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
            };
            if (['10001', '10002', '10003', '10000'].includes(tid)) {
                type = obj[tid];
                tid = '-1';
                params = { ...params, type, tid };
            }
            const { list = [], pages = 1 } = await goodsList(params);
            if (page >= pages) {
                this.listData[index].finished = true;
                // 重置推荐商品的状态，准备加载推荐
                this.recommendedPage = 1;
                this.recommendedFinished = false;
                this.recommendedLoading = false;
                this.recommendedList = [];

                // // 开始加载推荐商品第一页
                await this.loadRecommendedGoods();
            }
            if (page === 1) {
                this.$set(this.listData, index, {
                    page: 1,
                    list,
                    pages,
                    finished: page >= pages,
                });
            } else {
                this.listData[index].list = this.listData[index].list.concat(list);
            }
            this.listData[index].list.map((item) => {
                product_id = product_id ? `${product_id}、${item.gid}` : product_id;
            });
            Utils.reportEvent('product_show', { product_id });
            this.listData[index].isLoading = false;
            this.isLoaded = true;
            this.listData[index].page++;
        } catch (e) {
            console.error('getGoodsList e=', e);
        }
    }

    // 点击切换头部
    async swichMenu(current, item, type) {
        const event = {
            2: 'hot_click',
            3: 'mixiangzuhegou_click',
            10: 'xidiji_click',
            11: 'saodiji_click',
            12: 'xichenqi_click',
            13: 'chuifengji_click',
            14: 'jingyinyitiji_click',
            20: 'parts_zone',
        };
        Utils.reportEvent(event[item.tid], {});
        if (type === 'image') {
            // console.log(9999999999999)
            //  uni.navigateTo({
            //     url: `/pages/brandType/brandType`,
            //  });
            Utils.navigateTo(`/pagesA/brandType/brandType?tid=${item.tid}&partId=${item.tid}`);
            return;
        }
        if (this.tid == item.tid) return;
        console.log(type);
        this.swiperCurrent = this.current = current;
        this.tid = item.tid;
        this.currentTab = 'tab-' + (current - 1);

        // 切换tab时重置推荐商品状态
        this.recommendedList = [];
        this.recommendedPage = 1;
        this.recommendedFinished = false;
        this.recommendedLoading = false;
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_CLICK,
                id: Number(item.tid),
                name: item.name,
            },
            true,
        );
        if (current == 0 && this.scrollViewTop > 0) {
            this.scrollToTop();
        }
        // this.jump(0);
        this.$nextTick(async () => {
            if (this.isPartZone) {
                if (this.cateList.length === 0) {
                    await this.init();
                }
            } else {
                await this.init(this.listData[current].list.length === 0);
            }
        });
    }

    async swiperChange(e) {
        // console.log('swiperChange', e)
        // e.detail.source 可能值如下：autoplay 自动播放导致swiper变化。touch 用户划动引起swiper变化。其他原因将用空字符串表示。
        if (!e.detail.source) return; // 避免和切换tab触发的change事件 导致重复调用
        const current = e.detail.current;

        if (this.swiperCurrent === current) return;

        // 立即更新状态，避免延迟导致的跳动
        this.swiperCurrent = current;
        this.current = current;
        this.tid = this.tabsList[current].tid;
        this.currentTab = 'tab-' + (current - 1);

        // 切换时重置推荐商品状态
        this.recommendedList = [];
        this.recommendedPage = 1;
        this.recommendedFinished = false;
        this.recommendedLoading = false;
        if (e.detail.source === 'touch') {
            // 延迟加载数据，避免滑动时加载
            setTimeout(() => {
                this.debouncedInit(current);
            }, 300); // 增加延迟，等待滑动完全结束
        }
    }

    // 添加防抖方法
    debouncedInit: any = null;

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    swiperFinish(e) {
        // 只在必要时启用refresher，避免频繁操作
        this.enableRefresher = true;
    }

    onTabsChange(item) {
        this.swichMenu(item.index, item, 'text');
    }

    swiperTransition(e) {
        // 只在必要时禁用refresher，避免频繁操作
        this.enableRefresher = false;
    }

    // 预售/自定义 不展示券后价
    showCouponPrice(item: any) {
        return item.is_presale === 0 && item.is_ini === 0;
    }

    gotoGdetailPage(gid: string, name: string) {
        // aplus_queue.push({
        //     action: 'aplus.record',
        //     arguments: ['goodsDetail', 'CLK', {
        //         gid,
        //         name,
        //         z: 333,
        //         page_name: 'goodsDeatail', // 您当前页面的自定义页面编码（非必传）
        //     }]
        // });
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
                id: Number(gid),
                name: name,
            },
            true,
        );
        Utils.reportEvent('product_click', { product_id: gid });
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}`);
    }

    onreachBottom() {
        // 如果当前商品列表已加载完成，则加载推荐商品
        console.log(this.listData[this.current].finished);
        if (this.listData[this.current].finished) {
            // 加载推荐商品
            if (!this.recommendedFinished && !this.recommendedLoading) {
                this.loadRecommendedGoods();
            }
            return;
        }

        // 否则继续加载当前商品列表
        if (this.listData[this.current].isLoading) return;
        this.listData[this.current].isLoading = true;
        this._getListFactory();
    }

    viewOnreachBottom() {
        this.$emit('viewOnreachBottom');
    }

    viewRefresherrefresh() {
        this.isViewRefreshing = true;
        this.$emit('viewRefresherrefresh');
        setTimeout(() => {
            this.isViewRefreshing = false;
        }, 1000);
    }

    // 优化：滚动事件节流（限制100ms触发一次）
    private throttledOnScroll = throttle((e) => {
        const scrollTop = e.detail.scrollTop;
        this.scrollViewTop = scrollTop;
        // 控制回到顶部按钮的显示（降低判断频率）
        this.showBackToTop = scrollTop > 1000;
        this.$emit('viewScroll', { scrollTop });
    }, 10); // 100ms触发一次

    onViewScroll(e) {
        // const scrollTop = e.detail.scrollTop;
        // this.scrollViewTop = scrollTop;
        // // 控制回到顶部按钮的显示
        // this.showBackToTop = scrollTop > 1000; // 降低阈值到1000px，更容易触发
        // // 触发父组件的滚动事件
        // this.$emit('viewScroll', { scrollTop });
        this.throttledOnScroll(e);
    }

    handleViewScroll(e, type) {
        const scrollTop = e.detail.scrollTop;
        this.scrollFirstViewTop = scrollTop;
        // 控制回到顶部按钮的显示
        this.showBackToTop = scrollTop > 1000; // 降低阈值到1000px，更容易触发
        // 触发父组件的滚动事件
        this.$emit('viewScroll', { scrollTop });
        this.throttledOnScroll(e);
    }

    // 添加节流标志
    scrollThrottle: boolean = false;

    scrollToTop() {
        this.scrollTop = 0;
        // this.$nextTick(() => {
        //     this.scrollTop = 0;
        // });
        this.viewRefresherrefresh();
    }

    // 字体样式动态调整
    letterSpacing(index) {
        const name = this.tabsList[index] && this.tabsList[index].name;
        if (name && name.length < 5) {
            return 'tab-item-ls';
        }
        return '';
    }

    getElRect(elClass, dataVal = null) {
        const query = uni.createSelectorQuery().in(this);
        query
            .select('.' + elClass)
            .fields(
                {
                    size: true,
                },
                (res) => {
                    // 如果节点尚未生成，res值为null，循环调用执行
                    if (!res) {
                        setTimeout(() => {
                            this.getElRect(elClass);
                        }, 10);
                        return;
                    }
                    if (dataVal) {
                        this[dataVal] = res.height;
                    }
                },
            )
            .exec();
    }

    toProductRegister() {
        Utils.reportEvent('product_register_click', {
            product_register_status: this.products.length ? '已注册' : '未注册',
        });
        uni.navigateTo({
            url: `/pagesC/serve/serve`,
        });
    }

    // 展开与收起
    moreShow(itemIsShow, index) {
        this.cateList[index].isShow = !itemIsShow;
        this.$forceUpdate();
        const result = this.handleValue(this.cateList);
        this.result = result;
    }

    // 绑定
    handleValue(data) {
        const result = {};

        data.forEach((node) => {
            const id = node.id;
            const isShow = node.isShow;
            result[id] = isShow;
        });

        return result;
    }

    toPartsCate(c_id, name1, name2) {
        Utils.reportEvent('parts_type_click', { parts_type: name1, parts_category_name: name2 });
        uni.navigateTo({
            url: `/pagesA/partsCate/partsCate?id=${c_id}&name1=${name1}&name2=${name2}`,
        });
    }

    flagClick() {
        let parts_type = '';
        this.cateList.map((item) => {
            parts_type = parts_type ? `${parts_type}、${item.name}` : parts_type;
        });
        if (!this.flag) {
            Utils.reportEvent('parts_more', { parts_type });
        } else {
            Utils.reportEvent('parts_less', { parts_type });
        }
        this.flag = !this.flag;
    }

    overlayClick() {
        this.flag = false;
    }

    // 配件专区锚点滑动
    jump(index, item: any = {}) {
        this.activePartZoneTab = index;
        this.flag = false;
        this.$nextTick(() => {
            this.scrollIntoView = 'd_jump-' + index;
        });
        // this.scrollIntoView = 'd_jump-' + index;
    }

    _navigateTo() {
        uni.navigateTo({
            url: '/pagesA/search/search?search_type=' + 1,
        });
        Utils.reportEvent('search_click', {});
    }

    toShop() {
        Utils.navigateTo('/pages/shop/shop');
    }

    handleFilterClick(val) {
        if (val === 2) {
            Utils.navigateTo('/pages/shop/shop?tid=20&partId=' + this.tid);
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_PARTS_CLICK,
            });
        } else if (val === 3) {
            Utils.navigateTo('/pages/contents/contents?isMsg=1&tabsIndex=3&indexTopicId=' + this.tid);
        } else {
            this.currentFilterIndex = val;
        }
        if (val === 1) {
            Utils.logTrace({
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TABS_PRODUCT_CLICK,
            });
        }
    }

    // 组件销毁时清除节流定时器
    destroyed() {
        this.throttledOnScroll.cancel();
    }
}
</script>

<style lang="scss" scoped>
@import './Content.scss';

// 回到顶部按钮样式
.back-to-top {
    position: fixed;
    right: 30rpx;
    bottom: 150rpx;
    width: 70rpx;
    height: 70rpx;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s ease;

    // transition: opacity 0.3s ease, transform 0.3s ease;
    // opacity: 0.9;
    &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 1);
    }

    text {
        font-size: 18rpx;
        color: #333;
        margin-top: 2rpx;
    }
}

.back-to-top-icon {
    width: 30rpx;
    height: 30rpx;
    transform: rotate(-90deg);
}

.swiper-item {
    will-change: transform;
    transform: translateZ(0);
}

.goods-card {
    will-change: opacity, transform;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

// 猜你喜欢标题样式
.recommend-title {
    width: 100%;
    padding: 20rpx 0 20rpx;
    font-size: 32rpx;
    font-weight: 600;

    text {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        position: relative;
        padding: 0 40rpx;
    }
}

// 加载更多样式
.loading-more {
    padding: 30rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
}
</style>
<style lang="scss">
.tabs-view {
    background-color: #ffffff;
    height: 96rpx;
}
</style>
