<template>
    <view class="more" @click.stop="moreBtn">
        <image style="width: 46rpx; height: 46rpx;display: block;" src="https://wpm-cdn.dreame.tech/images/202306/225448-1686107296428.png"></image>
    </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

@Component
export default class More extends Vue {
    constructor() {
        super();
    }

    moreBtn() {
        this.$emit('moreShow')
    }
}
</script>
<style lang="scss" scoped>

</style>
