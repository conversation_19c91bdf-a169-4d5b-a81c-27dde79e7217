import md5Libs from '@/utils/md5.js';
import { UserModule } from '@/store/modules/user';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import axios from 'axios';
import adapter from 'axios-miniprogram-adapter';
import Constants from '@/common/Constants';
import qs from 'qs';
import _ from 'lodash';
const BASE_GO_URL = process.env.VUE_APP_GO_BASE_URL;
// 是否正在刷新的标记
let isRefreshing = false;

// 重试队列，每一项将是一个待执行的函数形式
let retryRequests = [];

function getSign(obj, security_key, sign_time = 0) {
    const cleanedObj = _.omitBy(
        obj,
        (value) => _.isUndefined(value) || _.isNull(value) || (_.isNumber(value) && _.isNaN(value)),
    );
    const sortArr = recursiveSerialization(cleanedObj).join('&') + sign_time + security_key;
    // console.log(sortArr)
    return md5Libs.md5(sortArr);
}

function recursiveSerialization(obj) {
    const sortArr = [];
    if (_.isArray(obj)) {
        const itemArr = [];
        obj.forEach((item, index) => {
            if (_.isObject(item)) {
                if (index == obj.length - 1) {
                    itemArr.push(`${recursiveSerialization(item).join('&')}`);
                } else {
                    itemArr.push(`${recursiveSerialization(item).join('&')}`);
                }
            } else {
                if (index == obj.length - 1) {
                    itemArr.push(item);
                } else {
                    itemArr.push(item + ',');
                }
            }
        });
        sortArr.push(`${itemArr.join('')}`);
    } else if (_.isObject(obj)) {
        const keysArr = [];
        const keysSorted = Object.keys(obj).sort(); // 排序名

        for (let i = 0; i < keysSorted.length; i++) {
            const key = keysSorted[i];
            // @ts-ignore
            const value = obj[key];
            if (_.isArray(value)) {
                keysArr.push(`${key}=[${recursiveSerialization(value).join('&')}]`);
            } else if (_.isObject(value)) {
                keysArr.push(`${key}=[${recursiveSerialization(value).join('&')}]`);
            } else {
                keysArr.push(`${key}=${value}`);
            }
        }

        sortArr.push(`${keysArr.join('&')}`);
    }

    return sortArr;
}

const axiosReq = axios.create({
    adapter: adapter,
    baseURL: BASE_GO_URL /* 根域名 */,
    timeout: 10000,
    dataType: 'json',
    headers: {
        'Content-Type': 'application/json',
    },
});

// h5本地开发环境user_id和sessid可以在配置文件改
let localConfig = {
    user_id: 0,
    sessid: '',
};

if (process.env.NODE_ENV === 'development') {
    try {
        localConfig = require('../../../local.config');
    } catch (err) {
        console.log('config.local.js不存在');
    }
}

// 请求的前置拦截
axiosReq.interceptors.request.use(
    (config) => {
        const custom = config?.custom;
        const timestamp = new Date().getTime();
        // console.warn('config custom', custom);
        // 是否开启loading
        if (custom?.loading) {
            uni.showLoading({ title: custom.title || '正在加载' });
        }

        const BASE_DATA = {
            user_id: +UserModule.user_id || localConfig.user_id,
            sessid: UserModule.sessid || localConfig.sessid,
            api: AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268',
            version: AppModule.version,
        };
        const security_key = AppModule.platform == 'ios' ? 'EfCj*YDcb5anqOs' : 'EfCj*YDcb5anqOs';
        config.data = {
            ...config.data,
            ...BASE_DATA,
        };
        const sign = getSign(config.data, security_key, timestamp);
        delete config.data.sign;
        delete config.data.sign_time;
        config.headers['dreame-api'] = AppModule.platform == 'ios' ? 'i_1666147923' : 'a_1664246268';
        config.headers['dreame-api-sign'] = sign;
        config.headers['dreame-api-timestamp'] = timestamp;
        // config.data = qs.stringify(config.data);
        // config.data.sign = sign;
        return config;
    },
    (config) => {
        // 可使用async await 做异步操作
        return Promise.reject(config);
    },
);

// 请求的回调拦截
axiosReq.interceptors.response.use(
    async (response) => {
        // console.log(response, 'response');
        const res = response.data;
        // // 自定义参数
        const custom = response.config?.custom;
        if (custom?.loading) {
            uni.hideLoading();
        }

        if (res.success && res.code === Constants.GO_RET_OK) {
            return res;
        } else if (res.code == Constants.GO_RET_SESSION_EXP) {
            // 清除登录信息重新登录
            UserModule.setSessId('');
            UserModule.setUserId('');
            UserModule.setIsLogin(false);
            const originalRequest = response.config;
            if (!isRefreshing) {
                isRefreshing = true;
                // 重新获取session
                return refreshSession()
                    .then(() => {
                        // 重试队列执行
                        retryRequests.forEach((cb) => cb());
                        retryRequests = [];
                        originalRequest.data = qs.parse(originalRequest.data);
                        return axiosReq(originalRequest);
                    })
                    .catch(() => {
                        // 如果重新获取session也失败了，则直接返回错误
                        isRefreshing = false;
                        retryRequests = [];
                        // if (UserModule.user_id) {
                        //     Utils.Toast('网络错误');
                        // }
                        return Promise.reject(res);
                    });
            } else {
                // 正在刷新session，返回一个未执行resolve的promise
                return new Promise((resolve) => {
                    // 将resolve放进队列，用一个函数形式来保存，等session刷新后直接执行
                    retryRequests.push(() => {
                        originalRequest.data = qs.parse(originalRequest.data);
                        resolve(axiosReq(originalRequest));
                    });
                });
            }
        } else {
            // 处理错误消息提示
            const shouldShowToast =
                // 有错误消息且未配置toastDeal时显示
                (res.msg && typeof res.msg === 'string' && !custom?.errorCodes) ||
                // 配置了toastDeal且不是特定错误码时显示
                (custom?.errorCodes && !custom.errorCodes.includes(res.code));

            if (shouldShowToast && res.msg) {
                Utils.Toast(res.msg);
            }
            return Promise.reject(res);
        }
    },
    (response) => {
        // if (UserModule.user_id) {
        //     Utils.Toast('网络错误');
        // }
        const res = response.data;
        // 自定义参数
        const custom = response.config?.custom;
        if (custom?.loading) {
            uni.hideLoading();
        }
        if (res.msg) {
            Utils.Toast(res.msg);
        }
        return Promise.reject(response);
    },
);

// 重新获取session的函数
function refreshSession() {
    Utils.refreshSession();
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
            window.onAppMessage = undefined;
            reject();
        }, 5000);
        window.onAppMessage = async (data) => {
            if (typeof data == 'string') {
                // 兼容字符串类型的数据
                data = JSON.parse(data);
            }
            data = data.data || data;
            await UserModule.setCommit(data);
            clearTimeout(timer);
            window.onAppMessage = undefined;
            resolve();
        };
    });
}

const request = {
    post(url, data, custom = {}) {
        return new Promise((resolve, reject) => {
            axiosReq
                .post(url, data, custom)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    get(url, params, custom = {}) {
        return new Promise((resolve, reject) => {
            axiosReq
                .get(url, { params }, custom)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 可添加其他HTTP请求方法
};

export default request;
