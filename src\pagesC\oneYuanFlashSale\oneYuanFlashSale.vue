<template>
  <view>
      <APPSHARE :link="link" />
      <view
          class="oneYuanFlashSale_container"
          v-if="loading && !isEndAcitivity"
          :style="{ 'margin-top': user_id ? '0' : '96rpx' }"
      >
          <view class="oneYuanFlashSale_content" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
              <view class="header">
                  <view class="left">
                      <image
                          v-show="$isInApp()"
                          class="search_back"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc328c9a8e8260014034.png"
                          @click="goBack"
                      ></image>
                  </view>
                  <image
                      class="header_title_img"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68830ba03fdd82620012136.png"
                  >
                  </image>
                  <view class="right">
                      <view v-show="$isInApp()" class="share" @click="shareActivity"></view>
                      <view class="rule" @click="openRulePopup"></view>
                  </view>
              </view>
              <view class="header_title"> </view>
              <!-- 活动倒计时 -->
              <view class="activity-time-down" :style="{ 'margin-top': 700 - statusBarHeight - 154 + 'rpx' }">
                  <view class="countdown-container">
                      <image
                          class="countdown-icon"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687b9d764ced73150011466.png"
                      />
                      <!-- <view class="countdown-label"></view> -->
                      <u-count-down
                          :time="countdownTime"
                          format="DD:HH:mm:ss"
                          autoStart
                          millisecond
                          @finish="onFinish"
                          @change="onChange"
                      >
                          <view class="time">
                              <view style="margin-right: 6rpx">仅剩</view>
                              <view class="time__item">
                                  <view class="time__item_text">{{
                                      timeData.days >= 10 ? timeData.days : '0' + timeData.days
                                  }}</view>
                                  <view style="margin: 0rpx 4rpx">天</view>
                              </view>
                              <view class="time__item">
                                  <view class="time__item_text">{{
                                      timeData.hours >= 10 ? timeData.hours : '0' + timeData.hours
                                  }}</view>
                                  <view style="margin: 0rpx 4rpx">时</view>
                              </view>
                              <view class="time__item">
                                  <view class="time__item_text">{{
                                      timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes
                                  }}</view>
                                  <view style="margin: 0rpx 4rpx">分</view>
                              </view>
                              <view class="time__item">
                                  <view class="time__item_text">{{
                                      timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
                                  }}</view>
                                  <view style="margin: 0rpx 4rpx">秒</view>
                              </view>
                          </view>
                      </u-count-down>
                  </view>
              </view>
              <view class="activity-time-text">活动每天10点准时开始！数量有限！先到先得！</view>
              <view class="content">
                  <view
                      class="content_title"
                      v-for="(item, index) in productList"
                      :key="index"
                      @click="handleAssist(item)"
                  >
                      <view class="content_img_wrap">
                          <image class="content_img" :src="item.goods_image" />
                          <image
                              class="content_img_icon"
                              src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6879fb73920835980010652.png"
                          />
                          <image
                              class="content_img_icon1"
                              src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bafc2860de5490011767.png"
                          />
                          <view
                              class="content_img_icon2"
                              v-if="
                                  item.sales / (item.sales + item.stock) > 0.8 &&
                                  item.sales / (item.sales + item.stock) < 1
                              "
                          >
                              <image
                                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c53135f18b3900011088.png"
                              />
                          </view>
                      </view>
                      <view class="content_title_text">
                          <view class="content_title_text_title">{{ item.show_name }}</view>
                          <view class="content_title_text_content">
                              <view
                                  style="
                                      display: flex;
                                      align-items: flex-end;
                                      width: 74rpx;
                                      height: 54rpx;
                                      color: #ff0084;
                                      font-weight: 600;
                                      padding-bottom: 10rpx;
                                  "
                              >
                                  <text style="font-size: 24rpx; align-self: flex-end; line-height: 1">¥</text>
                                  <view style="display: flex; align-items: flex-end; height: 54rpx">
                                      <text
                                          v-if="String(item.sale_price).indexOf('.') !== -1"
                                          style="font-size: 38rpx; line-height: 1; align-self: flex-end"
                                      >
                                          {{ String(item.sale_price).split('.')[0] }}
                                      </text>
                                      <text v-else style="font-size: 38rpx; line-height: 1; align-self: flex-end">
                                          {{ item.sale_price }}
                                      </text>
                                      <text
                                          v-if="String(item.sale_price).indexOf('.') !== -1"
                                          style="
                                              font-size: 38rpx;
                                              line-height: 1;
                                              align-self: flex-end;
                                              margin-left: 2rpx;
                                          "
                                      >
                                          .
                                      </text>
                                      <text
                                          v-if="String(item.sale_price).indexOf('.') !== -1"
                                          style="font-size: 24rpx; line-height: 1; align-self: flex-end"
                                      >
                                          {{ String(item.sale_price).split('.')[1] }}
                                      </text>
                                  </view>
                              </view>
                              <view class="content_title_text_grab_text"
                                  >优惠前：<text>{{ Math.floor(item.extra.mprice) }}元</text>
                              </view>
                              <!-- 抢商品 -->
                              <view class="content_title_text_grab">
                                  <view class="content_title_text_grab_bg">
                                      <view class="content_title_text_grab_bg_img">
                                          <!-- 进度条 -->
                                          <view
                                              class="content_title_text_grab_bg_progress_bg"
                                              v-if="item.stock !== 0"
                                          >
                                              <view class="content_title_text_content_progresss">
                                                  <view
                                                      class="progress"
                                                      :style="{
                                                          width: (item.sales / (item.stock + item.sales)) * 100 + '%',
                                                      }"
                                                  >
                                                  </view>
                                              </view>
                                              <view class="content_title_text_grab_text_progress_text">
                                                  已抢
                                                  {{
                                                      ((item.sales / (item.stock + item.sales)) * 100) % 1 === 0
                                                          ? (item.sales / (item.stock + item.sales)) * 100
                                                          : ((item.sales / (item.stock + item.sales)) * 100).toFixed(
                                                                2,
                                                            )
                                                  }}%
                                              </view>
                                          </view>
                                          <view class="content_title_text_grab_text_assist_bg" v-else></view>
                                          <!-- 抢商品 -->
                                          <view
                                              class="content_title_text_grab_text"
                                              :class="
                                                  item.user_seckill && item.user_seckill.order_status == 300
                                                      ? 'normal_buy_active'
                                                      : ''
                                              "
                                              v-if="item.stock !== 0"
                                          ></view>
                                          <view class="content_title_text_grab_text_assist" v-else> </view>
                                      </view>
                                  </view>
                              </view>
                          </view>
                      </view>
                  </view>
                  <!-- 测试助力弹窗按钮 -->
                  <!-- <view class="test_assist_button" @click="openAssistPopup">
                  <text>测试助力弹窗</text>
              </view> -->
              </view>
          </view>

          <!-- 活动规则弹窗 -->
          <u-popup
              :show="showRulePopup"
              v-prevent-scroll="showRulePopup"
              mode="center"
              :round="18"
              :safe-area-inset-bottom="false"
              catchtouchmove
          >
              <view class="rule-popup-content">
                  <view class="rule-popup-header">
                      <view class="rule-title">
                          <view class="rule-title-decoration left"></view>
                          <text class="rule-title-text">活动规则</text>
                          <view class="rule-title-decoration right"></view>
                      </view>
                      <view class="rule-popup-close" @click="closeRulePopup">
                          <view class="close-icon"></view>
                      </view>
                  </view>
                  <scroll-view scroll-y class="rule-container">
                      <view class="rule-content">
                          <view class="rule-content-text">
                              <view class="rule-section" v-html="baseInfo.rule"> </view>
                          </view>
                      </view>
                  </scroll-view>
              </view>
          </u-popup>

          <!-- 助力弹窗 -->
          <u-popup
              :show="showAssistPopup"
              mode="center"
              :round="10"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="closeAssistPopup"
          >
              <view class="passist_popup">
                  <!-- 头部标题 -->
                  <view class="assist_popup_header">
                      <image
                          class="assist_popup_header_left"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png"
                      />
                      <text class="assist_popup_title">快帮TA助力</text>
                      <image
                          class="assist_popup_header_right"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png"
                      />
                  </view>

                  <!-- 内容区域 -->
                  <view class="assist_popup_content">
                      <!-- 头像昵称 -->
                      <view class="assist_popup_user">
                          <image class="assist_popup_avatar" :src="inviter_info.avatar" />
                          <text class="assist_popup_nickname">{{ inviter_info.nick }}</text>
                      </view>

                      <!-- 倒计时 -->
                      <view class="assist_popup_countdown">
                          <text class="assist_popup_countdown_time">{{ popupCountdownTime }}</text>
                          <text class="assist_popup_countdown_desc">后未达成，本轮助力将失效</text>
                      </view>

                      <!-- 助力按钮 -->
                      <view class="assist_popup_button_container">
                          <view class="assist_popup_button" @click="handleAssistAction">
                              <text class="assist_popup_button_text">为TA助力</text>
                          </view>
                      </view>
                  </view>
              </view>
          </u-popup>

          <!-- 助力弹窗 -->
          <u-popup
              :show="goToSelectPopup"
              mode="center"
              :round="10"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="closeAssistPopup"
          >
              <view class="passist_popup">
                  <!-- 头部标题 -->
                  <view class="assist_popup_header">
                      <image
                          class="assist_popup_header_left"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png"
                      />
                      <text class="assist_popup_title">快帮TA助力</text>
                      <image
                          class="assist_popup_header_right"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png"
                      />
                  </view>

                  <!-- 内容区域 -->
                  <view class="assist_popup_content">
                      <!-- 头像昵称 -->
                      <view class="assist_popup_user">
                          <image class="assist_popup_avatar" :src="inviter_info.avatar" />
                          <text class="assist_popup_nickname">{{ inviter_info.nick }}</text>
                      </view>

                      <!-- 倒计时 -->
                      <view style="text-align: center; padding-top: 10rpx">
                          <view style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center">
                              恭喜您！已助力成功！
                          </view>
                          <view style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center">
                              快来活动区挑选您的心仪好物吧！
                          </view>
                      </view>

                      <!-- 助力按钮 -->
                      <view class="assist_popup_button_container">
                          <view class="assist_popup_button" @click="handleSelectAction">
                              <text class="assist_popup_button_text">去挑选</text>
                          </view>
                      </view>
                  </view>
              </view>
          </u-popup>

          <!-- 未付款弹窗 -->
          <u-popup :show="unPayPopup" mode="center" :round="10" width="600rpx">
              <view class="passist_popup-title">
                  <view class="rule-popup-close" @click="closeRulePopupPay">
                      <view class="close-icon"></view>
                  </view>
                  <!-- 头部标题 -->
                  <!-- <view class="assist_popup_header">
                      <image class="assist_popup_header_left"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png" />
                      <text class="assist_popup_title">温馨提示</text>
                      <image class="assist_popup_header_right"
                          src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png" />
                  </view> -->

                  <!-- 内容区域 -->
                  <view class="assist_popup_content">
                      <view style="text-align: center; height: 300rpx; padding: 0rpx 24rpx 0rpx; margin-top: 90rpx">
                          <view style="height: 100rpx; font-size: 24rpx; color: #fd4a55; font-weight: 600"
                              >订单编号：{{ order_no }}</view
                          >
                          <text style="font-size: 36rpx; color: #fd4a55; font-weight: 600; text-align: center"
                              >点击下方按钮完成支付，锁定心仪商品，库存有限先到先得！</text
                          >
                      </view>
                      <!-- 助力按钮 -->
                      <view class="assist_popup_button_container">
                          <view class="assist_popup_button" @click="getOrderPayStatus()">
                              <text class="assist_popup_button_text">去支付</text>
                          </view>
                      </view>
                  </view>
              </view>
          </u-popup>
          <!-- 商品售罄，去其它商品 -->
          <!-- <u-popup
              :show="isEmptyPopup"
              mode="center"
              :round="40"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="isEmptyPopup = false"
          >
              <view class="is_empty_popup">
                  <view class="is_empty_popup_title" @click="closeEmptyPopup"></view>
              </view>
          </u-popup> -->
          <u-popup
              :show="isEmptyPopup"
              mode="center"
              :round="40"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="isEmptyPopup = false"
              bgColor="transparent"
          >
              <view class="popup_box is_empty_popup2">
                  <image
                      class="popup_img_bg"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a5d87fbba777693895670.png"
                      mode="widthFix"
                  />
                  <view class="popup_close" @click="closeEmptyPopup"> </view>
                  <view class="popup_go" @click="goToThreeDiscount(1)"> </view>
              </view>
          </u-popup>
          <!-- 活动未开始 -->
          <!-- <u-popup
              :show="isActivityNotStartPopup"
              mode="center"
              :round="40"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="isActivityNotStartPopup = false"
          >
              <view class="is_activity_not_start">
                  <view class="is_empty_popup_title" @click="closeActivityNotStartPopup"></view>
              </view>
          </u-popup> -->
          <u-popup
              :show="isActivityNotStartPopup"
              mode="center"
              :round="40"
              width="600rpx"
              :closeOnClickOverlay="true"
              @close="isActivityNotStartPopup = false"
              bgColor="transparent"
          >
              <view class="popup_box is_activity_not_start2">
                  <image
                      class="popup_img_bg"
                      src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a5d977f30e19964097599.png"
                      mode="widthFix"
                  />
                  <view class="popup_close" @click="closeNoStartPopup"> </view>
                  <view class="popup_go" @click="goToThreeDiscount(2)"> </view>
              </view>
          </u-popup>
      </view>
      <view class="oneYuanFlashSale_container_end" v-if="isEndAcitivity && loading">
          <view class="left" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
              <image
                  class="search_back"
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cdc6691f655980010633.png"
                  @click="goBack"
              ></image>
          </view>
          <view class="oneYuanFlashSale_container_end_content"> </view>
          <view class="oneYuanFlashSale_container_end_content_title"> 活动已结束，返回上一页</view>
          <view
              @click="goBack"
              style="
                  margin-bottom: 316rpx;
                  background: linear-gradient(101deg, #ff2c49 -12%, #ff6a76 100%);
                  color: #ffffff;
                  font-weight: 600;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 612rpx;
                  height: 94rpx;
                  margin: 54rpx auto 0rpx;
                  border-radius: 58rpx;
                  font-size: 30rpx;
              "
          >
              确认</view
          >
      </view>
      <custom-toast ref="customToast" />
      <EarnMoneySpendTimeoutTips
          v-if="$isInApp() && from == 'goldCoins'"
          ref="TimeoutTips"
          :position="{ top: '400rpx' }"
          :task-code="'oneYuanPurchase'"
          :fromPage="from"
          :watchTime="15000"
      />
      <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'">
          <template #active_tag>
              <img
                  src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891d4845695e3550011642.png"
                  style="
                      width: 64rpx;
                      height: 64rpx;
                      position: absolute;
                      left: 20rpx;
                      top: 10rpx;
                      transform: translate(-50%, -50%);
                  "
              />
          </template>
      </share-dialog>
      <!-- 活动页强弹窗 -->
      <activity-popup
          :showPopup="isGroupGoodsPopup"
          bgImg="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d8d6335a182200033913.png"
          @link="goToGroupGoods"
          @close="closeGroupGoodsPopup"
      ></activity-popup>
        <EarnMoneySpendTimeoutTips
            v-if="$isInApp() && from == 'earnMoneySpend'"
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '400rpx' }"
            :task-code="'viewGoodsOneYuanMoney'"
        />
  </view>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import { getThemeActivity } from '@/http/memberActivity';
import { getOneYuanFlashSaleList, checkUserCanBeInvited, joinInvite } from '@/http/oneYuanFlashSalePage';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
import GoldCoinTimeoutTips from '@/components/GoldCoinTimeoutTips/GoldCoinTimeoutTips.vue';
import { CheckAppJump } from '@/common/decorators';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import ActivityPopup from './components/activityPopup/activityPopup.vue';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';
@Component({
  name: 'MemberActivity',
  components: {
      GoldCoinTimeoutTips,
      ShareDialog,
      ActivityPopup,
        EarnMoneySpendTimeoutTips,
  },
      filters: {},
    })
export default class OneYuanFlashSale extends Vue {
  $refs!: {
      TimeoutTips;
        EarnMoneySpendTimeoutTips;
  };

  public isGroupGoodsPopup: boolean = false;
  // public arr: any[] = [];
  public isEmptyPopup: boolean = false;
  public link: string = '';
  public isActivityNotStartPopup: boolean = false;
  public order_no: string = '';
  public unPayPopup = false;
  public goToSelectPopup = false;
  public isEndAcitivity: boolean = false; // 活动是否结束
  public inviter_info: any = {}; // 邀请人信息
  public baseInfo: any = {}; // 活动基础信息
  public loading = false;
  public showRulePopup = false;
  public showAssistPopup = false;
  public relate_id: number = 0; // 秒杀id
  public inviter_id: number = 0; // 老用户id
  public countdownTime: number = 0; // 倒计时时间
  public show: boolean = false;
  public productInfo: any = {};
    public from: string = '';
  get user_id(): any {
      return UserModule.userInfo.user_id;
  }

  public activityId: any = 0;

  public productList: any[] = [];

  // 助力弹窗相关数据
  public popupCountdownTime = '00:00:00';
  private popupCountdownTimer: any = null;
  private popupEndTime: number = 0;
  public assistTarget = {
      avatar: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a1a4c446d12800372451.png',
      nickname: '用户A',
  };

  public seckillId: number = 0;
  public preloadImageList: any[] = [
      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a5d977f30e19964097599.png', // 售罄弹窗
      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a5d87fbba777693895670.png', // 活动未开始弹窗
      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689d8d6335a182200033913.png', // 活动页强弹窗
  ];

  public finishGropGoodsPopup: boolean = false;
  get statusBarHeight() {
      return AppModule.statusBarHeight;
  }

  public timeData: any = {};

  onChange(e: any) {
      this.timeData = e;
  }

  closeRulePopupPay() {
      this.unPayPopup = false;
  }

  closeEmptyPopup() {
      this.isEmptyPopup = false;
      //  一元购售罄弹窗关闭
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
              .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT_CLOSE,
      });
  }

  closeNoStartPopup() {
      this.isActivityNotStartPopup = false;
      //  一元购商品活动未开始弹窗关闭
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
              .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START_CLOSE,
      });
  }

  goToGroupGoods() {
      this.isGroupGoodsPopup = false;
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_LINK,
      });
      Utils.navigateTo('/pagesB/groupGoods/groupGoods');
  }

  closeGroupGoodsPopup() {
      this.isGroupGoodsPopup = false;
  }

  // type 1 一元购商品售罄弹窗跳转三折购 2 一元购商品活动未开始弹窗跳转三折购
  goToThreeDiscount(type: number) {
      if (type == 1) {
          this.isEmptyPopup = false;
          // 一元购商品售罄弹窗跳转三折购
          Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT_LINK,
          });
      } else if (type == 2) {
          this.isActivityNotStartPopup = false;
          // 一元购商品活动未开始弹窗跳转三折购
          Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START_LINK,
          });
      }
      Utils.navigateTo('/pagesC/threeDiscount/aThreeDiscount');
  }

  closeActivityNotStartPopup() {
      this.isActivityNotStartPopup = false;
      Utils.navigateTo('/pagesC/ambassador/login/index');
  }

    goBack() {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RETURN,
            },
            true,
        );
        if (!this.finishGropGoodsPopup) {
            this.finishGropGoodsPopup = true;
            this.isGroupGoodsPopup = true;
             // 一元购活动页强弹窗
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP,
        });
        } else {
            Utils.goBack();
        }
    }

  openRulePopup() {
      Utils.logTrace(
          {
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_RULE_CLICK,
          },
          true,
      );
      this.showRulePopup = true;
  }

  closeRulePopup() {
      this.showRulePopup = false;
  }

  onFinish() {
      this.isEndAcitivity = true;
  }

  getOrderPayStatus() {
      uni.navigateTo({
          url: `/pagesA/orderDetail/orderDetail??order_no=${this.order_no}&user_order_type=1`,
      });
      this.unPayPopup = false;
      // getOrderPayStatus({
      //     order_id: this.order_id,
      // }).then(res => {
      //     console.log(res, 22992);
      // });
  }

  // 格式化时间
  private formatTime(seconds: number): string {
      const days = Math.floor(seconds / (24 * 3600));
      const hours = Math.floor((seconds % (24 * 3600)) / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${days}天${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
          .toString()
          .padStart(2, '0')}`;
  }

  // 格式化百分比
  private formatPercentage(value: number): string {
      const percentage = value;
      // 检查是否为整数
      if (Number.isInteger(percentage)) {
          return `${percentage}%`;
      } else {
          // 保留两位小数
          return `${percentage.toFixed(2)}%`;
      }
  }

  // 更新弹窗倒计时
  private updatePopupCountdown() {
      const now = new Date().getTime();
      const diff = Math.max(0, this.popupEndTime - now);
      const seconds = Math.floor(diff / 1000);
      if (seconds <= 0) {
          this.popupCountdownTime = '0天00:00:00';
          this.stopPopupCountdown();
          return;
      }
      this.popupCountdownTime = this.formatTime(seconds);
  }

  // 开始弹窗倒计时
  private startPopupCountdown() {
      console.log(this.baseInfo, 282828);
      // 设置弹窗倒计时结束时间为当前时间加1小时（示例）
      this.popupEndTime = this.baseInfo.end_time * 1000;
      this.updatePopupCountdown();
      this.popupCountdownTimer = setInterval(() => {
          this.updatePopupCountdown();
      }, 1000);
  }

  // 停止弹窗倒计时
  private stopPopupCountdown() {
      if (this.popupCountdownTimer) {
          clearInterval(this.popupCountdownTimer);
          this.popupCountdownTimer = null;
      }
  }

  // 显示助力弹窗
  openAssistPopup() {
      this.showAssistPopup = true;
      this.startPopupCountdown();
  }

  shareActivity() {
      if (UserModule.sdkVersion < 13) {
          Utils.Toast('请升级Dreame App至最新版本', null, null, 'color: #111111;font-weight: 500;width: 500rpx;');
          return;
      }
      //     const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?inviter_id=${this.user_id}`;
      //     const data = {
      //         ...Constants.GEN_SHARE_LINK_TYPE,
      //         jumpLink: url,
      //     };
      //     Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
      //         const params = {
      //             target: 'wechat,weixin_circle,qq,sina',
      //             type: 'web',
      //             content: {
      //                 url:
      //                     `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?link=` +
      //                     res.data,
      //                 share_image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688e158c562be3530011762.png' + '?x-oss-process=image/resize,w_200',
      //                 share_title: '追觅好物，限时一元购',
      //                 share_desc: '邀请新用户加入，新款扫地机器人一元拿走！',
      //             },
      //         };
      //         Utils.messageChannel('share', params);
      //     });
      const randomIndex = Math.floor(Math.random() * this.productList.length);
      this.productInfo = {
          name: this.productList[randomIndex].show_name,
          image: this.productList[randomIndex].goods_image,
          price: this.productList[randomIndex].sale_price,
          desc: '',
          priceColor: '#FF1F0E',
          descColor: '#C59245',
          imageBg:
              'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109a76e6e34520010690.png',
      };
      this.show = true;
  }

  handleShare(type: 'web' | 'image') {
      this.show = false;
      Utils.cardShare(type)(
          {
              target: 'wechat,weixin_circle,qq,sina,image_template,download',
              link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?inviter_id=${this.user_id}`,
              jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?inviter_id=${this.user_id}`,
          },
          {
              content: {
                  url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesC/oneYuanFlashSale/oneYuanFlashSale?inviter_id=${this.user_id}`,
                  image:
                      'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688e158c562be3530011762.png' +
                      '?x-oss-process=image/resize,w_200',
                  title: '追觅好物，限时一元购',
                  desc: '邀请新用户加入，高端好物一元拿走！',
              },
              extras: {
                  type: 'activity',
                  id: this.activityId,
                  goods: {
                      name: this.productInfo.name,
                      // desc: '邀请新用户加入，新款扫地机器人一元拿走！',
                      imageBg:
                          'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689109a76e6e34520010690.png',
                      image: this.productInfo.image,
                      price: this.productInfo.price,
                      priceColor: '#FF1F0E',
                      descColor: '#C59245',
                  },
                  copyText: '追觅好物，限时一元购,邀请新用户加入，新款扫地机器人一元拿走！',
              },
          },
      );
  }

  // 关闭助力弹窗
  closeAssistPopup() {
      this.showAssistPopup = false;
      this.stopPopupCountdown();
  }

  // 处理助力
  handleAssistAction() {
      // 判断当前时间是否在活动时间内
      const now = Math.floor(Date.now() / 1000);
      if (now < this.baseInfo.start_time) {
          Utils.Toast('当前不在活动时间内，无法进行下一步操作');
          return;
      }
      if (now > this.baseInfo.end_time) {
          this.isEndAcitivity = true;
          return;
      }
      joinInvite({
          invite_type: 1,
          inviter_id: this.inviter_id,
          relate_id: this.relate_id,
      }).then((res) => {
          this.closeAssistPopup();
          this.goToSelectPopup = true;
      });
  }

  handleSelectAction() {
      this.goToSelectPopup = false;
  }

  // 预加载图片
  preloadImages(urls) {
      urls.forEach((url) => {
          const img = new Image();
          img.src = url;
          img.onload = () => console.log(`${url} loaded`);
      });
  }

  @CheckAppJump()
  handleAssist(item) {
      Utils.logTrace(
          {
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_PRODUCT_CLICK,
              id: Number(item.goods_id),
              name: item.show_name,
          },
          true,
      );
      if (item.user_seckill && item.user_seckill.order_status == 300) {
          return;
      }
      if (
          item.user_seckill &&
          typeof item.user_seckill.order_status != 'undefined' &&
          item.user_seckill.order_status == 0
      ) {
          this.unPayPopup = true;
          this.order_no = item.user_seckill.order_no;
          return;
      }

      // 判断当前时间是否在00:00:00到9:59:59之间
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentSecond = now.getSeconds();

      // 将当前时间转换为秒数进行比较
      const currentTimeInSeconds = currentHour * 3600 + currentMinute * 60 + currentSecond;
      const startTimeInSeconds = 0; // 00:00:00
      const endTimeInSeconds = 9 * 3600 + 59 * 60 + 59; // 9:59:59

      if (item.stock === 0) {
          if (currentTimeInSeconds >= startTimeInSeconds && currentTimeInSeconds <= endTimeInSeconds) {
              // Utils.Toast('活动尚未开始，请10点后再来吧', 1000, null, 'width: 274rpx');
              this.isActivityNotStartPopup = true;
              // 一元购商品活动未开始强弹窗
              Utils.logTrace({
                  module: Constants.LOG_TRACE_MODULE_DREAME,
                  event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                      .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_NO_START,
              });
              return;
          }
          this.isEmptyPopup = true;
          //  一元购售罄弹窗
          Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                  .LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_POPUP_SELL_OUT,
          });
          // Utils.Toast('今日商品已售罄，请明日10点再来吧！', 1000, null, 'width: 274rpx');
          return;
      }
      if (item.user_seckill.has_seckill) {
          uni.navigateTo({
              url: `/pagesC/oneYuanFlashSale/assistancePage?seckill_id=${item.user_seckill.seckill_id}`,
          });
          return;
      }
      getOneYuanFlashSaleList({
          activity_id: Number(this.activityId),
          gid: item.goods_id,
      }).then((res) => {
          console.log(res, 22992);
          uni.navigateTo({ url: `/pagesC/oneYuanFlashSale/assistancePage?seckill_id=${res.seckill_id}` });
      });
  }

  async onLoad(options: any) {
        this.from = options.from;
      Utils.logTrace({
          module: Constants.LOG_TRACE_MODULE_DREAME,
          event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_EXPOSURE,
      });

      Utils.setPageTitle('追觅好物，限时一元购');
      this.link = options.link;
      this.seckillId = options.relate_id || Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID);
      this.activityId = Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID);
      const { modules, base_info } = await getThemeActivity({ id: Number(this.activityId) });
      if (modules.length > 0) {
          modules.forEach((item) => {
              if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                  this.productList = item.extra;
              }
          });
      }
      // Utils.Toast(JSON.stringify(this.productList), 100000, null, 'color: #111111;font-weight: 500;width: 500rpx;width: 300rpx;white-space:wrap');
      this.baseInfo = base_info;
      this.countdownTime =
          new Date(base_info.end_time * 1000).getTime() - new Date().getTime() > 0
              ? new Date(base_info.end_time * 1000).getTime() - new Date().getTime()
              : 0;
      if (this.baseInfo.end_time * 1000 < new Date().getTime()) {
          this.isEndAcitivity = true;
          this.loading = true;
          return true;
      } else {
          this.isEndAcitivity = false;
          this.loading = true;
      }
      if (options.relate_id) {
          this.inviter_id = options.inviter_id;
          this.relate_id = options.relate_id;
          checkUserCanBeInvited({
              inviter_id: this.inviter_id,
              relate_id: this.relate_id,
              invite_type: 1,
          }).then((res) => {
              if (res.can_invite) {
                  this.showAssistPopup = true;
                  this.inviter_info = res.inviter_info;
                  this.startPopupCountdown();
              }
          });
      }
  }

    async onShow() {
        const { modules, base_info } = await getThemeActivity({
            id: Number(this.activityId),
        });
        if (modules.length > 0) {
            modules.forEach((item) => {
                if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                    this.productList = item.extra;
                }
            });
        }
        this.baseInfo = base_info;
        this.countdownTime =
            new Date(base_info.end_time * 1000).getTime() - new Date().getTime() > 0
                ? new Date(base_info.end_time * 1000).getTime() - new Date().getTime()
                : 0;
        if (this.baseInfo.end_time * 1000 < new Date().getTime()) {
            this.isEndAcitivity = true;
        } else {
            this.isEndAcitivity = false;
        }
        this.loading = true;
        const win: any = window;
        win.onAppPageShow = async () => {
            const { modules, base_info } = await getThemeActivity({
                id: Number(this.activityId),
            });
            if (modules.length > 0) {
                modules.forEach((item) => {
                    if (item.extra && item.extra.length > 0 && item.module_code === 'SNAP_UP') {
                        this.productList = item.extra;
                    }
                });
            }
            this.baseInfo = base_info;
            this.countdownTime =
                new Date(base_info.end_time * 1000).getTime() - new Date().getTime() > 0
                    ? new Date(base_info.end_time * 1000).getTime() - new Date().getTime()
                    : 0;
            if (this.baseInfo.end_time * 1000 < new Date().getTime()) {
                this.isEndAcitivity = true;
            } else {
                this.isEndAcitivity = false;
            }
            this.loading = true;
        };
        setTimeout(() => {
            this.$refs.TimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
        setTimeout(() => {
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
        //  预加载图片
        this.preloadImages(this.preloadImageList);
    }

  onBackPress(options) {
      console.log(options, 222222);
      if (options.from === 'backbutton') {
          Utils.logTrace({
              module: Constants.LOG_TRACE_MODULE_DREAME,
              event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONE_YUAN_GOU_BACK,
          });
      }
  }

  onUnload() {
      // 清理弹窗倒计时定时器
      this.stopPopupCountdown();
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
  }
}
</script>

<style lang="scss" scoped>
.oneYuanFlashSale_container {
  min-height: 100vh;
  background: #ffecde;

  .oneYuanFlashSale_content {
      width: 100%;
      height: 100%;
      width: 100%;
      background-image: linear-gradient(to bottom, transparent 700rpx, #ffecde 700rpx, #ffecde 100%),
          url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68830b3b4df0f3190064370.png');
      background-size: 100% 100%, 100% 700rpx;

      .header {
          height: 108rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left {
              position: fixed;
              padding-left: 38rpx;
              color: #ffffff;
              font-size: 32rpx;
              display: flex;
              align-items: center;
              z-index: 1000;

              .search_back {
                  width: 46rpx;
                  height: 46rpx;
                  margin-right: 26rpx;
              }
          }

          .header_title_img {
              width: 162rpx;
              height: 60rpx;
              position: absolute;
              left: 92rpx;
          }

          .right {
              height: 100%;
              display: flex;
              align-items: center;

              .share {
                  position: absolute;
                  right: 98rpx;
                  height: 46rpx;
                  width: 46rpx;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfd593d30b2510010640.png')
                      no-repeat center center;
                  background-size: 100% 100%;
              }

              .rule {
                  position: absolute;
                  right: 28rpx;
                  height: 46rpx;
                  width: 46rpx;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/688dfca2106690670010343.png')
                      no-repeat center center;
                  background-size: 100% 100%;
              }
          }
      }

      .activity-time-down {
          padding: 20rpx 38rpx;
          width: 500rpx;
          height: 66rpx;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(180deg, #ff8d98 -3%, #ff4e72 26%, #ff265a 62%, #ff294c 112%);
          // background: linear-gradient(
          //     180deg,
          //     rgba(255, 149, 10, 0.225) 0%,
          //     rgba(255, 218, 225, 0.39) 100%,
          //     rgba(255, 47, 90, 0.5) 100%
          // );
          border-radius: 196rpx;
          // margin: 0rpx 16rpx 8rpx 16rpx;

          .countdown-container {
              display: flex;
              align-items: center;
              justify-content: center;

              .countdown-icon {
                  width: 30rpx;
                  height: 30rpx;
                  margin-right: 16rpx;
              }

              .countdown-label {
                  color: #777777;
                  font-size: 28rpx;
                  font-weight: 500;
              }

              .time {
                  color: #ffffff;
                  font-size: 28rpx;
                  font-weight: 500;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .time__item {
                      // margin-right: 2rpx;
                      display: flex;
                      align-items: center;
                      justify-content: center;

                      .time__item_text {
                          font-weight: 500;
                          background: #fff6d9;
                          width: 42rpx;
                          height: 50rpx;
                          border-radius: 6rpx;
                          font-weight: 600;
                          color: #fd1970;
                          font-size: 26rpx;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                      }
                  }
              }
          }
      }

      .activity-time-text {
          color: #c48d65;
          font-size: 20rpx;
          height: 26rpx;
          font-weight: normal;
          margin-bottom: 20rpx;
          text-align: center;
          line-height: 26rpx;
          margin-top: 8rpx;
      }

      .content {
          padding: 0rpx 16rpx 16rpx 16rpx;

          .content_title {
              background: #ffffff;
              border-radius: 40rpx;
              height: 300rpx;
              padding: 24rpx 24rpx 24rpx 24rpx;
              display: flex;

              .content_img_wrap {
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  position: relative;

                  .content_img {
                      width: 282rpx;
                      height: 252rpx;
                      border-radius: 20rpx;
                  }

                  .content_img_icon {
                      position: absolute;
                      top: -20rpx;
                      right: -32rpx;
                      width: 94rpx;
                      height: 68rpx;
                      animation: scale 0.8s infinite alternate;
                      transform-origin: center;

                      @keyframes scale {
                          0% {
                              transform: scale(1);
                          }

                          100% {
                              transform: scale(0.8);
                          }
                      }
                  }

                  .content_img_icon1 {
                      position: absolute;
                      top: 0rpx;
                      left: 0rpx;
                      width: 78rpx;
                      height: 30rpx;
                  }

                  .content_img_icon2 {
                      position: absolute;
                      bottom: 0rpx;
                      left: 0rpx;
                      width: 134rpx;
                      height: 30rpx;

                      image {
                          width: 100%;
                          height: 100%;
                          vertical-align: top;
                      }
                  }
              }

              &:not(:last-child) {
                  margin-bottom: 16rpx;
              }

              .content_img {
                  min-width: 282rpx;
                  width: 282rpx;
                  height: 252rpx;
                  border-radius: 20rpx;
                  overflow: hidden;
              }

              .content_title_text {
                  margin-left: 26rpx;
                  width: calc(100% - 282rpx - 24rpx);
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;

                  .content_title_text_title {
                      margin-top: 28rpx;
                      font-size: 28rpx;
                      color: #000000;
                      font-weight: 500;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      white-space: normal;
                      // width: calc(100% - 48rpx);
                  }

                  .content_title_text_content {
                      position: relative;

                      .content_title_text_content_assist_number {
                          /* NeutralColor中性色/Gray 4 */
                          color: #777777;
                          font-size: 14rpx;
                          font-weight: 500;
                          margin-top: 12rpx;
                          display: flex;
                          align-items: center;
                          gap: 8rpx;

                          .assist-avatars {
                              position: relative;
                              width: 50rpx;
                              height: 30rpx;
                              display: flex;
                              align-items: center;

                              .assist-avatar {
                                  position: absolute;
                                  width: 30rpx;
                                  height: 30rpx;
                                  border-radius: 50%;
                                  overflow: hidden;

                                  .avatar-img {
                                      width: 100%;
                                      height: 100%;
                                      object-fit: cover;
                                  }
                              }
                          }
                      }

                      .content_title_text_grab_text {
                          position: absolute;
                          color: #999999;
                          font-size: 20rpx;
                          margin-right: 16rpx;
                          bottom: 54rpx;
                      }

                      .content_title_text_grab {
                          display: flex;
                          justify-content: space-between;
                          height: 66rpx;
                          margin-top: 12rpx;
                          position: relative;

                          .content_title_text_grab_bg_img {
                              width: 380rpx;
                              height: 44rpx;
                              margin-top: 22rpx;
                              position: relative;

                              .content_title_text_grab_bg_progress_bg {
                                  width: 340rpx;
                                  height: 100%;
                                  // background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png') no-repeat center center;
                                  background-color: #fbf0ef;
                                  border-radius: 32rpx;
                                  background-size: 100% 100%;
                                  display: flex;
                                  align-items: center;

                                  .content_title_text_content_progresss {
                                      overflow: hidden;
                                      width: 140rpx;
                                      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c5846e25b29270011050.png')
                                          no-repeat center center;
                                      background-size: 100% 100%;
                                      border-radius: 200rpx;
                                      margin-left: 18rpx;
                                      height: 24rpx;
                                      display: flex;
                                      align-items: center;
                                      justify-content: space-between;

                                      .progress {
                                          height: 24rpx;
                                          background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c568bbd8f17760012181.png')
                                              no-repeat center center;
                                          background-size: 100% 100%;
                                          border-radius: 200rpx;
                                      }

                                      .progress-text {
                                          font-size: 20rpx;
                                          color: #ff3c3c;
                                          font-weight: 500;
                                      }
                                  }

                                  .content_title_text_grab_text_progress_text {
                                      position: absolute;
                                      bottom: 0rpx;
                                      font-size: 16rpx;
                                      left: 160rpx;
                                      color: #ff0084;
                                      height: 100%;
                                      display: flex;
                                      align-items: center;
                                  }
                              }

                              .content_title_text_grab_text_assist_bg {
                                  width: 340rpx;
                                  height: 100%;
                                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cc71d825e65340010549.png')
                                      no-repeat center center;
                                  background-size: 100% 100%;
                                  display: flex;
                                  align-items: center;
                              }

                              .content_title_text_grab_text {
                                  position: absolute;
                                  bottom: 0rpx;
                                  left: 254rpx;
                                  width: 130rpx;
                                  height: 66rpx;
                                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png')
                                      no-repeat center center;
                                  background-size: 100% 100%;
                              }

                              .normal_buy_active {
                                  position: absolute;
                                  bottom: 0rpx;
                                  left: 250rpx;
                                  width: 130rpx;
                                  height: 66rpx;
                                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e06fbbe9327810010375.png')
                                      no-repeat center center;
                                  background-size: 100% 100%;
                              }

                              .content_title_text_grab_text_assist {
                                  position: absolute;
                                  bottom: 0rpx;
                                  left: 250rpx;
                                  width: 130rpx;
                                  height: 66rpx;
                                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687c8cfd68c794290012295.png')
                                      no-repeat center center;
                                  background-size: 100% 100%;
                                  z-index: 1000;
                              }
                          }
                      }
                  }
              }
          }
      }
  }
}

.oneYuanFlashSale_container_end {
  width: 100%;
  background: #ffecde;
  min-height: 100vh;
  overflow: auto;

  .left {
      position: fixed;
      padding-left: 38rpx;
      color: #ffffff;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      z-index: 1000;
      height: 108rpx;
      display: flex;
      align-items: center;

      .search_back {
          width: 46rpx;
          height: 46rpx;
          margin-right: 26rpx;
      }
  }

  .oneYuanFlashSale_container_end_content {
      width: 100%;
      height: 100%;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e0b7517f2b0980010349.png')
          no-repeat center top;
      height: 904rpx;
      background-size: 100% 100%;
  }

  .oneYuanFlashSale_container_end_content_title {
      font-size: 30rpx;
      color: #606266;
      font-weight: 500;
      text-align: center;
      margin-top: 212rpx;
  }
}

// 测试按钮样式
.test_assist_button {
  background: #ff3c3c;
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 20rpx;
  text-align: center;
  margin: 20rpx auto;
  width: 200rpx;
  font-size: 28rpx;
  font-weight: 500;
}

// 活动规则弹窗样式
.rule-popup-content {
  width: 664rpx;
  height: 824rpx;
  max-height: 824rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb86eabbf07030010744.png')
      no-repeat center center;
  background-size: 100% 100%;
  border-radius: 32rpx;
  overflow: hidden;

  .rule-popup-header {
      padding: 94rpx 36rpx 30rpx;
      position: relative;
      text-align: center;

      .rule-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          position: relative;

          .rule-title-decoration {
              width: 20rpx;
              height: 20rpx;

              &.left {
                  margin-right: 16rpx;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4ce4419022690010318.png')
                      no-repeat center center;
                  background-size: 100% 100%;
                  width: 92rpx;
                  height: 20rpx;
              }

              &.right {
                  margin-left: 16rpx;
                  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687a4d132ca2f1830010546.png')
                      no-repeat center center;
                  background-size: 100% 100%;
                  width: 92rpx;
                  height: 20rpx;
              }
          }

          .rule-title-text {
              font-size: 36rpx;
              font-weight: 600;
              color: #ff0084;
              position: relative;
          }
      }

      .rule-popup-close {
          position: absolute;
          right: 36rpx;
          top: 24rpx;
          width: 46rpx;
          height: 46rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;

          .close-icon {
              width: 46rpx;
              height: 46rpx;
              background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
                  no-repeat center center;
              background-size: 100% 100%;
          }
      }
  }

  .rule-container {
      padding: 0 36rpx 32rpx;

      .rule-content {
          background: #ffffff;
          border-radius: 32rpx;
          padding: 24rpx 14rpx 24rpx 0rpx;

          .rule-content-text {
              padding: 0rpx 24rpx;
              max-height: 572rpx;
              height: 620rpx;
              overflow-y: auto;

              &::-webkit-scrollbar {
                  display: block;
                  width: 8rpx !important;
                  height: 0rpx !important;
                  opacity: 0; // 不生效
                  transition: height 2s;
              }

              &::-webkit-scrollbar-track {
                  border-radius: 10rpx !important;
              }

              &::-webkit-scrollbar-thumb {
                  background: #d8d8d8 !important;
                  border-radius: 10rpx !important;
              }
          }

          .rule-section {
              margin-bottom: 32rpx;
              font-size: 22rpx;
              color: #3d3d3d;
              line-height: 42rpx;

              &:last-child {
                  margin-bottom: 0;
              }

              .rule-section-title {
                  font-size: 32rpx;
                  font-weight: 600;
                  color: #333333;
                  margin-bottom: 16rpx;
                  line-height: 1.4;
              }

              .rule-section-content {
                  .rule-text {
                      font-size: 28rpx;
                      color: #666666;
                      line-height: 1.6;
                      margin-bottom: 12rpx;
                      text-align: justify;

                      &:last-child {
                          margin-bottom: 0;
                      }
                  }
              }
          }
      }
  }
}

::v-deep .is_empty_popup {
  height: 732rpx;
  width: 660rpx;
  border-radius: 40rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6883905d2418e1480012056.png')
      no-repeat center center;
  background-size: 100% 100%;
  position: relative;

  .is_empty_popup_title {
      position: absolute;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688392a852d7b3390012225.png')
          no-repeat center center;
      background-size: 100% 100%;
      width: 596rpx;
      height: 128rpx;
      bottom: 56rpx;
      left: 50%;
      transform: translateX(-50%);
      right: 0;
  }
}
// 售罄弹窗样式
::v-deep .popup_box {
  position: relative;

  .popup_close {
      position: absolute;
      top: 14rpx;
      right: 14rpx;
      width: 56rpx;
      height: 56rpx;
  }
  .popup_go {
      position: absolute;
      bottom: 52rpx;
      left: 30rpx;
      width: 576rpx;
      height: 134rpx;
  }
}

::v-deep .is_activity_not_start {
  height: 732rpx;
  width: 660rpx;
  border-radius: 40rpx;
  background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6883aded8146c5300029363.png')
      no-repeat center center;
  background-size: 100% 100%;

  .is_empty_popup_title {
      position: absolute;
      background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688392a852d7b3390012225.png')
          no-repeat center center;
      background-size: 100% 100%;
      width: 596rpx;
      height: 128rpx;
      bottom: 56rpx;
      left: 50%;
      transform: translateX(-50%);
      right: 0;
  }
}

// 助力弹窗样式
::v-deep .passist_popup {
  width: 664rpx;
  background: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;

  .assist_popup_header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 82rpx 0 0rpx;
      position: relative;

      .assist_popup_header_left {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          left: 30rpx;
      }

      .assist_popup_title {
          font-size: 36rpx;
          color: #ff0084;
          font-weight: 600;
      }

      .assist_popup_header_right {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          right: 30rpx;
      }
  }

  .assist_popup_content {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .assist_popup_user {
          margin-top: 108rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 10rpx;
          border: 2rpx solid #ff4848;
          width: calc(100% - 64rpx);
          height: 186rpx;
          border-radius: 32rpx;

          .assist_popup_avatar {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              object-fit: cover;
          }

          .assist_popup_nickname {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
          }
      }

      .assist_popup_countdown {
          margin-top: 64rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          .assist_popup_countdown_time {
              font-size: 36rpx;
              color: #f53f3f;
              font-weight: 600;
          }

          .assist_popup_countdown_desc {
              font-size: 24rpx;
              color: #666666;
          }
      }

      .assist_popup_button_container {
          height: 218rpx;
          width: 100%;
          display: flex;
          justify-content: center;

          .assist_popup_button {
              background: linear-gradient(90deg, rgba(255, 62, 74, 0) 0%, rgba(255, 118, 216, 0.41) 100%),
                  linear-gradient(180deg, #f33c4b -1%, #f33c4b 18%, #ff4230 103%, #e7322b 129%);
              box-shadow: inset 0px -4px 10px 0px #ffa797;
              border-radius: 792rpx;
              padding: 20rpx 60rpx;
              width: 590rpx;
              height: 122rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              margin-top: 36rpx;

              .assist_popup_button_text {
                  font-size: 32rpx;
                  color: #ffffff;
                  font-weight: 600;
              }
          }
      }
  }
}

// 助力弹窗样式
::v-deep .passist_popup-title {
  width: 664rpx;
  background: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;
  position: relative;

  .rule-popup-close {
      position: absolute;
      right: 36rpx;
      top: 24rpx;
      width: 46rpx;
      height: 46rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .close-icon {
          width: 46rpx;
          height: 46rpx;
          background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687cb4fa3799d2280011644.png')
              no-repeat center center;
          background-size: 100% 100%;
      }
  }

  .assist_popup_header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 82rpx 0 0rpx;
      position: relative;

      .assist_popup_header_left {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          left: 30rpx;
      }

      .assist_popup_title {
          font-size: 36rpx;
          color: #ff0084;
          font-weight: 600;
      }

      .assist_popup_header_right {
          width: 92rpx;
          height: 20rpx;
          position: absolute;
          right: 30rpx;
      }
  }

  .assist_popup_content {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      // padding-bottom: 58rpx;
      align-items: center;
      justify-content: space-between;

      .assist_popup_user {
          margin-top: 108rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 10rpx;
          border: 2rpx solid #ff4848;
          width: calc(100% - 64rpx);
          height: 186rpx;
          border-radius: 32rpx;

          .assist_popup_avatar {
              width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              object-fit: cover;
          }

          .assist_popup_nickname {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
          }
      }

      .assist_popup_countdown {
          margin-top: 64rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          .assist_popup_countdown_time {
              font-size: 36rpx;
              color: #f53f3f;
              font-weight: 600;
          }

          .assist_popup_countdown_desc {
              font-size: 24rpx;
              color: #666666;
          }
      }

      .assist_popup_button_container {
          width: 100%;
          display: flex;
          justify-content: center;
          margin-bottom: 38rpx;

          .assist_popup_button {
              background: linear-gradient(90deg, rgba(255, 62, 74, 0) 0%, rgba(255, 118, 216, 0.41) 100%),
                  linear-gradient(180deg, #f33c4b -1%, #f33c4b 18%, #ff4230 103%, #e7322b 129%);
              box-shadow: inset 0px -4px 10px 0px #ffa797;
              border-radius: 792rpx;
              padding: 20rpx 60rpx;
              width: 590rpx;
              height: 122rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              margin-top: 36rpx;

              .assist_popup_button_text {
                  font-size: 32rpx;
                  color: #ffffff;
                  font-weight: 600;
              }
          }
      }
  }
}
</style>
