<template>
  <view class="item-container">
    <view :class="['item', direction, handleGoodsDisable(good) ? 'disabled' : '']" @click="gotoGdetailPage"
      @touchstart="drawStart" @touchmove="drawMove" @touchend="drawEnd"
      :style="{ transform: `translateX(${-right}rpx)` }">
      <lazy-image v-if="isShowSelect" @click.stop="onSelectItem(index)"
        customStyle="min-width: 40rpx;width: 40rpx;height: 40rpx;margin-right: 16rpx;z-index: 1" :src="good.selected ? require('@/static/check_active.png') : require('@/static/check.png')
          " mode="aspectFit"></lazy-image>
      <text class="tag_image uni-presale-tag" v-if="good.is_presale == 1">预售</text>
      <view class="good-cover" :class="bigCover ? 'big' : ''">
        <!-- #ifdef H5 -->
        <lazy-image :lazyLoad="true" customStyle="width: 154rpx;height: 154rpx;border-radius: 24rpx"
          :src="good.cover_image || good.images" />
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <lazy-image :lazyLoad="true" customStyle="width: 154rpx;height: 154rpx;border-radius: 24rpx"
          :src="good.market_image || good.images" />
        <!-- #endif -->
      </view>
      <view class="good-detail">
        <view>
          <view class="good-name u-line-2">
            <text class="text">{{ good.name }}</text>
          </view>
        </view>
        <view v-if="direction !== 'vertical' && good.spec && good.spec.attr_cnf"
          class="attr u-flex u-col-center u-row-between">
          <view>
            <text class="attr-item" v-for="(item, index) in good.spec.attr_cnf" :key="index">{{
              item.at_val
            }}</text>
          </view>
        </view>
        <view class="num-text" v-if="isShowNum">
          x{{ good.num }}
        </view>
        <!-- 仅购物车页面展示 -->
        <view class="endTime" v-if="good.is_ini == 1 && good.gini_etime > 0 && isShowSelf && !handleGoodsDisable(good)">
          <text class="timeText">距活动结束剩</text>
          <view class="originTime">
            <u-count-down :time="good.gini_etime * 1000 - currentTime" format="DD天 HH:mm:ss"
              @finish="$emit('refreshList')"></u-count-down>
          </view>
        </view>
        <view class="u-flex u-col-center" style="margin-top: 32rpx;">
          <view v-if="good.spec && good.spec['price']" class="good-price">
            <view class="price">
              <view class="sign">¥</view>
              <view class="num">{{ good.spec['price'] }}</view>
              <image class="image" src="https://wpm-cdn.dreame.tech/images/202306/208663-1686300275768.png" />
            </view>
          </view>
          <view v-else class="good-price">
            <slot name="inner">
              <view class="price">
                <view class="sign">¥</view>
                <view class="num">{{ good.is_presale ? good.uprice : (Number(good.price || 0) - Number(good.subsidy_price || 0)).toFixed(2) }}</view>
                <image class="image" src="https://wpm-cdn.dreame.tech/images/202306/208663-1686300275768.png" />
              </view>
              <view class="mprice" v-if="isShowMprice && good.mprice">
                <view class="sign">¥</view>
                <view class="num">{{ good.mprice }}</view>
                <image class="image" src="https://wpm-cdn.dreame.tech/images/202306/208663-1686300275768.png" />
              </view>
            </slot>
          </view>
          <view v-if="isShowNumBox" @click.stop>
            <number-box v-if="good.limit_num > 0" :value.sync="good.num" :min="1" :max="good.limit_num" bgColor="#fff"
              inputWidth="66rpx" buttonWidth="48rpx" buttonSize="48rpx" fontSize="30rpx" color="#333333"
              :disabled="handleGoodsDisable(good)" @blur="valChange($event, good)" @change="valChange($event, good)"
              @overlimit="handleOverLimit">
            </number-box>
            <number-box v-else :value.sync="good.num" :min="1" bgColor="#fff" inputWidth="66rpx" buttonWidth="48rpx"
              buttonSize="48rpx" fontSize="30rpx" color="#333333" :disabled="handleGoodsDisable(good)"
              @blur="valChange($event, good)" @change="valChange($event, good)" @overlimit="handleOverLimit">
            </number-box>
          </view>
          <view class="deposit" v-if="good.is_presale == 1 && isShowdeduction">
            <text class="depositStyle"
              v-if="good.deposit != 0 && good.expand_price != 0 && good.deposit != good.expand_price">定金{{ good.deposit
              }}抵{{
  good.expand_price }}</text>
          </view>
        </view>
      </view>
      <image v-if="isNotSold" class="_image-image" src="@/static/shelves.png"></image>
      <image v-else-if="isSoldOut" class="_image-image" src="@/static/soldout.png"></image>

    </view>
    <view>
      <slot name="footer"></slot>
    </view>
    <view v-if="canDelete" class="delete u-flex u-row-center u-col-center" @click.stop="delData(good)">
      <text>删除</text>
    </view>
  </view>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit } from 'vue-property-decorator';
import NumberBox from '@/components/NumberBox/NumberBox.vue';
import Constants from '@/common/Constants';

@Component({
  components: {
    NumberBox,
  },
})
export default class GoodItem extends Vue {
  @Prop({
    type: Object,
    default: {},
  })
  readonly good;

  @Prop({ type: String, default: 'cart' })
  readonly type;

  @Prop({ type: String, default: 'vertical' })
  readonly direction;

  @Prop({ type: Boolean, default: false })
  readonly isShowSelf;

  @Prop({ type: Boolean, default: false })
  readonly isShowMprice;

  @Prop({ type: Boolean, default: false })
  readonly isShowSelect;

  @Prop({ type: Boolean, default: false })
  readonly isShowNumBox;

  @Prop({ type: Boolean, default: true })
  readonly isShowdeduction;

  @Prop({ type: Number, default: 0 })
  readonly index;

  @Prop({ type: Boolean, default: false })
  readonly canDelete;

  @Prop({ type: Boolean, default: true })
  readonly isShowDetail;

  @Prop({ type: Boolean, default: false })
  readonly bigCover;

  @Prop({ type: Boolean, default: false })
  readonly isShowNum;

  public goodNoneImageUrl: string = ''; // 蒙层图片
  public startX: number = 0;
  public isSoldOut: boolean = false;
  public isNotSold: boolean = false;
  public right: number = 0;
  public delBtnWidth: number = 180;
  public transition: string = 'transition: none 0 ease 0s';
  public currentTime: number = +new Date();

  async valChange(event, item) {
    try {
      this.$emit('valChange', { value: event.value });
    } catch (e) {
      console.error('valChange', e);
    }
  }

  handleChange(event, item) {
    if (this.type !== 'cart') {
      this.$emit('valChange', { value: event.value });
    }
  }

  // 处理无货、已下架 样式状态
  handleGoodsDisable(item) {
    if (
      String(item.status) === Constants.STATUS_SOLD_OUT ||
      String(item.is_del) === Constants.STATUS_DEL
    ) {
      this.isNotSold = true;
      return true;
    } else {
      this.isNotSold = false;
    }
    if (String(item.prod_status) === Constants.PROD_STATUS_NONE_GOODS) {
      this.isSoldOut = true;
      return true;
    } else {
      this.isSoldOut = false;
    }
    return false;
  }

  @Emit('onSelectItem')
  onSelectItem(index) { }

  gotoGdetailPage() {
    // Utils.addCountlyEvent({ key: 'gotoGdetailPage', segmentation: this.good })
    if (!this.isShowDetail || this.isNotSold || this.isSoldOut) return;
    uni.navigateTo({ url: `/pagesB/goodsDetail/goodsDetail?gid=${this.good.gid}&from=goldCoins` });
  }

  // 开始触摸滑动
  drawStart(e) {
    if (!this.canDelete) return;

    const touch = e.touches[0];
    this.startX = touch.clientX;
    this.transition = 'transition: none 0 ease 0s';
  }

  // 触摸滑动
  drawMove(e) {
    if (!this.canDelete) return;
    this.right = 0;
    this.transition = 'transition: none 0 ease 0s';
    const touch = e.touches[0];
    let disX = this.startX - touch.clientX;

    if (disX >= 0) {
      if (disX > this.delBtnWidth) {
        disX = this.delBtnWidth;
      }
      this.right = disX;
    } else {
      this.right = 0;
    }
  }

  // 触摸滑动结束
  drawEnd(e) {
    if (!this.canDelete) return;
    this.transition = 'transition: transform 300ms ease 0s;';
    if (this.right >= this.delBtnWidth / 2) {
      this.right = this.delBtnWidth - 12;
    } else {
      this.right = 0;
    }
  }

  handleOverLimit(type) {
    this.$emit('limitOver', type);
  }

  @Emit('delData')
  delData(good) { }
}
</script>
<style lang="scss" scoped>
.item-container {
  position: relative;
  background-color: $fill-color-bg-white;
  border-radius: 16rpx;
  height: 232rpx;
  margin: 16rpx 32rpx;
  padding: 38rpx 32rpx;

  .tag_image {
    position: absolute;
    left: -32rpx;
    top: -38rpx;
    z-index: inherit;
  }

  .item {
    z-index: 2;
  }

  .good-price {

    .price {
      @include flex(row, center, center, none);
      height: 46rpx;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: $brand-color-btn-text;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      border: 1rpx solid $brand-color-btn-text;
      transform: rotateZ(360deg);

      .sign {
        @include flex(row, center, center, none);

        font-size: 20rpx;
        font-weight: 500;
        margin-left: 16rpx;
        width: 20rpx;
        height: 44rpx;
        text-align: center;
      }

      .num {
        @include flex(row, center, center, none);

        font-size: 28rpx;
        font-family: MiSans-Medium, MiSans;
        font-weight: 500;
        height: 44rpx;
      }

      .image {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .mprice {
      margin-left: 8rpx;
      display: inline-block;
      height: 34rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $text-color-price;
      line-height: 34rpx;
      text-decoration: line-through;
    }
  }

  .deposit {
    margin-left: 16rpx;
    @include flex(row, center, center, none);

    .depositStyle {
      background: $fill-color-bg-white;
      border-radius: 24rpx;
      opacity: 1;
      border: 1rpx solid $uni-border-color-deposit;
      font-size: 20rpx;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: $fill-color-bg-serch;
      padding: 6rpx 16rpx;
      transform: rotateZ(360deg);
    }
  }

  .item {
    width: 100%;
    height: 154rpx;
    background: $text-color-white;
    border-radius: 16rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: relative;

    // .icon {
    //   min-width: 40rpx;
    //   width: 40rpx;
    //   height: 40rpx;
    //   margin-right: 16rpx;
    //   z-index: 1;
    // }

    .good-cover {
      width: 154rpx;
      min-width: 154rpx;
      height: 154rpx;
      margin-right: 16rpx;

      .image {
        width: 100%;
        height: 100%;
        border-radius: 24rpx;
      }

      &.big {
        width: 230rpx;
        min-width: 230rpx;
        height: 230rpx;
      }
    }

    .attr-item {
      color: $text-color-price;
      font-size: 28rpx;
      padding-right: 8rpx;
    }

    .good-detail {
      display: flex;
      flex-direction: column;
      min-height: 150rpx;
      flex: 1;

      .good-name {
        line-height: 38rpx;
        -webkit-text-size-adjust: none !important;

        ._image {
          display: inline-block;
          width: 62rpx;
          height: 38rpx;
          background: $fill-color-bg-serch;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          margin-right: 18rpx;
          color: $fill-color-bg-white;
          font-size: 20rpx;
          text-align: center;
        }

        .text {
          font-size: 28rpx;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: $text-color-primary;
        }
      }
    }
  }

  .delete {
    width: 180rpx;
    height: 100%;
    background-color: $fill-color-bg-serch;
    position: absolute;
    top: 0;
    right: 0rpx;
    font-size: 28rpx;
    font-family: MiSans-Medium, MiSans;
    font-weight: 500;
    color: $text-color-white;
    border-radius: 0rpx 16rpx 16rpx 0rpx;
    z-index: 1;

    .icon_delete {
      height: 80rpx;
      width: 80rpx;
    }
  }

  ._image-image {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 160rpx;
    height: 160rpx;
    z-index: 1;
  }

  .disabled::after {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 24rpx;
    background-color: rgba($color: $fill-color-bg-white, $alpha: 0.7);
  }

  .disabled {
    .icon {
      opacity: 0.7;
    }
  }

  .presaleStyle {
    width: 70rpx;
    height: 38rpx;
    background: $fill-color-bg-serch;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    opacity: 1;
    color: $text-color-white;
    margin-right: 18rpx;
    font-size: 19rpx;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: $text-color-white;
    line-height: 27rpx;
    padding: 8rpx 12rpx;
    text-align: center;
  }

  .postionRelative {
    position: relative;
    top: -6rpx;
  }

  .endTime {
    background-image: url("https://wpm-cdn.dreame.tech/images/202212/527618-1671012081184.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 40rpx;
    width: 100%;
    padding-left: 12rpx;
    line-height: 40rpx;
    display: flex;
    align-items: center;
    margin-top: 5rpx;

    .timeText {
      height: 28rpx;
      font-size: 20rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $text-color-timeTitle;
      line-height: 28rpx;
      margin-right: 8rpx;
    }

    .originTime {
      height: 28rpx;
      font-size: 20rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $text-color-timeTitle !important;
      line-height: 28rpx;

      ::v-deep .u-count-down__text {
        height: 28rpx;
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $text-color-timeTitle !important;
        line-height: 28rpx;
      }
    }

  }

  .num-text {
    text-align: right;
    font-size: 28rpx;
    line-height: 40rpx;
    color: $text-color-price;
  }
}
</style>
