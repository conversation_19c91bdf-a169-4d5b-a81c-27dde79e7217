<template>
    <view
        class="no-device"
        @click="addDevice(0)"
    >
        <!-- <div class="no-device-text-area">
            <div class="no-device-text-title">暂无设备</div>
            <div class="no-device-text-desc">您还未添加绑定追觅产品</div>
        </div> -->
        <!-- <div class="add-device-btns">
            <IconButton
                width="42%"
                leftIcon="https://wpm-cdn.dreame.tech/images/202306/225448-1686107296428.png"
                rightIcon="https://wpm-cdn.dreame.tech/images/202306/243935-1686290915081.png"
                text="手动添加"
                @click-handler="addDevice(0)"
            />
            <IconButton
                width="42%"
                leftIcon="https://wpm-cdn.dreame.tech/images/202306/225448-1686107296428.png"
                rightIcon="https://wpm-cdn.dreame.tech/images/202306/243935-1686290915081.png"
                text="扫码添加"
                @click-handler="addDevice(1)"
            />
        </div> -->
        <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f60747041a4600020679.png" style="width: 22rpx;height:22rpx;"></image>
        <view class="text">添加设备</view>
    </view>
</template>

<script lang="ts">
import Utils from '@/common/Utils';

// import IconButton from './IconButton.vue';

export default {
    name: 'WashMachine',
    components: {
        // IconButton,
    },
    props: {
        currentDevice: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentDeviceStatus: {}, // 当前设备状态
        };
    },
    methods: {
        // 添加设备
        addDevice(type = 0) {
            // type 0: 手动添加, 1: 扫码添加
            if (type === 0) {
                Utils.newMessageChannel('PAGE', 'push', { path: '/product_list' });
            } else {
                Utils.newMessageChannel('PAGE', 'push', { path: '/qr_scan' });
            }
        },
    }
}

</script>

<style scoped lang="scss">
/* .no-device {
    border-radius: 32rpx;
    overflow: hidden;
    background-image: url(./my-device-bg.png);
    background-size: cover;
}
.no-device-text-area {
    padding: 32rpx;
}
.no-device-text-title {
    color: #121212;
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: 500;
}
.no-device-text-desc {
    color: #121212;
    font-size: 24rpx;
    line-height: 32rpx;
    margin-top: 12rpx;
}
.add-device-btns {
    display: flex;
    justify-content: space-between;
    margin-top: 14rpx;
    padding: 24rpx;
} */

.no-device {
    width: 100%;
    border:2rpx dashed #E2E2E2;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 156rpx;
    border-radius: 16rpx;
    .icon{
        width:22rpx;
        height: 22rpx;
    }
    .text{
        color:#404040;
        font-size: 28rpx;
        font-family: MiSans;
        font-weight: normal;
        margin-left: 18rpx;
    }
}
</style>
