<template>
    <div
        class="image-button"
        :style="{ width }"
        @click="onclick"
    >
        <img :src="leftIcon" :alt="text" class="btn-left-image" />
        <div class="btn-text">{{ text }}</div>
        <img :src="rightIcon" :alt="text" class="btn--right-image" />
    </div>
</template>

<script lang="ts">
export default {
    name: 'ImageButton',
    props: {
        width: {
            type: String,
            default: '50%' // 默认宽度
        },
        leftIcon: {
            type: String,
            required: true
        },
        rightIcon: {
            type: String,
            default: ''
        },
        text: {
            type: String,
            default: ''
        },
        clickHandler: {
            type: Function,
            default: () => {}
        }
    },
    methods: {
        onclick() {
            this.$emit('click-handler');
        }
    }
}
</script>

<style scoped>
.image-button {
    display: flex;
    align-items: center;
    padding: 36rpx 24rpx;
    background-color: #FFFFFF;
    border-radius: 32rpx;
}
.btn-left-image {
    width: 48rpx;
    height: 48rpx;
}
.btn--right-image {
    width: 32rpx;
    height: 32rpx;
}
.btn-text {
    font-size: 28rpx;
    line-height: 40rpx;
    font-weight: 500;
    color: #404040;
    margin-left: 18rpx;
    flex: 1;
}
</style>
