import req from './index.js';
import http from '../index'
// 隐私协议信息
export const privaryData = (params) => req.post('api/v1/privacy/list-with-nopage', { ...params });

// 统一隐私协议
export const postAgreePrivary = (params) => req.post('api/v1/privacy-agree-log/create', { ...params });

/**
 * 追觅大使详情
 */
export const employeeInfo = () => req.post('api/v1/user-employee/info');

/**
 * 追觅大使排行榜
 */
export const employeeRank = () => req.post('api/v1/user-employee/rank');

/**
 * 追觅大使邀请注册列表
 */
export const employeeRegisterList = (params) => req.post('api/v1/user-employee/invite-register-list', { ...params });

/**
 * 追觅大使邀请购买列表
 */
export const employeePurchaseList = (params) => req.post('api/v1/user-employee/invite-purchase-list', { ...params });

/**
 * 关闭合伙人弹窗提示接口(即已明确该信息)
 */
export const employeeReceivedTips = (params) => http.post('main/user-employee/received-tips', { ...params })

/**
 * 推荐购买订单列表
 */
export const employeeOrderList = (params) => http.post('main/sales-commission/partner-list', { ...params })

/**
 * 提取现金
 */
export const withdrawMoney = (params) => http.post('main/user-withdraw-record/apply', { ...params })
