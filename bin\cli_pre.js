const args = process.argv.splice(2);
let versionCode, versionName;
try {
    args.forEach((val) => {
        if (val.indexOf('versionCode') > -1) {
            versionCode = val.split('-')[1];
        } else if (val.indexOf('versionName') > -1) {
            versionName = val.split('-')[1];
        }
    });
    if (isNaN(versionCode)) {
        throw 'versionCode error';
    }
} catch (error) {
    console.log(error);
    return;
}
const fs = require('fs');
const Hjson = require('hjson');
const { delFile, mkdirsSync } = require('./util/fsUtil');
const zipper = require('zip-local');
const crypto = require('crypto');
const fsHash = crypto.createHash('md5');
const manifestPath = './src/version.json';
const manifest = fs.readFileSync(manifestPath, 'utf-8');
const manifestInfo = Hjson.parse(manifest);
if (versionCode && versionName) {
    manifestInfo.versionCode = versionCode;
    manifestInfo.versionName = versionName;
    fs.writeFileSync(manifestPath, JSON.stringify(manifestInfo, null, 4));
}
fs.renameSync('./.env.production', './.env.production2');
console.log('---------------------production文件重命名成功!---------------------');
fs.renameSync('./.env.uat', './.env.production');
console.log('---------------------uat文件重命名成功!---------------------');
const execSync = require('child_process').execSync;
const outPath = 'out_pre/';
const outDistPath = `${outPath}dist/`;
const outTmp = `${outPath}tmp.zip`;
delFile('dist/build/h5');
delFile(outPath);
mkdirsSync(outDistPath);
console.log('--------------------开始打包h5--------------------');
execSync('npm run build:h5');
console.log(`--------------------开始移动h5资源到${outDistPath}--------------------"`);
execSync(`mv dist/build/h5/* ${outDistPath}`);
execSync(`cp app_config.json ${outDistPath}`);
execSync(`cp ${manifestPath} ${outDistPath}`);
console.log(`---------------------开始生成压缩包--------------------`);
zipper.sync.zip(outDistPath).compress().save(outTmp);
const stream = fs.readFileSync(outTmp);
fsHash.update(stream);
const md5 = fsHash.digest('hex');
console.log(`---------------------重命名压缩包--------------------`);
fs.renameSync(outTmp, `out_pre/${manifestInfo.appid}.zip`);
console.log(`---------------------生成md5文件--------------------`);
const md5Info = {
    versionCode: manifestInfo.versionCode,
    md5,
};
fs.writeFileSync(`${outPath}md5.json`, JSON.stringify(md5Info));
fs.renameSync('./.env.production', './.env.uat');
console.log('---------------------production文件重命名成功!---------------------');
fs.renameSync('./.env.production2', './.env.production');
console.log('---------------------production2文件重命名成功!---------------------');

console.log(`****************** 打包成功 ******************`);
