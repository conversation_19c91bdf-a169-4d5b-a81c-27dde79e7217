<template>
  <view class="search-container" :style="{ paddingTop: `${statusBarHeight}rpx` }">
      <!-- #ifdef MP-WEIXIN -->
      <CustomBar :title="title" background="#fff"></CustomBar>
      <!-- #endif -->
      <view class="search-area">
          <view class="serach_heard">
              <!-- #ifdef H5 -->
              <view class="search_return" @click="searchBack">
                  <img src="https://wpm-cdn.dreame.tech/images/202302/508756-1676371936507.png" alt="" />
              </view>
              <!-- #endif -->
              <view class="search_inputs">
                  <view class="search_border" :style="{ borderBottom: `2rpx solid ${borderBottomLine}` }">
                      <view class="search_input">
                          <!-- #ifdef MP-WEIXIN -->
                          <img
                              src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png"
                              class="input-icon"
                          />
                          <!-- #endif -->
                          <input
                              ref="input"
                              class="input"
                              type="text"
                              v-model.trim="keyword"
                              :focus="focus"
                              :maxlength="50"
                              :placeholder="placeholder"
                              confirm-type="search"
                              @input="handleInputSearch"
                              @confirm="handleSearchClick"
                              @keydown.enter="handleSearchClick"
                              placeholder-style="font-size: 28rpx;color: #777777;"
                          />
                      </view>
                      <image
                          :lazyLoad="true"
                          class="search_clear"
                          v-show="keyword.length > 0"
                          @click="clearInput"
                          src="https://wpm-cdn.dreame.tech/images/202306/446725-1686362627223.png"
                      />
                  </view>
              </view>
              <!-- #ifdef MP-WEIXIN -->
              <view class="search_cancel" @click="cancelClick">取消</view>
              <!-- #endif -->
              <!-- #ifdef H5 -->
              <view class="search_box" @click="handleSearchClick">搜索</view>
              <!-- #endif -->
          </view>
      </view>

      <view class="result-tabs" v-if="searchType === 2">
          <view
              v-for="(item, index) in resultTabList"
              :key="item.value"
              :class="{ active: index === resultTabIndex }"
              @click="handleResultTabClick(index)"
          >
              {{ item.label }}
          </view>
      </view>

      <!-- 历史搜索 -->
      <SearchHistory
          ref="SearchHistory"
          v-if="searchType === 1 && searchList.length === 0 && searchThinkList.length === 0"
          style="margin-top: 70rpx"
          @onSearch="handleHistorySearch"
      />

      <block v-if="hasSearchResult">
          <scroll-view
              v-if="searchList.length > 0 || searchThinkList.length > 0"
              scroll-y
              class="search-result"
              :style="{ height: `calc(100vh - ${scrollHeight}rpx)` }"
              lower-threshold="100"
              @scrolltolower="onreachBottom"
          >
              <!-- 联想列表 -->
              <view v-show="searchType === 1" :style="{ paddingTop: '8rpx' }">
                  <block v-for="(item, index) in searchThinkList" :key="item.content_id">
                      <view class="search-item" @click="handleJump(item, index)">
                          <!-- #ifdef MP-WEIXIN -->
                          <view class="search-item-text">
                              <rich-text class="u-line-1" :nodes="item.titleContent"></rich-text>
                          </view>
                          <!-- #endif -->
                          <!-- #ifdef H5 -->
                          <view class="search-item-text u-line-1" v-html="item.titleContent"></view>
                          <!-- #endif -->
                          <img src="https://wpm-cdn.dreame.tech/images/202411/072542-1732003571132.png" alt="" />
                      </view>
                  </block>
                  <view class="no-more" v-if="isLoaded && searchThinkList.length > 0">没有更多了~</view>
              </view>

              <!-- 结果列表 -->
              <view class="page-box" v-show="searchType === 2" v-if="searchList && searchList.length > 0">
                  <view class="my-waterfall" v-if="resultTabIndex === 0">
                      <my-Waterfall
                          :showView="false"
                          :finished="isLoaded"
                          v-model="searchList"
                          v-if="searchList"
                          @change="handleJump"
                          ref="uWaterfall"
                          paddingBottom="70rpx"
                      ></my-Waterfall>
                  </view>

                  <view v-else :style="{ paddingTop: '10rpx' }">
                      <UserRelationItem
                          v-for="item in searchList"
                          :r="item"
                          :key="item"
                          @load="handleSearchClick()"
                      />
                      <view class="no-more" v-if="isLoaded && searchList.length > 0">没有更多了~</view>
                  </view>
              </view>
          </scroll-view>

          <view v-if="searchList.length == 0 && searchType === 2" style="padding-top: 138rpx; padding-bottom: 200rpx">
              <u-empty
                  text="未找到相关结果"
                  icon="https://wpm-cdn.dreame.tech/images/202306/308776-1686360313376.png"
                  textSize="28rpx"
                  textColor="#777777"
                  width="508rpx"
                  height="508rpx"
              >
              </u-empty>
          </view>
      </block>

      <custom-toast ref="customToast" />
      <!-- #ifdef MP-WEIXIN -->
      <privacy />
      <!-- #endif -->
  </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import SearchHistory from './components/SearchHistory.vue';
import GoodItem from './components/GoodItem/GoodItem.vue';
import CustomSearch from '@/components/CustomSearch/CustomSearch.vue';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import { createSearchHistory, getContentListNew, getSearchUser } from '@/http/requestGo/community';
import MyWaterfall from '@/pages/contents/components/MyWaterfall.vue';
import { UserModule } from '@/store/modules/user';
import UserRelationItem from '../components/UserRelationItem/index.vue';

@Component({
  components: {
      SearchHistory,
      GoodItem,
      CustomSearch,
      MyWaterfall,
      UserRelationItem,
  },
})
export default class Search extends Vue {
  public title: string = '搜索';
  public bgColor: string = 'transparent'; // 导航栏背景
  public isLoaded: boolean = false;
  public keyword: string = '';
  public placeholder: string = '请输入要搜索的关键词';
  // 是否展示搜索列表
  public hasSearchResult: boolean = false;
  public focus: boolean = true;
  public list = [];

  public borderBottomLine: string = '#121212';
  // 查询结果tab
  public resultTabIndex: number = 0;
  public lastContentId: string = '';

  /** 1-联想列表搜索 2-结果列表搜索 */
  public searchType = 1;
  public isSearching = false;

  public page_size = 20;
  public contentPage = 1;
  public userPage = 1;
  public searchList: Array<any> = [];
  public searchThinkList: Array<any> = [];

  resultTabList = [
      {
          label: '全部',
          value: 0,
      },
      {
          label: '用户',
          value: 1,
      },
  ];

  get statusBarHeight(): number {
      let statusBarHeight;
      // #ifdef H5
      statusBarHeight = AppModule.statusBarHeight;
      // #endif
      // #ifdef MP-WEIXIN
      statusBarHeight = 0;
      // #endif
      return statusBarHeight;
  }

  get scrollHeight() {
      let result;
      // #ifdef H5
      result = AppModule.statusBarHeight + 88 + 100;
      // #endif
      // #ifdef MP-WEIXIN
      result = 370;
      // #endif
      return result;
  }

  get pagePaddingTop(): number {
      return AppModule.pagePaddingTop;
  }

  // 是否授权
  get wxAuth(): boolean {
      return UserModule.wxAuth;
  }

  // 是否授权手机号
  get isPhone(): boolean {
      return UserModule.isPhone;
  }

  $refs!: {
      input;
      SearchHistory: SearchHistory;
  };

  onLoad(options) {
      // #ifdef H5
      if (options.type === 'user') {
          this.resultTabIndex = 1
          this.placeholder = '搜索用户名字'
      }
      setTimeout(() => {
          this.$refs.input._focus();
      }, 2000);
      // #endif
  }

  onShow() {
      if (!this.keyword) {
          return;
      }
      this.initSearchStatus();
      this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
  }

  goBack() {
      Utils.goBack();
  }

  onBackPress(event) {
      if (event.from === 'backbutton') {
          Utils.goBack();
          return true;
      }
      return false;
  }

  handleHistorySearch(val) {
      this.keyword = val;
      this.searchType = 2;
      this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
  }

  clearInput() {
      this.keyword = '';
      this.searchType = 1;
      this.hasSearchResult = false;
      this.initSearchStatus();

      // #ifdef H5
      this.$refs.input._focus();
      // #endif
  }

  cancelClick() {
      return Utils.goBack();
  }

  searchBack() {
      return Utils.goBack();
  }

  handleJump(item, index) {
      // #ifdef H5
      if (item.jump_url) {
          Utils.navigateTo(`/pages/webView/webView?web_url=${encodeURIComponent(item.jump_url)}&title=${item.title}`);
          return;
      }
      // #endif
      if (this.resultTabIndex === 0) {
          const { content_id, type } = item;
          uni.navigateTo({
              url:
                  type === 1
                      ? `/pagesC/contentDetails/contentDetails?content_id=${content_id}`
                      : `/pagesC/contentVideoDetails/contentVideoDetails?content_id=${content_id}`,
          });
      } else {
          if (this.searchList[index]) {
              Utils.jumpPersonHome(this.searchList[index].user_id);
          } else {
              Utils.jumpPersonHome(item.user_id);
          }
      }
  }

  handleResultTabClick(index) {
      this.hasSearchResult = false;
      this.resultTabIndex = index;
      this.initSearchStatus();

      this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
  }

  // 每次搜索或者切换tab都需要重置的状态
  initSearchStatus() {
      this.contentPage = 1;
      this.userPage = 1;
      this.searchList = [];
      this.searchThinkList = [];
      this.lastContentId = '';
      this.isLoaded = false;
  }

  getHighlightedText(keyword, item, replaceStr = '<div style="display: inline-block; color: #AB8C5E;">$1</div>') {
      const regex = new RegExp(`(${keyword})`, 'i');
      return item.replace(regex, replaceStr);
  }

  updateSearchResult(res) {
      this.hasSearchResult = true;
      this.lastContentId = res.data.last_content_id;

      const result = res.data.list.map((item) => {
          const titleContent = this.getHighlightedText(this.keyword, item.title || item.nick_name);
          return { ...item, titleContent };
      });

      if (result.length < this.page_size) {
          this.isLoaded = true;
      }

      if (this.searchType === 1) {
          this.searchThinkList = [...this.searchThinkList, ...result];
      } else {
          // #ifdef MP-WEIXIN
          if (this.wxAuth && this.isPhone) {
              createSearchHistory({ keyword: this.keyword });
          }
          // #endif

          // #ifdef H5
          createSearchHistory({ keyword: this.keyword });
          // #endif
          this.searchList = [...this.searchList, ...result];
      }
  }

  handleContentSearch() {
      getContentListNew({
          keyword: this.keyword,
          page: this.contentPage++,
          page_size: this.page_size,
          search_type: this.searchType,
          last_content_id: this.lastContentId,
      })
          .then((res) => this.updateSearchResult(res))
          .finally(() => {
              this.isSearching = false;
          });
  }

  handleUserSearch() {
      getSearchUser({
          keyword: this.keyword,
          page: this.userPage++,
          page_size: this.page_size,
          search_type: this.searchType,
      })
          .then((res) => this.updateSearchResult(res))
          .finally(() => {
              this.isSearching = false;
          });
  }

  // input输入触发
  handleInputSearch() {
      this.searchType = 1;
      this.initSearchStatus();

      uni.$u.debounce(() => {
          // 键盘搜索优先级高，键盘搜索触发时，input输入不执行搜索回调
          if (this.isSearching) return;
          this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
      }, 500);
  }

  // 键盘搜索触发
  handleSearchClick() {
      if (this.keyword === '') {
          this.hasSearchResult = false;
          return;
      }

      this.isSearching = true;
      this.searchType = 2;
      this.initSearchStatus();
      this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
  }

  onreachBottom() {
      if (this.isLoaded) {
          return;
      }
      this.resultTabIndex === 0 ? this.handleContentSearch() : this.handleUserSearch();
  }
}
</script>
<style lang="scss" scoped>
@import './search.scss';
::v-deep .u-modal {
  .u-modal__content__text {
      text-align: center;
      font-family: PingFang SC;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      letter-spacing: 0px;
      color: $text-color-primary;
  }
}
</style>
