<template>
    <view class="point-swiper-container" :style="{ backgroundImage: `url(${bgUrl})` }">
        <view class="point-swiper-wrap" @click="handleJump">
            <view class="point-info-wrap">
                <view class="point-info-title">
                    <text class="point-info-title-text">积分价值</text>
                    <text class="point-info-value-text-value">{{ pointValue }}</text>
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';

@Component({})
export default class PointSwiper extends Vue {
    @Prop({ type: Array, default: [] })
    readonly points!: Array<any>;

    @Prop({ type: String, default: '' })
    readonly bgUrl!: string;

    @Prop({ type: Number, default: 0 })
    readonly deduction_rate!: number;

    @Prop({ type: String, default: '' })
    readonly jumpUrl!: string;

    @Prop({ type: String, default: '' })
    readonly type!: 'fiveHalf' | 'oneYun';

    get pointValue() {
        return (Number(this.points) / Number(this.deduction_rate)).toFixed(2);
    }

    // 五折购
    handleJump() {
        console.log(this.jumpUrl);
        Utils.navigateTo(this.jumpUrl);
    }
}
</script>
<style lang="scss" scoped>
.point-swiper-container {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
    // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888c7bd2d9d11870015529.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .point-swiper-wrap {
        width: 100%;
        height: 100%;
        .point-info-wrap {
            width: 100%;
            height: 100%;
            .point-info-title {
                position: absolute;
                bottom: 24rpx;
                left: 24rpx;
                font-size: 24rpx;
                color: #FF3385;
            }
            .point-info-title-text {
                color: #FF3385;
            }
            .point-info-value-text-value {
                color: #FFF562;
                margin-left: 20rpx;
            }
        }
    }
}
</style>
