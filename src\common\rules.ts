export const exploreBookingRules = {
    name: [
        {
            required: true,
            message: '请输入联系人姓名',
            trigger: 'blur',
        },
        {
            min: 1,
            max: 50,
            message: '联系人长度在1到50个字符',
            trigger: ['change', 'blur'],
        },
        // {
        //     pattern: /^[a-zA-Z\u4e00-\u9fa5]+$/,
        //     message: '请勿输入特殊字符',
        //     trigger: 'blur',
        // },
        // {
        //     // 此为同步验证，可以直接返回true或者false，如果是异步验证，稍微不同，见下方说明
        //     validator: (rule, value, callback) => {
        //         const regex: string = '^[a-z0-9A-Z\u4e00-\u9fa5]+$';
        //         console.log('1111', value.matches(regex));

        //         return value.matches(regex);
        //     },
        //     message: '联系人填写只能是中文或英文',
        //     // 触发器可以同时用blur和change，二者之间用英文逗号隔开
        //     trigger: ['change', 'blur'],
        // },
    ],
    phone: [
        {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
        },
        {
            validator: (rule, value, callback) => {
                return uni.$u.test.mobile(value);
            },
            message: '请输入正确的手机号',
            trigger: ['blur'],
        },
    ],
    verify_code: [
        {
            required: true,
            message: '请输入验证码',
            trigger: ['blur'],
        },
        {
            message: '验证码只能为数字',
            trigger: ['blur'],
        },
    ],
    area: [
        {
            required: true,
            message: '请选择联系地区',
            trigger: 'change',
        },
    ],
    detail: [
        {
            required: true,
            message: '请填写详细地区',
            trigger: 'blur',
        },
        {
            min: 1,
            max: 100,
            message: '详细地址长度在1到100个字符',
            trigger: ['change', 'blur'],
        },
    ],
    expect_time: [
        {
            required: true,
            message: '请选择勘测日期',
            trigger: 'change',
        },
    ],
    context_type: [
        {
            required: true,
            message: '请选择家庭环境',
            trigger: 'change',
        },
    ],
    is_buy: [
        {
            required: true,
            message: '请选择是否已购买追觅产品',
            trigger: 'change',
        },
    ],
    sn: [
        {
            required: true,
            message: '请输入SN编码',
            trigger: 'blur',
        },
        {
            min: 13,
            message: '请输入正确的追觅产品SN编码',
            trigger: ['blur'],
        },
    ],
    sn_config_str: [
        {
            required: true,
            message: '请选择产品信息',
            trigger: 'change',
        },
    ],
    is_explore: [
        {
            required: true,
            message: '请选择是否上门勘测',
            trigger: 'change',
        },
    ],
};

export const exploreCancelRules = {
    sn: [
        {
            required: true,
            message: '请输入SN编码',
            trigger: 'blur',
        },
        {
            min: 13,
            message: '请输入正确的追觅产品SN编码',
            trigger: ['blur'],
        },
    ],
};
