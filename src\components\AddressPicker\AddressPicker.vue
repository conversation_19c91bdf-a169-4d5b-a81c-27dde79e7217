<template>
    <view>
        <u-picker :show="show" ref="uPicker" :columns="columns" :immediateChange="true" @confirm="confirm" @cancel="cancel" @close="cancel"
            @change="changeHandler" :closeOnClickOverlay="isOverlayClose" keyName="label" :cancelColor="cancelColor"
            :confirmColor="confirmColor"></u-picker>
    </view>
</template>

<script lang="ts">
import { Vue, Component, Emit, Prop } from 'vue-property-decorator';
import UPicker from './picker.vue';
import Utils from '@/common/Utils';
import { getAdminDivision } from '@/http/address';

@Component({
    components: {
        UPicker,
    },
})

export default class AddressPicker extends Vue {
    $refs!: {
        uPicker
    };

    @Prop({ type: Boolean, default: false })
    readonly isAsync

    @Prop({ type: Boolean, default: false })
    readonly isOverlayClose;

    @Prop({ type: String, default: '' })
    readonly cancelColor;

    @Prop({ type: String, default: '' })
    readonly confirmColor;

    public show: boolean = false;
    protected province: Array<any> = [];
    protected city: Array<any> = [];
    protected area: Array<any> = [];
    public columns = [];

    protected isLoading = false;
    protected waitOpen = false;
    protected isError = false;

    async changeHandler(e) {
        const { columnIndex, value, picker = this.$refs.uPicker } = e;
        console.log(columnIndex, value, picker);

        if (columnIndex === 0) {
            if (this.isAsync) {
                const pId = value[0].id;
                const city = await this.getAdminDivision({ event: 'CITY', province_id: pId })
                const area = await this.getAdminDivision({ event: 'COUNTY', city_id: city[0].id })
                picker.setColumnValues(1, city);
                picker.setColumnValues(2, area);
            } else {
                const pIndex = value[0].index;
                const city = this.mapIdAndIndex(this.city[pIndex]);
                const area = this.mapIdAndIndex(this.area[pIndex][0]);
                picker.setColumnValues(1, city);
                picker.setColumnValues(2, area);
            }
        }
        if (columnIndex === 1) {
            if (this.isAsync) {
                const cId = value[1].id;
                const area = await this.getAdminDivision({ event: 'COUNTY', city_id: cId })
                picker.setColumnValues(2, area);
            } else {
                const pIndex = value[0].index;
                const cIndex = value[1].index;
                const area = this.mapIdAndIndex(this.area[pIndex][cIndex]);
                picker.setColumnValues(2, area);
            }
        }
    }

    created() {
        this.init();
    }

    open() {
        if (this.isLoading || this.isError) {
            // 如果之前加载发生错误, 则需重新加载地址列表
            this.isError && this.init();
            uni.showLoading({ title: '加载中' });
            // 标记等待状态, 在获取地址列表后展示弹窗
            this.waitOpen = true;
            return;
        }
        this.show = true;
    }

    mapIdAndIndex(arr) {
        return arr.map((item, i) => ({ ...item, index: i, id: item.value }));
    }

    async getAdminDivision(params) {
        const region = await getAdminDivision(params)
        return region.map(item => ({
            ...item,
            label: item.new_name,
            id: item.$id,
            value: item.$id
        }))
    }

    async init() {
        this.isLoading = true;
        try {
            if (this.isAsync) {
                const province = await this.getAdminDivision({ event: 'PROVINCE' })
                const city = await this.getAdminDivision({ event: 'CITY', province_id: province[0].id })
                const area = await this.getAdminDivision({ event: 'COUNTY', city_id: city[0].id })

                this.columns = [province, city, area]
            } else {
                const { p, c, a } = await Utils.getAddress();
                this.province = p;
                this.city = c;
                this.area = a;

                // 初始化省市区
                const province = this.mapIdAndIndex(this.province);
                const city = this.mapIdAndIndex(this.city[0]);
                const area = this.mapIdAndIndex(this.area[0][0]);
                this.columns = [province, city, area];
            }

            this.isError = false;
        } catch (error) {
            this.isError = true;
            Utils.Toast('获取地址列表失败, 请重试');
        }
        this.isLoading = false;
        uni.hideLoading();
        if (this.waitOpen) {
            // 等待状态下自动打开弹窗
            this.waitOpen = false;
            this.show = true;
        }
    }

    getAllAddress() { }

    @Emit('confirm')
    confirm(e) {
        this.show = false;
        return e;
    }

    @Emit('cancel')
    cancel(e) {
        this.show = false;
        return e;
    }
}
</script>

<style lang="scss">
::v-deep .u-popup__content {
    border-radius: 24rpx 24rpx 0 0;
}

::v-deep .u-toolbar {
    margin-top: 54rpx;
}

::v-deep .u-picker__view__column__item {
    background: $fill-color-bg-gray;
}

::v-deep .u-toolbar__cancel__wrapper {
    font-size: 32rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 42rpx;
}
</style>
