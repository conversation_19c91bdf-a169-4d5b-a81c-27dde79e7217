export default {
    props: {
        // 是否展示modal
        show: {
            type: Boolean,
            default: uni.$u.props.modal.show,
        },
        openType: {
            type: String,
            default: '',
        },
        // 标题
        title: {
            type: [String],
            default: uni.$u.props.modal.title,
        },
        titleStyle: {
            type: String,
            default: '',
        },
        backgroundColor: {
            type: String,
            default: uni.$u.props.modal.backgroundColor,
        },
        showClose: {
            type: Boolean,
            default: false,
        },
        // tipArr: {
        //     type: Array,
        //     default: [],
        // },
        // 弹窗内容
        content: {
            type: String,
            default: uni.$u.props.modal.content,
        },
        // 弹窗内容
        contentStyle: {
            type: String,
            default: '',
        },
        // 确认文案
        confirmText: {
            type: String,
            default: uni.$u.props.modal.confirmText,
        },
        // 取消文案
        cancelText: {
            type: String,
            default: uni.$u.props.modal.cancelText,
        },
        // 确认文案
        confirmStyle: {
            type: String | Object,
            default: '',
        },
        // 取消文案
        cancelStyle: {
            type: String | Object,
            default: '',
        },
        // 是否显示取消按钮
        showCancelButton: {
            type: Boolean,
            default: uni.$u.props.modal.showCancelButton,
        },
        // 是否显示取消按钮
        showConfirmButton: {
            type: Boolean,
            default: true,
        },
        customModalPad: {
            type: Boolean,
            default: false,
        },
        customModalStyle: {
            type: Boolean,
            default: false,
        },
        // 对调确认和取消的位置
        buttonSize: {
            type: String,
            default: '',
        },
        // 按钮尺寸
        buttonReverse: {
            type: Boolean,
            default: uni.$u.props.modal.buttonReverse,
        },
        // 是否开启缩放效果
        zoom: {
            type: Boolean,
            default: uni.$u.props.modal.zoom,
        },
        // 是否异步关闭，只对确定按钮有效
        asyncClose: {
            type: Boolean,
            default: uni.$u.props.modal.asyncClose,
        },
        // 是否允许点击遮罩关闭modal
        closeOnClickOverlay: {
            type: Boolean,
            default: uni.$u.props.modal.closeOnClickOverlay,
        },
        // 给一个负的margin-top，往上偏移，避免和键盘重合的情况
        negativeTop: {
            type: [String, Number],
            default: uni.$u.props.modal.negativeTop,
        },
        // modal宽度，不支持百分比，可以数值，px，rpx单位
        width: {
            type: [String, Number],
            default: '616rpx',
        },
        height: {
            type: [String, Number],
            default: 'auto',
        },
        backdropFilter: {
            type: [String, Number],
            default: '0',
        },
        closeable: {
            type: Boolean,
            default: uni.$u.props.modal.closeable,
        },
        boxShadow: {
            type: String,
            default: '',
        },
    },
};
