// 填充颜色渐变
$fill-color-bglg: linear-gradient(90deg, rgba(0, 0, 0, 0.09) 0%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg2: linear-gradient(133deg, #fff2cb 0%, #eedec1 98%);
$fill-color-bglg3: linear-gradient(270deg, #ffffff 55%, rgba(255, 255, 255, 0) 100%);
$fill-color-bglg4: linear-gradient(243deg, #ee6b00 -9%, #f6a253 97%);
// 文字颜色
$text-color-more: #7d7d7d;
$top: 58rpx;
$bottm: 60rpx;
$total: $bottm + $top;

// 定义CSS变量用于指示条动态移动
:root {
  --indicator-progress: 0;
}

// 优化商品卡片性能
.goods-card {
  will-change: auto; // 移除will-change，减少性能开销
  transition: none; // 移除过渡动画，减少滑动时的跳动
  backface-visibility: hidden;
  transform: translateZ(0);
  overflow: hidden; // 防止内容溢出
}

// 优化swiper性能
.swiper-container {
  transform: translateZ(0);
  will-change: auto; // 移除will-change，减少性能开销
  -webkit-overflow-scrolling: touch; // iOS滚动优化
  backface-visibility: hidden; // 防止3D变换导致的闪烁
}

.swiper-slide {
  transform: translateZ(0);
  will-change: auto; // 移除will-change，减少性能开销
  -webkit-overflow-scrolling: touch;
  backface-visibility: hidden;
  overflow: hidden; // 防止内容溢出
}

.swiper-item {
  will-change: auto; // 移除will-change，减少性能开销
  transform: translateZ(0);
  backface-visibility: hidden;
  overflow: hidden;
}

.scroll-view {
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// 优化tab切换性能
.tab-item {
  transform: translateZ(0);
  will-change: transform, opacity;
  transition: all 0.2s ease-out;
  backface-visibility: hidden;
}

.tab-item-active {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

@keyframes slide {
  0% {
    width: 0;
    opacity: 0;
    /* 初始时完全透明 */
  }

  1% {
    opacity: 1;
    /* 快速淡入 */
  }

  100% {
    width: 50%;
    /* 最终宽度 */
  }
}

.container {
  -webkit-overflow-scrolling: touch;
  height: 100vh;
  // background: linear-gradient(180deg, #FFFEFD 4%, rgba(255, 253, 248, 0) 98%);
  // background: linear-gradient(180deg, #F3EBDD 19%, #F5F5F6 32%);
  background: #FFFFFF !important;
  position: relative;

  .header {
    .search {
      margin: 8rpx 32rpx 16rpx 32rpx;
    }

    // border-top: 2rpx solid $line-color-devider;
  }



  .tab {
    -webkit-overflow-scrolling: touch;
    // background-color: $fill-color-bg-white;
    // padding-bottom: 20rpx;
    padding: 20rpx 32rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    // position: absolute;
    left: 0;
    right: 0;
    z-index: 9990;
    background-color: #fff;
    // transform: translateY(-100%);
    transition: all 0.5s ease-in-out;
    // margin-bottom: 30rpx;

    .tab-category {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-shrink: 0;
      padding-left: 24rpx;

      img {
        width: 32rpx;
        height: 32rpx;
      }

      span {
        font-size: 32rpx;
        line-height: 40px;
        color: #121212;
      }
    }

    .tab-view {
      -webkit-overflow-scrolling: touch;
      width: 100%;
      // height: 100rpx;
      overflow-x: scroll;
      align-items: flex-start;

      ::v-deep .uni-scroll-view-content {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-items: center;
      }

      .tab-item {
        // margin-top: 18rpx;
        margin-right: 32rpx;
        flex-shrink: 0;
        font-size: 32rpx;
        color: $text-color-primary;
        // line-height: 38rpx;
        position: relative;
        transition: all 0.1s ease-in-out;
        color: #404040;
        align-items: flex-start;
        display: flex;
        flex-direction: column;


        // &:first-child {
        //     padding-left: 24rpx;
        // }

        // &:last-child {
        //     padding-right: 24rpx;
        // }
        .line {
          width: 60%;
          height: 4rpx;
          margin-top: 16rpx;
          position: absolute;
          left: 0;
          bottom: 0;
          overflow: hidden;
          margin: 0 20%;
          // background-color: #121212;
          // width: 100%;
          // height: 4rpx;
          // // background-color: #121212;
          // margin-top: 16rpx;
          // overflow: hidden; /* 隐藏超出部分 */
          // position: relative; /* 定位父元素 */
        }

        .name {
          margin-bottom: 20rpx;
        }

        .icon {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 58rpx;
          height: 58rpx;
          background-image: url('https://wpm-cdn.dreame.tech/images/202309/64f2dc819ef5d6511594332.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -8rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 48rpx;
          height: 4rpx;
          background: $text-color-primary;
          transition: all 0.3s ease;
          opacity: 0;
        }
      }

      .tab-item-active {
        font-weight: 500;
        font-size: 36rpx;
        position: relative;
        color: #121212;

        .line {
          // width: 0;
          // height: 4rpx;
          // background: #DBC49A;
          // margin-top: 16rpx;
          // position: absolute;
          // left: 0;
          // bottom: 0;
          width: 60%;
          height: 4rpx;
          margin-top: 16rpx;
          position: absolute;
          left: 0;
          bottom: 0;
          overflow: hidden;
          margin: 0 20%;
          background-color: #DBC49A;
          // animation: slideCenter 0.5s cubic-bezier(0.25, 0.1, 0.5, 1) forwards; /* 应用动画 */
          animation: slide 0.4s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
          /* 使用缓动函数使动画更平滑 */
          transform-origin: 20% 80%;
          /* 确保变换从左侧开始 */
        }

        // &::after {
        //     content: '';
        //     position: absolute;
        //     bottom: -25rpx;
        //     left: 50%;
        //     transform: translateX(-50%);
        //     width: 75rpx;
        //     height: 4rpx;
        //     border-radius: 99px;
        //     // background: $text-color-primary;
        //     background: #DBC49A;
        //     transition: all 0.3s ease;
        //     opacity: 1;
        // }
      }
    }
  }

  .tab-filter {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: white;
    padding: 0rpx 32rpx;
    // padding-top: 20rpx;
    padding-bottom: 20rpx;

    &__item {
      min-width: 172rpx;
      height: 72rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 158rpx;
      background: #F6F6F6;
      font-size: 28rpx;
      color: #404040;
      margin-right: 20rpx;

      &.active {
        background: #E8DEC1;
        color: #8C6533;
      }
    }
  }

  .main {
    flex: 1;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;

    .menu-wrap {
      flex: 1;
      height: 100%;
      overflow: hidden;
      -webkit-overflow-scrolling: touch;

      .tab-view {
        width: 185rpx;
        height: 100%;
        background-color: $fill-color-bg-gray;
        // padding-bottom: 128rpx;

        .tab-item {
          height: 100rpx;
          box-sizing: border-box;
          font-size: 28rpx;
          color: $text-color-primary;
          line-height: 38rpx;
          position: relative;
          margin-bottom: 16rpx;
          transition: color 0.3s ease, border-color 0.3s ease;

          .name {
            z-index: 1;
            position: absolute;
            top: 32rpx;
          }

          .line {
            height: 1px;
          }
        }

        .tab-item-active {
          height: 128rpx;
          box-sizing: border-box;
          font-size: 28rpx;
          font-weight: 500;
          line-height: 38rpx;
          color: $fill-color-primary-active;
          background: $fill-color-bglg;
          margin-bottom: 16rpx;
        }

        .tab-item-active::before {
          position: absolute;
          content: '';
          display: block;
          bottom: 28rpx;
          left: 50%;
          transform: translateX(-50%);
          height: 81rpx;
          width: 81rpx;
          z-index: 1;
          background-image: url('https://wpm-cdn.dreame.tech/images/202306/622515-1686643884105.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .tab-item-ls {
          letter-spacing: 8rpx;
        }
      }

      .right-box {
        width: 100%;
        flex: 1;
        height: 100%;
        background-color: #f5f5f5;

        // #ifdef H5
        padding-bottom: env(safe-area-inset-bottom);
        // #endif
        -webkit-overflow-scrolling: touch;

        .page-view-new {
          margin-top: 16rpx;
        }

        .page-view {
          @include flex(row, space-between, flex-start, wrap);
          // padding: 8rpx 8rpx 0;
          padding: 16rpx 16rpx 0;

          @media screen and (min-width: 450px) {
            padding: 16rpx 18rpx 0;
            padding-bottom: 18rpx;
          }

          position: relative;
          -webkit-overflow-scrolling: touch;

          .class-item {
            // width: 360rpx;
            width: calc(calc(100vw - 48rpx)/2);
            // width: 50%;
            // background: #ffffff;
            border-radius: 24rpx;
            margin-bottom: 16rpx;
            overflow: hidden;

            .class-item-top {
              width: 100%;
              padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
              margin: 0 auto 36rpx;
              position: relative;
              // background-color: #f5f5f5; // 添加默认背景色
              overflow: hidden; // 防止内容溢出
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689c89cccd4838410028672.png');

              // 简化占位动画，减少性能开销
              .ysIcon {
                width: 70rpx;
                height: 30rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed073f1b089900015618.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;

              }

              .htIcon {
                width: 70rpx;
                height: 30rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed073f24cb9920013215.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
              }

              .newIcon {
                width: 70rpx;
                height: 30rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed078ade457120011517.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
              }

              .msIcon {
                width: 70rpx;
                height: 30rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/689ed078adbc07120064166.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
              }

              .tag {
                position: absolute;
                right: 36rpx;
                top: 44rpx;
                z-index: 9;
              }

              .yushouImg {
                position: absolute;
                top: 0;
                left: -2prx;
                width: 150rpx;
                height: 70rpx;
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6baf24410011380.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                z-index: 99;
              }

              .hotProImg {
                position: absolute;
                top: 0;
                left: -2prx;
                width: 150rpx;
                height: 70rpx;
                // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d36c841d8200010937.png');
                background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6888a95a6adab4380010532.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                z-index: 99;
              }

              .newProImg {
                position: absolute;
                top: 0;
                left: -2prx;
                z-index: 99;
                width: 150rpx;
                height: 70rpx;
                // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68879d33c15417920012131.png');


                background-repeat: no-repeat;
                background-size: 100% 100%;
              }

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                // background: #f0f0f0; // 使用纯色替代渐变动画
                border-radius: 8rpx 8rpx 0 0;
                z-index: 1;
                pointer-events: none; // 防止干扰点击事件
              }

              // 图片加载完成后隐藏占位
              &.loaded::before {
                display: none;
              }

              // LazyImage容器样式
              .lazy-image-container {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
                z-index: 2;
                background-color: #f5f5f5;
                overflow: hidden; // 防止图片溢出

                // 简化占位动画，减少滑动时的性能开销
                &::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: #f0f0f0; // 使用纯色替代动画
                  border-radius: 8rpx 8rpx 0 0;
                  z-index: 1;
                  pointer-events: none;
                }

                // 加载完成后隐藏占位
                &.loaded::before {
                  display: none;
                }

                // 加载失败时的样式
                &.error::before {
                  background: #e0e0e0; // 简化错误样式
                }

                // 确保图片在占位层之上，并优化性能
                ::v-deep image,
                ::v-deep img {
                  position: absolute;
                  top: 0;
                  right: 0;
                  bottom: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  z-index: 2;
                  transition: none; // 移除过渡动画，减少滑动时的跳动
                  object-fit: cover; // 确保图片填充整个容器
                  backface-visibility: hidden; // 防止3D变换导致的闪烁
                  transform: translateZ(0); // 启用硬件加速
                  will-change: auto; // 移除will-change，减少性能开销
                }
              }

              .custom_tag {
                position: absolute;
                top: 0;
                right: 0;
                width: 126rpx;
                height: 58rpx;
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687791bc05b370230010836.png') no-repeat;
                background-size: cover;
                border-radius: 0 8rpx 0 0;
                z-index: 99;
              }

              &.extend {
                padding-bottom: $total;

                img {
                  top: $top;
                  bottom: $bottm;
                  height: calc(100% - $total);
                }

                ::v-deep image {
                  top: $top;
                  bottom: $bottm;
                  height: calc(100% - $total);
                }
              }

              .atmosphereImg {
                position: absolute;
                top: 0;
                right: 0;
                bottom: -2rpx;
                left: 0;
                z-index: 10;
                border-radius: 8rpx 8rpx 0 0;
                background-repeat: no-repeat;
                background-size: 100% 100%;
              }

              img {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2; // 确保在占位层之上
                // background-color: #fff;
              }

              ::v-deep image {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 8rpx 8rpx 0 0;
                z-index: 2; // 确保在占位层之上
              }
            }

            .class-item-bottom {
              padding: 0 32rpx 12rpx;

              .name {
                position: relative;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 26rpx;
                color: #20252b;
                line-height: 32rpx;
                min-height: 74rpx;
                text-align: left;

                .titleW {
                  line-height: 40rpx;
                }

                .tag {
                  position: absolute;
                  left: 0;
                }

                .brand {
                  width: 52rpx;
                  height: 26rpx;
                  border-radius: 4px;
                  background-color: #000000;
                  color: #B59A6E;
                  text-align: center;
                  padding: 6rpx 8rpx;

                }

                .strictly-selected {
                  width: 52rpx;
                  height: 26rpx;
                  border-radius: 4px;
                  background: linear-gradient(121deg, #318AF7 13%, #28B0EE 100%, #2997E3 100%);

                  color: #fff;
                  text-align: center;
                  padding: 6rpx 8rpx;
                }

                .ecology {
                  width: 52rpx;
                  height: 26rpx;
                  border-radius: 4px;
                  background-color: #FFF4EF;
                  color: #FB3019;
                  text-align: center;
                  padding: 6rpx 8rpx;
                }
              }

              .couponText {
                box-sizing: content-box;
                width: 100rpx;
                // height: 36rpx;
                margin: 16rpx 0 10rpx;
                padding: 6rpx 0rpx 6rpx 6rpx;
                background: linear-gradient(89deg,
                    rgba(252, 46, 46, 0.09) 0%,
                    rgba(228, 43, 43, 0) 100%);
                border-radius: 4rpx 4rpx 4rpx 4rpx;
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 20rpx;
                color: #ee3c2f;
                line-height: 24rpx;
              }

              .price {
                display: flex;
                align-items: baseline;
                margin-top: 16rpx;
                font-family: MiSans;
                width: 100%;
                color: #FF3654;
                font-size: 28rpx;
                height: 52rpx;
                // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bb5646edc54540011575.png');
                // background-color: #fbf0ef;
                border-radius: 32rpx;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                position: relative;

                .qiangIcon {
                  width: 90rpx;
                  height: 42rpx;
                  background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/68a851686939c4310026277.png');
                  background-repeat: no-repeat;
                  background-size: 100% 100%;
                }

                .priceBox {
                  color: #a6a6a6;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  width: 100%;
                  height: 66rpx;
                  // display: flex;
                  // align-items: baseline;
                  // justify-content: flex-start;
                  // flex-direction: row;

                }

                // padding-left: 24rpx;
                .content_title_text_grab_text {
                  // background-image: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687bbbb12e9d21910010654.png');
                  // background-size: 100% 100%;
                  // background-repeat: no-repeat;
                  // width: 126rpx;
                  // height: 52rpx;
                  // line-height: 52rpx;
                  // // position: absolute;
                  // // bottom: 0rpx;
                  // // // top: -10rpx;
                  // // right: 0;
                  // background: linear-gradient(270deg, #FF1C7C 5%, #FF4242 100%);
                  // color: #fff;
                  // text-align: center;
                  // border-radius: 160rpx;
                  // font-weight: bold;
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  width: 126rpx;
                  color: #909399;
                  text-align: center;
                  // border-radius: 160rpx;
                  // font-weight: bold;
                  font-size: 20rpx;
                }

                .unit {
                  height: 24rpx;
                  font-weight: 500;
                  font-size: 24rpx;
                  color: #FF3654;
                  line-height: 52rpx;
                }

                .optimizePrice {
                  height: 52rpx;
                  font-weight: 600;
                  font-size: 32rpx;
                  color: #FF3654;
                  line-height: 52rpx;
                }

                .originPrice {
                  font-family: MiSans;
                  font-size: 22rpx;
                  font-weight: normal;
                  line-height: normal;
                  /* NeutralColor中性色/Gray 5 */
                  color: #A6A6A6;
                  margin-left: 8rpx;
                  text-decoration: line-through;
                }

                .dot {
                  height: 32rpx;
                  font-family: MiSans, MiSans;
                  font-weight: 500;
                  font-size: 24rpx;
                  color: #404040;
                  line-height: 32rpx;
                }

                .primaryPrice {
                  height: 32rpx;
                  font-weight: 400;
                  font-size: 24rpx;
                  color: #a6a6a6;
                  line-height: 32rpx;
                  text-decoration-line: line-through;
                }

                .coupon-text {
                  margin-left: 4rpx;
                  font-family: MiSans, MiSans;
                  font-weight: 400;
                  font-size: 20rpx;
                  color: #ee3c2f;
                  line-height: 24rpx;
                }

                .price_skeleton {
                  display: flex;

                  ::v-deep .u-skeleton {
                    flex: 0 0 auto;

                    &:nth-child(1) {
                      margin-right: 16rpx;
                    }
                  }
                }
              }
            }

            // &:nth-child(even) {
            //     transform: translateX(8rpx);
            // }
          }

          /* 媒体查询适应折叠屏 */
          @media screen and (min-width: 450px) {
            .class-item {
              width: calc(calc(100vw - 32rpx - 36rpx)/4);

              // &:nth-child(even) {
              //     transform: none;
              // }

              // &:not(:nth-child(4n+1)) {
              //     margin-left: 12rpx;
              // }
            }
          }

          .list-title {
            position: fixed;
            top: 34rpx;
            right: 0;
            left: 0;
            z-index: 100;
            background: #ffffff00;

            .title-img {
              position: absolute;
              top: 0rpx;
              right: 26rpx;
              z-index: 100;
              // width: 100rpx;
              height: 38rpx;
              display: flex;
              align-items: center;
              font-family: MiSans, MiSans;
              font-weight: 400;
              font-size: 28rpx;
              color: #404040;
              line-height: 38rpx;
              text-align: left;

              // background: $fill-color-bglg3;
              .arrowTransform {
                transition: 0.2s;
                transform-origin: center;
              }

              .arrowTransformReturn {
                transition: 0.2s;
                transform-origin: center;
                transform: rotate(180deg);
              }
            }

            .drop-down {
              position: relative;
              top: -38rpx;
              max-height: calc(100% - 94rpx);
              overflow: hidden;
              overflow-y: auto;

              // border-radius: 0px 0px 23rpx 23rpx;
              background: #ffffff;
              transition: all 0.3s ease-in-out;

              .down-box {
                height: 88rpx;
                padding: 0 16rpx;
                font-family: MiSans, MiSans;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 88rpx;
                color: $text-color-regular;

                &.active {
                  position: relative;
                  font-weight: 400;
                  color: #8c6533;
                  text-transform: none;
                  background: rgba(241, 223, 200, 0.27);
                  border-radius: 4rpx 4rpx 4rpx 4rpx;

                  &::after {
                    content: '';
                    position: absolute;
                    right: 26rpx;
                    top: 24rpx;
                    width: 40rpx;
                    height: 40rpx;
                    background-image: url('https://wpm-cdn.dreame.tech/images/202411/672c6a81546803460056226.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                  }
                }
              }
            }
          }

          .group-list {
            position: relative;
            top: 0;
            z-index: 10;
            overflow-y: auto;
            height: 100%;
            // #ifdef MP-WEIXIN
            padding-bottom: 86rpx;
            // #endif
            // #ifdef H5
            padding-bottom: calc(86rpx);
            // #endif
            -webkit-overflow-scrolling: touch;

            .item {
              .item-title {
                position: sticky;
                top: -1rpx;
                z-index: 10;
                height: 92rpx;
                padding: 0 24rpx;
                font-family: MiSans, MiSans;
                font-weight: 500;
                font-size: 32rpx;
                color: #404040;
                line-height: 40rpx;
                background: #f5f5f5;
                display: flex;
                align-items: center;
              }

              .item-content {
                @include flex(row, flex-start, flex-start, wrap);
                position: relative;
                -webkit-overflow-scrolling: touch;
                padding: 0rpx 8rpx 0;

                .item-content-block {
                  width: calc(50% - 4rpx);
                  overflow: hidden;
                  margin-bottom: 8rpx;
                  border-radius: 8rpx 8rpx 8rpx 8rpx;
                  background: #ffffff;

                  .class-content-top {
                    width: 100%;
                    padding-top: 113%; // 图片尺寸 1170*1320 高宽比 1.13
                    overflow: hidden;
                    margin: 0 auto 36rpx;
                    position: relative;

                    img {
                      position: absolute;
                      top: 0;
                      right: 0;
                      bottom: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                    }

                    ::v-deep image {
                      position: absolute;
                      top: 0;
                      right: 0;
                      bottom: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                      border-radius: 8rpx 8rpx 0 0;
                    }
                  }

                  .class-content-bottom {
                    padding: 0rpx 38rpx 46rpx;

                    .name {
                      height: 64rpx;
                      font-family: MiSans, MiSans;
                      font-weight: 500;
                      font-size: 26rpx;
                      line-height: 32rpx;
                      text-align: left;
                      color: #20252b;
                    }
                  }

                  // &:nth-child(even) {
                  //     transform: translateX(8rpx);
                  // }
                }

                @media screen and (min-width: 450px) {
                  .item-content-block {
                    width: calc(25% - 10rpx);

                    // &:nth-child(even) {
                    //     transform: none;
                    // }

                    // &:not(:nth-child(4n+1)) {
                    //     margin-left: 12rpx;
                    // }
                  }
                }
              }

              .more {
                @include flex(row, center, center, none);
                margin-top: 32rpx;

                .text {
                  font-size: 24rpx;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: $text-color-more;
                  line-height: 46rpx;
                }

                .downArrow {
                  width: 32rpx;
                  height: 32rpx;
                  background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
                  background-repeat: no-repeat;
                  background-size: 100% 100%;
                }

                .upArrow {
                  width: 32rpx;
                  height: 32rpx;
                  background-image: url('https://wpm-cdn.dreame.tech/images/202303/880696-1679558059359.png');
                  background-repeat: no-repeat;
                  background-size: 100% 100%;
                  transform: rotate(180deg);
                }
              }
            }
          }

          .epmty {
            width: 100%;
            display: flex;
            align-items: center;
            flex-direction: column;
            margin-bottom: 8rpx;
            margin-top: 258rpx;

            .text {
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              font-size: 28rpx;
              color: #777777;
              line-height: 38rpx;
            }
          }
        }

        .no-more {
          // margin: 46rpx auto;
          // color: $text-color-secondary;
          // line-height: 100rpx;
          margin: 48rpx 0;
          color: #A6A6A6;
          line-height: 32rpx;
          font-size: 24rpx;
          text-align: center;
        }
      }
    }

    .reg {
      width: 100%;
      position: fixed;
      left: 0;
      bottom: 0;
      height: 86rpx;
      box-sizing: content-box;
      @include flex(row, space-between, center, none);
      background: $fill-color-bglg2;
      // #ifdef H5
      padding-bottom: env(safe-area-inset-bottom);
      // #endif
      z-index: 10;

      .regLeft {
        font-size: 28rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        margin-left: 32rpx;
        color: $brand-color-btn-text;
        line-height: 38rpx;
      }

      .regRight {
        width: 132rpx;
        height: 54rpx;
        background: $text-color-primary;
        border-radius: 235rpx;
        margin-right: 32rpx;
        font-size: 22rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: $text-color-white;
        line-height: 54rpx;
        text-align: center;
      }
    }
  }
}

.flex-container {
  display: flex;
}

.column {
  width: 25%;
}

.left-column {
  padding: 16rpx 0rpx 8rpx 0rpx;
}

.right-column {
  width: 50%;
  padding: 0rpx 16rpx;
}

.skeleton-container {
  padding: 16rpx 0rpx 8rpx 0rpx;
}

.recommend-container {
  background: transparent !important;
  // background: linear-gradient(180deg, #FFFFFF 47%, rgba(255, 255, 255, 0) 100%);
  overflow: hidden;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;

  .recommend-container-slot-wrap {
    border-radius: 40rpx 40rpx 0 0;
    background: #FFFFFF;
  }

}