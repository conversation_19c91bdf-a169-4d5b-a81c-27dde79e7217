<template>
    <view class="container">
        <APPSHARE :link="link"></APPSHARE>
        <view class="header">
            <CustomBar
                v-if="$isInApp()"
                title=""
                :background="controlBgc ? '#ffffff' : 'transparent'"
                :bottomHeight="76"
                titleColor="#333333"
                backIcon="https://wpm-cdn.dreame.tech/images/202308/64eed6d5c3e5c8023980118.png"
            >
                <template>
                    <view class="imageWrap">
                        <view>
                            <image
                                class="cityIcon"
                                src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68849dbdb34f67340012114.png"
                            >
                            </image>
                        </view>
                        <view class="points-num">
                            <view class="points-num-level" @click="handlePointsClick">
                                <MemberLevel @click="handlePointsClick"></MemberLevel>
                                <!-- <image :src="levelIcon" style="width: 64rpx; height: 64rpx"></image>
                            <text class="points-num-level-text">{{ levelInfo.name }}</text> -->
                            </view>
                            <view class="pointWrap" @click="handlePointDetailsClick">
                                <image
                                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6880785584c495440011529.png"
                                    style="width: 48rpx; height: 48rpx; margin-left: 24rpx"
                                ></image>
                                <!-- <text @click="handlePointDetailsClick" class="points-num-text">{{ points }}</text> -->
                            </view>
                            <view class="animate-amount">
                                <view class="digit-container" v-for="(digit, index) in animatedDigits" :key="index">
                                    <!-- 小数点特殊处理 -->
                                    <view v-if="digit.isDecimal" class="decimal-point">{{ digit.digit }}</view>
                                    <!-- 数字滚动动画 -->
                                    <view
                                        v-else
                                        class="digit-scroll"
                                        :style="{
                                            transform: `translateY(-${digit.offset}%)`,
                                            transitionDelay: `${digit.delay}s`,
                                        }"
                                    >
                                        <view class="digit-item" v-for="num in 10" :key="num"
                                            >{{ (num - 1) % 10 }}
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <!-- <text class="points-price-text" @click="handlePointDetailsClick">{{ points }}</text> -->
                        </view>
                    </view>
                </template>
            </CustomBar>
        </view>
        <view class="bannerWrap"  :style="{ 'margin-top': $isInApp() ? '0' : '96rpx' }">
            <!-- <image src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687f7490aae217000012133.png" class="banner"></image> -->
            <!-- 邀请banner -->
            <view class="invite_banner">
                <view class="invite_btn" @click="inviteNewUserTask"></view>
                <view v-if="$isInApp()" class="invite_rules_and_share u-flex" @click.stop>
                    <view class="left_icon u-flex-1" @click="showShareDialog"></view>
                    <view class="right_icon u-flex-1" @click="inviteRules"></view>
                </view>
                <!-- <view class="invite_rules" @click="inviteRules"> 活动规则 </view> -->
                <view class="invite_rules_num">
                    {{ pointsPrice }}
                </view>
            </view>
        </view>
        <view class="points-mall">
            <view class="swiper-content">
                <view v-if="waresList[0] && waresList[0].length > 0">
                    <view class="content" ref="swiperContent">
                        <!-- 使用左右布局的积分商品组件 -->
                        <pointProductsHorizontal
                            v-for="item in waresList[0]"
                            :key="'horizontal-' + item.id"
                            :pointProductsInfo="item"
                        />
                         <!-- <pointProducts v-for="item in waresList[0]" :key="item.id" :pointProductsInfo="item" /> -->
                    </view>
                    <view class="no-more" v-if="isLoaded && waresList[0] && waresList[0].length > 0"> 到底了~ </view>
                </view>
                <u-empty
                    v-else
                    text="没有满足条件的结果"
                    icon="https://wpm-cdn.dreame.tech/images/202306/308776-1686360313376.png"
                    textSize="28rpx"
                    textColor="#777777"
                    marginTop="148rpx"
                    width="508rpx"
                    height="508rpx"
                >
                </u-empty>
            </view>
        </view>
        <custom-toast ref="customToast" />
        <PointDialog
            v-if="showDialog"
            @closeDialog="closeDialog"
            :title="'您的好友已登录，恭喜入账'"
            :point="countTotal"
            :type="'default'"
        />
        <PointDialog
            v-if="showConsumeDialog"
            @closeDialog="closeConsumeDialog"
            :title="'您的好友已购物，恭喜入账'"
            :point="ConsumeCount"
            :type="'default'"
        />
        <PromotionDialog v-if="showPromotionDialog" @closeDialog="closePromotionDialog" />
        <!-- #ifdef MP-WEIXIN -->
        <WxLogin ref="wxLogin" @success="loginSuccess"></WxLogin>
        <privacy />
        <!-- #endif -->

        <!-- 全屏gif动效 -->

        <view class="gif-animation-overlay" v-show="showGifAnimation && $isInApp()">
            <view class="gif-animation-container">
                <img
                    class="gif-animation-image"
                    src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/6884a3d9228ff1420018772.gif"
                />
            </view>
        </view>
        <gold-coin-timeout-tips task-code="pointsShopping" ref="TimeoutTips" />
        <EarnMoneySpendTimeoutTips
            ref="EarnMoneySpendTimeoutTips"
            :position="{ top: '360rpx' }"
            :task-code="'pointsShopping'"
            :fromPage="fromPage"
            :watchTime="15000"
        />
        <!-- 分享按钮 -->
         <!-- <view class="share_btn" @click="showShareDialog" v-if="$isInApp()">
            <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891e49653b3f3430011436.png" alt=""/>
         </view> -->
         <share-dialog :show.sync="show" :productInfo="productInfo" @share="handleShare" :shareType="'active'" />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getScoreCollectAll, getScoreCollect, getScoreUncollect } from '@/http/vip';
import { VIPModule, MineBasicInfo } from '@/store/modules/vip';
import WxLogin from '@/components/WxLogin/WxLogin.vue';
import pointProducts from '@/components/pointProducts/pointProducts.vue';
import pointProductsHorizontal from '@/components/pointProducts/pointProductsHorizontal.vue';
import { AppModule } from '@/store/modules/app';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';
// import { goodsList } from '@/http/goods';
import { GoodsModule } from '@/store/modules/goods';
import MemberLevel from './MemberLevel.vue';
import { UserModule } from '@/store/modules/user';
import { joinInvite } from '@/http/oneYuanFlashSalePage';
import PointDialog from './components/PointDialog.vue';
import { getInvitePoint, enterPointMall, getWaresList } from '@/http/pointsMall';
import pointItem from './components/pointItem.vue';
import PromotionDialog from './components/PromotionDialog.vue';
import GoldCoinTimeoutTips from '@/components/GoldCoinTimeoutTips/GoldCoinTimeoutTips.vue';
import ShareDialog from '@/components/shareDialog/shareDialog.vue';
import { CheckAppJump } from '@/common/decorators';
import EarnMoneySpendTimeoutTips from '@/components/EarnMoneySpendTimeouTips/EarnMoneySpendTimeouTips.vue';

@Component({
    components: {
        pointProducts,
        pointProductsHorizontal,
        WxLogin,
        MemberLevel,
        PointDialog,
        pointItem,
        PromotionDialog,
        GoldCoinTimeoutTips,
        ShareDialog,
        EarnMoneySpendTimeoutTips,
    },
})
export default class Point extends Vue {
    $refs!: {
        TimeoutTips;
        EarnMoneySpendTimeoutTips;
    };

    public performList: Array<any> = [];
    public points_show: Boolean = false;
    public eventName: String = '';
    // 推广弹窗
    public showPromotionDialog: boolean = false;
    public show: boolean = false;
    public productInfo: any = {};

    public numValue: String = '';
    public pointList: Array<any> = [];
    public urlMap: Object = {
        v1: 'https://wpm-cdn.dreame.tech/images/202309/64f7ed9f950066104938716.png',
        v2: 'https://wpm-cdn.dreame.tech/images/202309/64f7ee289d6406449272579.png',
        v3: 'https://wpm-cdn.dreame.tech/images/202309/64f7ee881f3981279272578.png',
        v4: 'https://wpm-cdn.dreame.tech/images/202309/64f7f115d61548769272522.png',
        v5: 'https://wpm-cdn.dreame.tech/images/202309/64f7f19a940ed6064894855.png',
    };

    public levelInfo: any = null;

    public current: Number = 0;
    public current2: Number = 0;
    public activeTabs: Array<any> = [];
    public swiperCurrent: number = 0;
    public titleStatus: Array<any> = [];
    public pointProductList: Array<any> = [];
    public CloneSortList: Array<any> = [];
    public waresList: Array<any> = [];
    public sortName: String = '价格/积分排序'; // 排序名称
    public selectedSort: any = {}; // 排序点击确定的时候是否有选中的值

    public exchangeList: Array<any> = [{ name: '仅积分' }, { name: '我能兑换' }];

    public sortList: Array<any> = [
        { name: '价格从高到低', selected: false },
        { name: '价格从低到高', selected: false },
        { name: '积分从高到低', selected: false },
        { name: '积分从低到高', selected: false },
    ];

    public isAnimating: boolean = false; // 动画状态
    public activeTab: number = 0;
    public tebTop: number = 0;
    public controlBgc: boolean = false; // 控制CustomBar背景颜色
    public controlSticky: boolean = false; // 控制sticky吸顶背景颜色
    public dropDownShow: boolean = false; // 控制价格或积分排序展示
    public tab: number = 0; // 1：新品
    public is_point: number = 0; // 1:仅积分 0：不是仅积分
    public is_my: number = 0; // 1:我能兑换 0：所有
    public sort_rule: any = {}; // {'type':'price','act':'up'} type: price,价格 point,积分 act:排序，up升序 down降序
    public page: number = 1; // 当前页，默认1
    public page_size: number = 10; // 页数，默认20
    public resetSelected: boolean = false; // 控制重置按钮字体颜色
    public isLoaded: boolean = false;
    public isRender: boolean = false;
    public isScrollLocked: boolean = false;
    public res: any = {};
    public scrollTopHeight: number = 0; // = 滑动定位高估
    public windowHeight: number = 0; // 页面高度
    public diff: number = 100; // 触底阀值
    public currentPageHeight: number = 0; // 总元素高度
    public currentTab: string = 'tab-0'; // 总元素高度
    public allHeight: number = 0; // 全部时候高度
    public currentItem: any = { tid: '-1' };
    public shareUrl: string = '';
    public showDialog: boolean = false;
    public countTotal: number = 0;
    public showConsumeDialog: boolean = false;
    public ConsumeCount: number = 0;
    public showAddPointDialog: boolean = false;
    public showGifAnimation: boolean = false; // 控制gif动效显示
    public gifImageLoaded: boolean = false; // gif图片加载状态
    public link: string = '';
    public fromPage: string = '';

    get amount(): Number {
        return VIPModule.basicInfo.totalPoints || 0;
    }

    get pointsPrice(): Number | string {
        // @ts-ignore
        return (Number(VIPModule.basicInfo.totalPoints) / Number(100)).toFixed(2) || 0;
    }

    get animatedDigits() {
        const amountStr = this.amount.toString();
        const digits = amountStr.split('').map((digit, index) => {
            // 检查是否为小数点
            if (digit === '.') {
                return {
                    digit: '.',
                    offset: 0,
                    index: index,
                    delay: 0,
                    isDecimal: true,
                };
            }

            const num = parseInt(digit);
            // 计算每个数字的偏移量，每个数字占10%的高度
            const offset = this.isAnimating ? num * 10 : 0;
            // 所有数字同时滚动，延迟时间设为0
            const delay = 0;
            return {
                digit: num,
                offset: offset,
                index: index,
                delay: delay,
                isDecimal: false,
            };
        });
        return digits;
    }

    get mineBasic(): MineBasicInfo {
        return VIPModule.basicInfo;
    }

    get totalPoints(): Number {
        return VIPModule.basicInfo.totalPoints || 0;
    }

    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    get user_id(): number | string {
        return UserModule.user_id;
    }

    get contentHeight(): number {
        const windowHeight = Utils.pxToRpx(this.windowHeight);
        const top = this.pagePaddingTop;
        return windowHeight - (top - 4);
    }

    get isFoldableDevice() {
        console.log(Utils.isFoldableDevice());
        return Utils.isFoldableDevice();
    }

    async getScoreCollectAll() {
        await getScoreCollectAll({});
    }

    async getScoreCollect(id) {
        await getScoreCollect(id);
    }

    async getScoreUncollect() {
        try {
            const _ScoreUncollect = await getScoreUncollect({});
            this.performList = _ScoreUncollect;
            if (this.performList.length > 0) {
                this.points_show = true;
            }
            this.getScrollTopHeight();
        } catch (error) {}
    }

    showShareDialog() {
        // 获取随机商品
        const randomIndex = Math.floor(Math.random() * this.waresList[0].length);
        const item = this.waresList[0][randomIndex];
            this.productInfo = {
                name: item.name,
                image: item.cover_image,
                price: item.price,
                desc: item.introduce,
                imageBg: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202508/6891e83bc1d3e7940011182.png',
                priceColor: '#FF1F0E',
                descColor: '#C59245',
            }
        this.show = true;
    }

    // 获取积分商品枚举内容
    async getWaresPointsEnum() {
        try {
            await GoodsModule.asyncGetTagList();
            this.titleStatus = GoodsModule.tagList.filter((tag) => {
                return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            });
            this.titleStatus.unshift({
                name: '推荐',
                tid: '-1',
                type: -2,
                tag_img: 'https://wpm-cdn.dreame.tech/images/202309/64f17ee38419b5411224913.png',
            });
            // console.log(_tagList)
            // this.seftBrandLists = GoodsModule.tagList.filter((tag) => {
            //     return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            // });
            // this.listData = [];
            // _tagList.unshift(...this.firstTabLists);
            // this.tabsList = _tagList.filter((tag) => {
            //     this.listData.push({ list: [], pages: 1, finished: false, page: 1 });
            //     return Number(tag.tid) > 3 && Number(tag.tid) !== 20;
            // });
            // this.titleStatus = await getWaresPointsEnum({ keyword: 'tab' });
            // this.titleStatus.unshift({ name: '全部', value: 0 });
        } catch (error) {
            console.error('getWaresPointsEnum', error);
        }
    }

    inviteRules() {
        uni.navigateTo({
            url: '/pagesA/point/pointRules',
        });
    }

    closeConsumeDialog() {
        this.showConsumeDialog = false;
    }

    closePromotionDialog() {
        this.showPromotionDialog = false;
    }

    // 调用进入积分商城接口
    callEnterPointMall() {
        enterPointMall().then((res) => {
            if (res > 0) {
                this.showGifAnimation = true; // 显示gif动效
                // 3秒后关闭gif动效
                setTimeout(() => {
                    this.showGifAnimation = false;
                    VIPModule.getBasicInfo();
                }, 3000);
            }
        });
    }

    @CheckAppJump()
    inviteNewUserTask() {
        uni.navigateTo({
            url: '/pagesA/missionCenter/missionCenter',
        });
    }

    public taskCode: string = '';

    onLoad(options) {
        this.fromPage = options.from || '';
        this.taskCode = this.fromPage == 'goldCoins' ? 'viewGoodsGold' : 'viewGoodsMoney';
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_POINT_EXPOSURE,
        });

        this.link = options.link;
        const win: any = window;
        this.loginSuccess();
        win.onShow = () => {
            VIPModule.getBasicInfo();
        };
        const { sharerUid } = options;
        if (sharerUid) {
            joinInvite({
                invite_type: 3,
                inviter_id: sharerUid,
                relate_id: 3,
            }).then((res) => {
                console.log(res);
            });
        }
        getInvitePoint({ auto_mark_read: 1 }).then((res) => {
            if (res.count > 0) {
                const CountObj = res.list.reduce(
                    (acc, item) => {
                        if (item.popup_type === 2) {
                            acc.count += Number(item.remark);
                        } else if (item.popup_type === 3) {
                            acc.consumeCount += Number(item.remark);
                        } else if (item.popup_type === 4) {
                            acc.hasPromotion = true;
                        }
                        return acc;
                    },
                    {
                        count: 0,
                        consumeCount: 0,
                        hasPromotion: false,
                    },
                );

                if (CountObj.hasPromotion) {
                    this.showPromotionDialog = true;
                } else if (CountObj.consumeCount > 0) {
                    this.ConsumeCount = CountObj.consumeCount;
                    this.showConsumeDialog = true;
                } else {
                    this.countTotal = CountObj.count;
                    this.showDialog = true;
                }
            }
        });
        // 触发数字动画
        this.$nextTick(() => {
            this.triggerNumberAnimation();
        });

        setTimeout(() => {
            this.callEnterPointMall();
        }, 3000);
    }

    // 触发数字动画
    triggerNumberAnimation() {
        // 重置动画状态
        this.isAnimating = false;
        this.$forceUpdate();
        // 延迟启动动画
        setTimeout(() => {
            this.isAnimating = true;
            this.$forceUpdate();
        }, 10);
    }

    closeDialog() {
        this.showDialog = false;
    }

    async loginSuccess() {
        await this.getScoreUncollect();
        this.getWaresPointsEnum();
        VIPModule.getBasicInfo();
        this.init();
        const { windowHeight } = uni.getSystemInfoSync();
        this.windowHeight = windowHeight;
        this.CloneSortList = uni.$u.deepClone(this.sortList);
        this.$nextTick(() => {
            this.pageIsTop();
        });
    }

    onShow() {
        this.isScrollLocked = false;
        // const win: any = window;
        // win.onAppPageShow = async () => {
        setTimeout(() => {
            // this.$refs.TimeoutTips.getViewGoodsTaskStatus();
            this.$refs.EarnMoneySpendTimeoutTips.getViewGoodsTaskStatus();
        }, 1000);
        // };
    }

    async init() {
        this.isScrollLocked = true;
        this._initPageData();
        await this.getData();
        this.isScrollLocked = false;
    }

    swiperChange(e) {
        console.log('swiperChange', e);
        const current = e.detail.current;
        this.swiperCurrent = current;
        this.tab = this.titleStatus[current].value;
        this.currentItem = this.titleStatus[current];
        this.current = current;
        const i = current - 1;
        this.currentTab = 'tab-' + i;
        this.init();
    }

    async getData() {
        this.isRender = true;
        const { tid } = this.currentItem;
        console.log('%c tid: ', 'font-size:16px;background-color: #7F2B82;color:#fff;', tid);
        const params = {
            source: 2,
            page: this.page,
            page_size: this.page_size,
            tab: this.tab,
            is_point: this.is_point,
            is_my: this.is_my,
            sort_rule: JSON.stringify(this.sort_rule)
        }
        const { list = [] } = await getWaresList(params);
        if (list.length < this.page_size) {
            this.isLoaded = true;
        }
        this.titleStatus.map((item, index) => {
            if (!this.waresList[index]) {
                this.waresList[index] = [];
            }
        });
        if (this.page == 1) {
            this.waresList[this.swiperCurrent] = list;
        } else {
            this.waresList[this.swiperCurrent] = this.waresList[this.swiperCurrent].concat(list);
        }
        this.allHeight = this.waresList[this.swiperCurrent].length;
        this.$forceUpdate();
        this.$nextTick(() => {
            this.getPageHeight();
            this.isRender = false;
        });
    }

    getPageHeight() {
        uni.createSelectorQuery()
            .select('.container')
            .boundingClientRect((rect) => {
                this.currentPageHeight = rect.height;
            })
            .exec();
    }

    inviteNewUser() {
        const url = `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/new_point?sharerUid=${this.user_id}`;
        const data = {
            ...Constants.GEN_SHARE_LINK_TYPE,
            jumpLink: url,
        };
        //  `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/share_undertaking_page?link=` + res.data
        Utils.newMessageChannel('SHARE', 'genShareLink', data, (res) => {
            console.log(res.data);
            const params = {
                target: 'wechat,qq,sina',
                type: 'web',
                content: {
                    url:
                        `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/inner_share_undertaking_page?link=` +
                        res.data,
                    share_image:
                        'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688220c82fb331950011310.png',
                    share_title: `一起追觅好物，共享10倍积分`,
                    share_desc: `上线秒送500积分`,
                },
            };
            Utils.messageChannel('share', params);
        });
    }

    onUnload() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    onHide() {
        this.$refs.EarnMoneySpendTimeoutTips.clearTimer();
        this.$refs.EarnMoneySpendTimeoutTips.clearInterfaceRequest();
    }

    handleShare(type: 'web' | 'image') {
        this.show = false;
        Utils.cardShare(type)({
            target: 'wechat,weixin_circle,qq,sina,image_template,download',
            link: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/shop_point`,
            jumpLink: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/shop_point`,
        }, {
            content: {
                url: `${process.env.VUE_APP_BASE_URL}front/AppMall/?isNavBar=0#/pagesA/point/shop_point`,
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/688220c82fb331950011310.png',
                title: `一起追觅好物，共享10倍积分`,
                desc: `上线秒送500积分`,
            },
            extras: {
                type: 'activity',
                id: Number(process.env.VUE_APP_SECKILL_ACTIVITY_ID).toString(),
                goods: {
                    ...this.productInfo,
                },
                copyText: '您的积分再不用就浪费啦！限量盲盒玩偶/配件耗材都开通积分兑换啦！库存有限！',
            }
        });
    }

    // 初始化数据
    _initPageData() {
        this.page = 1; // 重置页数
        this.isLoaded = false; // 重置触底状态
    }

    // 监听页面滚动
    onPageScroll(e) {
        if (this.isScrollLocked) return;
        const num = Utils.pxToRpx(e.scrollTop);
        if (num > 0) {
            this.controlBgc = true;
        } else {
            this.controlBgc = false;
        }

        if (this.tebTop - this.pagePaddingTop <= num) {
            this.controlSticky = true;
        } else {
            this.controlSticky = false;
            this.dropDownShow = false;
        }

        const scrollTop = e.scrollTop;
        if (this.isLoaded || this.isRender) return;
        if (this.currentPageHeight - scrollTop - this.windowHeight < this.diff) {
            this.page++;
            this.getData();
        }
    }

    handlePointsClick() {
        uni.navigateTo({
            url: '/pages/vipCenter/vipCenter',
        });
        Utils.logTrace({
            module: Constants.LOG_TRACE_MODULE_DREAME,
            event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_INDEX_TOP_POINTS_CLICK,
        });
    }

    handlePointDetailsClick() {
        uni.navigateTo({
            url: '/pagesA/pointDetails/pointDetails',
        });
    }

    // 获取teb距离页面top值
    pageIsTop() {
        uni.createSelectorQuery()
            .select('.points-mall')
            .boundingClientRect((res) => {
                this.tebTop = Utils.pxToRpx(res.top);
            })
            .exec();
    }

    async Collect(index, item, id) {
        await this.getScoreCollect({ id });
        this.performList.splice(index, 1);
        this.pointList.unshift({
            id: item.id,
            eventName: item.eventName,
            numValue: item.numValue,
            updateTime: new Date().getTime(),
        });
        if (this.performList.length === 0) {
            this.points_show = false;
            setTimeout(() => {
                this.getScrollTopHeight();
                this.pageIsTop();
            }, 200);
        }
        VIPModule.getBasicInfo();
    }

    async CollectAll() {
        await this.getScoreCollectAll();
        VIPModule.getBasicInfo();
    }

    toPointDetails() {
        uni.navigateTo({
            url: '/pagesA/pointDetails/pointDetails',
        });
    }

    async swichMenu(index, item) {
        this.currentItem = item;
        const i = index - 1;
        this.currentTab = 'tab-' + i;
        this.current = index;
        uni.pageScrollTo({
            scrollTop: 0, // 滚动到吸顶位置
            duration: 0, // 滚动动画的持续时间，单位为ms
        });
        this.swiperCurrent = index;
    }

    async exchangeClick(index) {
        this.dropDownShow = false;

        const tabIndex = this.activeTabs.indexOf(index);
        if (tabIndex !== -1) {
            this.activeTabs.splice(tabIndex, 1);

            if (index === 0) {
                this.is_point = 0;
                this.sortList = this.CloneSortList;
            } else if (index === 1) {
                this.is_my = 0;
            }
        } else {
            this.activeTabs.push(index);

            if (index === 0) {
                this.is_point = 1;
                this.sortList = this.sortList.slice(2);
            } else if (index === 1) {
                this.is_my = 1;
            }
        }

        await this.init();

        uni.pageScrollTo({
            scrollTop: this.scrollTopHeight, // 滚动到吸顶位置
            duration: 0, // 滚动动画的持续时间，单位为ms
        });
    }

    // 计算吸顶位置
    getScrollTopHeight() {
        if (this.points_show) {
            this.scrollTopHeight = uni.upx2px(436 + 124 - this.pagePaddingTop);
        } else {
            this.scrollTopHeight = uni.upx2px(436 - this.pagePaddingTop);
        }
    }

    get levelIcon(): string {
        const level: any = VIPModule.basicInfo.level.level;
        // 定义等级与图片的映射关系
        const levelIcons: { [key: string]: string } = {
            v1: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687637a03aa022400011297.png',
            v4: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763749bba6d7690013034.png',
            v2: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763765cf8708500011384.png',
            v5: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763782964976160022687.png',
            v3: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68763725e57ce9400010630.png',
        };

        // 默认使用v1等级的图片
        return levelIcons[level];
    }

    toSearch() {
        this.isScrollLocked = true;
        uni.navigateTo({
            url: '/pagesA/search/search?search_type=' + 1,
        });
    }
}
</script>

<style lang="scss" scoped>
@import './shop_point.scss';

.search_container {
    display: flex;
    width: 100%;
    height: 72rpx;
    align-items: center;
    justify-content: center;
    margin-top: 10rpx;
    padding: 0 22rpx;

    .search_input {
        background-color: #fff;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 72rpx;
        padding: 0 30rpx;
    }
}

.search_input_input {
    color: #a6a6a6 !important;
    height: 100%;
    font-size: 28rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
}

.points-num {
    display: flex;
    position: absolute;
    top: 1/2;
    right: 20rpx;
    align-items: center;
    justify-content: center;

    .points-num-level {
        display: flex;
        align-items: center;
        justify-content: center;

        image {
            width: 64rpx;
            height: 64rpx;
        }

        .points-num-level-text {
            font-size: 28rpx;
            margin-left: 12rpx;
            font-weight: normal;
            color: #ca7538;
            white-space: nowrap;
            /* 禁止换行 */
            overflow: hidden;
            /* 超出内容隐藏 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            max-width: 200px;
            /* 需要限制宽度才能生效 */
        }
    }

    .points-num-text {
        font-size: 20rpx;
        font-weight: normal;
        color: #c2a271;
    }
}

/* 全屏gif动效样式 */
.gif-animation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gif-animation-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.gif-animation-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
}
.share_btn {
    position: fixed;
    bottom: 120rpx;
    right: 40rpx;
    width: 100rpx;
    height: 100rpx;
    z-index: 9999;
    img {
        width: 100%;
    }
}
</style>
