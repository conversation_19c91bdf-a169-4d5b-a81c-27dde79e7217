<template>
    <view>
        <view class="contents-container" v-if="isNetworkConnect">
            <view class="title" :style="{ 'padding-top': statusBarHeight + 'rpx' }">
                <view class="u-flex u-row-between" style="width: 750rpx; padding-right: 30rpx; background: #ffffff">
                    <view class="u-flex" @click="backPage">
                        <img src="@/static/goodDetail/back2.png"
                            style="width: 48rpx; height: 48rpx; margin-left: 20rpx" />
                        <view style="margin-left: 20rpx; font-size: 36rpx; font-weight: 600; color: #121212">我的好友</view>
                    </view>
                    <view class="community-search" @click="goSearch">
                        <img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/686a1623a0de76590410838.png"
                            alt="" />
                    </view>
                </view>
                <block>
                    <view class="swiper-box">
                        <view class="swiper-item">
                            <scroll-view class="scroll-view" scroll-y using-sticky :refresher-enabled="canPullDown"
                                :refresher-triggered="isRefreshing" :upper-threshold="0" :lower-threshold="300"
                                @refresherrefresh="refresherrefresh" @scrolltolower="onScrollToLower">
                                <view style="margin-top: 24rpx; padding-bottom: 100rpx;">
                                    <view v-if="personalList.length" class="u-item-box"
                                        v-for="(item, index) in personalList" :key="index"
                                        @click="goUserInfo(item.user_id)">
                                        <view class="u-flex">
                                            <view style="position: relative">
                                                <img :src="item.avatar"
                                                    style="width: 100rpx; height: 100rpx; border-radius: 50rpx" />
                                                <image v-if="item.user_avatar_icon" style="
                                                        width: 40rpx;
                                                        height: 40rpx;
                                                        position: absolute;
                                                        bottom: 0;
                                                        right: 0;
                                                        z-index: 99999;
                                                    "
                                                    :src="item.user_avatar_icon"
                                                    mode="aspectFill"></image>
                                            </view>
                                            <view style="margin-left: 20rpx; width: 320rpx;">
                                                <view class="u-item-title">{{ item.nick_name }}</view>
                                                <view class="u-item-content">{{ item.uid }}</view>
                                            </view>
                                        </view>
                                        <view class="u-item-flow">互相关注</view>
                                    </view>
                                    <view v-if="!isRefreshing && !personalList.length">
                                        <view class="u-item-box">
                                            <view class="u-item-left">
                                                <img src="@/static/friend/icon_group.png"
                                                    style="width: 48rpx; height: 48rpx" />
                                                <view style="font-size: 28rpx; color: #777777; margin-left: 16rpx">
                                                  没有好友列表，邀请好友来发消息</view>
                                            </view>
                                            <view class="u-item-right" @click="toInvite">去邀请</view>
                                        </view>
                                    </view>
                                    <view class="u-recommend-box">
                                        <view class="u-flex u-row-between">
                                            <view class="u-recommend-title">推荐好友</view>
                                            <view class="u-flex" style="margin-right: 24rpx;" @click="goRefresh">
                                                <img src="@/static/friend/icon_friend_refresh.png"
                                                    style="width: 40rpx; height: 40rpx;">
                                                <view style="font-size: 28rpx; color: #777777; margin-left: 10rpx;">刷新
                                                </view>
                                            </view>
                                        </view>
                                        <view class="u-recommend-content" v-for="(item, index) in recommentList"
                                            :key="index">
                                            <view class="u-recommend-left">
                                                <view style="width: 96rpx; position: relative;" @click="goUserInfo(item.user_id)">
                                                    <img :src="item.avatar"
                                                        style="width: 96rpx; height: 96rpx; border-radius: 48rpx; position: relative;">
                                                    <img v-if="item.user_avatar_icon" :src="item.user_avatar_icon" style="width: 35rpx;height: auto; position: absolute;bottom: 0;right: 0;" />
                                                </view>
                                                <view class="u-flex-col" style="margin-left: 32rpx; width: 320rpx;">
                                                    <view style="font-size: 32rpx; color: #121212;">{{ item.nick_name }}
                                                    </view>
                                                    <view style="font-size: 28rpx; color: #777777;">{{ item.uid }}
                                                    </view>
                                                </view>
                                            </view>
                                            <view class="u-recomment-right" v-if="item.follow_status == 0"
                                                @click="handleUserFollow(item, index)">
                                                <img src="@/static/friend/icon_recomment_add.png"
                                                    style="width: 32rpx; height: 32rpx;">
                                                关注
                                            </view>
                                            <view class="u-recomment-right-yes" v-if="item.follow_status == 1"
                                                @click="handleUserFollow(item, index)">
                                                已关注
                                            </view>
                                        </view>
                                    </view>
                                    <view class="u-good-box" style="margin-top: 40rpx;">
                                        <view class="u-good-title">推荐商品</view>
                                        <view class="u-good-content">
                                            <block v-for="(itm, index) in goodsList" :key="index">
                                                <view class="class-item" @click="gotoGdetailPage(itm.gid, itm.name)">
                                                    <view class="class-item-top"
                                                        :class="{ extend: !!itm.atmosphere_img }">
                                                        <LazyImage :src="itm.cover_image" class="lazyImg"> </LazyImage>
                                                    </view>
                                                    <view class="class-item-bottom">
                                                        <view class="u-good-title-img">
                                                            <view class="text"
                                                                style="font-size: 28rpx; color: #121212;">
                                                                <!-- <img class="indent-icon"
                                                                    src="@/static/friend/icon_brand.png"
                                                                    style="width: 52rpx; height: 26rpx;"> -->
                                                                {{ itm.name }}
                                                            </view>
                                                        </view>
                                                        <view
                                                            style="font-size: 28rpx; color: #404040; font-weight: 500; margin-top: 24rpx;">
                                                            ￥{{ itm.price }}</view>
                                                    </view>
                                                </view>
                                            </block>
                                        </view>
                                    </view>
                                    <view class="u-good-bottom" @click="gotoMoreGood(2, '商品')">
                                        查看更多商品
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </block>
            </view>
        </view>
        <NoNetwork v-else :isNetworkConnect="isNetworkConnect" @onRefresh="onRefresh" />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import CustomSwiper from './components/CustomSwiper.vue';
import NoNetwork from '@/components/NoNetwork/NoNetwork.vue';
import { AppModule } from '@/store/modules/app';
import { UserModule } from '@/store/modules/user';
import Utils from '@/common/Utils';
import DefaultPage from '@/components/DefaultPage/DefaultPage.vue';
import { searchFollowList, userFollow, userRelation, userFollowDelete, getRecommentList } from '@/http/requestGo/community';
import { goodsList } from '@/http/goods';
import Constants from '@/common/Constants';

@Component({
    components: {
        CustomSwiper,
        NoNetwork,
        Comment,
        DefaultPage,
    },
})
export default class Contents extends Vue {
    $refs: {
        exploreRef;
        CommunityRef;
    };

    // 网络状态
    get isNetworkConnect(): Boolean {
        return AppModule.isNetworkConnect;
    }

    get statusBarHeight(): number {
        return AppModule.statusBarHeight;
    }

    public personalList: any = [];
    public recommentList: any = [];
    public allCommentList: any = [];
    public goodsList: any = []
    public currentPage: number = 1;
    public commentCount: number = 0;

    public scrollTop: number = 0; // 控制scroll-view滚动位置
    public flowList: any = [];
    public isLoading: Boolean = true;
    public finished: Boolean = false; // 是否加载完成
    public tabLoading: Boolean = false; // 是否加载完成
    public isRefreshing: Boolean = false;

    public page: number = 1;
    public page_size: number = 10;
    public Pid: number = 0;
    public totalPages: number = 1;
    public timer: any = null;
    public index: number = 0;
    public creator: any = -1;
    public operContentOpen: boolean = false; // 二次确认弹框
    public operContent: any = {
        type: '',
        titie: '',
        tip: '',
        confirmText: '',
        cancelText: '',
    };

    refresherrefresh() {
        // 重置分页信息，重新加载第一页
        this.page = 1;
        this.totalPages = 1;
        // this.personalList = [];
        this.isRefreshing = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
            await this.init();
            this.isRefreshing = false;
        }, 200);
        this.$emit('refresh');
    }

    onScrollToLower() {
        console.log(';onScrollToLower');
        if (this.page >= this.totalPages) {
            return;
        }
        this.loadMore();
    }

    loadMore() {
        // 如果正在加载中或已加载全部数据，不执行操作
        if (this.isLoading || this.page >= this.totalPages) return;
        this.page++;
        this.init();
    }

    async onLoad() {
        this.personalList = [];
        this.init();
        this.getRecommentList()
        this.getRecommendGood()
    }

    async onRefresh() {
        this.init();
    }

    async init() {
        if (this.page > this.totalPages) {
            this.isLoading = false;
            return;
        }
        try {
            this.isLoading = true;
            const page_size: number = 10;
            const res = await searchFollowList({ keyword: '', page: this.page, page_size: this.page_size, type: [3] });
            console.log('res ===', res);
            if (res.success) {
                const { list = [] } = res.data;
                if (list === null || list.length < page_size) {
                    this.page = 1;
                }
                if (list.length > 0 && this.page == 1) {
                    this.personalList = list;
                }
                if (list.length > 0 && this.page != 1) {
                    this.personalList = this.personalList.concat(list);
                }
                console.log('personalList ==== ', this.personalList);
                this.page++;
            }
            this.canPullDown = true;
            this.isLoading = false;
        } catch (e) {
            this.canPullDown = true;
            this.isLoading = false;
            console.error('getWlist err=', e);
        }
    }

    async getRecommentList() {
        this.recommentList = []
        const res = await getRecommentList({ user_id: '', page: this.page, page_size: 21 });
        console.log('res', res)
        if (res.success) {
            this.allCommentList = res.data.list;
            if (res.data.list) {
                if (res.data.list.length >= 3) {
                    for (let i = 0; i < 3; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                } else {
                    for (let i = 0; i < res.data.list.length; i++) {
                        this.recommentList.push(res.data.list[i]);
                    }
                }
                console.log('this.recommentList', this.recommentList);
            }
        }
    }

    async getRecommendGood() {
        const params = {
            page: 1,
            page_size: 4,
            single: 1, // 区分券后价 新老版本 不传会直接查出券后价 传1 通过下面接口查出券后价
            type: -2,
            tid: -1,
        };
        const res = await goodsList(params)
        console.log('res', res)
        if (res.list) {
            for (let index = 0; index < res.list.length; index++) {
                const element = res.list[index];
                if (index <= 3) {
                    this.goodsList.push(element);
                }
            }
        }
    }

    goRefresh() {
        if (this.commentCount >= 10) {
            this.commentCount = 0;
            this.getRecommentList()
        } else {
            const count = this.allCommentList.length / 3;
            if (this.currentPage == count) {
                this.currentPage = 1;
            } else {
                this.currentPage++;
            }
            this.recommentList = this.allCommentList.slice((this.currentPage - 1) * 3, (this.currentPage - 1) * 3 + 3);
        }
    }

    clickFriend(item, index) {
        if (index !== -1) {
            this.$set(this.recommentList[index], 'follow_status', 1);
        }
    }

    toInvite() {
        console.log('toInvite')
        Utils.navigateTo(`/pages/contents/components/myFriendList/myFriendList`);
    }

    gotoGdetailPage(gid: string, name: string) {
        Utils.logTrace(
            {
                module: Constants.LOG_TRACE_MODULE_DREAME,
                event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_PRODUCT_DETAIL_CLICK,
                id: Number(gid),
                name: name,
            },
            true,
        );
        Utils.reportEvent('product_click', { product_id: gid });
        Utils.navigateTo(`/pagesB/goodsDetail/goodsDetail?gid=${gid}`);
    }

    goUserInfo(creator) {
        console.log('goUserInfo', creator);
        Utils.jumpPersonHome(creator);
    }

    handleUserFollow(item, index) {
        console.log(item)
        this.index = index;
        this.creator = item.user_id;

        uni.$u.throttle(async () => {
            const user_id = Number(UserModule.user_id);
            const other_id = Number(item.user_id);
            if (item.follow_status === 0) {
                userFollow({ user_id, followed_user_id: other_id }).then(() => {
                    Utils.Toast('关注成功');
                    this.commentCount = +this.commentCount;
                    userRelation({ user_id, to_user_id: other_id }).then((res) => {
                        item.follow_status = res.data.follow_status;
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                });
            } else {
                const res: any = await userFollowDelete({ followed_user_id: +this.creator });
                if (res.success) {
                    this.commentCount = -this.commentCount;
                    Utils.Toast('取消关注');
                    userRelation({ user_id: +UserModule.user_id, to_user_id: +this.creator }).then((res) => {
                        this.changeFollowStatus(item, res.data.follow_status);
                    });
                }
                this.operContentOpen = false;
                setTimeout(() => {
                    this.operContent = { type: '' };
                }, 500);
            }
        }, 500);
    }

    gotoMoreGood(tid, title) {
        if (tid == 20) {
            Utils.reportEvent('parts_zone', {});
        }
        if (title) {
            Utils.reportEvent('see_all', {
                good_type: title,
            });
        }
        uni.setStorageSync('tid', tid);
        Utils.navigateTo('/pages/shop/shop?tid=' + tid);
    }

    changeFollowStatus(item, status) {
        this.recommentList.forEach((i) => {
            if (i.user_id === item.user_id) {
                i.follow_status = status;
            }
        });
    }

    goSearch() {
        Utils.navigateTo(`/pagesC/search/search?type=user`);
    }

    public canPullDown: boolean = true;

    backPage() {
        Utils.goBack();
    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view-refresher {
    background-color: #ffffff !important;
}

.swiper-box {
    height: 100vh;

    // #ifdef H5
    ::v-deep .uni-swiper-wrapper {
        overflow: hidden !important;
    }

    // #endif
    .swiper-item {
        width: 100%;
        height: 100%;

        .scroll-view {
            height: 100%;
            background-color: #ffffff;

            // #ifdef H5
            ::v-deep .uni-scroll-view {
                .uni-scroll-view-content {
                    height: fit-content;
                }
            }

            // #endif

            .u-item-box {
                height: 152rpx;
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 24rpx;
                justify-content: space-between;

                .u-item-left {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                }

                .u-item-right {
                    background-color: #f5edd6;
                    color: #8c6533;
                    width: 154rpx;
                    height: 56rpx;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    padding: 6rpx 10rpx;
                    border-radius: 28rpx;
                    font-size: 28rpx;
                }

                .u-item-title {
                    font-size: 32rpx;
                    color: #121212;
                    font-weight: 400;
                }

                .u-item-content {
                    font-size: 28rpx;
                    color: #777777;
                }

                .u-item-flow {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    font-size: 28rpx;
                    color: #a6a6a6;
                    border-radius: 32rpx;
                    height: 64rpx;
                    width: 192rpx;
                    border: 2rpx solid #a6a6a6;
                }
            }

            .u-recommend-box {
                width: 100%;
                display: flex;
                flex-direction: column;
                margin-top: 40rpx;

                .u-recommend-title {
                    font-size: 36rpx;
                    color: #111111;
                    font-weight: 600;
                    padding: 0 32rpx;
                }

                .u-recommend-content {
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                    padding: 24rpx 20rpx;

                    .u-recommend-left {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                    }

                    .u-recomment-right {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        border: 2rpx solid #DBC49A;
                        height: 64rpx;
                        width: 192rpx;
                        border-radius: 32rpx;
                        font-size: 28rpx;
                        color: #C2A271;
                        font-weight: 500;
                    }

                    .u-recomment-right-yes {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        border: 2rpx solid #A6A6A6;
                        height: 64rpx;
                        width: 192rpx;
                        border-radius: 32rpx;
                        font-size: 28rpx;
                        color: #A6A6A6;
                        font-weight: 500;
                    }
                }
            }

            .u-good-box {
                display: flex;
                flex-direction: column;

                .u-good-title {
                    font-size: 36rpx;
                    color: #111111;
                    font-weight: 600;
                    padding: 0 64rpx;
                }

                .u-good-content {
                    @include flex(row, space-between, flex-start, wrap);
                    padding: 0 8rpx 0;
                }

                .class-item {
                    width: calc(calc(100vw - 48rpx)/2);
                    background: #ffffff;
                    border-radius: 24rpx;
                    margin-bottom: 16rpx;
                    overflow: hidden;

                    .class-item-top {
                        width: 100%;
                        padding-top: 113%; // // 图片尺寸 1170*1320 高宽比 1.13
                        margin: 0 auto 36rpx;
                        position: relative;
                        // background-color: #f5f5f5; // 添加默认背景色
                        overflow: hidden; // 防止内容溢出

                        img {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            z-index: 2; // 确保在占位层之上
                        }

                        ::v-deep image {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            border-radius: 8rpx 8rpx 0 0;
                            z-index: 2; // 确保在占位层之上
                        }
                    }

                    .class-item-bottom {
                        padding: 0 32rpx 12rpx;

                        .u-good-title-img {
                            overflow: hidden;

                            .text {
                                display: inline;
                                /* 确保文本环绕 */
                                text-align: justify;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 2;
                                text-overflow: ellipsis;
                                // text-indent: 67rpx;
                                overflow: hidden;
                                position: relative;

                                .indent-icon {
                                    width: 52rpx;
                                    height: 26rpx;
                                    margin-top: 10rpx;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                }
                            }
                        }
                    }
                }
            }

            .u-good-bottom {
                background-color: #F5EDD6;
                color: #8C6533;
                height: 88rpx;
                border-radius: 44rpx;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                font-size: 32rpx;
                font-weight: 500;
                margin: 60rpx 24rpx 208rpx 24rpx;
            }

            .u-no-data {
                width: 100%;
                font-size: 28rpx;
                color: #999999;
                text-align: center;
                margin-top: 206rpx;
            }
        }
    }
}

.contents-container {
    position: relative;
    background: #ffffff;

    // ::v-deep .uni-swiper-wrapper {
    //     overflow: auto;
    // }

    .title {
        display: flex;
        flex-direction: column;
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        z-index: 3;
        color: $color-2A2A2A;
        display: flex;
        background: #ffffff;
        align-items: unset;
        // #ifdef H5
        justify-content: center;
        // #endif
        // #ifdef MP-WEIXIN
        justify-content: flex-start;
        // #endif

        .status-bar-left {
            flex-direction: column;
            margin-right: auto;
            justify-content: start;
            align-items: start;

            .text {
                margin-top: 10rpx;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 27rpx;
                color: #121212;
                line-height: 38rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }

        .status-bar-image {
            padding: 2rpx 30rpx 0 38rpx;
            background: #f6f6f6;
            display: flex;
            align-items: center;

            .left-bar {
                .back_bar {
                    padding-right: 20rpx;

                    img,
                    image {
                        height: 50rpx;
                    }
                }
            }

            .title-image {
                background: url('https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202502/720574-1740109971236.png') no-repeat;
                background-size: cover;
                width: 212rpx;
                height: 29.3rpx;
            }

            .text {
                font-size: 25rpx;
            }

            text {
                margin-left: 14rpx;
                font-family: MiSans;
                font-size: 40rpx;
                font-weight: 600;
                line-height: normal;
                letter-spacing: 0px;
                font-variation-settings: 'opsz' auto;
                color: #040000;
            }
        }

        .status-bar {
            width: 100%;
        }
    }
}

.community-search,
.community-publish {
    margin-left: 30rpx;

    img {
        display: block;
        height: 46rpx;
        width: 46rpx;
    }
}

.loading-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 0 50rpx 0;
    width: 100%;
}
</style>
