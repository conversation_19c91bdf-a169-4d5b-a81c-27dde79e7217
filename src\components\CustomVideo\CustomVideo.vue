<template>
    <iframe :onload="onloadCode" style="width:100%;height:100%;background: #000;"></iframe>
</template>
<script lang="ts">
import { Prop, Vue, Component } from 'vue-property-decorator';

@Component
export default class CustomVideo extends Vue {
    @Prop({ type: String, default: '' })
    readonly videoUrl!: String;

    get onloadCode(): string {
        const onloadCode = `const url = '${this.videoUrl}';
        this.contentWindow.document.body.innerHTML = '<video style="width: 100%;height: 100%" controls="controls"  src="' + url + '"></video>';
        `
        return onloadCode
    }
}
</script>
