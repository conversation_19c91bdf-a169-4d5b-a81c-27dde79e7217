<template>
    <view class="one-yurn-buy-container" :style="{ backgroundImage: `url(${bgUrl})` }">
        <view class="one-yurn-buy-wrap" @click="handleJump">
            <view class="one-yurn-buy-goods-list">
                <view class="one-yurn-buy-goods-item" v-for="(item, index) in oneGoodsList.slice(0, 3)" :key="index">
                    <image class="one-yurn-buy-goods-img" :src="item.goods_image" mode="contain"></image>
                    <view class="one-yurn-buy-goods-price">
                        <!-- <text class="icon">¥</text> -->
                        <text class="one-yurn-buy-price">￥{{ item.sale_price }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';
import Constants from '@/common/Constants';

@Component({})
export default class oneYunBuyItem extends Vue {
    @Prop({ type: Array, default: [] })
    readonly oneGoodsList!: Array<any>;

    @Prop({ type: String, default: '' })
    readonly bgUrl!: string;

    @Prop({ type: String, default: '' })
    readonly jumpUrl!: string;

    @Prop({ type: String, default: '' })
    readonly type!: 'fiveHalf' | 'oneYun' | 'eightDis' | 'sixDis';

    // 五折购
    handleJump() {
        if (this.type === 'fiveHalf') {
            Utils.logTrace(
                {
                    module: Constants.LOG_TRACE_MODULE_DREAME,
                    event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_HALF_PRICE_PIRCHASE,
                },
                true,
            );
        } else if (this.type === 'oneYun') {
            Utils.logTrace(
                {
                    module: Constants.LOG_TRACE_MODULE_DREAME,
                    event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_ONEMONEY_CLICK,
                },
                true,
            );
        } else if (this.type === 'eightDis') {
            Utils.logTrace(
                {
                    module: Constants.LOG_TRACE_MODULE_DREAME,
                    event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS
                        .LOG_TRACE_MODULE_DREAME_EVENTS_EIGHT_DISCOUNT_ACTIVITY_CLICK,
                },
                true,
            );
        } else if (this.type === 'sixDis') {
            Utils.logTrace(
                {
                    module: Constants.LOG_TRACE_MODULE_DREAME,
                    event: Constants.LOG_TRACE_MODULE_DREAME_EVENTS.LOG_TRACE_MODULE_DREAME_EVENTS_SIX_DISCOUNT_ACTIVITY_CLICK,
                },
                true,
            );
        }
        Utils.navigateTo(this.jumpUrl);
    }
}
</script>
<style lang="scss" scoped>
.one-yurn-buy-container {
    height: 100%;
    border-radius: 16rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .one-yurn-buy-wrap {
        width: 100%;
        height: 100%;
        position: relative;
        .one-yurn-buy-goods-list {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            position: absolute;
            gap: 16rpx;
            bottom: 16rpx;
            left: 16rpx;
            .one-yurn-buy-goods-item {
                width: 90rpx;
                height: 90rpx;
                border-radius: 16rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                .one-yurn-buy-goods-img {
                    width: 68rpx;
                    height: 68rpx;
                    border-radius: 18rpx;
                    margin-top: 2;
                    object-fit: contain;
                }
                .one-yurn-buy-goods-price {
                    font-size: 13rpx;
                    color: #ff7a1c;
                    height: 16px;
                    line-height: 16px;
                    text-align: center;
                }
            }
        }
    }
}
</style>
